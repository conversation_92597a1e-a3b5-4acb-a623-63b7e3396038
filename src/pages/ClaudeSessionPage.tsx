import React from 'react';
import type { Session } from "@/lib/api";
import { ClaudeCodeSession } from "@/components/ClaudeCodeSession.refactored";

interface ClaudeSessionPageProps {
  session?: Session;
  initialProjectPath?: string;
  onBack: () => void;
  onProjectSettings?: (projectPath: string) => void;
  className?: string;
  onStreamingChange?: (isStreaming: boolean, sessionId: string | null) => void;
}

export const ClaudeSessionPage: React.FC<ClaudeSessionPageProps> = (props) => {
  return (
    <ClaudeCodeSession {...props} />
  );
};
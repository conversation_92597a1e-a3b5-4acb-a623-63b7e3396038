import React, { createContext, useContext, useState, useEffect, useRef, useMemo, useCallback } from "react";
import { api, type Session } from "@/lib/api";
import { open } from "@tauri-apps/plugin-dialog";
import { listen, type UnlistenFn } from "@tauri-apps/api/event";
import type { ClaudeStreamMessage } from "@/components/AgentExecution";
import { useModeContext } from "@/components/IntelligentModeIntegration";
import { useProjectContext } from "@/contexts/ProjectContextProvider";
import { useGitContext } from "@/contexts/GitContextProvider";

// 1. Define the shape of the context data
interface SessionContextType {
  // State
  projectPath: string;
  setProjectPath: React.Dispatch<React.SetStateAction<string>>;
  messages: ClaudeStreamMessage[];
  isLoading: boolean;
  error: string | null;
  rawJsonlOutput: string[];
  copyPopoverOpen: boolean;
  setCopyPopoverOpen: React.Dispatch<React.SetStateAction<boolean>>;
  totalTokens: number;
  claudeSessionId: string | null;
  effectiveSession: Session | null;
  showTimeline: boolean;
  setShowTimeline: React.Dispatch<React.SetStateAction<boolean>>;
  showSettings: boolean;
  setShowSettings: React.Dispatch<React.SetStateAction<boolean>>;
  showForkDialog: boolean;
  setShowForkDialog: React.Dispatch<React.SetStateAction<boolean>>;
  showSlashCommandsSettings: boolean;
  setShowSlashCommandsSettings: React.Dispatch<React.SetStateAction<boolean>>;
  queuedPrompts: Array<{ id: string; prompt: string; model: "sonnet" | "opus" }>;
  showPreview: boolean;
  setShowPreview: React.Dispatch<React.SetStateAction<boolean>>;
  previewUrl: string;
  setPreviewUrl: React.Dispatch<React.SetStateAction<string>>;
  showPreviewPrompt: boolean;
  setShowPreviewPrompt: React.Dispatch<React.SetStateAction<boolean>>;
  splitPosition: number;
  setSplitPosition: React.Dispatch<React.SetStateAction<number>>;
  isPreviewMaximized: boolean;
  setIsPreviewMaximized: React.Dispatch<React.SetStateAction<boolean>>;
  forkSessionName: string;
  setForkSessionName: React.Dispatch<React.SetStateAction<string>>;
  timelineVersion: number;
  
  // Derived State
  displayableMessages: ClaudeStreamMessage[];

  // Handlers
  handleSelectPath: () => Promise<void>;
  handleSubmitPrompt: (prompt: string) => Promise<void>;
  handleCopyAsJsonl: () => Promise<void>;
  handleCopyAsMarkdown: () => Promise<void>;
  handleCheckpointSelect: () => Promise<void>;
  handleCancelExecution: () => Promise<void>;
  handleFork: (checkpointId: string) => void;
  handleConfirmFork: () => Promise<void>;
  handleLinkDetected: (url: string) => void;
  handleClosePreview: () => void;
  handlePreviewUrlChange: (url: string) => void;
  handleTogglePreviewMaximize: () => void;
  removeQueuedPrompt: (id: string) => void;
}

// 2. Create the context with a default value
const SessionContext = createContext<SessionContextType | undefined>(undefined);

// 3. Create the Provider component
interface SessionProviderProps {
  children: React.ReactNode;
  session?: Session;
  initialProjectPath?: string;
  onStreamingChange?: (isStreaming: boolean, sessionId: string | null) => void;
}

export const SessionProvider: React.FC<SessionProviderProps> = ({ 
  children, 
  session, 
  initialProjectPath = "",
  onStreamingChange 
}) => {
  const modeContext = useModeContext();
  const projectContextHook = useProjectContext();
  const gitContextHook = useGitContext();
  const [projectPath, setProjectPath] = useState(initialProjectPath || session?.project_path || "");
  const [messages, setMessages] = useState<ClaudeStreamMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rawJsonlOutput, setRawJsonlOutput] = useState<string[]>([]);
  const [copyPopoverOpen, setCopyPopoverOpen] = useState(false);
  const [isFirstPrompt, setIsFirstPrompt] = useState(!session);
  const [totalTokens, setTotalTokens] = useState(0);
  const [extractedSessionInfo, setExtractedSessionInfo] = useState<{ sessionId: string; projectId: string } | null>(null);
  const [claudeSessionId, setClaudeSessionId] = useState<string | null>(null);
  const [showTimeline, setShowTimeline] = useState(false);
  const [timelineVersion, setTimelineVersion] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [showForkDialog, setShowForkDialog] = useState(false);
  const [showSlashCommandsSettings, setShowSlashCommandsSettings] = useState(false);
  const [forkCheckpointId, setForkCheckpointId] = useState<string | null>(null);
  const [forkSessionName, setForkSessionName] = useState("");
  const [queuedPrompts, setQueuedPrompts] = useState<Array<{ id: string; prompt: string; model: "sonnet" | "opus" }>>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [previewUrl, setPreviewUrl] = useState("");
  const [showPreviewPrompt, setShowPreviewPrompt] = useState(false);
  const [splitPosition, setSplitPosition] = useState(50);
  const [isPreviewMaximized, setIsPreviewMaximized] = useState(false);

  const unlistenRefs = useRef<UnlistenFn[]>([]);
  const hasActiveSessionRef = useRef(false);
  const queuedPromptsRef = useRef(queuedPrompts);
  const isMountedRef = useRef(true);
  const isListeningRef = useRef(false);

  useEffect(() => {
    queuedPromptsRef.current = queuedPrompts;
  }, [queuedPrompts]);

  const effectiveSession = useMemo(() => {
    if (session) return session;
    if (extractedSessionInfo) {
      return {
        id: extractedSessionInfo.sessionId,
        project_id: extractedSessionInfo.projectId,
        project_path: projectPath,
        created_at: Date.now(),
      } as Session;
    }
    return null;
  }, [session, extractedSessionInfo, projectPath]);

  const displayableMessages = useMemo(() => {
    return messages.filter((message, index) => {
      if (message.isMeta && !message.leafUuid && !message.summary) return false;
      if (message.type === "user" && message.message) {
        if (message.isMeta) return false;
        const msg = message.message;
        if (!msg.content || (Array.isArray(msg.content) && msg.content.length === 0)) return false;
        if (Array.isArray(msg.content)) {
          let hasVisibleContent = false;
          for (const content of msg.content) {
            if (content.type === "text") {
              hasVisibleContent = true;
              break;
            }
            if (content.type === "tool_result") {
              let willBeSkipped = false;
              if (content.tool_use_id) {
                for (let i = index - 1; i >= 0; i--) {
                  const prevMsg = messages[i];
                  if (prevMsg.type === 'assistant' && prevMsg.message?.content && Array.isArray(prevMsg.message.content)) {
                    const toolUse = prevMsg.message.content.find((c: any) => c.type === 'tool_use' && c.id === content.tool_use_id);
                    if (toolUse) {
                      const toolName = toolUse.name?.toLowerCase();
                      const toolsWithWidgets = ['task', 'edit', 'multiedit', 'todowrite', 'ls', 'read', 'glob', 'bash', 'write', 'grep'];
                      if (toolsWithWidgets.includes(toolName) || toolUse.name?.startsWith('mcp__')) {
                        willBeSkipped = true;
                      }
                      break;
                    }
                  }
                }
              }
              if (!willBeSkipped) {
                hasVisibleContent = true;
                break;
              }
            }
          }
          if (!hasVisibleContent) return false;
        }
      }
      return true;
    });
  }, [messages]);

  const loadSessionHistory = useCallback(async () => {
    if (!session) return;
    try {
      setIsLoading(true);
      setError(null);
      const history = await api.loadSessionHistory(session.id, session.project_id);
      const loadedMessages: ClaudeStreamMessage[] = history.map(entry => ({ ...entry, type: entry.type || "assistant" }));
      setMessages(loadedMessages);
      setRawJsonlOutput(history.map(h => JSON.stringify(h)));
      setIsFirstPrompt(false);
    } catch (err) {
      console.error("Failed to load session history:", err);
      setError("Failed to load session history");
    } finally {
      setIsLoading(false);
    }
  }, [session]);

  const handleSendPrompt = useCallback(async (prompt: string, model: "sonnet" | "opus" = "sonnet") => {
    if (!projectPath) {
      setError("Please select a project directory first");
      return;
    }
    if (isLoading) {
      const newPrompt = { id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, prompt, model };
      setQueuedPrompts(prev => [...prev, newPrompt]);
      return;
    }
    try {
      setIsLoading(true);
      setError(null);
      hasActiveSessionRef.current = true;
      if (effectiveSession && !claudeSessionId) {
        setClaudeSessionId(effectiveSession.id);
      }
      // Full implementation of handleSendPrompt logic from ClaudeCodeSession.tsx goes here
      // This is a placeholder for brevity
      console.log("Sending prompt:", { prompt, model, projectPath, claudeSessionId });
      
      const userMessage: ClaudeStreamMessage = {
        type: "user",
        message: { content: [{ type: "text", text: prompt }] }
      };
      setMessages(prev => [...prev, userMessage]);

      // This is where the call to api.executeClaudeCode or api.resumeClaudeCode would go.
      // The event listener setup would also be here.
      
    } catch (err) {
      console.error("Failed to send prompt:", err);
      setError("Failed to send prompt");
      setIsLoading(false);
      hasActiveSessionRef.current = false;
    }
  }, [projectPath, isLoading, effectiveSession, claudeSessionId]);
  
  const handleSubmitPrompt = useCallback(async (prompt: string) => {
    await handleSendPrompt(prompt, "sonnet");
  }, [handleSendPrompt]);

  // Placeholder for other handlers...
  const handleSelectPath = async () => { console.log('handleSelectPath'); };
  const handleCopyAsJsonl = async () => { console.log('handleCopyAsJsonl'); };
  const handleCopyAsMarkdown = async () => { console.log('handleCopyAsMarkdown'); };
  const handleCheckpointSelect = async () => { console.log('handleCheckpointSelect'); };
  const handleCancelExecution = async () => { console.log('handleCancelExecution'); };
  const handleFork = (checkpointId: string) => { console.log('handleFork', checkpointId); };
  const handleConfirmFork = async () => { console.log('handleConfirmFork'); };
  const handleLinkDetected = (url: string) => { console.log('handleLinkDetected', url); };
  const handleClosePreview = () => { console.log('handleClosePreview'); };
  const handlePreviewUrlChange = (url: string) => { console.log('handlePreviewUrlChange', url); };
  const handleTogglePreviewMaximize = () => { console.log('handleTogglePreviewMaximize'); };
  const removeQueuedPrompt = (id: string) => {
    setQueuedPrompts(prev => prev.filter(p => p.id !== id));
  };


  useEffect(() => {
    if (session) {
      loadSessionHistory();
    }
  }, [session, loadSessionHistory]);

  useEffect(() => {
    onStreamingChange?.(isLoading, claudeSessionId);
  }, [isLoading, claudeSessionId, onStreamingChange]);

  const value: SessionContextType = {
    projectPath,
    setProjectPath,
    messages,
    isLoading,
    error,
    rawJsonlOutput,
    copyPopoverOpen,
    setCopyPopoverOpen,
    totalTokens,
    claudeSessionId,
    effectiveSession,
    showTimeline,
    setShowTimeline,
    showSettings,
    setShowSettings,
    showForkDialog,
    setShowForkDialog,
    showSlashCommandsSettings,
    setShowSlashCommandsSettings,
    queuedPrompts,
    showPreview,
    setShowPreview,
    previewUrl,
    setPreviewUrl,
    showPreviewPrompt,
    setShowPreviewPrompt,
    splitPosition,
    setSplitPosition,
    isPreviewMaximized,
    setIsPreviewMaximized,
    forkSessionName,
    setForkSessionName,
    timelineVersion,
    displayableMessages,
    handleSelectPath,
    handleSubmitPrompt,
    handleCopyAsJsonl,
    handleCopyAsMarkdown,
    handleCheckpointSelect,
    handleCancelExecution,
    handleFork,
    handleConfirmFork,
    handleLinkDetected,
    handleClosePreview,
    handlePreviewUrlChange,
    handleTogglePreviewMaximize,
    removeQueuedPrompt,
  };

  return (
    <SessionContext.Provider value={value}>
      {children}
    </SessionContext.Provider>
  );
};

// 4. Create a custom hook for easy consumption
export const useSession = (): SessionContextType => {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error("useSession must be used within a SessionProvider");
  }
  return context;
};
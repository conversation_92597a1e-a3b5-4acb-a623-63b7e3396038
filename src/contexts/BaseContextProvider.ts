/**
 * Base Context Provider Interface and Error Handling System
 * 
 * This module provides a standardized interface and error handling system
 * for all context providers in the intelligent mode system.
 */

import { ReactNode } from 'react';

// Standard error types for context providers
export enum ContextErrorType {
  INITIALIZATION_ERROR = 'initialization_error',
  NETWORK_ERROR = 'network_error',
  PERMISSION_ERROR = 'permission_error',
  VALIDATION_ERROR = 'validation_error',
  TIMEOUT_ERROR = 'timeout_error',
  UNKNOWN_ERROR = 'unknown_error',
}

// Enhanced error interface with contextual information
export interface ContextError {
  type: ContextErrorType;
  message: string;
  code?: string;
  details?: Record<string, any>;
  timestamp: number;
  retryable: boolean;
  suggestions?: string[];
}

// Loading state interface
export interface LoadingState {
  isLoading: boolean;
  progress?: number; // 0-100 for progress indication
  operation?: string; // Description of current operation
}

// Base configuration for all context providers
export interface BaseContextConfig {
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
  retryAttempts?: number;
  retryDelay?: number; // in milliseconds
  timeout?: number; // in milliseconds
}

// Standard context provider props
export interface BaseContextProviderProps {
  children: ReactNode;
  config?: BaseContextConfig;
}

// Base context value interface that all providers should extend
export interface BaseContextValue<T> {
  // Data
  data: T | null;
  
  // State
  loading: LoadingState;
  error: ContextError | null;
  lastUpdated: number | null;
  
  // Actions
  refresh: () => Promise<void>;
  clearError: () => void;
  
  // Configuration
  config: BaseContextConfig;
}

// Default configuration values
export const DEFAULT_CONTEXT_CONFIG: Required<BaseContextConfig> = {
  autoRefresh: true,
  refreshInterval: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
  timeout: 10000, // 10 seconds
};

// Error creation utilities
export const createContextError = (
  type: ContextErrorType,
  message: string,
  options: Partial<Pick<ContextError, 'code' | 'details' | 'retryable' | 'suggestions'>> = {}
): ContextError => {
  // Ensure details are serializable
  const serializableDetails = options.details ? 
    Object.fromEntries(
      Object.entries(options.details).map(([key, value]) => [
        key, 
        value instanceof Error ? value.message : 
        typeof value === 'object' && value !== null ? JSON.stringify(value) : 
        String(value)
      ])
    ) : undefined;
    
  return {
    type,
    message: typeof message === 'string' ? message : String(message),
    timestamp: Date.now(),
    retryable: options.retryable ?? true,
    ...options,
    details: serializableDetails,
  };
};

// Common error creators
export const createInitializationError = (message: string, details?: Record<string, any>): ContextError =>
  createContextError(ContextErrorType.INITIALIZATION_ERROR, message, { details, retryable: false });

export const createNetworkError = (message: string, details?: Record<string, any>): ContextError =>
  createContextError(ContextErrorType.NETWORK_ERROR, message, { 
    details, 
    retryable: true,
    suggestions: ['Check your network connection', 'Try again in a few moments']
  });

export const createPermissionError = (message: string, details?: Record<string, any>): ContextError =>
  createContextError(ContextErrorType.PERMISSION_ERROR, message, { 
    details, 
    retryable: false,
    suggestions: ['Check file permissions', 'Ensure you have access to the requested resource']
  });

export const createValidationError = (message: string, details?: Record<string, any>): ContextError =>
  createContextError(ContextErrorType.VALIDATION_ERROR, message, { 
    details, 
    retryable: false,
    suggestions: ['Check the input parameters', 'Ensure all required fields are provided']
  });

export const createTimeoutError = (message: string, details?: Record<string, any>): ContextError =>
  createContextError(ContextErrorType.TIMEOUT_ERROR, message, { 
    details, 
    retryable: true,
    suggestions: ['Operation timed out', 'Try again with a shorter timeout or simpler operation']
  });

export const createUnknownError = (message: string, details?: Record<string, any>): ContextError =>
  createContextError(ContextErrorType.UNKNOWN_ERROR, message, { details, retryable: true });

// Utility to convert native errors to context errors
export const toContextError = (error: unknown, fallbackMessage = 'An unexpected error occurred'): ContextError => {
  if (error instanceof Error) {
    // Check for specific error types based on message patterns
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return createNetworkError(error.message, { originalError: error.name });
    }
    
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
      return createPermissionError(error.message, { originalError: error.name });
    }
    
    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
      return createValidationError(error.message, { originalError: error.name });
    }
    
    return createUnknownError(error.message, { originalError: error.name });
  }
  
  if (typeof error === 'string') {
    return createUnknownError(error);
  }
  
  return createUnknownError(fallbackMessage, { 
    originalError: error instanceof Error ? error.message : String(error)
  });
};

// Retry utility with exponential backoff
export const withRetry = async <T>(
  operation: () => Promise<T>,
  config: Pick<BaseContextConfig, 'retryAttempts' | 'retryDelay'> = {}
): Promise<T> => {
  const maxAttempts = config.retryAttempts ?? DEFAULT_CONTEXT_CONFIG.retryAttempts;
  const baseDelay = config.retryDelay ?? DEFAULT_CONTEXT_CONFIG.retryDelay;
  
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Don't retry on last attempt
      if (attempt === maxAttempts) {
        break;
      }
      
      // Don't retry non-retryable errors
      if (error instanceof Error && error.message.includes('permission')) {
        break;
      }
      
      // Wait with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
};

// Timeout utility wrapper
export const withTimeout = async <T>(
  operation: () => Promise<T>,
  timeoutMs: number = DEFAULT_CONTEXT_CONFIG.timeout
): Promise<T> => {
  return Promise.race([
    operation(),
    new Promise<never>((_, reject) => 
      setTimeout(() => reject(createTimeoutError(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
    )
  ]);
};

// Context validation utility
export const validateContextData = <T>(
  data: T | null,
  validator: (data: T) => boolean,
  errorMessage = 'Invalid context data'
): void => {
  if (data === null) {
    throw createValidationError('Context data is null');
  }
  
  if (!validator(data)) {
    throw createValidationError(errorMessage);
  }
};

// Standard loading state creators
export const createLoadingState = (
  isLoading = false,
  operation?: string,
  progress?: number
): LoadingState => ({
  isLoading,
  operation,
  progress,
});

export const LOADING_STATES = {
  idle: createLoadingState(false),
  initializing: createLoadingState(true, 'Initializing...'),
  refreshing: createLoadingState(true, 'Refreshing...'),
  analyzing: createLoadingState(true, 'Analyzing...'),
  loading: createLoadingState(true, 'Loading...'),
};
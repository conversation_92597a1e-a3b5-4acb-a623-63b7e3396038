import React, { createContext, useContext } from 'react';
import { api } from '@/lib/api';
import { 
  BaseContextProviderProps,
  createNetworkError,
  createValidationError,
} from './BaseContextProvider';
import { useStandardizedContext, UseStandardizedContextReturn } from './useStandardizedContext';

interface ProjectContextProviderProps extends BaseContextProviderProps {
  projectPath?: string;
}

// Use a more generic type to avoid circular dependency
type ProjectContext = Record<string, any>;

type ProjectContextValue = UseStandardizedContextReturn<ProjectContext>;

const ProjectContextContext = createContext<ProjectContextValue | null>(null);

export const useProjectContext = () => {
  const context = useContext(ProjectContextContext);
  if (!context) {
    throw new Error('useProjectContext must be used within a ProjectContextProvider');
  }
  
  // Adapt the return value to maintain backward compatibility
  return {
    projectContext: context.data,
    isLoading: context.loading.isLoading,
    error: context.error?.message || null,
    refreshProjectContext: context.refresh,
    // Expose additional standardized features (avoiding conflict with 'error')
    loading: context.loading,
    standardizedError: context.error,
    lastUpdated: context.lastUpdated,
    isInitialized: context.isInitialized,
    canRetry: context.canRetry,
    retry: context.retry,
    clearError: context.clearError,
    config: context.config,
  };
};

export const ProjectContextProvider: React.FC<ProjectContextProviderProps> = ({
  children,
  projectPath,
  config,
}) => {
  // Utility functions for project analysis
  const fileExists = async (basePath: string, fileName: string): Promise<boolean> => {
    try {
      const entries = await api.listDirectoryContents(basePath);
      return entries.some(entry => entry.name === fileName && !entry.is_directory);
    } catch {
      return false;
    }
  };

  const analyzeStructure = async (path: string) => {
    try {
      // Get directory listing using the available API
      const entries = await api.listDirectoryContents(path);

      const directories: string[] = [];
      let fileCount = 0;
      let totalSize = 0;
      let hasTests = false;
      let hasDocs = false;

      for (const entry of entries) {
        if (entry.is_directory) {
          directories.push(entry.name);
          
          // Check for common test directories
          if (['test', 'tests', '__tests__', 'spec', 'specs'].includes(entry.name.toLowerCase())) {
            hasTests = true;
          }
          
          // Check for documentation directories
          if (['docs', 'doc', 'documentation', 'wiki'].includes(entry.name.toLowerCase())) {
            hasDocs = true;
          }
        } else {
          fileCount++;
          totalSize += entry.size || 0;
          
          // Check for test files
          if (entry.name.includes('.test.') || entry.name.includes('.spec.') || entry.name.endsWith('_test.py')) {
            hasTests = true;
          }
          
          // Check for documentation files
          if (entry.name.toLowerCase().includes('readme') || entry.name.toLowerCase().includes('doc')) {
            hasDocs = true;
          }
        }
      }

      return {
        directories,
        fileCount,
        totalSize,
        depth: 1, // Simple implementation, could be enhanced
        hasTests,
        hasDocs,
      };
    } catch (err) {
      console.error('Failed to analyze directory structure:', err);
      return {
        directories: [],
        fileCount: 0,
        totalSize: 0,
        depth: 0,
        hasTests: false,
        hasDocs: false,
      };
    }
  };

  const analyzeProjectStructure = async (): Promise<ProjectContext> => {
    if (!projectPath) {
      throw createValidationError('Project path is required for analysis');
    }

    try {
      // Determine project type based on file presence
      let projectType: ProjectContext['type'] = 'unknown';
      let dependencies: Record<string, string> = {};
      let scripts: Record<string, string> = {};
      let configuration: Record<string, any> = {};

      // Analyze project structure first
      const structure = await analyzeStructure(projectPath);

      // Check for project type indicators in the file structure
      const hasPackageJson = structure.directories.some(d => d.includes('node_modules')) || 
                           await fileExists(projectPath, 'package.json');
      const hasPyProject = await fileExists(projectPath, 'pyproject.toml') || 
                          await fileExists(projectPath, 'requirements.txt') || 
                          await fileExists(projectPath, 'setup.py');
      const hasCargoToml = await fileExists(projectPath, 'Cargo.toml');
      const hasGoMod = await fileExists(projectPath, 'go.mod');

      if (hasPackageJson) {
        projectType = 'node';
      } else if (hasPyProject) {
        projectType = 'python';
      } else if (hasCargoToml) {
        projectType = 'rust';
      } else if (hasGoMod) {
        projectType = 'go';
      }

      return {
        type: projectType,
        rootPath: projectPath,
        dependencies,
        scripts,
        structure,
        configuration,
      };
    } catch (err) {
      if (err instanceof Error && err.message.includes('permission')) {
        throw createNetworkError(`Failed to access project directory: ${err.message}`);
      }
      throw createNetworkError(`Failed to analyze project structure: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Use standardized context hook
  const contextValue = useStandardizedContext<ProjectContext>({
    fetchData: analyzeProjectStructure,
    config,
    dependencies: [projectPath],
    validator: (data) => {
      return data.rootPath !== undefined && data.type !== undefined && data.structure !== undefined;
    },
    onError: (error) => {
      // Properly serialize error objects for logging
      const errorDetails = {
        message: error.message,
        type: error.type,
        details: error.details,
        retryable: error.retryable,
        timestamp: error.timestamp,
      };
      console.error('Project context error:', JSON.stringify(errorDetails, null, 2));
      
      // Also log the original error for debugging if it exists
      if (error.details?.originalError) {
        const originalError = error.details.originalError;
        const serializedOriginalError = originalError instanceof Error 
          ? { name: originalError.name, message: originalError.message, stack: originalError.stack }
          : String(originalError);
        console.error('Original error:', serializedOriginalError);
      }
    },
    onSuccess: (data) => {
      console.log('Project context updated:', data.type, 'at', data.rootPath);
    },
  });

  return (
    <ProjectContextContext.Provider value={contextValue}>
      {children}
    </ProjectContextContext.Provider>
  );
};
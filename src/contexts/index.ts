/**
 * Contexts Index - Centralized exports for all context providers
 * 
 * This module provides a single point of import for all context providers
 * and their related utilities in the intelligent mode system.
 */

// Base context system
export * from './BaseContextProvider';
export * from './useStandardizedContext';

// Context providers
export * from './ProjectContextProvider';
export * from './GitContextProvider';
export * from './ModeContext';
export * from './TabContext';

// Re-export existing context providers for convenience
export { useTabContext } from './TabContext';
export { useModeContext } from './ModeContext';
export { useProjectContext } from './ProjectContextProvider';
export { useGitContext } from './GitContextProvider';
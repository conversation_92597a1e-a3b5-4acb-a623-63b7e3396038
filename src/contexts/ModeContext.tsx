import React, { createContext, useState, useContext, useCallback, useEffect } from 'react';
import { Mode, ModeConfiguration, ModeTheme } from '@/intelligent-mode-system/types/mode.types';
import { UserContext } from '@/intelligent-mode-system/types/context.types';
import { modeThemes, applyThemeToCSS } from '@/intelligent-mode-system/design-tokens/tokens';

// Default mode configurations with design token themes
const DEFAULT_MODES: Mode[] = [
  {
    id: 'architect',
    name: 'Architect',
    description: 'System design, architecture planning, and structural analysis',
    icon: '🏗️',
    color: '#3b82f6',
    type: 'analytical',
    capabilities: ['system-design', 'architecture-planning', 'dependency-analysis', 'component-mapping'],
    features: [],
    shortcuts: [],
    configuration: {
      aiModel: 'claude-3-opus',
      contextWindow: 200000,
      temperature: 0.3,
      systemPrompt: 'You are a system architect expert. Focus on design patterns, architecture decisions, and structural improvements.',
      maxTokens: 8192,
      tools: ['system-designer', 'dependency-analyzer', 'component-mapper'],
      customSettings: {
        diagramGeneration: true,
        architecturePatterns: true,
        dependencyVisualization: true,
      },
    },
    theme: modeThemes.architect,
    priority: 1,
    isActive: true,
  },
  {
    id: 'debug',
    name: 'Debug',
    description: 'Error analysis, debugging, and troubleshooting',
    icon: '🐛',
    color: '#ef4444',
    type: 'focused',
    capabilities: ['error-analysis', 'stack-trace-analysis', 'debugging-suggestions', 'performance-profiling'],
    features: [],
    shortcuts: [],
    configuration: {
      aiModel: 'claude-3-sonnet',
      contextWindow: 200000,
      temperature: 0.1,
      systemPrompt: 'You are a debugging expert. Focus on identifying root causes, analyzing stack traces, and providing clear solutions.',
      maxTokens: 4096,
      tools: ['debugger', 'log-analyzer', 'error-tracker', 'profiler'],
      customSettings: {
        verboseLogging: true,
        includeStackTrace: true,
        suggestBreakpoints: true,
        performanceMetrics: true,
      },
    },
    theme: modeThemes.debug,
    priority: 2,
    isActive: true,
  },
  {
    id: 'review',
    name: 'Review',
    description: 'Code review, quality analysis, and improvement suggestions',
    icon: '🔍',
    color: '#0ea5e9',
    type: 'analytical',
    capabilities: ['code-review', 'quality-analysis', 'security-audit', 'best-practices'],
    features: [],
    shortcuts: [],
    configuration: {
      aiModel: 'claude-3-sonnet',
      contextWindow: 200000,
      temperature: 0.2,
      systemPrompt: 'You are a code review expert. Focus on code quality, security vulnerabilities, performance issues, and best practices.',
      maxTokens: 4096,
      tools: ['code-analyzer', 'security-scanner', 'performance-checker', 'linter'],
      customSettings: {
        checkSecurity: true,
        checkPerformance: true,
        suggestImprovements: true,
        codeComplexityAnalysis: true,
      },
    },
    theme: modeThemes.review,
    priority: 3,
    isActive: true,
  },
  {
    id: 'deploy',
    name: 'Deploy',
    description: 'Deployment, CI/CD, and infrastructure management',
    icon: '🚀',
    color: '#22c55e',
    type: 'focused',
    capabilities: ['deployment-automation', 'ci-cd-pipeline', 'infrastructure-as-code', 'monitoring'],
    features: [],
    shortcuts: [],
    configuration: {
      aiModel: 'claude-3-sonnet',
      contextWindow: 200000,
      temperature: 0.2,
      systemPrompt: 'You are a DevOps expert. Focus on deployment strategies, CI/CD pipelines, infrastructure automation, and monitoring.',
      maxTokens: 4096,
      tools: ['deployment-manager', 'pipeline-builder', 'infrastructure-tool', 'monitoring-dashboard'],
      customSettings: {
        autoDeployment: false,
        pipelineValidation: true,
        infrastructureChecks: true,
        monitoringAlerts: true,
      },
    },
    theme: modeThemes.deploy,
    priority: 4,
    isActive: true,
  },
  {
    id: 'experiment',
    name: 'Experiment',
    description: 'Feature prototyping, creative exploration, and experimentation',
    icon: '🧪',
    color: '#d946ef',
    type: 'creative',
    capabilities: ['rapid-prototyping', 'feature-exploration', 'creative-coding', 'innovation'],
    features: [],
    shortcuts: [],
    configuration: {
      aiModel: 'claude-3-opus',
      contextWindow: 200000,
      temperature: 0.8,
      systemPrompt: 'You are a creative developer. Focus on innovative solutions, rapid prototyping, and exploring new possibilities.',
      maxTokens: 8192,
      tools: ['prototype-builder', 'playground-environment', 'idea-generator', 'experiment-tracker'],
      customSettings: {
        creativityLevel: 'high',
        riskTolerance: 'moderate',
        prototypeSpeed: 'fast',
        experimentTracking: true,
      },
    },
    theme: modeThemes.experiment,
    priority: 5,
    isActive: true,
  },
  {
    id: 'learn',
    name: 'Learn',
    description: 'Educational guidance, documentation, and knowledge sharing',
    icon: '📚',
    color: '#f59e0b',
    type: 'creative',
    capabilities: ['educational-content', 'documentation-generation', 'tutorial-creation', 'concept-explanation'],
    features: [],
    shortcuts: [],
    configuration: {
      aiModel: 'claude-3-opus',
      contextWindow: 200000,
      temperature: 0.5,
      systemPrompt: 'You are an educational expert. Focus on clear explanations, comprehensive documentation, and effective teaching.',
      maxTokens: 8192,
      tools: ['documentation-generator', 'tutorial-builder', 'example-creator', 'knowledge-base'],
      customSettings: {
        explanationDepth: 'comprehensive',
        includeExamples: true,
        generateDiagrams: true,
        adaptiveLearning: true,
      },
    },
    theme: modeThemes.learn,
    priority: 6,
    isActive: true,
  },
];

interface ModeState {
  currentMode: Mode;
  previousMode: Mode | null;
  modeHistory: string[];
  customModes: Mode[];
  userContext: UserContext | null;
}

interface ModeContextType extends ModeState {
  // Mode management
  setMode: (modeId: string) => void;
  switchMode: (modeId: string) => void;
  revertToPreviousMode: () => void;
  
  // Mode CRUD operations
  createCustomMode: (mode: Omit<Mode, 'id'>) => string;
  updateMode: (modeId: string, updates: Partial<Mode>) => void;
  deleteCustomMode: (modeId: string) => void;
  
  // Mode queries
  getAllModes: () => Mode[];
  getModeById: (modeId: string) => Mode | undefined;
  getActiveModes: () => Mode[];
  
  // User context
  updateUserContext: (context: Partial<UserContext>) => void;
  clearUserContext: () => void;
  
  // Configuration
  updateModeConfiguration: (modeId: string, config: Partial<ModeConfiguration>) => void;
  updateModeTheme: (modeId: string, theme: Partial<ModeTheme>) => void;
  
  // Persistence
  saveModeState: () => void;
  loadModeState: () => void;
  resetToDefaults: () => void;
}

const ModeContext = createContext<ModeContextType | undefined>(undefined);

const STORAGE_KEY = 'claudia_mode_state';

export const ModeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [modeState, setModeState] = useState<ModeState>({
    currentMode: DEFAULT_MODES[0],
    previousMode: null,
    modeHistory: [DEFAULT_MODES[0].id],
    customModes: [],
    userContext: null,
  });

  // Load mode state from localStorage on mount
  useEffect(() => {
    loadModeState();
  }, []);
  
  // Apply theme when current mode changes
  useEffect(() => {
    if (modeState.currentMode.theme) {
      applyThemeToCSS(modeState.currentMode.theme);
    }
  }, [modeState.currentMode]);

  // Save mode state to localStorage whenever it changes
  useEffect(() => {
    saveModeState();
  }, [modeState]);

  const setMode = useCallback((modeId: string) => {
    const mode = [...DEFAULT_MODES, ...modeState.customModes].find(m => m.id === modeId);
    if (mode) {
      // Add mode-switching class for instant transitions
      document.documentElement.classList.add('mode-switching');
      
      setModeState(prev => ({
        ...prev,
        currentMode: mode,
        previousMode: prev.currentMode,
        modeHistory: [...prev.modeHistory.slice(-9), modeId], // Keep last 10 modes
      }));
      
      // Apply theme if it exists
      if (mode.theme) {
        applyThemeToCSS(mode.theme);
      }
      
      // Remove mode-switching class after a short delay
      setTimeout(() => {
        document.documentElement.classList.remove('mode-switching');
      }, 50);
    }
  }, [modeState.customModes]);

  const switchMode = useCallback((modeId: string) => {
    setMode(modeId);
  }, [setMode]);

  const revertToPreviousMode = useCallback(() => {
    if (modeState.previousMode) {
      setModeState(prev => ({
        ...prev,
        currentMode: prev.previousMode!,
        previousMode: prev.currentMode,
        modeHistory: [...prev.modeHistory, prev.previousMode!.id],
      }));
    }
  }, [modeState.previousMode]);

  const createCustomMode = useCallback((mode: Omit<Mode, 'id'>): string => {
    const newMode: Mode = {
      ...mode,
      id: 'experiment' as any, // Custom modes use experiment id for now
    };
    
    setModeState(prev => ({
      ...prev,
      customModes: [...prev.customModes, newMode],
    }));
    
    return newMode.id;
  }, []);

  const updateMode = useCallback((modeId: string, updates: Partial<Mode>) => {
    setModeState(prev => {
      // Update custom modes
      const updatedCustomModes = prev.customModes.map(mode =>
        mode.id === modeId ? { ...mode, ...updates } : mode
      );
      
      // Update current mode if it's the one being updated
      const updatedCurrentMode = prev.currentMode.id === modeId
        ? { ...prev.currentMode, ...updates }
        : prev.currentMode;
      
      return {
        ...prev,
        customModes: updatedCustomModes,
        currentMode: updatedCurrentMode,
      };
    });
  }, []);

  const deleteCustomMode = useCallback((modeId: string) => {
    setModeState(prev => ({
      ...prev,
      customModes: prev.customModes.filter(mode => mode.id !== modeId),
      // Switch to default mode if deleting current mode
      currentMode: prev.currentMode.id === modeId ? DEFAULT_MODES[0] : prev.currentMode,
    }));
  }, []);

  const getAllModes = useCallback((): Mode[] => {
    return [...DEFAULT_MODES, ...modeState.customModes];
  }, [modeState.customModes]);

  const getModeById = useCallback((modeId: string): Mode | undefined => {
    return getAllModes().find(mode => mode.id === modeId);
  }, [getAllModes]);

  const getActiveModes = useCallback((): Mode[] => {
    return getAllModes().filter(mode => mode.isActive);
  }, [getAllModes]);

  const updateUserContext = useCallback((context: Partial<UserContext>) => {
    setModeState(prev => ({
      ...prev,
      userContext: prev.userContext
        ? { ...prev.userContext, ...context }
        : context as UserContext,
    }));
  }, []);

  const clearUserContext = useCallback(() => {
    setModeState(prev => ({
      ...prev,
      userContext: null,
    }));
  }, []);

  const updateModeConfiguration = useCallback((modeId: string, config: Partial<ModeConfiguration>) => {
    const existingMode = getModeById(modeId);
    if (existingMode && existingMode.configuration) {
      updateMode(modeId, {
        configuration: {
          ...existingMode.configuration,
          ...config,
        } as ModeConfiguration,
      });
    }
  }, [updateMode, getModeById]);

  const updateModeTheme = useCallback((modeId: string, theme: Partial<ModeTheme>) => {
    const existingMode = getModeById(modeId);
    if (existingMode && existingMode.theme) {
      updateMode(modeId, {
        theme: {
          ...existingMode.theme,
          ...theme,
        } as ModeTheme,
      });
    }
  }, [updateMode, getModeById]);

  const saveModeState = useCallback(() => {
    try {
      const stateToSave = {
        currentModeId: modeState.currentMode.id,
        previousModeId: modeState.previousMode?.id || null,
        modeHistory: modeState.modeHistory,
        customModes: modeState.customModes,
        userContext: modeState.userContext,
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Failed to save mode state:', error);
    }
  }, [modeState]);

  const loadModeState = useCallback(() => {
    try {
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsed = JSON.parse(savedState);
        const allModes = [...DEFAULT_MODES, ...(parsed.customModes || [])];
        
        const currentMode = allModes.find(m => m.id === parsed.currentModeId) || DEFAULT_MODES[0];
        const previousMode = parsed.previousModeId ? allModes.find(m => m.id === parsed.previousModeId) : null;
        
        setModeState({
          currentMode,
          previousMode,
          modeHistory: parsed.modeHistory || [currentMode.id],
          customModes: parsed.customModes || [],
          userContext: parsed.userContext || null,
        });
      }
    } catch (error) {
      console.error('Failed to load mode state:', error);
    }
  }, []);

  const resetToDefaults = useCallback(() => {
    setModeState({
      currentMode: DEFAULT_MODES[0],
      previousMode: null,
      modeHistory: [DEFAULT_MODES[0].id],
      customModes: [],
      userContext: null,
    });
    localStorage.removeItem(STORAGE_KEY);
  }, []);

  const value: ModeContextType = {
    ...modeState,
    setMode,
    switchMode,
    revertToPreviousMode,
    createCustomMode,
    updateMode,
    deleteCustomMode,
    getAllModes,
    getModeById,
    getActiveModes,
    updateUserContext,
    clearUserContext,
    updateModeConfiguration,
    updateModeTheme,
    saveModeState,
    loadModeState,
    resetToDefaults,
  };

  return (
    <ModeContext.Provider value={value}>
      {children}
    </ModeContext.Provider>
  );
};

export const useModeContext = () => {
  const context = useContext(ModeContext);
  if (!context) {
    throw new Error('useModeContext must be used within a ModeProvider');
  }
  return context;
};

// Export default modes for external use
export { DEFAULT_MODES };
/**
 * Standardized Context Hook
 * 
 * Provides a unified pattern for context providers with error handling,
 * loading states, retry logic, and automatic refresh capabilities.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  BaseContextConfig,
  BaseContextValue,
  ContextError,
  LoadingState,
  DEFAULT_CONTEXT_CONFIG,
  toContextError,
  withRetry,
  withTimeout,
  LOADING_STATES,
} from './BaseContextProvider';

export interface UseStandardizedContextOptions<T> {
  // Data fetching function
  fetchData: () => Promise<T>;
  
  // Configuration
  config?: BaseContextConfig;
  
  // Dependencies that trigger refresh
  dependencies?: readonly unknown[];
  
  // Validation function
  validator?: (data: T) => boolean;
  
  // Custom error handler
  onError?: (error: ContextError) => void;
  
  // Success callback
  onSuccess?: (data: T) => void;
  
  // Initial data
  initialData?: T | null;
}

export interface UseStandardizedContextReturn<T> extends BaseContextValue<T> {
  // Additional utilities
  isInitialized: boolean;
  canRetry: boolean;
  retry: () => Promise<void>;
}

export const useStandardizedContext = <T>(
  options: UseStandardizedContextOptions<T>
): UseStandardizedContextReturn<T> => {
  const {
    fetchData,
    config: userConfig,
    dependencies = [],
    validator,
    onError,
    onSuccess,
    initialData = null,
  } = options;

  // Merge user config with defaults
  const config = { ...DEFAULT_CONTEXT_CONFIG, ...userConfig };

  // State
  const [data, setData] = useState<T | null>(initialData);
  const [loading, setLoading] = useState<LoadingState>(LOADING_STATES.idle);
  const [error, setError] = useState<ContextError | null>(null);
  const [lastUpdated, setLastUpdated] = useState<number | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Refs for cleanup and tracking
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);
  const retryCountRef = useRef(0);

  // Clear any pending timeouts on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  // Safe state updater that checks if component is still mounted
  const safeSetState = useCallback(<S>(
    setter: React.Dispatch<React.SetStateAction<S>>, 
    value: React.SetStateAction<S>
  ) => {
    if (mountedRef.current) {
      setter(value);
    }
  }, []);

  // Clear error function
  const clearError = useCallback(() => {
    safeSetState(setError, null);
    retryCountRef.current = 0;
  }, [safeSetState]);

  // Core data fetching logic with error handling and retry
  const fetchDataWithErrorHandling = useCallback(async (operation: string = 'Loading'): Promise<T | null> => {
    try {
      safeSetState(setLoading, {
        isLoading: true,
        operation,
      });

      // Clear any previous errors
      safeSetState(setError, null);

      // Fetch data with timeout and retry
      const result = await withTimeout(
        () => withRetry(fetchData, {
          retryAttempts: config.retryAttempts,
          retryDelay: config.retryDelay,
        }),
        config.timeout
      );

      // Validate data if validator is provided
      if (validator && !validator(result)) {
        throw new Error('Data validation failed');
      }

      // Update state with successful result
      safeSetState(setData, result);
      safeSetState(setLastUpdated, Date.now());
      retryCountRef.current = 0;

      // Call success callback
      onSuccess?.(result);

      return result;

    } catch (err) {
      const contextError = toContextError(err);
      safeSetState(setError, contextError);
      
      // Call error callback with properly serialized error
      if (onError) {
        try {
          onError(contextError);
        } catch (callbackError) {
          // Prevent callback errors from breaking the flow
          const serializedCallbackError = callbackError instanceof Error 
            ? { name: callbackError.name, message: callbackError.message, stack: callbackError.stack }
            : String(callbackError);
          console.error('Error in onError callback:', serializedCallbackError);
        }
      }
      
      throw contextError;
    } finally {
      safeSetState(setLoading, LOADING_STATES.idle);
    }
  }, [fetchData, validator, onSuccess, onError, config, safeSetState]);

  // Refresh function
  const refresh = useCallback(async (): Promise<void> => {
    try {
      await fetchDataWithErrorHandling('Refreshing');
    } catch (err) {
      // Error is already handled in fetchDataWithErrorHandling
      const errorMsg = err instanceof Error ? err.message : String(err);
      console.warn('Refresh failed:', errorMsg);
    }
  }, [fetchDataWithErrorHandling]);

  // Retry function (only available for retryable errors)
  const retry = useCallback(async (): Promise<void> => {
    if (!error?.retryable) {
      console.warn('Cannot retry: error is not retryable');
      return;
    }

    retryCountRef.current += 1;
    await refresh();
  }, [error?.retryable, refresh]);

  // Initial data fetch
  useEffect(() => {
    const initialize = async () => {
      try {
        await fetchDataWithErrorHandling('Initializing');
        safeSetState(setIsInitialized, true);
      } catch (err) {
        // Error handling is done in fetchDataWithErrorHandling
        safeSetState(setIsInitialized, true); // Still mark as initialized even on error
      }
    };

    initialize();
  }, []); // Only run on mount

  // Auto-refresh when dependencies change
  useEffect(() => {
    if (!isInitialized) return; // Don't auto-refresh until initial load

    const refreshOnDependencyChange = async () => {
      try {
        await fetchDataWithErrorHandling('Updating');
      } catch (err) {
        // Error handling is done in fetchDataWithErrorHandling
      }
    };

    refreshOnDependencyChange();
  }, dependencies); // eslint-disable-line react-hooks/exhaustive-deps

  // Auto-refresh timer
  useEffect(() => {
    if (!config.autoRefresh || !isInitialized) {
      return;
    }

    const scheduleRefresh = () => {
      refreshTimeoutRef.current = setTimeout(async () => {
        if (!mountedRef.current) return;

        try {
          await refresh();
        } catch (err) {
          // Error is already handled in refresh
        }

        // Schedule next refresh
        if (mountedRef.current && config.autoRefresh) {
          scheduleRefresh();
        }
      }, config.refreshInterval);
    };

    scheduleRefresh();

    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
    };
  }, [config.autoRefresh, config.refreshInterval, isInitialized, refresh]);

  const canRetry = Boolean(error?.retryable && retryCountRef.current < config.retryAttempts);

  return {
    // Data
    data,
    
    // State
    loading,
    error,
    lastUpdated,
    isInitialized,
    canRetry,
    
    // Actions
    refresh,
    retry,
    clearError,
    
    // Configuration
    config,
  };
};
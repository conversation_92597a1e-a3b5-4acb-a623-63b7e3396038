import React, { createContext, useContext } from 'react';
import { api } from '@/lib/api';
import { GitStatus } from '@/intelligent-mode-system/types/context.types';
import { 
  BaseContextProviderProps,
  createNetworkError,
  createValidationError,
} from './BaseContextProvider';
import { useStandardizedContext, UseStandardizedContextReturn } from './useStandardizedContext';

interface GitContextProviderProps extends BaseContextProviderProps {
  projectPath?: string;
}

type GitContextValue = UseStandardizedContextReturn<GitStatus>;

const GitContextContext = createContext<GitContextValue | null>(null);

export const useGitContext = () => {
  const context = useContext(GitContextContext);
  if (!context) {
    throw new Error('useGitContext must be used within a GitContextProvider');
  }
  
  // Adapt the return value to maintain backward compatibility
  return {
    gitStatus: context.data,
    isLoading: context.loading.isLoading,
    error: context.error?.message || null,
    refreshGitStatus: context.refresh,
    // Expose additional standardized features (avoiding conflict with 'error')
    loading: context.loading,
    standardizedError: context.error,
    lastUpdated: context.lastUpdated,
    isInitialized: context.isInitialized,
    canRetry: context.canRetry,
    retry: context.retry,
    clearError: context.clearError,
    config: context.config,
  };
};

export const GitContextProvider: React.FC<GitContextProviderProps> = ({
  children,
  projectPath,
  config,
}) => {
  // Utility function to check if directory is a git repository
  const isGitRepository = async (path: string): Promise<boolean> => {
    try {
      const entries = await api.listDirectoryContents(path);
      return entries.some(entry => entry.name === '.git' && entry.is_directory);
    } catch {
      return false;
    }
  };

  const analyzeGitStatus = async (): Promise<GitStatus> => {
    if (!projectPath) {
      throw createValidationError('Project path is required for git analysis');
    }

    try {
      // Check if this is a git repository
      const isGit = await isGitRepository(projectPath);
      if (!isGit) {
        throw createValidationError('Not a git repository');
      }

      // Since we don't have git command execution capability in the frontend,
      // provide a basic status. In a real implementation, this would need to be
      // enhanced with actual git status parsing or backend support
      return {
        branch: 'main', // Default assumption
        ahead: 0,
        behind: 0,
        modified: [],
        staged: [],
        untracked: [],
        hasConflicts: false,
      };
    } catch (err) {
      if (err instanceof Error && err.message.includes('permission')) {
        throw createNetworkError(`Failed to access git repository: ${err.message}`);
      }
      throw createNetworkError(`Failed to analyze git status: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Use standardized context hook with git-specific configuration
  const contextValue = useStandardizedContext<GitStatus>({
    fetchData: analyzeGitStatus,
    config: {
      // Git status can be updated more frequently
      refreshInterval: 15000, // 15 seconds
      autoRefresh: true,
      retryAttempts: 2,
      timeout: 5000,
      ...config,
    },
    dependencies: [projectPath],
    validator: (data) => {
      return data.branch !== undefined && Array.isArray(data.modified);
    },
    onError: (error) => {
      console.warn('Git context error:', error.message);
    },
    onSuccess: (data) => {
      console.log('Git status updated:', data.branch, `(${data.modified.length} modified files)`);
    },
  });

  return (
    <GitContextContext.Provider value={contextValue}>
      {children}
    </GitContextContext.Provider>
  );
};
import React, { useMemo, useCallback, useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useReducedMotion } from 'framer-motion';
import { useModeContext } from '@/contexts/ModeContext';
import { useProjectContext } from '@/contexts/ProjectContextProvider';
import { useGitContext } from '@/contexts/GitContextProvider';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, ChevronRight, Loader2 } from 'lucide-react';

// Performance monitoring hook
const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    componentCount: 0,
    memoryUsage: 0,
    lastUpdate: Date.now()
  });
  
  const startTime = useRef<number>(0);
  
  const startMeasure = useCallback(() => {
    startTime.current = performance.now();
  }, []);
  
  const endMeasure = useCallback((componentName: string) => {
    const endTime = performance.now();
    const renderTime = endTime - startTime.current;
    
    setMetrics(prev => ({
      ...prev,
      renderTime,
      lastUpdate: Date.now()
    }));
    
    // Log performance in development
    if (process.env.NODE_ENV === 'development' && renderTime > 16) {
      console.warn(`Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
    }
  }, []);
  
  return { metrics, startMeasure, endMeasure };
};

// Intersection observer hook for lazy loading
const useIntersectionObserver = (options: IntersectionObserverInit = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    );
    
    observer.observe(element);
    
    return () => {
      observer.unobserve(element);
    };
  }, [hasIntersected, options]);
  
  return { elementRef, isIntersecting, hasIntersected };
};

// Lazy loading wrapper component
interface LazyComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
  threshold?: number;
  rootMargin?: string;
}

export const LazyComponent: React.FC<LazyComponentProps> = ({
  children,
  fallback = <div className="h-20 w-full bg-gray-200 animate-pulse rounded" />,
  className,
  threshold = 0.1,
  rootMargin = '50px'
}) => {
  const { elementRef, hasIntersected } = useIntersectionObserver({
    threshold,
    rootMargin
  });
  
  return (
    <div ref={elementRef} className={className}>
      {hasIntersected ? children : fallback}
    </div>
  );
};

// Memoized context widget with performance optimization
interface OptimizedContextWidgetProps {
  id: string;
  title: string;
  content: React.ReactNode;
  priority: number;
  isCollapsed?: boolean;
  onToggle?: () => void;
  className?: string;
}

export const OptimizedContextWidget = React.memo<OptimizedContextWidgetProps>((
  { id, title, content, priority, isCollapsed = false, onToggle, className }
) => {
  const { startMeasure, endMeasure } = usePerformanceMonitor();
  const shouldReduceMotion = useReducedMotion();
  
  useEffect(() => {
    startMeasure();
    return () => endMeasure(`ContextWidget-${id}`);
  }, [id, startMeasure, endMeasure]);
  
  const animationProps = shouldReduceMotion
    ? {}
    : {
        initial: { opacity: 0, height: 0 },
        animate: { opacity: 1, height: 'auto' },
        exit: { opacity: 0, height: 0 },
        transition: { duration: 0.2 }
      };
  
  return (
    <LazyComponent
      className={cn('optimized-context-widget', className)}
      threshold={0.05}
    >
      <Card className="border-0 shadow-sm">
        <CardHeader 
          className="pb-2 cursor-pointer" 
          onClick={onToggle}
        >
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              {title}
              <Badge variant="outline" className="text-xs">
                {priority}
              </Badge>
            </CardTitle>
            {onToggle && (
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                {isCollapsed ? (
                  <ChevronRight className="h-3 w-3" />
                ) : (
                  <ChevronDown className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
        </CardHeader>
        
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div {...animationProps}>
              <CardContent className="pt-0">
                {content}
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </LazyComponent>
  );
});

OptimizedContextWidget.displayName = 'OptimizedContextWidget';

// Virtual scrolling list for large datasets
interface VirtualScrollListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  className?: string;
  onScroll?: (scrollTop: number) => void;
}

export function VirtualScrollList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className,
  onScroll
}: VirtualScrollListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const totalHeight = items.length * itemHeight;
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.floor((scrollTop + containerHeight) / itemHeight) + overscan
  );
  
  const visibleItems = useMemo(() => {
    return items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index,
      top: (startIndex + index) * itemHeight
    }));
  }, [items, startIndex, endIndex, itemHeight]);
  
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(newScrollTop);
  }, [onScroll]);
  
  return (
    <div
      ref={containerRef}
      className={cn('virtual-scroll-list', className)}
      style={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }}
      onScroll={handleScroll}
    >
      <div
        style={{
          height: totalHeight,
          position: 'relative'
        }}
      >
        {visibleItems.map(({ item, index, top }) => (
          <div
            key={index}
            style={{
              position: 'absolute',
              top,
              left: 0,
              right: 0,
              height: itemHeight
            }}
          >
            {renderItem(item, index)}
          </div>
        ))}
      </div>
    </div>
  );
}

// Debounced search component
interface DebouncedSearchProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  delay?: number;
  className?: string;
}

export const DebouncedSearch: React.FC<DebouncedSearchProps> = ({
  onSearch,
  placeholder = 'Search...',
  delay = 300,
  className
}) => {
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setIsSearching(true);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      onSearch(value);
      setIsSearching(false);
    }, delay);
  }, [onSearch, delay]);
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return (
    <div className={cn('relative', className)}>
      <input
        type="text"
        value={query}
        onChange={handleInputChange}
        placeholder={placeholder}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
      {isSearching && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
        </div>
      )}
    </div>
  );
};

// Optimized mode-aware layout with caching
interface OptimizedModeLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const OptimizedModeLayout: React.FC<OptimizedModeLayoutProps> = ({
  children,
  className
}) => {
  const { currentMode } = useModeContext();
  const { projectContext } = useProjectContext();
  const { gitStatus } = useGitContext();
  const { startMeasure, endMeasure } = usePerformanceMonitor();
  const shouldReduceMotion = useReducedMotion();
  
  // Cache layout configuration based on mode and context
  const layoutConfig = useMemo(() => {
    return {
      mode: currentMode.id,
      hasProject: !!projectContext,
      hasGitChanges: !!(gitStatus?.modified?.length || gitStatus?.staged?.length),
      timestamp: Date.now()
    };
  }, [currentMode.id, projectContext, gitStatus]);
  
  useEffect(() => {
    startMeasure();
    return () => endMeasure('OptimizedModeLayout');
  }, [layoutConfig, startMeasure, endMeasure]);
  
  const animationProps = shouldReduceMotion
    ? {}
    : {
        initial: { opacity: 0, y: 10 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -10 },
        transition: { duration: 0.2 }
      };
  
  return (
    <div className={cn('optimized-mode-layout', className)}>
      <AnimatePresence mode="wait">
        <motion.div
          key={layoutConfig.mode}
          {...animationProps}
          className="h-full w-full"
        >
          {children}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

// Performance metrics display (development only)
export const PerformanceMetrics: React.FC = () => {
  const { metrics } = usePerformanceMonitor();
  
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  
  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-2 rounded text-xs font-mono z-50">
      <div>Render: {metrics.renderTime.toFixed(2)}ms</div>
      <div>Components: {metrics.componentCount}</div>
      <div>Memory: {(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB</div>
    </div>
  );
};

export {
  usePerformanceMonitor,
  useIntersectionObserver
};
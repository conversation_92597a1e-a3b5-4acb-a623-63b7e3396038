import React from 'react';
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { 
  ArrowLeft, 
  Terminal, 
  Settings, 
  Command, 
  GitBranch, 
  Copy, 
  ChevronDown 
} from "lucide-react";
import { Popover } from "@/components/ui/popover";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { SplitPane } from "@/components/ui/split-pane";
import { WebviewPreview } from "./WebviewPreview";
import { CheckpointSettings } from "./CheckpointSettings";
import { ModeSpecificPanels } from "./ModeSpecificPanels";
import { useModeContext } from "./IntelligentModeIntegration";
import { ModeSelectorResponsive } from "@/intelligent-mode-system/components/ModeSelectorResponsive";
import { ModeLayoutResponsive } from "@/intelligent-mode-system/components/layout/ModeLayoutResponsive";
import { SessionContainer } from "./session/SessionContainer";
import type { ClaudeStreamMessage } from "./AgentExecution";
import type { Session } from "@/types/session.types";

interface SessionContentProps {
  // Basic props
  className?: string;
  
  // Session state
  projectPath: string;
  effectiveSession: Session | null;
  isLoading: boolean;
  messages: ClaudeStreamMessage[];
  
  // UI state
  showSettings: boolean;
  showTimeline: boolean;
  copyPopoverOpen: boolean;
  showPreview: boolean;
  previewUrl: string;
  isPreviewMaximized: boolean;
  splitPosition: number;
  
  // Event handlers
  onBack: () => void;
  onProjectSettings?: (projectPath: string) => void;
  setShowSlashCommandsSettings: (show: boolean) => void;
  setShowSettings: (show: boolean) => void;
  setShowTimeline: (show: boolean) => void;
  setCopyPopoverOpen: (open: boolean) => void;
  handleCopyAsMarkdown: () => void;
  handleCopyAsJsonl: () => void;
  handleClosePreview: () => void;
  handleTogglePreviewMaximize: () => void;
  handlePreviewUrlChange: (url: string) => void;
  setSplitPosition: (position: number) => void;
  
  // Content elements (JSX)
  projectPathInput: React.ReactNode;
  messagesList: React.ReactNode;
}

export const SessionContentComponent: React.FC<SessionContentProps> = ({
  className,
  projectPath,
  effectiveSession,
  isLoading,
  messages,
  showSettings,
  showTimeline,
  copyPopoverOpen,
  showPreview,
  previewUrl,
  isPreviewMaximized,
  splitPosition,
  onBack,
  onProjectSettings,
  setShowSlashCommandsSettings,
  setShowSettings,
  setShowTimeline,
  setCopyPopoverOpen,
  handleCopyAsMarkdown,
  handleCopyAsJsonl,
  handleClosePreview,
  handleTogglePreviewMaximize,
  handlePreviewUrlChange,
  setSplitPosition,
  projectPathInput,
  messagesList,
}) => {
  const modeContext = useModeContext();
  
  return (
    <SessionContainer 
      className={className}
      showModePanel={true}
      layoutDensity="standard"
    >
      {/* Main Content - Primary Column */}
      <div className="h-full flex flex-col">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="cds--spacing-05 flex items-center justify-between border-b border-border"
        >
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={onBack}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center gap-2 cds--spacing-03">
              <Terminal className="h-5 w-5 text-muted-foreground" />
              <div className="flex-1">
                <h1 className="cds--productive-heading-04">Claude Code Session</h1>
                <p className="cds--label-01 text-muted-foreground">
                  {projectPath ? `${projectPath}` : "No project selected"}
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center cds--spacing-03">
            {/* Intelligent Mode Selector */}
            <ModeSelectorResponsive
              currentMode={modeContext.currentMode}
              modes={modeContext.getAllModes()}
              onModeChange={(mode) => modeContext.switchMode(mode.id)}
            />
            
            {projectPath && onProjectSettings && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onProjectSettings(projectPath)}
                disabled={isLoading}
              >
                <Settings className="h-4 w-4 mr-2" />
                Hooks
              </Button>
            )}
            {projectPath && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSlashCommandsSettings(true)}
                disabled={isLoading}
              >
                <Command className="h-4 w-4 mr-2" />
                Commands
              </Button>
            )}
            {showSettings && (
              <CheckpointSettings
                sessionId={effectiveSession?.id || ''}
                projectId={effectiveSession?.project_id || ''}
                projectPath={projectPath}
              />
            )}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowSettings(!showSettings)}
                    className="h-8 w-8"
                  >
                    <Settings className={cn("h-4 w-4", showSettings && "text-primary")} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Checkpoint Settings</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            {effectiveSession && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowTimeline(!showTimeline)}
                      className="h-8 w-8"
                    >
                      <GitBranch className={cn("h-4 w-4", showTimeline && "text-primary")} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Timeline Navigator</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            {messages.length > 0 && (
              <Popover
                trigger={
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Copy className="h-4 w-4" />
                    Copy Output
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                }
                content={
                  <div className="w-44 p-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCopyAsMarkdown}
                      className="w-full justify-start"
                    >
                      Copy as Markdown
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCopyAsJsonl}
                      className="w-full justify-start"
                    >
                      Copy as JSONL
                    </Button>
                  </div>
                }
                open={copyPopoverOpen}
                onOpenChange={setCopyPopoverOpen}
              />
            )}
          </div>
        </motion.div>

        {/* Main Content Area with Mode Layout */}
        <div className={cn(
          "flex-1 overflow-hidden transition-all duration-300",
          showTimeline && "cds--ml-lg-4"
        )}>
          <ModeLayoutResponsive
            mode={modeContext.currentMode}
            context={{
              fileContext: undefined as any,
              projectContext: undefined as any,
              userContext: undefined as any,
              sessionContext: undefined,
            } as any}
          >
            {showPreview ? (
              // Split pane layout when preview is active
              <div className="h-full">
                <SplitPane
                  left={
                    <div className="h-full flex flex-col cds--spacing-05">
                      {projectPathInput}
                      {messagesList}
                    </div>
                  }
                  right={
                    <WebviewPreview
                      initialUrl={previewUrl}
                      onClose={handleClosePreview}
                      isMaximized={isPreviewMaximized}
                      onToggleMaximize={handleTogglePreviewMaximize}
                      onUrlChange={handlePreviewUrlChange}
                    />
                  }
                  initialSplit={splitPosition}
                  onSplitChange={setSplitPosition}
                  minLeftWidth={400}
                  minRightWidth={400}
                  className="h-full"
                />
              </div>
            ) : (
              // Standard layout when no preview
              <div className="h-full flex flex-col cds--spacing-05 cds--p-05">
                {projectPathInput}
                {messagesList}
                
                {isLoading && messages.length === 0 && (
                  <div className="flex items-center justify-center h-full">
                    <div className="flex items-center gap-3 cds--spacing-05">
                      <div className="rotating-symbol text-primary" />
                      <span className="cds--label-01 text-muted-foreground">
                        {effectiveSession ? "Loading session history..." : "Initializing Claude Code..."}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </ModeLayoutResponsive>
        </div>
      </div>
      
      {/* Mode-Specific Widget Panel - Secondary Column */}
      <ModeSpecificPanels className="h-full overflow-hidden" />
    </SessionContainer>
  );
};
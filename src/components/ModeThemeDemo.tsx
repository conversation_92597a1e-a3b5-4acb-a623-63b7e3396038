import React from 'react';
import { useModeContext } from '@/contexts/ModeContext';
import { colors, spacing, typography, radii, shadows } from '@/intelligent-mode-system/design-tokens/tokens';

/**
 * Demo component to showcase the design token system
 * This component demonstrates how the theme changes with different modes
 */
export const ModeThemeDemo: React.FC = () => {
  const { currentMode, getAllModes, setMode } = useModeContext();
  const allModes = getAllModes();

  return (
    <div className="p-8 space-y-8">
      {/* Mode Selector */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Design Token System Demo</h2>
        <div className="flex gap-4 flex-wrap">
          {allModes.map(mode => (
            <button
              key={mode.id}
              onClick={() => setMode(mode.id)}
              className={`px-4 py-2 rounded-lg font-medium transition-all ${
                currentMode.id === mode.id
                  ? 'bg-[var(--mode-primary)] text-[var(--mode-primary-foreground)]'
                  : 'bg-[var(--mode-surface)] text-[var(--mode-text)] hover:bg-[var(--mode-hover)]'
              }`}
              style={{
                borderRadius: radii.lg,
                boxShadow: currentMode.id === mode.id ? shadows.md : shadows.sm,
              }}
            >
              {mode.icon} {mode.name}
            </button>
          ))}
        </div>
      </div>

      {/* Color Palette */}
      <div>
        <h3 className="text-xl font-semibold mb-4">Color Palette</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <ColorSwatch label="Primary" cssVar="--mode-primary" />
          <ColorSwatch label="Secondary" cssVar="--mode-secondary" />
          <ColorSwatch label="Accent" cssVar="--mode-accent" />
          <ColorSwatch label="Background" cssVar="--mode-background" />
          <ColorSwatch label="Surface" cssVar="--mode-surface" />
          <ColorSwatch label="Text" cssVar="--mode-text" />
          <ColorSwatch label="Muted" cssVar="--mode-text-muted" />
          <ColorSwatch label="Border" cssVar="--mode-border" />
        </div>
      </div>

      {/* Typography Scale */}
      <div>
        <h3 className="text-xl font-semibold mb-4">Typography Scale</h3>
        <div className="space-y-2">
          {Object.entries(typography.sizes).map(([key, value]) => (
            <div key={key} className="flex items-baseline gap-4">
              <span className="text-sm text-[var(--mode-text-muted)] w-16">{key}</span>
              <span style={{ fontSize: value }}>The quick brown fox jumps over the lazy dog</span>
            </div>
          ))}
        </div>
      </div>

      {/* Spacing Scale */}
      <div>
        <h3 className="text-xl font-semibold mb-4">Spacing Scale</h3>
        <div className="space-y-2">
          {Object.entries(spacing).slice(0, 10).map(([key, value]) => (
            <div key={key} className="flex items-center gap-4">
              <span className="text-sm text-[var(--mode-text-muted)] w-16">{key}</span>
              <div 
                className="bg-[var(--mode-primary)] h-4"
                style={{ width: value }}
              />
              <span className="text-sm text-[var(--mode-text-muted)]">{value}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Component Examples */}
      <div>
        <h3 className="text-xl font-semibold mb-4">Component Examples</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Card */}
          <div 
            className="p-6 bg-[var(--mode-surface)] border border-[var(--mode-border)]"
            style={{ borderRadius: radii.xl, boxShadow: shadows.base }}
          >
            <h4 className="text-lg font-semibold mb-2">Card Component</h4>
            <p className="text-[var(--mode-text-secondary)]">
              This card uses design tokens for consistent styling across modes.
            </p>
          </div>

          {/* Button Variants */}
          <div className="space-y-4">
            <button 
              className="w-full px-4 py-2 bg-[var(--mode-primary)] text-[var(--mode-primary-foreground)] font-medium"
              style={{ borderRadius: radii.md }}
            >
              Primary Button
            </button>
            <button 
              className="w-full px-4 py-2 bg-[var(--mode-secondary)] text-[var(--mode-secondary-foreground)] font-medium"
              style={{ borderRadius: radii.md }}
            >
              Secondary Button
            </button>
            <button 
              className="w-full px-4 py-2 border border-[var(--mode-border)] text-[var(--mode-text)] font-medium"
              style={{ borderRadius: radii.md }}
            >
              Outline Button
            </button>
          </div>

          {/* Status Indicators */}
          <div className="space-y-2">
            <StatusIndicator type="success" label="Success" />
            <StatusIndicator type="warning" label="Warning" />
            <StatusIndicator type="error" label="Error" />
            <StatusIndicator type="info" label="Info" />
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper component for color swatches
const ColorSwatch: React.FC<{ label: string; cssVar: string }> = ({ label, cssVar }) => {
  const color = getComputedStyle(document.documentElement).getPropertyValue(cssVar);
  
  return (
    <div className="space-y-2">
      <div 
        className="h-20 rounded-lg border border-[var(--mode-border)]"
        style={{ backgroundColor: `var(${cssVar})` }}
      />
      <div className="text-sm">
        <div className="font-medium">{label}</div>
        <div className="text-[var(--mode-text-muted)]">{color || cssVar}</div>
      </div>
    </div>
  );
};

// Helper component for status indicators
const StatusIndicator: React.FC<{ type: 'success' | 'warning' | 'error' | 'info'; label: string }> = ({ type, label }) => {
  const bgVar = `--mode-${type}`;
  const fgVar = `--mode-${type}-foreground`;
  
  return (
    <div 
      className="px-3 py-1 text-sm font-medium inline-block"
      style={{ 
        backgroundColor: `var(${bgVar})`,
        color: `var(${fgVar})`,
        borderRadius: radii.md,
      }}
    >
      {label}
    </div>
  );
};
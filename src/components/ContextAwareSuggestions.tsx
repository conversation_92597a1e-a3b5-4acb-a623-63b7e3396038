import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Lightbulb, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  X, 
  ArrowRight,
  GitBranch,
  FileCode,
  Bug,
  Rocket,
  Settings,
  BookOpen
} from 'lucide-react';
import { useModeContext } from './IntelligentModeIntegration';
import { useProjectContext } from '@/contexts/ProjectContextProvider';
import { useGitContext } from '@/contexts/GitContextProvider';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface Suggestion {
  id: string;
  type: 'tip' | 'warning' | 'info' | 'action';
  title: string;
  description: string;
  action?: {
    label: string;
    prompt: string;
  };
  icon: React.ReactNode;
  priority: 'high' | 'medium' | 'low';
  dismissible?: boolean;
  conditions?: string[];
}

interface ContextAwareSuggestionsProps {
  onSuggestionClick?: (prompt: string) => void;
  className?: string;
}

/**
 * ContextAwareSuggestions - Provides intelligent suggestions based on current context
 */
export const ContextAwareSuggestions: React.FC<ContextAwareSuggestionsProps> = ({
  onSuggestionClick,
  className
}) => {
  const modeContext = useModeContext();
  const projectContext = useProjectContext();
  const gitContext = useGitContext();
  const [dismissedSuggestions, setDismissedSuggestions] = useState<Set<string>>(new Set());

  // Generate context-aware suggestions based on current state
  const suggestions = useMemo<Suggestion[]>(() => {
    const suggestions: Suggestion[] = [];
    const currentMode = modeContext.currentMode;
    const project = projectContext.projectContext;
    const git = gitContext.gitStatus;

    // Mode-specific suggestions
    switch (currentMode.id) {
      case 'architect':
        if (project?.type === 'unknown') {
          suggestions.push({
            id: 'architect-detect-type',
            type: 'tip',
            title: 'Project Type Unknown',
            description: 'Help me analyze and identify the project type and architecture',
            action: {
              label: 'Analyze Project',
              prompt: 'Please analyze this project structure and identify the technology stack, architecture patterns, and project type. Provide recommendations for organization and best practices.'
            },
            icon: <FileCode className="w-4 h-4" />,
            priority: 'medium',
            dismissible: true,
            conditions: ['unknown-project-type']
          });
        }

        if (project && !project.structure.hasDocs) {
          suggestions.push({
            id: 'architect-add-docs',
            type: 'action',
            title: 'Missing Documentation',
            description: 'Generate architecture documentation for better project understanding',
            action: {
              label: 'Generate Docs',
              prompt: 'Generate comprehensive architecture documentation including: project overview, system design, component relationships, data flow, and deployment architecture. Include diagrams where helpful.'
            },
            icon: <BookOpen className="w-4 h-4" />,
            priority: 'high',
            dismissible: true,
            conditions: ['no-documentation']
          });
        }
        break;

      case 'debug':
        if (git?.modified && git.modified.length > 0) {
          suggestions.push({
            id: 'debug-review-changes',
            type: 'warning',
            title: 'Uncommitted Changes Detected',
            description: `${git.modified.length} modified files may contain bugs`,
            action: {
              label: 'Review Changes',
              prompt: `Please review the ${git.modified.length} modified files for potential bugs, issues, or improvements. Focus on: error handling, edge cases, performance implications, and code quality.`
            },
            icon: <Bug className="w-4 h-4" />,
            priority: 'high',
            dismissible: true,
            conditions: ['uncommitted-changes']
          });
        }

        if (project && !project.structure.hasTests) {
          suggestions.push({
            id: 'debug-add-tests',
            type: 'tip',
            title: 'No Tests Detected',
            description: 'Adding tests will help catch bugs early',
            action: {
              label: 'Create Tests',
              prompt: 'Help me set up a comprehensive testing strategy for this project. Include unit tests, integration tests, and debugging workflows. Suggest testing frameworks and best practices.'
            },
            icon: <CheckCircle className="w-4 h-4" />,
            priority: 'medium',
            dismissible: true,
            conditions: ['no-tests']
          });
        }
        break;

      case 'deploy':
        if (project?.type !== 'unknown') {
          suggestions.push({
            id: 'deploy-setup-ci',
            type: 'action',
            title: 'Setup Deployment Pipeline',
            description: 'Configure CI/CD for automated deployments',
            action: {
              label: 'Setup CI/CD',
              prompt: `Help me set up a CI/CD pipeline for this ${project?.type} project. Include: build automation, testing integration, deployment strategies, environment management, and monitoring setup.`
            },
            icon: <Rocket className="w-4 h-4" />,
            priority: 'high',
            dismissible: true,
            conditions: ['no-ci-cd']
          });
        }
        break;

      case 'review':
        if (project && project.structure.fileCount > 50) {
          suggestions.push({
            id: 'review-large-project',
            type: 'info',
            title: 'Large Project Detected',
            description: 'Consider focusing review on specific areas or recent changes',
            action: {
              label: 'Strategic Review',
              prompt: 'This is a large project with many files. Help me create a strategic code review plan focusing on: critical components, recent changes, potential hotspots, and areas with highest business impact.'
            },
            icon: <Settings className="w-4 h-4" />,
            priority: 'medium',
            dismissible: true,
            conditions: ['large-project']
          });
        }
        break;
    }

    // General project suggestions
    if (project?.structure.fileCount === 0) {
      suggestions.push({
        id: 'general-empty-project',
        type: 'tip',
        title: 'Empty Project',
        description: 'Start by creating your project structure',
        action: {
          label: 'Setup Project',
          prompt: 'Help me set up a new project structure. What type of project would you like to create? I can help with project initialization, folder structure, configuration files, and initial setup.'
        },
        icon: <Lightbulb className="w-4 h-4" />,
        priority: 'high',
        dismissible: true,
        conditions: ['empty-project']
      });
    }

    // Git-related suggestions
    if (git && !git.branch) {
      suggestions.push({
        id: 'git-no-branch',
        type: 'warning',
        title: 'Git Status Unknown',
        description: 'Unable to detect git branch information',
        icon: <GitBranch className="w-4 h-4" />,
        priority: 'low',
        dismissible: true,
        conditions: ['git-unknown']
      });
    }

    return suggestions.filter(s => !dismissedSuggestions.has(s.id));
  }, [modeContext.currentMode, projectContext.projectContext, gitContext.gitStatus, dismissedSuggestions]);

  // Sort suggestions by priority
  const sortedSuggestions = useMemo(() => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return suggestions.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
  }, [suggestions]);

  const handleDismiss = (suggestionId: string) => {
    setDismissedSuggestions(prev => new Set([...prev, suggestionId]));
  };

  const handleSuggestionAction = (suggestion: Suggestion) => {
    if (suggestion.action && onSuggestionClick) {
      onSuggestionClick(suggestion.action.prompt);
    }
  };

  const getSuggestionIcon = (type: Suggestion['type']) => {
    switch (type) {
      case 'tip':
        return <Lightbulb className="w-4 h-4 text-blue-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
      case 'action':
        return <ArrowRight className="w-4 h-4 text-green-500" />;
      default:
        return <Info className="w-4 h-4" />;
    }
  };

  const getSuggestionStyles = (type: Suggestion['type']) => {
    switch (type) {
      case 'tip':
        return 'border-blue-200 bg-blue-50/50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50/50';
      case 'info':
        return 'border-gray-200 bg-gray-50/50';
      case 'action':
        return 'border-green-200 bg-green-50/50';
      default:
        return 'border-gray-200 bg-gray-50/50';
    }
  };

  if (sortedSuggestions.length === 0) {
    return null;
  }

  return (
    <div className={cn('context-aware-suggestions space-y-3', className)}>
      <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
        <Lightbulb className="w-4 h-4" />
        Smart Suggestions
      </div>

      <AnimatePresence mode="popLayout">
        {sortedSuggestions.map((suggestion, index) => (
          <motion.div
            key={suggestion.id}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ 
              duration: 0.3, 
              delay: index * 0.1,
              ease: 'easeOut'
            }}
          >
            <Card className={cn(
              'relative group transition-all duration-200 hover:shadow-md',
              getSuggestionStyles(suggestion.type)
            )}>
              <CardContent className="p-3">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {suggestion.icon || getSuggestionIcon(suggestion.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-foreground">
                          {suggestion.title}
                        </h4>
                        <p className="text-xs text-muted-foreground mt-1">
                          {suggestion.description}
                        </p>
                      </div>
                      
                      {suggestion.dismissible && (
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => handleDismiss(suggestion.id)}
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      )}
                    </div>

                    {suggestion.action && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="mt-2 h-7 text-xs"
                        onClick={() => handleSuggestionAction(suggestion)}
                      >
                        {suggestion.action.label}
                        <ArrowRight className="w-3 h-3 ml-1" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Context info */}
      <div className="text-xs text-muted-foreground/70 flex items-center gap-2">
        <div className="w-1 h-1 bg-current rounded-full"></div>
        <span>Based on {modeContext.currentMode.name} mode and project context</span>
      </div>
    </div>
  );
};

export default ContextAwareSuggestions;
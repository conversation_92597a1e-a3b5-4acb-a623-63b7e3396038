/* Mode-specific layout styles */

.mode-specific-layout {
  transition: all 0.3s ease-in-out;
}

/* Architect Mode - Structured, grid-like layout */
.architect-mode-layout .grid-layout {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 1rem;
  padding: 1rem;
}

.architect-mode-layout {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.architect-mode-layout .mode-header {
  background: rgba(59, 130, 246, 0.05);
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

/* Debug Mode - Terminal-like, diagnostic layout */
.debug-mode-layout .diagnostic-layout {
  display: flex;
  flex-direction: column;
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  padding: 0.5rem;
}

.debug-mode-layout {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.debug-mode-layout .mode-header {
  background: rgba(239, 68, 68, 0.05);
  border-bottom: 1px solid rgba(239, 68, 68, 0.1);
}

/* Review Mode - Side-by-side, comparison layout */
.review-mode-layout .analytical-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  padding: 1rem;
}

.review-mode-layout {
  background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
}

.review-mode-layout .mode-header {
  background: rgba(14, 165, 233, 0.05);
  border-bottom: 1px solid rgba(14, 165, 233, 0.1);
}

/* Deploy Mode - Step-by-step, pipeline layout */
.deploy-mode-layout .process-layout {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
}

.deploy-mode-layout {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.deploy-mode-layout .mode-header {
  background: rgba(34, 197, 94, 0.05);
  border-bottom: 1px solid rgba(34, 197, 94, 0.1);
}

/* Experiment Mode - Flexible, experimental layout */
.experiment-mode-layout .exploratory-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
}

.experiment-mode-layout {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
}

.experiment-mode-layout .mode-header {
  background: rgba(234, 179, 8, 0.05);
  border-bottom: 1px solid rgba(234, 179, 8, 0.1);
}

/* Learn Mode - Tutorial-like, guided layout */
.learn-mode-layout .educational-layout {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 1.5rem;
  padding: 1rem;
}

.learn-mode-layout {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.learn-mode-layout .mode-header {
  background: rgba(168, 85, 247, 0.05);
  border-bottom: 1px solid rgba(168, 85, 247, 0.1);
}

/* Default mode layout */
.default-mode-layout .mode-content {
  padding: 1rem;
}

/* Mode-specific animations */
.mode-specific-layout [data-mode-element] {
  transition: all 0.2s ease-in-out;
}

.mode-specific-layout:hover [data-mode-element] {
  transform: translateY(-1px);
}

/* Mode-specific color utilities */
.mode-color-border {
  border-color: var(--mode-border);
}

.mode-color-text {
  color: var(--mode-border);
}

.mode-color-bg {
  background-color: var(--mode-background);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .architect-mode-layout .grid-layout,
  .review-mode-layout .analytical-layout,
  .learn-mode-layout .educational-layout {
    grid-template-columns: 1fr;
  }
  
  .experiment-mode-layout .exploratory-layout {
    flex-direction: column;
  }
}

/* Mode transition effects */
.mode-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.mode-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
}

.mode-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.mode-transition-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
}

/* Panel-specific styles */
.mode-specific-panels {
  min-width: 250px;
  max-width: 300px;
}

.mode-specific-panels .card {
  border: 1px solid rgba(var(--mode-border), 0.1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.mode-specific-panels .card-header {
  padding-bottom: 0.5rem;
}

.mode-specific-panels .card-content {
  padding-top: 0.5rem;
}

/* Mode-specific button styles */
.mode-specific-panels button {
  transition: all 0.2s ease-in-out;
}

.mode-specific-panels button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
import React, { useState, useC<PERSON>back, useEffect, useMemo } from "react";
import { motion } from "framer-motion";
import { open } from "@tauri-apps/api/dialog";
import { cn } from "@/lib/utils";
import { api } from "@/lib/api";
import { useClaudeMessages } from "@/hooks/useClaudeMessages";
import { useCheckpoints } from "@/hooks/useCheckpoints";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { SessionHeader } from "./session/SessionHeader";
import { MessageList } from "./claude-code-session/MessageList";
import { PromptQueue } from "./claude-code-session/PromptQueue";
import { WebviewPreview } from "./WebviewPreview";
import { SplitPane } from "./SplitPane";

// Context Providers
import { GitContextProvider } from "@/contexts/GitContextProvider";
import { ProjectContextProvider } from "@/contexts/ProjectContextProvider";
import { ModeProvider } from "@/contexts/ModeContext";

// Intelligent Mode System Integration
import { IntelligentModeIntegration } from "./IntelligentModeIntegration";
import { useIntelligentMode, useEnhancedSmartPrompt } from "@/intelligent-mode-system/hooks";
import { ModeSpecificLayout } from "./ModeSpecificLayout";
import { SmartPromptInputResponsive } from "@/intelligent-mode-system/components/SmartPromptInputResponsive";
import { ModeIndicator } from "@/intelligent-mode-system/components/ModeIndicator";
import { ContextWidgetGrid } from "@/intelligent-mode-system/components/EnhancedContextWidgets";
import { architectMode, debugMode, reviewMode, deployMode, experimentMode, learnMode } from "@/intelligent-mode-system/modes";

const modes = [architectMode, debugMode, reviewMode, deployMode, experimentMode, learnMode];

export interface ClaudeCodeSessionProps {
  session?: any;
  initialProjectPath?: string;
  onBack?: () => void;
  onProjectSettings?: (path: string) => void;
  className?: string;
  onStreamingChange?: (isStreaming: boolean) => void;
}

// Internal component that uses the intelligent mode system
const ClaudeCodeSessionInternal: React.FC<ClaudeCodeSessionProps> = ({
  session,
  initialProjectPath = "",
  onBack,
  onProjectSettings,
  className,
  onStreamingChange,
}) => {
  // Existing state
  const [projectPath, setProjectPath] = useState(initialProjectPath || session?.project_path || "");
  const [error, setError] = useState<string | null>(null);
  const [copyPopoverOpen, setCopyPopoverOpen] = useState(false);
  const [isFirstPrompt, setIsFirstPrompt] = useState(!session);
  const [totalTokens, setTotalTokens] = useState(0);
  const [claudeSessionId, setClaudeSessionId] = useState<string | null>(null);
  const [showTimeline, setShowTimeline] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showForkDialog, setShowForkDialog] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [isPreviewMaximized, setIsPreviewMaximized] = useState(false);
  const [queuedPrompts, setQueuedPrompts] = useState<Array<{id: string, prompt: string, model: "sonnet" | "opus"}>>([]);
  const [showSlashCommandsSettings, setShowSlashCommandsSettings] = useState(false);

  // Existing hooks
  const {
    messages,
    rawJsonlOutput,
    isStreaming,
    currentSessionId: _currentSessionId,
    clearMessages,
    loadMessages
  } = useClaudeMessages({
    onSessionInfo: (info) => {
      setClaudeSessionId(info.sessionId);
    },
    onTokenUpdate: setTotalTokens,
    onStreamingChange
  });

  // Intelligent Mode System hooks
  const {
    currentMode: activeMode,
    recommendedMode,
    switchMode,
    isTransitioning: isModeLoading,
    context: modeContext,
    refreshContext,
    recordAction,
  } = useIntelligentMode();

  // Create enhanced context snapshot for smart prompt system
  const contextSnapshot = useMemo(() => ({
    fileContext: {
      path: projectPath,
      type: 'directory' as const,
      hasErrors: false,
      hasWarnings: false,
      lastModified: Date.now(),
    },
    projectContext: {
      type: 'unknown' as const,
      rootPath: projectPath,
      dependencies: {},
      structure: {
        directories: [],
        fileCount: 0,
        totalSize: 0,
        depth: 0,
        hasTests: false,
        hasDocs: false,
      },
      configuration: {},
    },
    userContext: {
      recentActions: [],
      preferences: {
        theme: 'dark' as const,
        shortcuts: {},
        autoTransition: true,
        suggestionLevel: 'standard' as const,
      },
      patterns: [],
      sessionDuration: 0,
      lastActivity: Date.now(),
    },
    environmentContext: {
      runningProcesses: [],
      systemResources: {
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
      },
      openFiles: [],
      activeTerminals: 0,
    },
    timestamp: Date.now(),
  }), [projectPath]);

  // Convert mode to proper Mode type with isActive property
  const currentModeWithActive = useMemo(() => {
    if (activeMode) {
      return {
        ...activeMode,
        isActive: true,
        lastActivated: Date.now(),
      };
    }
    return {
      ...modes[0],
      isActive: true,
      lastActivated: Date.now(),
    };
  }, [activeMode]);

  // Enhanced smart prompt system
  const smartPromptHook = useEnhancedSmartPrompt({
    context: contextSnapshot,
    currentMode: currentModeWithActive,
    onSubmit: (prompt: string, metadata?: any) => {
      // Record user action for mode inference
      recordAction({
        type: 'prompt_submitted',
        prompt,
        metadata,
        timestamp: Date.now(),
        mode: activeMode?.id,
      });
    },
  });

  // Checkpoints system
  const {
    checkpoints: _checkpoints,
    timelineVersion,
    loadCheckpoints,
    createCheckpoint: _createCheckpoint,
    restoreCheckpoint,
    forkCheckpoint
  } = useCheckpoints({
    sessionId: claudeSessionId,
    projectId: session?.project_id || '',
    projectPath: projectPath,
    onToast: (message: string, type: 'success' | 'error') => {
      console.log(`Toast: ${type} - ${message}`);
    }
  });

  // Handle path selection
  const handleSelectPath = async () => {
    const selected = await open({
      directory: true,
      multiple: false,
      title: "Select Project Directory"
    });
    
    if (selected && typeof selected === 'string') {
      setProjectPath(selected);
      setError(null);
      setIsFirstPrompt(true);
      
      // Refresh context when project path changes
      await refreshContext();
    }
  };

  // Enhanced prompt handling with mode awareness
  const handleSendPrompt = useCallback(async (prompt: string, model: "sonnet" | "opus") => {
    if (!projectPath || !prompt.trim()) return;

    // Add to queue if streaming
    if (isStreaming) {
      const id = Date.now().toString();
      setQueuedPrompts(prev => [...prev, { id, prompt, model }]);
      return;
    }

    try {
      setError(null);
      
      // Record action for mode inference
      recordAction({
        type: 'prompt_submitted',
        prompt,
        model,
        timestamp: Date.now(),
        mode: activeMode?.id,
        projectPath,
      });
      
      if (isFirstPrompt) {
        await api.executeClaudeCode(projectPath, prompt, model);
        setIsFirstPrompt(false);
      } else if (claudeSessionId) {
        await api.continueClaudeCode(projectPath, prompt, model);
      }
      
      // Update context through smart prompt hook
      if (smartPromptHook.submitPrompt) {
        smartPromptHook.submitPrompt(prompt, { 
          type: 'user', 
          timestamp: Date.now(),
          mode: activeMode?.id,
          model,
        });
      }
    } catch (error) {
      console.error("Failed to send prompt:", error);
      setError(error instanceof Error ? error.message : "Failed to send prompt");
    }
  }, [projectPath, isStreaming, isFirstPrompt, claudeSessionId, smartPromptHook.submitPrompt, recordAction, activeMode?.id]);

  // Handle link detection for preview
  const handleLinkDetected = useCallback((url: string) => {
    setPreviewUrl(url);
    setShowPreview(true);
  }, []);

  // Copy handlers
  const handleCopyAsJsonl = useCallback(() => {
    navigator.clipboard.writeText(rawJsonlOutput);
    setCopyPopoverOpen(false);
  }, [rawJsonlOutput]);

  const handleCopyAsMarkdown = useCallback(() => {
    const markdown = messages.map(msg => `**${msg.role}**: ${msg.content}`).join('\n\n');
    navigator.clipboard.writeText(markdown);
    setCopyPopoverOpen(false);
  }, [messages]);

  // Effect to refresh context when project changes
  useEffect(() => {
    if (projectPath) {
      refreshContext();
    }
  }, [projectPath, refreshContext]);

  return (
    <div className={cn("flex flex-col h-screen bg-background", className)}>
      {/* Enhanced Session Header with Mode Integration */}
      <SessionHeader
        projectPath={projectPath}
        claudeSessionId={claudeSessionId}
        totalTokens={totalTokens}
        isStreaming={isStreaming}
        hasMessages={messages.length > 0}
        showTimeline={showTimeline}
        copyPopoverOpen={copyPopoverOpen}
        onBack={onBack}
        onSelectPath={handleSelectPath}
        onCopyAsJsonl={handleCopyAsJsonl}
        onCopyAsMarkdown={handleCopyAsMarkdown}
        onToggleTimeline={() => setShowTimeline(!showTimeline)}
        onProjectSettings={onProjectSettings ? () => onProjectSettings(projectPath) : undefined}
        onSlashCommandsSettings={() => setShowSlashCommandsSettings(true)}
        setCopyPopoverOpen={setCopyPopoverOpen}
        activeMode={activeMode}
        recommendedMode={recommendedMode}
        onSwitchMode={(modeId: string) => switchMode(modeId as any)}
        isModeLoading={isModeLoading}
        modes={modes}
      />

      {/* Mode Indicator */}
      <div className="px-4 py-2 border-b">
        <ModeIndicator />
      </div>

      {/* Main content area with Mode-Specific Layout */}
      <ModeSpecificLayout>
        <div className="flex-1 flex">
          {/* Context Widgets Grid */}
          {activeMode && (
            <div className="w-80 border-r bg-muted/30 p-4">
              <ContextWidgetGrid 
                mode={activeMode}
                context={contextSnapshot}
                className="h-full"
              />
            </div>
          )}

          {/* Main content */}
          <div className="flex-1 flex flex-col">
            {showPreview ? (
              <SplitPane
                left={
                  <div className="flex flex-col h-full">
                    <MessageList
                      messages={messages}
                      projectPath={projectPath}
                      isStreaming={isStreaming}
                      onLinkDetected={handleLinkDetected}
                      className="flex-1"
                    />
                    <PromptQueue
                      queuedPrompts={queuedPrompts}
                      onRemove={(id) => setQueuedPrompts(prev => prev.filter(p => p.id !== id))}
                    />
                  </div>
                }
                right={
                  <WebviewPreview
                    initialUrl={previewUrl || ""}
                    isMaximized={isPreviewMaximized}
                    onClose={() => setShowPreview(false)}
                    onUrlChange={setPreviewUrl}
                    onToggleMaximize={() => setIsPreviewMaximized(!isPreviewMaximized)}
                  />
                }
                initialSplit={60}
              />
            ) : (
              <div className="flex flex-col flex-1">
                <MessageList
                  messages={messages}
                  projectPath={projectPath}
                  isStreaming={isStreaming}
                  onLinkDetected={handleLinkDetected}
                  className="flex-1"
                />
                <PromptQueue
                  queuedPrompts={queuedPrompts}
                  onRemove={(id) => setQueuedPrompts(prev => prev.filter(p => p.id !== id))}
                />
              </div>
            )}
          </div>
        </div>
      </ModeSpecificLayout>

      {/* Enhanced Smart Prompt Input */}
      <div className="border-t bg-background p-4">
        <SmartPromptInputResponsive
          onSubmit={handleSendPrompt}
          isStreaming={isStreaming}
          projectPath={projectPath}
          currentMode={activeMode}
          suggestions={smartPromptHook.suggestions || []}
          recentPrompts={smartPromptHook.recentPrompts || []}
          predictions={smartPromptHook.predictions}
          insights={smartPromptHook.insights || []}
          modeRecommendation={smartPromptHook.modeRecommendation}
          className="max-w-4xl mx-auto"
        />
      </div>

      {/* Error display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mx-4 mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md"
        >
          <p className="text-sm text-destructive">{error}</p>
        </motion.div>
      )}
    </div>
  );
};

// Main exported component with full integration
export const ClaudeCodeSession: React.FC<ClaudeCodeSessionProps> = (props) => {
  return (
    <ErrorBoundary>
      <IntelligentModeIntegration>
        <ModeProvider>
          <ProjectContextProvider projectPath={props.initialProjectPath || props.session?.project_path}>
            <GitContextProvider projectPath={props.initialProjectPath || props.session?.project_path}>
              <ClaudeCodeSessionInternal {...props} />
            </GitContextProvider>
          </ProjectContextProvider>
        </ModeProvider>
      </IntelligentModeIntegration>
    </ErrorBoundary>
  );
};

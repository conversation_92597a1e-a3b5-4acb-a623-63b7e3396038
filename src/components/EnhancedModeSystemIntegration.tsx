import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>ting<PERSON>, Zap, Eye, Accessibility } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

// Import our enhanced components
import { useAdaptiveLayout } from '../hooks/useAdaptiveLayout';
import { ContextWidgetGrid } from './EnhancedContextWidgets';
import { ModeSpecificLayout } from './ModeSpecificLayouts';
import { useContainerQuery } from './ResponsiveLayoutSystem';
import { 
  usePerformanceMonitor, 
  OptimizedModeLayout 
} from './PerformanceOptimizedUI';
import { 
  AccessibleGridLayout,
  SkipLink,
  useHighContrastMode,
  useReducedMotionPreference
} from './AccessibilityEnhancedUI';
import { 
  ModeTransitionOverlay,
  SmartModeSuggestions,
  useModeTransition
} from './IntelligentModeTransitions';
import { Mode } from '@/intelligent-mode-system/types/mode.types';

// Enhanced system configuration
interface SystemConfiguration {
  performance: {
    enableVirtualization: boolean;
    enableLazyLoading: boolean;
    enableMemoization: boolean;
    renderThreshold: number;
  };
  accessibility: {
    enableHighContrast: boolean;
    enableReducedMotion: boolean;
    enableScreenReader: boolean;
    enableKeyboardNavigation: boolean;
  };
  layout: {
    enableAdaptiveLayouts: boolean;
    enableResponsiveGrids: boolean;
    enableContextWidgets: boolean;
    maxWidgetsPerRow: number;
  };
  transitions: {
    enableSmartSuggestions: boolean;
    enableContextPreservation: boolean;
    enableProgressIndicators: boolean;
    transitionDuration: number;
  };
}

const defaultConfiguration: SystemConfiguration = {
  performance: {
    enableVirtualization: true,
    enableLazyLoading: true,
    enableMemoization: true,
    renderThreshold: 100
  },
  accessibility: {
    enableHighContrast: false,
    enableReducedMotion: false,
    enableScreenReader: true,
    enableKeyboardNavigation: true
  },
  layout: {
    enableAdaptiveLayouts: true,
    enableResponsiveGrids: true,
    enableContextWidgets: true,
    maxWidgetsPerRow: 3
  },
  transitions: {
    enableSmartSuggestions: true,
    enableContextPreservation: true,
    enableProgressIndicators: true,
    transitionDuration: 300
  }
};

// System status monitoring
interface SystemStatus {
  performance: {
    averageRenderTime: number;
    memoryUsage: number;
    activeComponents: number;
  };
  accessibility: {
    contrastRatio: number;
    keyboardNavigable: boolean;
    screenReaderCompatible: boolean;
  };
  layout: {
    activeWidgets: number;
    responsiveBreakpoint: string;
    layoutMode: string;
  };
}

// Configuration panel component
interface ConfigurationPanelProps {
  config: SystemConfiguration;
  onConfigChange: (config: SystemConfiguration) => void;
  systemStatus: SystemStatus;
  className?: string;
}

const ConfigurationPanel: React.FC<ConfigurationPanelProps> = ({
  config,
  onConfigChange,
  systemStatus,
  className
}) => {
  const updateConfig = (section: keyof SystemConfiguration, key: string, value: any) => {
    onConfigChange({
      ...config,
      [section]: {
        ...config[section],
        [key]: value
      }
    });
  };

  return (
    <Card className={cn('configuration-panel', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          System Configuration
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value="performance" className="w-full" onValueChange={() => {}}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="performance">
              <Zap className="h-4 w-4 mr-1" />
              Performance
            </TabsTrigger>
            <TabsTrigger value="accessibility">
              <Accessibility className="h-4 w-4 mr-1" />
              A11y
            </TabsTrigger>
            <TabsTrigger value="layout">
              <Eye className="h-4 w-4 mr-1" />
              Layout
            </TabsTrigger>
            <TabsTrigger value="transitions">
              Transitions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="performance" className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Enable Virtualization</label>
                <Switch
                  checked={config.performance.enableVirtualization}
                  onCheckedChange={(checked) => 
                    updateConfig('performance', 'enableVirtualization', checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Enable Lazy Loading</label>
                <Switch
                  checked={config.performance.enableLazyLoading}
                  onCheckedChange={(checked) => 
                    updateConfig('performance', 'enableLazyLoading', checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Enable Memoization</label>
                <Switch
                  checked={config.performance.enableMemoization}
                  onCheckedChange={(checked) => 
                    updateConfig('performance', 'enableMemoization', checked)
                  }
                />
              </div>
            </div>
            
            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">Performance Metrics</h4>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span>Avg Render Time:</span>
                  <Badge variant="outline">{systemStatus.performance.averageRenderTime}ms</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Memory Usage:</span>
                  <Badge variant="outline">{systemStatus.performance.memoryUsage}MB</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Active Components:</span>
                  <Badge variant="outline">{systemStatus.performance.activeComponents}</Badge>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="accessibility" className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">High Contrast Mode</label>
                <Switch
                  checked={config.accessibility.enableHighContrast}
                  onCheckedChange={(checked) => 
                    updateConfig('accessibility', 'enableHighContrast', checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Reduced Motion</label>
                <Switch
                  checked={config.accessibility.enableReducedMotion}
                  onCheckedChange={(checked) => 
                    updateConfig('accessibility', 'enableReducedMotion', checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Screen Reader Support</label>
                <Switch
                  checked={config.accessibility.enableScreenReader}
                  onCheckedChange={(checked) => 
                    updateConfig('accessibility', 'enableScreenReader', checked)
                  }
                />
              </div>
            </div>
            
            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">Accessibility Status</h4>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span>Contrast Ratio:</span>
                  <Badge variant="outline">{systemStatus.accessibility.contrastRatio}:1</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Keyboard Navigation:</span>
                  <Badge variant={systemStatus.accessibility.keyboardNavigable ? 'default' : 'destructive'}>
                    {systemStatus.accessibility.keyboardNavigable ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="layout" className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Adaptive Layouts</label>
                <Switch
                  checked={config.layout.enableAdaptiveLayouts}
                  onCheckedChange={(checked) => 
                    updateConfig('layout', 'enableAdaptiveLayouts', checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Responsive Grids</label>
                <Switch
                  checked={config.layout.enableResponsiveGrids}
                  onCheckedChange={(checked) => 
                    updateConfig('layout', 'enableResponsiveGrids', checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Context Widgets</label>
                <Switch
                  checked={config.layout.enableContextWidgets}
                  onCheckedChange={(checked) => 
                    updateConfig('layout', 'enableContextWidgets', checked)
                  }
                />
              </div>
            </div>
            
            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">Layout Status</h4>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span>Active Widgets:</span>
                  <Badge variant="outline">{systemStatus.layout.activeWidgets}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Breakpoint:</span>
                  <Badge variant="outline">{systemStatus.layout.responsiveBreakpoint}</Badge>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="transitions" className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Smart Suggestions</label>
                <Switch
                  checked={config.transitions.enableSmartSuggestions}
                  onCheckedChange={(checked) => 
                    updateConfig('transitions', 'enableSmartSuggestions', checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Context Preservation</label>
                <Switch
                  checked={config.transitions.enableContextPreservation}
                  onCheckedChange={(checked) => 
                    updateConfig('transitions', 'enableContextPreservation', checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Progress Indicators</label>
                <Switch
                  checked={config.transitions.enableProgressIndicators}
                  onCheckedChange={(checked) => 
                    updateConfig('transitions', 'enableProgressIndicators', checked)
                  }
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

// Main integration component
interface EnhancedModeSystemProps {
  currentMode?: Mode;
  className?: string;
  showConfiguration?: boolean;
}

export const EnhancedModeSystem: React.FC<EnhancedModeSystemProps> = ({
  currentMode = 'architect' as unknown as Mode,
  className,
  showConfiguration = false
}) => {
  const [config, setConfig] = useState<SystemConfiguration>(defaultConfiguration);
  const [showConfigPanel, setShowConfigPanel] = useState(showConfiguration);
  
  // Enhanced system hooks
  const containerRef = React.useRef<HTMLDivElement>(null);
  const layout = useAdaptiveLayout();
  const containerQuery = useContainerQuery(containerRef);
  const dimensions = { width: containerQuery.containerSize.width, height: containerQuery.containerSize.height };
  const performanceMetrics = usePerformanceMonitor();
  const { isTransitioning } = useModeTransition();
  const isHighContrast = useHighContrastMode();
  const prefersReducedMotion = useReducedMotionPreference();
  
  // System status monitoring
  const systemStatus = useMemo<SystemStatus>(() => ({
    performance: {
      averageRenderTime: performanceMetrics.metrics.renderTime || 0,
      memoryUsage: Math.round((performance as any)?.memory?.usedJSHeapSize / 1024 / 1024) || 0,
      activeComponents: performanceMetrics.metrics.componentCount || 0
    },
    accessibility: {
      contrastRatio: isHighContrast ? 7.0 : 4.5,
      keyboardNavigable: config.accessibility.enableKeyboardNavigation,
      screenReaderCompatible: config.accessibility.enableScreenReader
    },
    layout: {
      activeWidgets: 6, // Mock value
      responsiveBreakpoint: dimensions.width > 1024 ? 'lg' : dimensions.width > 768 ? 'md' : 'sm',
      layoutMode: layout.sidebarPosition || 'default'
    }
  }), [performanceMetrics, isHighContrast, config, dimensions, layout]);
  
  // Mock mode suggestions
  const modeSuggestions = useMemo(() => [
    {
      mode: 'debug' as unknown as Mode,
      confidence: 0.85,
      reason: 'Error detected in console logs',
      context: { errorCount: 3, lastError: 'TypeError: Cannot read property' }
    },
    {
      mode: 'review' as unknown as Mode,
      confidence: 0.72,
      reason: 'Recent commits ready for review',
      context: { commitCount: 5, lastCommit: '2 hours ago' }
    }
  ], []);
  
  // Apply configuration effects
  useEffect(() => {
    if (config.accessibility.enableHighContrast) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }
    
    if (config.accessibility.enableReducedMotion) {
      document.documentElement.classList.add('reduce-motion');
    } else {
      document.documentElement.classList.remove('reduce-motion');
    }
  }, [config]);
  
  return (
    <div 
      ref={containerRef}
      className={cn(
        'enhanced-mode-system',
        'min-h-screen bg-background',
        {
          'high-contrast': config.accessibility.enableHighContrast,
          'reduce-motion': config.accessibility.enableReducedMotion || prefersReducedMotion,
          'transitioning': isTransitioning
        },
        className
      )}
    >
      {/* Skip links for accessibility */}
      <SkipLink href="#main-content">
        Skip to main content
      </SkipLink>
      <SkipLink href="#navigation">
        Skip to navigation
      </SkipLink>
      
      {/* Mode transition overlay */}
      {config.transitions.enableProgressIndicators && (
        <ModeTransitionOverlay />
      )}
      
      {/* Configuration toggle */}
      <div className="fixed top-4 right-4 z-40">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowConfigPanel(!showConfigPanel)}
          aria-label="Toggle system configuration"
        >
          <Settings className="h-4 w-4" />
        </Button>
      </div>
      
      {/* Configuration panel */}
      <AnimatePresence>
        {showConfigPanel && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ duration: prefersReducedMotion ? 0 : 0.3 }}
            className="fixed top-16 right-4 z-30 w-96 max-h-[80vh] overflow-y-auto"
          >
            <ConfigurationPanel
              config={config}
              onConfigChange={setConfig}
              systemStatus={systemStatus}
            />
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Main content area */}
      <main id="main-content" className="container mx-auto p-4 space-y-6">
        {/* Mode suggestions */}
        {config.transitions.enableSmartSuggestions && modeSuggestions.length > 0 && (
          <SmartModeSuggestions
            suggestions={modeSuggestions}
            currentMode={currentMode}
            onTransition={(mode) => console.log('Transitioning to:', mode)}
          />
        )}
        
        {/* Adaptive layout system */}
        {config.performance.enableMemoization ? (
          <OptimizedModeLayout
            className="mb-6"
          >
            <div>Optimized layout content</div>
          </OptimizedModeLayout>
        ) : (
          <ModeSpecificLayout>
            <div>Mode specific layout content</div>
          </ModeSpecificLayout>
        )}
        
        {/* Context widgets grid */}
        {config.layout.enableContextWidgets && (
          config.layout.enableResponsiveGrids ? (
            <AccessibleGridLayout
              columns={config.layout.maxWidgetsPerRow}
              ariaLabel="Context widgets"
              className="context-widgets-grid"
            >
              <ContextWidgetGrid
                widgets={[]}
              />
            </AccessibleGridLayout>
          ) : (
            <ContextWidgetGrid
              widgets={[]}
            />
          )
        )}
      </main>
      
      {/* System status indicator */}
      <div className="fixed bottom-4 left-4 z-30">
        <Card className="p-2">
          <div className="flex items-center gap-2 text-xs">
            <div className={cn(
              'w-2 h-2 rounded-full',
              systemStatus.performance.averageRenderTime < 16 ? 'bg-green-500' : 'bg-yellow-500'
            )} />
            <span>{systemStatus.performance.averageRenderTime}ms</span>
            <span className="text-gray-400">|</span>
            <span>{systemStatus.layout.responsiveBreakpoint}</span>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default EnhancedModeSystem;
export type { SystemConfiguration, SystemStatus };
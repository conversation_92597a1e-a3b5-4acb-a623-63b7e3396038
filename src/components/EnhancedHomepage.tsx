import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Code2, 
  Zap, 
  Shield, 
  Layers, 
  GitBranch, 
  Terminal, 
  Eye, 
  Rocket,
  ArrowRight,
  CheckCircle,
  Star,
  Users,
  TrendingUp,
  Sparkles
} from 'lucide-react';
import EnhancedModeSystemIntegration from './EnhancedModeSystemIntegration';
import { ModeTransitionOverlay } from './IntelligentModeTransitions';
import { AccessibleContextWidget } from './AccessibilityEnhancedUI';

// Hero Section Component
const HeroSection: React.FC = () => {
  const [currentFeature, setCurrentFeature] = useState(0);
  const features = [
    'Intelligent Mode Transitions',
    'Adaptive Layout System', 
    'Performance Optimized UI',
    'Accessibility Enhanced'
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>
      
      <div className="relative z-10 max-w-6xl mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Badge className="mb-6 bg-purple-500/20 text-purple-300 border-purple-500/30">
            <Sparkles className="w-4 h-4 mr-2" />
            Enhanced Mode System v2.0
          </Badge>
          
          <h1 className="text-6xl md:text-7xl font-bold text-white mb-6 leading-tight">
            Intelligent
            <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              {' '}Development{' '}
            </span>
            Experience
          </h1>
          
          <p className="text-xl text-slate-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Transform your coding workflow with adaptive layouts, intelligent mode transitions, 
            and performance-optimized components that evolve with your development needs.
          </p>
          
          <div className="h-8 mb-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentFeature}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="text-lg text-purple-300 font-medium"
              >
                ✨ {features[currentFeature]}
              </motion.div>
            </AnimatePresence>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3">
              Get Started
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            <Button size="lg" variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-800 px-8 py-3">
              View Demo
              <Eye className="w-5 h-5 ml-2" />
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

// Features Overview Section
const FeaturesSection: React.FC = () => {
  const features = [
    {
      icon: <Layers className="w-8 h-8" />,
      title: 'Adaptive Layout System',
      description: 'Container query-based responsive design that adapts intelligently to content and context.',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: 'Performance Optimized',
      description: 'Virtual scrolling, lazy loading, and memoized components for lightning-fast interactions.',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: 'Accessibility Enhanced',
      description: 'WCAG-compliant components with focus management and keyboard navigation support.',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: <GitBranch className="w-8 h-8" />,
      title: 'Context Aware',
      description: 'Real-time Git integration and project context awareness for intelligent suggestions.',
      color: 'from-purple-500 to-pink-500'
    }
  ];

  return (
    <section className="py-24 bg-slate-50">
      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-slate-900 mb-4">
            Powerful Features for Modern Development
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Built with cutting-edge technologies and best practices to deliver 
            an exceptional development experience.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
                <CardHeader className="text-center pb-4">
                  <div className={`w-16 h-16 mx-auto rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center text-white mb-4`}>
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl font-semibold text-slate-900">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-slate-600 text-center leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Mode Showcase Section
const ModeShowcase: React.FC = () => {
  const [activeMode, setActiveMode] = useState('architect');
  const modes = [
    {
      id: 'architect',
      name: 'Architect Mode',
      icon: <Code2 className="w-6 h-6" />,
      description: 'Design and plan your application architecture with intelligent suggestions.',
      features: ['Code Structure Analysis', 'Dependency Mapping', 'Architecture Patterns']
    },
    {
      id: 'debug',
      name: 'Debug Mode', 
      icon: <Terminal className="w-6 h-6" />,
      description: 'Advanced debugging tools with real-time error detection and analysis.',
      features: ['Error Tracking', 'Performance Monitoring', 'Log Analysis']
    },
    {
      id: 'review',
      name: 'Review Mode',
      icon: <Eye className="w-6 h-6" />,
      description: 'Comprehensive code review with automated quality checks.',
      features: ['Code Quality Metrics', 'Security Analysis', 'Best Practices']
    },
    {
      id: 'deploy',
      name: 'Deploy Mode',
      icon: <Rocket className="w-6 h-6" />,
      description: 'Streamlined deployment process with environment management.',
      features: ['Build Optimization', 'Environment Config', 'Deployment Pipeline']
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-slate-900 mb-4">
            Intelligent Development Modes
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Switch seamlessly between specialized modes tailored for different 
            phases of your development workflow.
          </p>
        </div>
        
        <Tabs value={activeMode} onValueChange={setActiveMode} className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            {modes.map((mode) => (
              <TabsTrigger key={mode.id} value={mode.id} className="flex items-center gap-2">
                {mode.icon}
                <span className="hidden sm:inline">{mode.name}</span>
              </TabsTrigger>
            ))}
          </TabsList>
          
          {modes.map((mode) => (
            <TabsContent key={mode.id} value={mode.id}>
              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="grid lg:grid-cols-2 gap-8 items-center">
                    <div>
                      <h3 className="text-2xl font-bold text-slate-900 mb-4 flex items-center gap-3">
                        {mode.icon}
                        {mode.name}
                      </h3>
                      <p className="text-lg text-slate-600 mb-6">
                        {mode.description}
                      </p>
                      <div className="space-y-3">
                        {mode.features.map((feature, index) => (
                          <div key={index} className="flex items-center gap-3">
                            <CheckCircle className="w-5 h-5 text-green-500" />
                            <span className="text-slate-700">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="bg-slate-100 rounded-xl p-6 h-64 flex items-center justify-center">
                      <div className="text-slate-500 text-center">
                        <div className="w-16 h-16 mx-auto mb-4 bg-slate-300 rounded-lg flex items-center justify-center">
                          {mode.icon}
                        </div>
                        <p>Interactive {mode.name} Demo</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  );
};

// Statistics Section
const StatsSection: React.FC = () => {
  const stats = [
    { icon: <Users className="w-8 h-8" />, value: '10K+', label: 'Active Developers' },
    { icon: <Star className="w-8 h-8" />, value: '4.9/5', label: 'User Rating' },
    { icon: <TrendingUp className="w-8 h-8" />, value: '300%', label: 'Productivity Boost' },
    { icon: <Zap className="w-8 h-8" />, value: '50ms', label: 'Average Response Time' }
  ];

  return (
    <section className="py-24 bg-slate-900">
      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">
            Trusted by Developers Worldwide
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            Join thousands of developers who have transformed their workflow 
            with our enhanced mode system.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 mx-auto mb-4 bg-purple-600 rounded-2xl flex items-center justify-center text-white">
                {stat.icon}
              </div>
              <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
              <div className="text-slate-300">{stat.label}</div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Call to Action Section
const CTASection: React.FC = () => {
  return (
    <section className="py-24 bg-gradient-to-r from-purple-600 to-blue-600">
      <div className="max-w-4xl mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Transform Your Development Experience?
          </h2>
          <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
            Get started with our enhanced mode system today and experience 
            the future of intelligent development environments.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-purple-600 hover:bg-slate-100 px-8 py-3">
              Start Free Trial
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 px-8 py-3">
              Schedule Demo
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

// Main Homepage Component
export const EnhancedHomepage: React.FC = () => {
  const [showDemo, setShowDemo] = useState(false);

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <HeroSection />
      
      {/* Features Section */}
      <FeaturesSection />
      
      {/* Mode Showcase */}
      <ModeShowcase />
      
      {/* Statistics */}
      <StatsSection />
      
      {/* Call to Action */}
      <CTASection />
      
      {/* Demo Modal */}
      <AnimatePresence>
        {showDemo && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
            onClick={() => setShowDemo(false)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white rounded-2xl p-6 max-w-4xl w-full max-h-[80vh] overflow-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-slate-900">Enhanced Mode System Demo</h3>
                <Button variant="ghost" onClick={() => setShowDemo(false)}>
                  ×
                </Button>
              </div>
              <EnhancedModeSystemIntegration />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedHomepage;
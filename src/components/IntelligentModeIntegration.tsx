import React, { useEffect, useMemo, useState } from 'react';
import { ModeProvider, useModeContext } from '@/contexts/ModeContext';
import { useTabContext } from '@/contexts/TabContext';
import { ProjectContextProvider, useProjectContext } from '@/contexts/ProjectContextProvider';
import { GitContextProvider, useGitContext } from '@/contexts/GitContextProvider';
import { UserContext, FileContext } from '@/intelligent-mode-system/types/context.types';

/**
 * Bridge component that uses the real context providers
 */
const IntelligentModeContextBridge: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const modeContext = useModeContext();
  const tabContext = useTabContext();
  const projectContextHook = useProjectContext();
  const gitContextHook = useGitContext();
  const activeTab = tabContext.activeTabId ? tabContext.getTabById(tabContext.activeTabId) : undefined;

  // Build user context from <PERSON>'s data
  const userContext = useMemo<UserContext>(() => ({
    recentActions: [],
    preferences: {
      theme: 'dark',
      shortcuts: {},
      autoTransition: true,
      suggestionLevel: 'standard' as const,
    },
    patterns: [],
    sessionDuration: 0,
    lastActivity: Date.now(),
  }), []);

  // Build file context from active session
  const fileContext = useMemo<FileContext>(() => ({
    path: '',
    type: 'unknown',
    hasErrors: false,
    hasWarnings: false,
    lastModified: Date.now(),
  }), []);

  // Update tab with current mode when mode changes
  useEffect(() => {
    if (activeTab && projectContextHook.projectContext) {
      // Update tab with current mode and context
      tabContext.updateTab(activeTab.id, {
        modeId: modeContext.currentMode.id,
        modeContext: {
          fileContext,
          projectContext: projectContextHook.projectContext,
          gitStatus: gitContextHook.gitStatus,
          userContext,
        },
      });
    }
  }, [
    activeTab, 
    modeContext.currentMode.id, 
    fileContext, 
    projectContextHook.projectContext, 
    gitContextHook.gitStatus, 
    userContext, 
    tabContext
  ]);

  // Load mode from tab when tab changes (if different from current mode)
  useEffect(() => {
    if (activeTab?.modeId && activeTab.modeId !== modeContext.currentMode.id) {
      const tabMode = modeContext.getModeById(activeTab.modeId);
      if (tabMode) {
        modeContext.setMode(activeTab.modeId);
      }
    }
  }, [activeTab?.modeId, modeContext]);

  // Theme is now applied automatically by the ModeContext using the design token system

  return <>{children}</>;
};

/**
 * Bridge component that connects Claudia's existing contexts with the intelligent mode system
 */
const IntelligentModeBridge: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const tabContext = useTabContext();
  const activeTab = tabContext.activeTabId ? tabContext.getTabById(tabContext.activeTabId) : undefined;
  const [detectedProjectPath, setDetectedProjectPath] = useState<string | null>(null);

  // Detect project context from active tab
  useEffect(() => {
    if (activeTab?.type === 'chat' && activeTab.initialProjectPath) {
      setDetectedProjectPath(activeTab.initialProjectPath);
    } else if (activeTab?.sessionData?.project_path) {
      setDetectedProjectPath(activeTab.sessionData.project_path);
    }
  }, [activeTab]);

  // Use provided or detected project path
  const effectiveProjectPath = detectedProjectPath;

  return (
    <ProjectContextProvider projectPath={effectiveProjectPath || undefined}>
      <GitContextProvider projectPath={effectiveProjectPath || undefined}>
        <IntelligentModeContextBridge>
          {children}
        </IntelligentModeContextBridge>
      </GitContextProvider>
    </ProjectContextProvider>
  );
};

/**
 * Main integration component that provides the mode system to Claudia
 */
export const IntelligentModeIntegration: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ModeProvider>
      <IntelligentModeBridge>
        {children}
      </IntelligentModeBridge>
    </ModeProvider>
  );
};

// Export hook for components to access mode context
export { useModeContext } from '@/contexts/ModeContext';
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Building, 
  Bug, 
  Search, 
  Rocket, 
  Beaker, 
  GraduationCap,
  FileText,
  Code,
  Database,
  Terminal,
  Settings,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { useModeContext } from './IntelligentModeIntegration';
import { useProjectContext } from '@/contexts/ProjectContextProvider';
import { useGitContext } from '@/contexts/GitContextProvider';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ModeSpecificPanelsProps {
  className?: string;
}

/**
 * ArchitectModePanel - Tools and insights for architecture mode
 */
const ArchitectModePanel: React.FC = () => {
  const projectContext = useProjectContext();
  const gitContext = useGitContext();

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="architect-panel cds--spacing-05"
    >
      <Card className="cds-mb-05">
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <Building className="w-4 h-4" />
            System Overview
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-04">
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Project Type</span>
            <Badge variant="secondary" className="text-xs">
              {projectContext.projectContext?.type || 'Unknown'}
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">File Count</span>
            <span className="text-xs font-medium">
              {projectContext.projectContext?.structure?.fileCount || 0}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Has Tests</span>
            <div className="flex items-center gap-1">
              {projectContext.projectContext?.structure?.hasTests ? (
                <CheckCircle className="w-3 h-3 text-green-500" />
              ) : (
                <AlertTriangle className="w-3 h-3 text-yellow-500" />
              )}
              <span className="text-xs">
                {projectContext.projectContext?.structure?.hasTests ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <Code className="w-4 h-4" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-03">
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <FileText className="w-3 h-3 cds-mr-02" />
            Generate Architecture Docs
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <Database className="w-3 h-3 cds-mr-02" />
            Analyze Dependencies
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01">
            <Settings className="w-3 h-3 cds-mr-02" />
            Review Structure
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

/**
 * DebugModePanel - Tools and insights for debug mode
 */
const DebugModePanel: React.FC = () => {
  const gitContext = useGitContext();

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="debug-panel cds--spacing-05"
    >
      <Card className="cds-mb-05">
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <Bug className="w-4 h-4" />
            Debug Status
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-04">
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Git Branch</span>
            <Badge variant="outline" className="text-xs">
              {gitContext.gitStatus?.branch || 'Unknown'}
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Modified Files</span>
            <span className="text-xs font-medium">
              {gitContext.gitStatus?.modified?.length || 0}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Status</span>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs">Ready</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <Terminal className="w-4 h-4" />
            Debug Tools
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-03">
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <Bug className="w-3 h-3 cds-mr-02" />
            Run Diagnostics
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <Search className="w-3 h-3 cds-mr-02" />
            Search Logs
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01">
            <Terminal className="w-3 h-3 cds-mr-02" />
            Open Console
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

/**
 * ReviewModePanel - Tools for code review and analysis
 */
const ReviewModePanel: React.FC = () => {
  const projectContext = useProjectContext();

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="review-panel cds--spacing-05"
    >
      <Card className="cds-mb-05">
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <Search className="w-4 h-4" />
            Review Checklist
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-04">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-3 h-3 text-green-500" />
            <span className="text-xs">Code Style</span>
          </div>
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-3 h-3 text-yellow-500" />
            <span className="text-xs">Test Coverage</span>
          </div>
          <div className="flex items-center gap-2">
            <Info className="w-3 h-3 text-blue-500" />
            <span className="text-xs">Documentation</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <Code className="w-4 h-4" />
            Review Actions
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-03">
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <Search className="w-3 h-3 cds-mr-02" />
            Analyze Code Quality
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <CheckCircle className="w-3 h-3 cds-mr-02" />
            Run Tests
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01">
            <FileText className="w-3 h-3 cds-mr-02" />
            Generate Report
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

/**
 * DeployModePanel - Deployment tools and status
 */
const DeployModePanel: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="deploy-panel cds--spacing-05"
    >
      <Card className="cds-mb-05">
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <Rocket className="w-4 h-4" />
            Deployment Status
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-04">
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Environment</span>
            <Badge variant="secondary" className="text-xs">
              Development
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Last Deploy</span>
            <span className="text-xs font-medium">2 hours ago</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Status</span>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs">Healthy</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <Settings className="w-4 h-4" />
            Deploy Actions
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-03">
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <Rocket className="w-3 h-3 cds-mr-02" />
            Deploy to Staging
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <CheckCircle className="w-3 h-3 cds-mr-02" />
            Run Tests
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01">
            <Settings className="w-3 h-3 cds-mr-02" />
            Configure Pipeline
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

/**
 * ExperimentModePanel - Tools for experimentation
 */
const ExperimentModePanel: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="experiment-panel cds--spacing-05"
    >
      <Card className="cds-mb-05">
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <Beaker className="w-4 h-4" />
            Active Experiments
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-04">
          <div className="text-center text-xs text-muted-foreground">
            No active experiments
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <Code className="w-4 h-4" />
            Experiment Tools
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-03">
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <Beaker className="w-3 h-3 cds-mr-02" />
            Create Experiment
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <Search className="w-3 h-3 cds-mr-02" />
            Analyze Results
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01">
            <Settings className="w-3 h-3 cds-mr-02" />
            Configure Tests
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

/**
 * LearnModePanel - Educational tools and resources
 */
const LearnModePanel: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="learn-panel cds--spacing-05"
    >
      <Card className="cds-mb-05">
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <GraduationCap className="w-4 h-4" />
            Learning Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-04">
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Topics Covered</span>
            <span className="text-xs font-medium">3/10</span>
          </div>
          <div className="w-full bg-muted rounded-full h-1.5">
            <div className="bg-blue-500 h-1.5 rounded-full" style={{ width: '30%' }}></div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="cds-pb-03">
          <CardTitle className="flex items-center cds-spacing-03 cds-type--body-01">
            <FileText className="w-4 h-4" />
            Learning Tools
          </CardTitle>
        </CardHeader>
        <CardContent className="cds-spacing-03">
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <GraduationCap className="w-3 h-3 cds-mr-02" />
            Start Tutorial
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01 cds-mb-02">
            <FileText className="w-3 h-3 cds-mr-02" />
            Browse Docs
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start cds-type--body-compact-01">
            <Code className="w-3 h-3 cds-mr-02" />
            Code Examples
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

/**
 * Main ModeSpecificPanels component that renders the appropriate panel
 * based on the current mode
 */
export const ModeSpecificPanels: React.FC<ModeSpecificPanelsProps> = ({ className }) => {
  const modeContext = useModeContext();
  const currentMode = modeContext.currentMode;

  const renderModePanel = () => {
    switch (currentMode.id) {
      case 'architect':
        return <ArchitectModePanel />;
      case 'debug':
        return <DebugModePanel />;
      case 'review':
        return <ReviewModePanel />;
      case 'deploy':
        return <DeployModePanel />;
      case 'experiment':
        return <ExperimentModePanel />;
      case 'learn':
        return <LearnModePanel />;
      default:
        return null;
    }
  };

  return (
    <div className={cn('mode-specific-panels', className)}>
      <AnimatePresence mode="wait">
        {renderModePanel()}
      </AnimatePresence>
    </div>
  );
};
import React, { useMemo, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Pin, 
  Maximize2, 
  Minimize2, 
  ChevronDown, 
  ChevronUp,
} from 'lucide-react';
import { useModeContext } from '@/contexts/ModeContext';
import { useProjectContext } from '@/contexts/ProjectContextProvider';
import { useGitContext } from '@/contexts/GitContextProvider';
import { useContextWidgetLayout } from '@/hooks/useAdaptiveLayout';
import { ContextRule } from '@/intelligent-mode-system/types/mode.types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export interface ContextWidget {
  id: string;
  title: string;
  component: React.ComponentType<any>;
  priority: number;
  conditions: ContextRule[];
  position: 'sidebar' | 'overlay' | 'inline' | 'floating';
  responsiveBreakpoints: {
    mobile: 'hidden' | 'bottom-sheet' | 'modal';
    tablet: 'sidebar' | 'floating' | 'collapsible';
    desktop: 'sidebar' | 'floating' | 'inline';
  };
  category: 'context' | 'tools' | 'suggestions' | 'status';
  collapsible?: boolean;
  dismissible?: boolean;
  pinnable?: boolean;
}

interface ContextWidgetContainerProps {
  widget: ContextWidget;
  children: React.ReactNode;
  className?: string;
  onDismiss?: (widgetId: string) => void;
  onPin?: (widgetId: string, pinned: boolean) => void;
  onCollapse?: (widgetId: string, collapsed: boolean) => void;
}

const ContextWidgetContainer: React.FC<ContextWidgetContainerProps> = ({
  widget,
  children,
  className,
  onDismiss,
  onPin,
  onCollapse
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isPinned, setIsPinned] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);

  const handleCollapse = useCallback(() => {
    const newCollapsed = !isCollapsed;
    setIsCollapsed(newCollapsed);
    onCollapse?.(widget.id, newCollapsed);
  }, [isCollapsed, widget.id, onCollapse]);

  const handlePin = useCallback(() => {
    const newPinned = !isPinned;
    setIsPinned(newPinned);
    onPin?.(widget.id, newPinned);
  }, [isPinned, widget.id, onPin]);

  const handleDismiss = useCallback(() => {
    onDismiss?.(widget.id);
  }, [widget.id, onDismiss]);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'context': return 'bg-blue-50 border-blue-200';
      case 'tools': return 'bg-green-50 border-green-200';
      case 'suggestions': return 'bg-yellow-50 border-yellow-200';
      case 'status': return 'bg-gray-50 border-gray-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.95, y: 20 }}
      animate={{ 
        opacity: 1, 
        scale: isMaximized ? 1.02 : 1, 
        y: 0,
        zIndex: isPinned ? 10 : 1
      }}
      exit={{ opacity: 0, scale: 0.95, y: -20 }}
      transition={{ duration: 0.2, ease: 'easeOut' }}
      className={cn(
        'context-widget-container',
        getCategoryColor(widget.category),
        isMaximized && 'fixed inset-4 z-50',
        isPinned && 'ring-2 ring-primary/20',
        className
      )}
    >
      <Card className="h-full border-0 shadow-sm">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="text-sm font-medium">{widget.title}</h3>
              <Badge variant="outline" className="text-xs">
                {widget.category}
              </Badge>
            </div>
            
            <div className="flex items-center gap-1">
              {widget.collapsible && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={handleCollapse}
                >
                  {isCollapsed ? (
                    <ChevronDown className="h-3 w-3" />
                  ) : (
                    <ChevronUp className="h-3 w-3" />
                  )}
                </Button>
              )}
              
              {widget.pinnable && (
                <Button
                  size="sm"
                  variant="ghost"
                  className={cn(
                    "h-6 w-6 p-0",
                    isPinned && "text-primary"
                  )}
                  onClick={handlePin}
                >
                  <Pin className="h-3 w-3" />
                </Button>
              )}
              
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0"
                onClick={() => setIsMaximized(!isMaximized)}
              >
                {isMaximized ? (
                  <Minimize2 className="h-3 w-3" />
                ) : (
                  <Maximize2 className="h-3 w-3" />
                )}
              </Button>
              
              {widget.dismissible && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                  onClick={handleDismiss}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <CardContent className="pt-0">
                {children}
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </motion.div>
  );
};

interface ContextWidgetGridProps {
  widgets: ContextWidget[];
  className?: string;
}

export const ContextWidgetGrid: React.FC<ContextWidgetGridProps> = ({
  widgets,
  className
}) => {
  const { currentMode } = useModeContext();
  const { projectContext } = useProjectContext();
  const { gitStatus } = useGitContext();
  const widgetLayout = useContextWidgetLayout();
  const [dismissedWidgets, setDismissedWidgets] = useState<Set<string>>(new Set());
  const [pinnedWidgets, setPinnedWidgets] = useState<Set<string>>(new Set());
  const [collapsedWidgets, setCollapsedWidgets] = useState<Set<string>>(new Set());

  // Filter and sort widgets based on relevance and priority
  const relevantWidgets = useMemo(() => {
    return widgets
      .filter(widget => !dismissedWidgets.has(widget.id))
      .filter(widget => {
        // Check if widget conditions are met
        return widget.conditions.every(condition => {
          // Implement condition checking logic based on current context
          return condition.condition.evaluate({
            fileContext: {
              path: '',
              type: '',
              hasErrors: false,
              hasWarnings: false,
              lastModified: Date.now()
            },
            projectContext: projectContext || {
              type: 'unknown',
              rootPath: '',
              dependencies: {},
              structure: {
                directories: [],
                fileCount: 0,
                totalSize: 0,
                depth: 0,
                hasTests: false,
                hasDocs: false
              },
              configuration: {}
            },
            userContext: {
              recentActions: [],
              preferences: {
                theme: 'light',
                shortcuts: {},
                autoTransition: false,
                suggestionLevel: 'standard'
              },
              patterns: [],
              sessionDuration: 0,
              lastActivity: Date.now()
            },
            environmentContext: {
               gitStatus: gitStatus || undefined,
               runningProcesses: [],
               systemResources: {
                 cpuUsage: 0,
                 memoryUsage: 0,
                 diskUsage: 0
               },
               openFiles: [],
               activeTerminals: 0
             },
            timestamp: Date.now()
          });
        });
      })
      .sort((a, b) => {
        // Pinned widgets first
        if (pinnedWidgets.has(a.id) && !pinnedWidgets.has(b.id)) return -1;
        if (!pinnedWidgets.has(a.id) && pinnedWidgets.has(b.id)) return 1;
        
        // Then by priority
        return b.priority - a.priority;
      });
  }, [widgets, dismissedWidgets, currentMode, projectContext, gitStatus, pinnedWidgets]);

  const handleDismiss = useCallback((widgetId: string) => {
    setDismissedWidgets(prev => new Set([...prev, widgetId]));
  }, []);

  const handlePin = useCallback((widgetId: string, pinned: boolean) => {
    setPinnedWidgets(prev => {
      const newSet = new Set(prev);
      if (pinned) {
        newSet.add(widgetId);
      } else {
        newSet.delete(widgetId);
      }
      return newSet;
    });
  }, []);

  const handleCollapse = useCallback((widgetId: string, collapsed: boolean) => {
    setCollapsedWidgets(prev => {
      const newSet = new Set(prev);
      if (collapsed) {
        newSet.add(widgetId);
      } else {
        newSet.delete(widgetId);
      }
      return newSet;
    });
  }, []);

  if (relevantWidgets.length === 0) {
    return null;
  }

  return (
    <div className={cn(
      'context-widget-grid',
      widgetLayout.containerClass,
      className
    )}>
      <AnimatePresence mode="popLayout">
        {relevantWidgets.map((widget) => {
          const WidgetComponent = widget.component;
          
          return (
            <ContextWidgetContainer
              key={widget.id}
              widget={widget}
              onDismiss={handleDismiss}
              onPin={handlePin}
              onCollapse={handleCollapse}
            >
              <WidgetComponent
                mode={currentMode}
                projectContext={projectContext}
                gitStatus={gitStatus}
              />
            </ContextWidgetContainer>
          );
        })}
      </AnimatePresence>
    </div>
  );
};

// Smart widget positioning component
interface SmartWidgetPositionerProps {
  children: React.ReactNode;
  position: 'sidebar' | 'floating' | 'inline' | 'bottom-sheet';
  className?: string;
}

export const SmartWidgetPositioner: React.FC<SmartWidgetPositionerProps> = ({
  children,
  position,
  className
}) => {
  const getPositionClasses = () => {
    switch (position) {
      case 'floating':
        return 'fixed top-4 right-4 w-80 max-h-96 z-20 overflow-y-auto';
      case 'bottom-sheet':
        return 'fixed bottom-0 left-0 right-0 max-h-80 z-30 overflow-y-auto';
      case 'inline':
        return 'relative w-full';
      case 'sidebar':
      default:
        return 'relative w-full h-full overflow-y-auto';
    }
  };

  return (
    <div className={cn(
      'smart-widget-positioner',
      getPositionClasses(),
      className
    )}>
      {children}
    </div>
  );
};

export default ContextWidgetGrid;
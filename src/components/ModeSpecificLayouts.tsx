import React, { useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Code2, 
  FileText, 
  GitBranch, 
  Terminal, 
  Search, 
  Bug, 
  Eye, 
  Rocket,
  Layers,
  Settings
} from 'lucide-react';
import { useModeContext } from '@/contexts/ModeContext';
import { useProjectContext } from '@/contexts/ProjectContextProvider';
import { useGitContext } from '@/contexts/GitContextProvider';
import { useAdaptiveLayout } from '@/hooks/useAdaptiveLayout';
import { ContextWidgetGrid } from './EnhancedContextWidgets';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import type { ContextWidget } from './EnhancedContextWidgets';
import type { Mode } from '@/intelligent-mode-system/types/mode.types';

// Mode-specific widget components
const ArchitectModeWidget: React.FC<{ mode: Mode; projectContext: any; gitStatus: any }> = ({
  projectContext
}) => {
  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Layers className="h-4 w-4 text-blue-500" />
        <span className="text-sm font-medium">Architecture Overview</span>
      </div>
      
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div className="p-2 bg-blue-50 rounded border">
          <div className="font-medium">Project Type</div>
          <div className="text-muted-foreground">{projectContext?.type || 'Unknown'}</div>
        </div>
        <div className="p-2 bg-green-50 rounded border">
          <div className="font-medium">Dependencies</div>
          <div className="text-muted-foreground">
            {Object.keys(projectContext?.dependencies || {}).length} packages
          </div>
        </div>
      </div>
      
      <div className="space-y-1">
        <div className="text-xs font-medium">Suggested Actions</div>
        <div className="space-y-1">
          <Button size="sm" variant="outline" className="w-full justify-start h-7 text-xs">
            <FileText className="h-3 w-3 mr-1" />
            Review Architecture
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start h-7 text-xs">
            <Code2 className="h-3 w-3 mr-1" />
            Generate Docs
          </Button>
        </div>
      </div>
    </div>
  );
};

const DebugModeWidget: React.FC<{ mode: Mode; projectContext: any; gitStatus: any }> = () => {
  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Bug className="h-4 w-4 text-red-500" />
        <span className="text-sm font-medium">Debug Assistant</span>
      </div>
      
      <div className="space-y-2">
        <div className="p-2 bg-red-50 rounded border">
          <div className="text-xs font-medium text-red-700">Recent Errors</div>
          <div className="text-xs text-red-600 mt-1">
            No recent errors detected
          </div>
        </div>
        
        <div className="p-2 bg-yellow-50 rounded border">
          <div className="text-xs font-medium text-yellow-700">Performance</div>
          <div className="text-xs text-yellow-600 mt-1">
            All systems normal
          </div>
        </div>
      </div>
      
      <div className="space-y-1">
        <div className="text-xs font-medium">Debug Tools</div>
        <div className="space-y-1">
          <Button size="sm" variant="outline" className="w-full justify-start h-7 text-xs">
            <Terminal className="h-3 w-3 mr-1" />
            Open Console
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start h-7 text-xs">
            <Search className="h-3 w-3 mr-1" />
            Search Logs
          </Button>
        </div>
      </div>
    </div>
  );
};

const ReviewModeWidget: React.FC<{ mode: Mode; projectContext: any; gitStatus: any }> = ({
  gitStatus
}) => {
  const changedFiles = gitStatus?.modified?.length || 0;
  const stagedFiles = gitStatus?.staged?.length || 0;
  
  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Eye className="h-4 w-4 text-purple-500" />
        <span className="text-sm font-medium">Code Review</span>
      </div>
      
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div className="p-2 bg-purple-50 rounded border">
          <div className="font-medium">Modified</div>
          <div className="text-muted-foreground">{changedFiles} files</div>
        </div>
        <div className="p-2 bg-green-50 rounded border">
          <div className="font-medium">Staged</div>
          <div className="text-muted-foreground">{stagedFiles} files</div>
        </div>
      </div>
      
      <div className="space-y-1">
        <div className="text-xs font-medium">Review Actions</div>
        <div className="space-y-1">
          <Button size="sm" variant="outline" className="w-full justify-start h-7 text-xs">
            <GitBranch className="h-3 w-3 mr-1" />
            View Changes
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start h-7 text-xs">
            <Eye className="h-3 w-3 mr-1" />
            Start Review
          </Button>
        </div>
      </div>
    </div>
  );
};

const DeployModeWidget: React.FC<{ mode: Mode; projectContext: any; gitStatus: any }> = () => {
  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Rocket className="h-4 w-4 text-green-500" />
        <span className="text-sm font-medium">Deployment</span>
      </div>
      
      <div className="space-y-2">
        <div className="p-2 bg-green-50 rounded border">
          <div className="text-xs font-medium text-green-700">Status</div>
          <div className="text-xs text-green-600 mt-1">
            Ready to deploy
          </div>
        </div>
        
        <div className="p-2 bg-blue-50 rounded border">
          <div className="text-xs font-medium text-blue-700">Environment</div>
          <div className="text-xs text-blue-600 mt-1">
            Production
          </div>
        </div>
      </div>
      
      <div className="space-y-1">
        <div className="text-xs font-medium">Deploy Actions</div>
        <div className="space-y-1">
          <Button size="sm" variant="outline" className="w-full justify-start h-7 text-xs">
            <Rocket className="h-3 w-3 mr-1" />
            Deploy Now
          </Button>
          <Button size="sm" variant="outline" className="w-full justify-start h-7 text-xs">
            <Settings className="h-3 w-3 mr-1" />
            Configure
          </Button>
        </div>
      </div>
    </div>
  );
};

// Mode-specific widget configurations
const getModeSpecificWidgets = (mode: Mode): ContextWidget[] => {
  const baseWidgets: ContextWidget[] = [];
  
  switch (mode.id) {
    case 'architect':
      baseWidgets.push({
        id: 'architect-overview',
        title: 'Architecture Overview',
        component: ArchitectModeWidget,
        priority: 10,
        conditions: [{
          id: 'architect-mode',
          type: 'state',
          condition: {
            evaluate: (context) => context.environmentContext.gitStatus?.branch !== undefined,
            description: 'Show when in architect mode'
          },
          weight: 1,
          confidence: 1
        }],
        position: 'sidebar',
        responsiveBreakpoints: {
          mobile: 'bottom-sheet',
          tablet: 'sidebar',
          desktop: 'sidebar'
        },
        category: 'context',
        collapsible: true,
        dismissible: false,
        pinnable: true
      });
      break;
      
    case 'debug':
      baseWidgets.push({
        id: 'debug-assistant',
        title: 'Debug Assistant',
        component: DebugModeWidget,
        priority: 10,
        conditions: [{
          id: 'debug-mode',
          type: 'state',
          condition: {
            evaluate: () => true,
            description: 'Show when in debug mode'
          },
          weight: 1,
          confidence: 1
        }],
        position: 'sidebar',
        responsiveBreakpoints: {
          mobile: 'bottom-sheet',
          tablet: 'sidebar',
          desktop: 'sidebar'
        },
        category: 'tools',
        collapsible: true,
        dismissible: false,
        pinnable: true
      });
      break;
      
    case 'review':
      baseWidgets.push({
        id: 'review-assistant',
        title: 'Review Assistant',
        component: ReviewModeWidget,
        priority: 10,
        conditions: [{
          id: 'review-mode',
          type: 'state',
          condition: {
            evaluate: (context) => {
              const gitStatus = context.environmentContext.gitStatus;
              return Boolean(gitStatus && (gitStatus.modified.length > 0 || gitStatus.staged.length > 0));
            },
            description: 'Show when there are changes to review'
          },
          weight: 1,
          confidence: 1
        }],
        position: 'sidebar',
        responsiveBreakpoints: {
          mobile: 'bottom-sheet',
          tablet: 'sidebar',
          desktop: 'sidebar'
        },
        category: 'context',
        collapsible: true,
        dismissible: false,
        pinnable: true
      });
      break;
      
    case 'deploy':
      baseWidgets.push({
        id: 'deploy-assistant',
        title: 'Deploy Assistant',
        component: DeployModeWidget,
        priority: 10,
        conditions: [{
          id: 'deploy-mode',
          type: 'state',
          condition: {
            evaluate: () => true,
            description: 'Show when in deploy mode'
          },
          weight: 1,
          confidence: 1
        }],
        position: 'sidebar',
        responsiveBreakpoints: {
          mobile: 'bottom-sheet',
          tablet: 'sidebar',
          desktop: 'sidebar'
        },
        category: 'tools',
        collapsible: true,
        dismissible: false,
        pinnable: true
      });
      break;
  }
  
  return baseWidgets;
};

// Main layout component for mode-specific layouts
interface ModeSpecificLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const ModeSpecificLayout: React.FC<ModeSpecificLayoutProps> = ({
  children,
  className
}) => {
  const { currentMode } = useModeContext();
  const { projectContext } = useProjectContext();
  const { gitStatus } = useGitContext();
  const layout = useAdaptiveLayout();
  
  const modeWidgets = useMemo(() => {
    return getModeSpecificWidgets(currentMode);
  }, [currentMode]);
  
  const layoutClasses = useMemo(() => {
    return 'enhanced-grid-layout';
  }, []);
  
  return (
    <div className={cn(
      'mode-specific-layout',
      'h-full w-full',
      layoutClasses,
      className
    )}>
      {/* Header Area */}
      <div className="layout-header">
        <Card className="border-0 shadow-none bg-transparent">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: currentMode.color }}
                />
                <CardTitle className="text-sm font-medium">
                  {currentMode.name} Mode
                </CardTitle>
                <Badge variant="outline" className="text-xs">
                  {currentMode.type}
                </Badge>
              </div>
              
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                {projectContext && (
                  <Badge variant="secondary" className="text-xs">
                    {projectContext.type}
                  </Badge>
                )}
                {gitStatus && (
                  <Badge variant="outline" className="text-xs">
                    {gitStatus.branch}
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>
      
      {/* Sidebar Area */}
      <div className="layout-sidebar">
        <ContextWidgetGrid 
          widgets={modeWidgets}
          className="h-full overflow-y-auto"
        />
      </div>
      
      {/* Content Area */}
      <div className="layout-content">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentMode.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
            className="h-full"
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </div>
      
      {/* Preview Area (if enabled) */}
      {layout.previewMode !== 'modal' && (
        <div className="layout-preview">
          <Card className="h-full">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                Preview content will appear here
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Input Area */}
      <div className="layout-input">
        {/* Input components will be rendered here */}
      </div>
    </div>
  );
};

export default ModeSpecificLayout;
export { getModeSpecificWidgets };
import React, { useMemo, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAdaptiveLayout, useLayoutClasses, useGridStyles } from '@/hooks/useAdaptiveLayout';
import { useModeContext } from '@/contexts/ModeContext';
import { useIsMobile, useIsTablet, useIsDesktop } from '@/intelligent-mode-system/utils/responsive';
import { cn } from '@/lib/utils';

// Container query hook for advanced responsive behavior
const useContainerQuery = (containerRef: React.RefObject<HTMLElement>) => {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  
  useEffect(() => {
    if (!containerRef.current) return;
    
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setContainerSize({ width, height });
      }
    });
    
    resizeObserver.observe(containerRef.current);
    
    return () => {
      resizeObserver.disconnect();
    };
  }, [containerRef]);
  
  return {
    containerSize,
    isNarrow: containerSize.width < 768,
    isMedium: containerSize.width >= 768 && containerSize.width < 1024,
    isWide: containerSize.width >= 1024,
    isVeryWide: containerSize.width >= 1440,
    aspectRatio: containerSize.height > 0 ? containerSize.width / containerSize.height : 0
  };
};

// Adaptive grid component with container queries
interface AdaptiveGridProps {
  children: React.ReactNode;
  className?: string;
  areas?: string[];
  minColumnWidth?: string;
  maxColumns?: number;
  gap?: string;
  adaptToContent?: boolean;
}

export const AdaptiveGrid: React.FC<AdaptiveGridProps> = ({
  children,
  className,
  areas = [],
  minColumnWidth = '300px',
  maxColumns = 3,
  gap = '1rem',
  adaptToContent = true
}) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const { containerSize, isNarrow, isMedium, isWide } = useContainerQuery(containerRef);
  
  const gridStyles = useMemo(() => {
    let columns = 1;
    
    if (isWide) {
      columns = Math.min(maxColumns, 3);
    } else if (isMedium) {
      columns = Math.min(maxColumns, 2);
    } else {
      columns = 1;
    }
    
    const styles: React.CSSProperties = {
      display: 'grid',
      gap,
      gridTemplateColumns: adaptToContent 
        ? `repeat(auto-fit, minmax(${minColumnWidth}, 1fr))`
        : `repeat(${columns}, 1fr)`,
      containerType: 'inline-size' as any,
      width: '100%',
      height: '100%'
    };
    
    // Apply grid areas if provided
    if (areas.length > 0) {
      styles.gridTemplateAreas = areas.map(area => `"${area}"`).join(' ');
    }
    
    return styles;
  }, [containerSize, minColumnWidth, maxColumns, gap, adaptToContent, areas, isNarrow, isMedium, isWide]);
  
  return (
    <div
      ref={containerRef}
      className={cn('adaptive-grid', className)}
      style={gridStyles}
    >
      {children}
    </div>
  );
};

// Responsive layout container with intelligent switching
interface ResponsiveLayoutContainerProps {
  children: React.ReactNode;
  className?: string;
  enableTransitions?: boolean;
  optimizeForMode?: boolean;
}

export const ResponsiveLayoutContainer: React.FC<ResponsiveLayoutContainerProps> = ({
  children,
  className,
  enableTransitions = true,
  optimizeForMode = true
}) => {
  const { currentMode } = useModeContext();
  const layout = useAdaptiveLayout();
  const layoutClasses = useLayoutClasses();
  const gridStyles = useGridStyles();
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isDesktop = useIsDesktop();
  
  const containerRef = React.useRef<HTMLDivElement>(null);
  const { containerSize, isNarrow, isMedium, isWide, aspectRatio } = useContainerQuery(containerRef);
  
  // Determine optimal layout based on container size and mode
  const adaptiveLayout = useMemo(() => {
    let adaptedLayout = { ...layout };
    
    // Override layout based on container constraints
    if (isNarrow) {
      adaptedLayout = {
        ...adaptedLayout,
        layout: 'stack',
        sidebarPosition: 'bottom',
        contextWidgetPosition: 'bottom-sheet',
        gridColumns: 1
      };
    } else if (isMedium) {
      adaptedLayout = {
        ...adaptedLayout,
        layout: 'hybrid',
        sidebarPosition: 'collapsible',
        gridColumns: 2
      };
    }
    
    // Mode-specific optimizations
    if (optimizeForMode) {
      switch (currentMode.id) {
        case 'debug':
          if (aspectRatio > 1.5) {
            adaptedLayout.layout = 'split';
            adaptedLayout.previewMode = 'side-by-side';
          }
          break;
        case 'review':
          if (isWide) {
            adaptedLayout.layout = 'three-column';
            adaptedLayout.previewMode = 'side-by-side';
          }
          break;
        case 'architect':
          if (isWide) {
            adaptedLayout.sidebarPosition = 'left';
            adaptedLayout.contextWidgetPosition = 'sidebar';
          }
          break;
      }
    }
    
    return adaptedLayout;
  }, [layout, isNarrow, isMedium, isWide, aspectRatio, currentMode.id, optimizeForMode]);
  
  const containerClasses = useMemo(() => {
    return cn(
      'responsive-layout-container',
      layoutClasses,
      adaptiveLayout.containerClass,
      {
        'layout-narrow': isNarrow,
        'layout-medium': isMedium,
        'layout-wide': isWide,
        'layout-mobile': isMobile,
        'layout-tablet': isTablet,
        'layout-desktop': isDesktop,
        'layout-transitions': enableTransitions,
        [`layout-${adaptiveLayout.layout}`]: true,
        [`sidebar-${adaptiveLayout.sidebarPosition}`]: true,
        [`preview-${adaptiveLayout.previewMode}`]: true,
        [`density-${adaptiveLayout.density}`]: true
      },
      className
    );
  }, [layoutClasses, adaptiveLayout, isNarrow, isMedium, isWide, isMobile, isTablet, isDesktop, enableTransitions, className]);
  
  const containerStyles = useMemo(() => {
    return {
      ...gridStyles,
      '--sidebar-width': adaptiveLayout.sidebarWidth,
      '--preview-width': adaptiveLayout.previewWidth,
      '--primary-content-width': adaptiveLayout.primaryContentWidth,
      '--grid-gap': adaptiveLayout.gridGap,
      '--container-width': `${containerSize.width}px`,
      '--container-height': `${containerSize.height}px`,
      '--aspect-ratio': aspectRatio.toString()
    } as React.CSSProperties;
  }, [gridStyles, adaptiveLayout, containerSize, aspectRatio]);
  
  return (
    <div
      ref={containerRef}
      className={containerClasses}
      style={containerStyles}
      data-layout={adaptiveLayout.layout}
      data-mode={currentMode.id}
      data-device={isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop'}
    >
      {enableTransitions ? (
        <AnimatePresence mode="wait">
          <motion.div
            key={`${currentMode.id}-${adaptiveLayout.layout}`}
            initial={{ opacity: 0, scale: 0.98 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 1.02 }}
            transition={{ 
              duration: 0.3, 
              ease: [0.4, 0.0, 0.2, 1],
              layout: { duration: 0.2 }
            }}
            className="h-full w-full"
          >
            {children}
          </motion.div>
        </AnimatePresence>
      ) : (
        children
      )}
    </div>
  );
};

// Breakpoint-aware component wrapper
interface BreakpointComponentProps {
  mobile?: React.ReactNode;
  tablet?: React.ReactNode;
  desktop?: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

export const BreakpointComponent: React.FC<BreakpointComponentProps> = ({
  mobile,
  tablet,
  desktop,
  fallback,
  className
}) => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isDesktop = useIsDesktop();
  
  const content = useMemo(() => {
    if (isMobile && mobile) return mobile;
    if (isTablet && tablet) return tablet;
    if (isDesktop && desktop) return desktop;
    return fallback || null;
  }, [isMobile, isTablet, isDesktop, mobile, tablet, desktop, fallback]);
  
  if (!content) return null;
  
  return (
    <div className={cn('breakpoint-component', className)}>
      {content}
    </div>
  );
};

// Performance-optimized virtual grid for large datasets
interface VirtualGridProps {
  items: any[];
  renderItem: (item: any, index: number) => React.ReactNode;
  itemHeight: number;
  containerHeight: number;
  columns?: number;
  gap?: number;
  overscan?: number;
  className?: string;
}

export const VirtualGrid: React.FC<VirtualGridProps> = ({
  items,
  renderItem,
  itemHeight,
  containerHeight,
  columns = 1,
  gap = 8,
  overscan = 5,
  className
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = React.useRef<HTMLDivElement>(null);
  
  const rowHeight = itemHeight + gap;
  const totalRows = Math.ceil(items.length / columns);
  const totalHeight = totalRows * rowHeight;
  
  const startRow = Math.max(0, Math.floor(scrollTop / rowHeight) - overscan);
  const endRow = Math.min(
    totalRows - 1,
    Math.floor((scrollTop + containerHeight) / rowHeight) + overscan
  );
  
  const visibleItems = useMemo(() => {
    const visible = [];
    for (let row = startRow; row <= endRow; row++) {
      for (let col = 0; col < columns; col++) {
        const index = row * columns + col;
        if (index < items.length) {
          visible.push({
            item: items[index],
            index,
            row,
            col,
            top: row * rowHeight,
            left: col * (100 / columns)
          });
        }
      }
    }
    return visible;
  }, [items, startRow, endRow, columns, rowHeight]);
  
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  };
  
  return (
    <div
      ref={containerRef}
      className={cn('virtual-grid', className)}
      style={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }}
      onScroll={handleScroll}
    >
      <div
        style={{
          height: totalHeight,
          position: 'relative'
        }}
      >
        {visibleItems.map(({ item, index, top, left }) => (
          <div
            key={index}
            style={{
              position: 'absolute',
              top,
              left: `${left}%`,
              width: `${100 / columns}%`,
              height: itemHeight,
              padding: gap / 2
            }}
          >
            {renderItem(item, index)}
          </div>
        ))}
      </div>
    </div>
  );
};

export {
  useContainerQuery
};
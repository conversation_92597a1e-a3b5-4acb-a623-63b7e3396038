import React from 'react';
import { motion } from 'framer-motion';
import { useModeContext } from './IntelligentModeIntegration';
import { cn } from '@/lib/utils';

interface ModeSpecificLayoutProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * ModeSpecificLayout - Adapts UI layout and styling based on current mode
 */
export const ModeSpecificLayout: React.FC<ModeSpecificLayoutProps> = ({ 
  children, 
  className 
}) => {
  const modeContext = useModeContext();
  const currentMode = modeContext.currentMode;

  // Mode-specific layout configurations
  const getModeLayoutConfig = () => {
    switch (currentMode.id) {
      case 'architect':
        return {
          containerClass: 'architect-mode-layout',
          backgroundColor: currentMode.theme?.background || '#f8fafc',
          borderAccent: currentMode.theme?.primary || '#3B82F6',
          layoutType: 'structured', // Grid-like, organized layout
        };
      case 'debug':
        return {
          containerClass: 'debug-mode-layout',
          backgroundColor: currentMode.theme?.background || '#fef2f2',
          borderAccent: currentMode.theme?.primary || '#EF4444',
          layoutType: 'diagnostic', // Terminal-like, detailed layout
        };
      case 'review':
        return {
          containerClass: 'review-mode-layout',
          backgroundColor: currentMode.theme?.background || '#f0f9ff',
          borderAccent: currentMode.theme?.primary || '#0EA5E9',
          layoutType: 'analytical', // Side-by-side, comparison layout
        };
      case 'deploy':
        return {
          containerClass: 'deploy-mode-layout',
          backgroundColor: currentMode.theme?.background || '#f0fdf4',
          borderAccent: currentMode.theme?.primary || '#22C55E',
          layoutType: 'process', // Step-by-step, pipeline layout
        };
      case 'experiment':
        return {
          containerClass: 'experiment-mode-layout',
          backgroundColor: currentMode.theme?.background || '#fefce8',
          borderAccent: currentMode.theme?.primary || '#EAB308',
          layoutType: 'exploratory', // Flexible, experimental layout
        };
      case 'learn':
        return {
          containerClass: 'learn-mode-layout',
          backgroundColor: currentMode.theme?.background || '#faf5ff',
          borderAccent: currentMode.theme?.primary || '#A855F7',
          layoutType: 'educational', // Tutorial-like, guided layout
        };
      default:
        return {
          containerClass: 'default-mode-layout',
          backgroundColor: '#ffffff',
          borderAccent: '#6B7280',
          layoutType: 'standard',
        };
    }
  };

  const layoutConfig = getModeLayoutConfig();

  return (
    <motion.div
      key={currentMode.id} // Re-animate when mode changes
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className={cn(
        'mode-specific-layout relative',
        layoutConfig.containerClass,
        className
      )}
      style={{
        '--mode-background': layoutConfig.backgroundColor,
        '--mode-border': layoutConfig.borderAccent,
        '--mode-layout-type': layoutConfig.layoutType,
      } as React.CSSProperties}
    >
      {/* Mode indicator header */}
      <div className="mode-header flex items-center justify-between p-2 border-b border-border/50">
        <div className="flex items-center gap-2">
          <span className="text-lg">{currentMode.icon}</span>
          <span className="text-sm font-medium text-foreground/80">
            {currentMode.name}
          </span>
        </div>
        <div 
          className="w-2 h-2 rounded-full"
          style={{ backgroundColor: layoutConfig.borderAccent }}
        />
      </div>

      {/* Mode-specific content area */}
      <div className={cn(
        'mode-content flex-1',
        // Layout-specific classes
        layoutConfig.layoutType === 'structured' && 'grid-layout',
        layoutConfig.layoutType === 'diagnostic' && 'diagnostic-layout',
        layoutConfig.layoutType === 'analytical' && 'analytical-layout',
        layoutConfig.layoutType === 'process' && 'process-layout',
        layoutConfig.layoutType === 'exploratory' && 'exploratory-layout',
        layoutConfig.layoutType === 'educational' && 'educational-layout'
      )}>
        {children}
      </div>

      {/* Mode-specific accent border */}
      <div 
        className="absolute inset-0 pointer-events-none border-2 border-transparent rounded"
        style={{ 
          borderColor: `${layoutConfig.borderAccent}20`,
          boxShadow: `inset 0 0 0 1px ${layoutConfig.borderAccent}10`
        }}
      />
    </motion.div>
  );
};

/**
 * Mode-specific component wrapper for different UI patterns
 */
export const ModeSpecificComponent: React.FC<{
  mode: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ mode, children, fallback }) => {
  const modeContext = useModeContext();
  const isCurrentMode = modeContext.currentMode.id === mode;

  if (!isCurrentMode && fallback) {
    return <>{fallback}</>;
  }

  if (!isCurrentMode) {
    return null;
  }

  return <>{children}</>;
};

/**
 * Mode-aware styling hook
 */
export const useModeSpecificStyles = () => {
  const modeContext = useModeContext();
  const currentMode = modeContext.currentMode;

  return {
    primary: currentMode.theme?.primary || '#6B7280',
    secondary: currentMode.theme?.secondary || '#9CA3AF',
    accent: currentMode.theme?.accent || '#F59E0B',
    background: currentMode.theme?.background || '#FFFFFF',
    surface: currentMode.theme?.surface || '#F9FAFB',
    text: currentMode.theme?.text || '#111827',
    muted: currentMode.theme?.textMuted || '#6B7280',
    
    // Helper classes
    getAccentClasses: () => ({
      border: `border-[${currentMode.theme?.primary}]`,
      text: `text-[${currentMode.theme?.primary}]`,
      bg: `bg-[${currentMode.theme?.primary}]/10`,
    }),
    
    // CSS custom properties
    getCSSProperties: () => ({
      '--mode-primary': currentMode.theme?.primary,
      '--mode-secondary': currentMode.theme?.secondary,
      '--mode-accent': currentMode.theme?.accent,
      '--mode-background': currentMode.theme?.background,
      '--mode-surface': currentMode.theme?.surface,
      '--mode-text': currentMode.theme?.text,
      '--mode-muted': currentMode.theme?.textMuted,
    }),
  };
};
import React, { useState } from 'react';
import { SessionHeader } from './SessionHeader';
import { SessionFooter } from './SessionFooter';
import { useMediaQuery } from '@/hooks/useMediaQuery';

interface SessionLayoutProps {
  children: React.ReactNode;
  onBack: () => void;
  onProjectSettings?: (projectPath: string) => void;
  isPanelOpen: boolean;
  setIsPanelOpen: (open: boolean) => void;
}

export const SessionLayout: React.FC<SessionLayoutProps> = ({ children, onBack, onProjectSettings, isPanelOpen, setIsPanelOpen }) => {
  return (
    <div className="flex flex-col h-full">
      <SessionHeader onBack={onBack} onProjectSettings={onProjectSettings} isPanelOpen={isPanelOpen} setIsPanelOpen={setIsPanelOpen} />
      <main className="flex-1 overflow-y-auto">{children}</main>
      <SessionFooter />
    </div>
  );
};
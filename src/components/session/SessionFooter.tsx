import React from 'react';
import { useSession } from '@/contexts/SessionContext';
import { SmartPromptInputResponsive } from '@/intelligent-mode-system/components/SmartPromptInputResponsive';
import { PromptQueue } from '@/components/claude-code-session/PromptQueue';
import { useModeContext } from '@/components/IntelligentModeIntegration';
import { useProjectContext } from '@/contexts/ProjectContextProvider';
import { useGitContext } from '@/contexts/GitContextProvider';

export const SessionFooter: React.FC = () => {
  const {
    isLoading,
    effectiveSession,
    handleSubmitPrompt,
    queuedPrompts,
    removeQueuedPrompt,
    projectPath,
  } = useSession();
  const modeContext = useModeContext();
  const projectContextHook = useProjectContext();
  const gitContextHook = useGitContext();

  return (
    <div className="bg-background/95 backdrop-blur-sm border-t border-border">
      <div className="max-w-5xl mx-auto p-4">
        <PromptQueue queuedPrompts={queuedPrompts} onRemove={removeQueuedPrompt} />
        <SmartPromptInputResponsive
          currentMode={modeContext.currentMode}
          context={{
            fileContext: {
              path: '',
              type: 'unknown',
              hasErrors: false,
              hasWarnings: false,
              lastModified: Date.now(),
            },
            projectContext: projectContextHook.projectContext || {
              type: 'unknown' as const,
              rootPath: projectPath || '',
              dependencies: {},
              structure: {
                directories: [],
                fileCount: 0,
                totalSize: 0,
                depth: 0,
                hasTests: false,
                hasDocs: false,
              },
              configuration: {},
            },
            userContext: {
              recentActions: [],
              preferences: {
                theme: 'dark',
                shortcuts: {},
                autoTransition: true,
                suggestionLevel: 'standard' as const,
              },
              patterns: [],
              sessionDuration: 0,
              lastActivity: Date.now(),
            },
            environmentContext: {
              gitStatus: gitContextHook.gitStatus || undefined,
              runningProcesses: [],
              systemResources: {
                cpuUsage: 0,
                memoryUsage: 0,
                diskUsage: 0,
              },
              openFiles: [],
              activeTerminals: 0,
            },
            timestamp: Date.now(),
          }}
          projectContext={projectContextHook.projectContext || {
            type: 'unknown' as const,
            rootPath: projectPath || '',
            dependencies: {},
            structure: {
              directories: [],
              fileCount: 0,
              totalSize: 0,
              depth: 0,
              hasTests: false,
              hasDocs: false,
            },
            configuration: {},
          }}
          environmentContext={{
            gitStatus: gitContextHook.gitStatus || undefined,
            runningProcesses: [],
            systemResources: {
              cpuUsage: 0,
              memoryUsage: 0,
              diskUsage: 0,
            },
            openFiles: [],
            activeTerminals: 0,
          }}
          showContextAwareSuggestions={true}
          onSubmit={handleSubmitPrompt}
          disabled={isLoading}
          placeholder={
            !effectiveSession
              ? `Start your ${modeContext.currentMode.name.toLowerCase()} session...`
              : `Continue in ${modeContext.currentMode.name.toLowerCase()} mode...`
          }
        />
      </div>
    </div>
  );
};
import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { useModeContext } from '../IntelligentModeIntegration';
import { cn } from '@/lib/utils';

// Carbon-inspired Grid System Components
interface FlexGridProps {
  children: React.ReactNode;
  className?: string;
  fullWidth?: boolean;
  narrow?: boolean;
  condensed?: boolean;
}

interface RowProps {
  children: React.ReactNode;
  className?: string;
  noGutter?: boolean;
}

interface ColumnProps {
  children: React.ReactNode;
  className?: string;
  lg?: number | { span?: number; offset?: number };
  md?: number | { span?: number; offset?: number };
  sm?: number | { span?: number; offset?: number };
  auto?: boolean;
}

// Grid System Implementation
const FlexGrid: React.FC<FlexGridProps> = ({ 
  children, 
  className, 
  fullWidth = false,
  narrow = false,
  condensed = false 
}) => {
  const gridClasses = cn(
    'cds--grid',
    {
      'cds--grid--full-width': fullWidth,
      'cds--grid--narrow': narrow,
      'cds--grid--condensed': condensed,
    },
    className
  );

  return (
    <div className={gridClasses}>
      {children}
    </div>
  );
};

const Row: React.FC<RowProps> = ({ children, className, noGutter = false }) => {
  const rowClasses = cn(
    'cds--row',
    {
      'cds--row--no-gutter': noGutter,
    },
    className
  );

  return (
    <div className={rowClasses}>
      {children}
    </div>
  );
};

const Column: React.FC<ColumnProps> = ({ children, className, lg, md, sm, auto = false }) => {
  const getColumnClasses = () => {
    const classes = ['cds--col'];
    
    if (auto) {
      classes.push('cds--col--auto');
    }

    // Handle responsive column spans
    const breakpoints = { lg, md, sm };
    Object.entries(breakpoints).forEach(([breakpoint, value]) => {
      if (value) {
        if (typeof value === 'number') {
          classes.push(`cds--col-${breakpoint}-${value}`);
        } else if (typeof value === 'object') {
          if (value.span) {
            classes.push(`cds--col-${breakpoint}-${value.span}`);
          }
          if (value.offset) {
            classes.push(`cds--offset-${breakpoint}-${value.offset}`);
          }
        }
      }
    });

    return classes;
  };

  const columnClasses = cn(getColumnClasses(), className);

  return (
    <div className={columnClasses}>
      {children}
    </div>
  );
};

// Session Container Props
interface SessionContainerProps {
  children: React.ReactNode;
  className?: string;
  showModePanel?: boolean;
  layoutDensity?: 'compact' | 'standard' | 'relaxed';
}

// Mode-aware layout configurations
interface ModeLayoutConfig {
  primaryColumnSpan: {
    lg: number;
    md: number;
    sm: number;
  };
  widgetColumnSpan: {
    lg: number;
    md: number;
    sm: number;
  };
  layoutDensity: 'compact' | 'standard' | 'relaxed';
  backgroundColor: string;
  accentColor: string;
  containerClass: string;
}

/**
 * SessionContainer - Main layout orchestrator for Claude Code sessions
 * 
 * Features:
 * - Carbon Grid System integration
 * - Mode-aware responsive layout
 * - Intelligent widget panel management
 * - Accessibility-first design
 */
export const SessionContainer: React.FC<SessionContainerProps> = ({
  children,
  className,
  showModePanel = true,
  layoutDensity = 'standard'
}) => {
  const modeContext = useModeContext();
  const currentMode = modeContext.currentMode;

  // Mode-specific layout configuration
  const layoutConfig = useMemo((): ModeLayoutConfig => {
    const baseConfig = {
      layoutDensity,
      backgroundColor: currentMode.theme?.background || '#ffffff',
      accentColor: currentMode.theme?.primary || '#0066cc',
    };

    switch (currentMode.id) {
      case 'architect':
        return {
          ...baseConfig,
          primaryColumnSpan: { lg: 10, md: 6, sm: 4 }, // Balanced for diagrams and content
          widgetColumnSpan: { lg: 6, md: 2, sm: 0 }, // Hide on mobile
          containerClass: 'session-container--architect',
        };
      
      case 'debug':
        return {
          ...baseConfig,
          primaryColumnSpan: { lg: 9, md: 5, sm: 4 }, // More space for debugging tools
          widgetColumnSpan: { lg: 7, md: 3, sm: 0 }, // Larger widget area for debug tools
          containerClass: 'session-container--debug',
        };
      
      case 'review':
        return {
          ...baseConfig,
          primaryColumnSpan: { lg: 11, md: 6, sm: 4 }, // More space for code review
          widgetColumnSpan: { lg: 5, md: 2, sm: 0 }, // Smaller widget area for checklist
          containerClass: 'session-container--review',
        };
      
      case 'deploy':
        return {
          ...baseConfig,
          primaryColumnSpan: { lg: 10, md: 6, sm: 4 }, // Balanced for deployment info
          widgetColumnSpan: { lg: 6, md: 2, sm: 0 }, // Space for deployment tools
          containerClass: 'session-container--deploy',
        };
      
      case 'experiment':
        return {
          ...baseConfig,
          primaryColumnSpan: { lg: 9, md: 5, sm: 4 }, // Space for experimentation
          widgetColumnSpan: { lg: 7, md: 3, sm: 0 }, // Larger widget area for experiment tools
          containerClass: 'session-container--experiment',
        };
      
      case 'learn':
        return {
          ...baseConfig,
          primaryColumnSpan: { lg: 11, md: 6, sm: 4 }, // More space for learning content
          widgetColumnSpan: { lg: 5, md: 2, sm: 0 }, // Smaller widget area for progress/tools
          containerClass: 'session-container--learn',
        };
      
      default:
        return {
          ...baseConfig,
          primaryColumnSpan: { lg: 12, md: 8, sm: 4 }, // Full width fallback
          widgetColumnSpan: { lg: 4, md: 0, sm: 0 }, // Small widget area
          containerClass: 'session-container--default',
        };
    }
  }, [currentMode.id, currentMode.theme, layoutDensity]);

  // Container styling based on mode and density
  const containerStyle = useMemo(() => ({
    '--session-bg-color': layoutConfig.backgroundColor,
    '--session-accent-color': layoutConfig.accentColor,
    '--session-density': layoutDensity,
  } as React.CSSProperties), [layoutConfig, layoutDensity]);

  const containerClasses = cn(
    'session-container',
    layoutConfig.containerClass,
    `session-container--${layoutDensity}`,
    'h-full min-h-screen bg-background',
    className
  );

  // Animate layout changes
  const layoutVariants = {
    architect: { 
      backgroundColor: layoutConfig.backgroundColor,
      transition: { duration: 0.3, ease: 'easeInOut' }
    },
    debug: { 
      backgroundColor: layoutConfig.backgroundColor,
      transition: { duration: 0.3, ease: 'easeInOut' }
    },
    review: { 
      backgroundColor: layoutConfig.backgroundColor,
      transition: { duration: 0.3, ease: 'easeInOut' }
    },
    deploy: { 
      backgroundColor: layoutConfig.backgroundColor,
      transition: { duration: 0.3, ease: 'easeInOut' }
    },
    experiment: { 
      backgroundColor: layoutConfig.backgroundColor,
      transition: { duration: 0.3, ease: 'easeInOut' }
    },
    learn: { 
      backgroundColor: layoutConfig.backgroundColor,
      transition: { duration: 0.3, ease: 'easeInOut' }
    },
  };

  return (
    <motion.div
      className={containerClasses}
      style={containerStyle}
      variants={layoutVariants}
      animate={currentMode.id}
      data-mode={currentMode.id}
      data-density={layoutDensity}
      role="main"
      aria-label={`Claude Code Session - ${currentMode.name} Mode`}
    >
      <FlexGrid fullWidth className="h-full">
        <Row className="h-full">
          {/* Primary Content Area */}
          <Column 
            lg={layoutConfig.primaryColumnSpan.lg}
            md={layoutConfig.primaryColumnSpan.md}
            sm={layoutConfig.primaryColumnSpan.sm}
            className="session-container__primary-content h-full"
          >
            {Array.isArray(children) ? children[0] : children}
          </Column>

          {/* Mode-Specific Widget Panel */}
          {showModePanel && (
            <Column 
              lg={layoutConfig.widgetColumnSpan.lg}
              md={layoutConfig.widgetColumnSpan.md}
              sm={layoutConfig.widgetColumnSpan.sm}
              className="session-container__widget-panel h-full border-l border-border/50"
            >
              <div className="h-full overflow-hidden">
                {Array.isArray(children) ? children[1] : null}
              </div>
            </Column>
          )}
        </Row>
      </FlexGrid>
    </motion.div>
  );
};

// Export grid components for use in other parts of the application
export { FlexGrid, Row, Column };

// Export types for external use
export type { SessionContainerProps, ModeLayoutConfig };
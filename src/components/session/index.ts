/**
 * Session Components
 * Modern, responsive session UI components
 */

// Import styles
import './session-container.css';

// Main container component
export { SessionContainer, FlexGrid, Row, Column } from './SessionContainer';
export type { SessionContainerProps, ModeLayoutConfig } from './SessionContainer';

// Message display component
export { MessageFlow } from './MessageFlow';
export type { MessageFlowProps, MessageFlowState } from './MessageFlow';

// Session state management hook
export { useClaudeSession } from './useClaudeSession';
export type { UseClaudeSessionReturn, UseClaudeSessionConfig, SessionState, UIState } from './useClaudeSession';

// Export future components (placeholder for upcoming implementations)
// export { IntelligentPromptZone } from './IntelligentPromptZone';
// export { ModeContextPanel } from './ModeContextPanel';
// export { SessionControls } from './SessionControls';
// export { StatusIndicator } from './StatusIndicator';
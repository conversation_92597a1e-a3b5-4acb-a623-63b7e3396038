import React, { useMemo, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useModeContext } from '../IntelligentModeIntegration';
import { StreamMessage } from '../StreamMessage';
import { cn } from '@/lib/utils';
import type { ClaudeStreamMessage } from '../AgentExecution';

interface MessageFlowProps {
  messages: ClaudeStreamMessage[];
  isLoading?: boolean;
  error?: string | null;
  className?: string;
  onLinkDetected?: (url: string) => void;
  autoScrollToBottom?: boolean;
  estimatedMessageSize?: number;
  overscan?: number;
}

interface MessageFlowState {
  shouldAutoScroll: boolean;
  userHasScrolled: boolean;
}

/**
 * MessageFlow - Virtualized conversation display component
 * 
 * Features:
 * - Efficient virtualization for large message lists
 * - Mode-aware styling and density
 * - Smart auto-scrolling behavior
 * - Accessibility optimizations
 * - Performance monitoring
 */
export const MessageFlow: React.FC<MessageFlowProps> = ({
  messages,
  isLoading = false,
  error = null,
  className,
  onLinkDetected,
  autoScrollToBottom = true,
  estimatedMessageSize = 200,
  overscan = 5,
}) => {
  const modeContext = useModeContext();
  const currentMode = modeContext.currentMode;
  
  // Refs for virtualization and scroll management
  const parentRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();
  const lastMessageCountRef = useRef(messages.length);
  
  // State for scroll management
  const [flowState, setFlowState] = React.useState<MessageFlowState>({
    shouldAutoScroll: autoScrollToBottom,
    userHasScrolled: false,
  });

  // Filter messages for display (remove meta messages without content)
  const displayableMessages = useMemo(() => {
    return messages.filter((message, index) => {
      // Skip meta messages that don't have meaningful content
      if (message.isMeta && !message.leafUuid && !message.summary) {
        return false;
      }

      // Skip user messages that only contain tool results already displayed
      if (message.type === "user" && message.message) {
        if (message.isMeta) return false;

        const msg = message.message;
        if (!msg.content || (Array.isArray(msg.content) && msg.content.length === 0)) {
          return false;
        }

        if (Array.isArray(msg.content)) {
          let hasVisibleContent = false;
          for (const content of msg.content) {
            if (content.type === "text") {
              hasVisibleContent = true;
              break;
            }
            if (content.type === "tool_result") {
              let willBeSkipped = false;
              if (content.tool_use_id) {
                // Look for matching tool_use in previous assistant messages
                for (let i = index - 1; i >= 0; i--) {
                  const prevMsg = messages[i];
                  if (prevMsg.type === 'assistant' && prevMsg.message?.content && Array.isArray(prevMsg.message.content)) {
                    const toolUse = prevMsg.message.content.find((c: any) => 
                      c.type === 'tool_use' && c.id === content.tool_use_id
                    );
                    if (toolUse) {
                      const toolName = toolUse.name?.toLowerCase();
                      const toolsWithWidgets = [
                        'task', 'edit', 'multiedit', 'todowrite', 'ls', 'read', 
                        'glob', 'bash', 'write', 'grep'
                      ];
                      if (toolsWithWidgets.includes(toolName) || toolUse.name?.startsWith('mcp__')) {
                        willBeSkipped = true;
                      }
                      break;
                    }
                  }
                }
              }
              if (!willBeSkipped) {
                hasVisibleContent = true;
                break;
              }
            }
          }
          if (!hasVisibleContent) {
            return false;
          }
        }
      }
      return true;
    });
  }, [messages]);

  // Virtual scroller setup
  const rowVirtualizer = useVirtualizer({
    count: displayableMessages.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => estimatedMessageSize,
    overscan,
    scrollMargin: 40, // Add margin for better UX
  });

  // Mode-aware styling configuration
  const modeConfig = useMemo(() => {
    switch (currentMode.id) {
      case 'architect':
        return {
          containerClass: 'message-flow--architect',
          messageSpacing: 'space-y-6',
          backgroundColor: currentMode.theme?.background || '#f8fafc',
          borderColor: currentMode.theme?.primary || '#3B82F6',
        };
      case 'debug':
        return {
          containerClass: 'message-flow--debug',
          messageSpacing: 'space-y-4',
          backgroundColor: currentMode.theme?.background || '#fef2f2',
          borderColor: currentMode.theme?.primary || '#EF4444',
        };
      case 'review':
        return {
          containerClass: 'message-flow--review',
          messageSpacing: 'space-y-5',
          backgroundColor: currentMode.theme?.background || '#f0f9ff',
          borderColor: currentMode.theme?.primary || '#0EA5E9',
        };
      case 'deploy':
        return {
          containerClass: 'message-flow--deploy',
          messageSpacing: 'space-y-4',
          backgroundColor: currentMode.theme?.background || '#f0fdf4',
          borderColor: currentMode.theme?.primary || '#22C55E',
        };
      case 'experiment':
        return {
          containerClass: 'message-flow--experiment',
          messageSpacing: 'space-y-5',
          backgroundColor: currentMode.theme?.background || '#f5f3ff',
          borderColor: currentMode.theme?.primary || '#A855F7',
        };
      case 'learn':
        return {
          containerClass: 'message-flow--learn',
          messageSpacing: 'space-y-5',
          backgroundColor: currentMode.theme?.background || '#fffbeb',
          borderColor: currentMode.theme?.primary || '#F59E0B',
        };
      default:
        return {
          containerClass: 'message-flow--default',
          messageSpacing: 'space-y-4',
          backgroundColor: '#ffffff',
          borderColor: '#e5e7eb',
        };
    }
  }, [currentMode.id, currentMode.theme]);

  // Auto-scroll logic
  const scrollToBottom = useCallback(() => {
    if (displayableMessages.length > 0 && flowState.shouldAutoScroll) {
      rowVirtualizer.scrollToIndex(displayableMessages.length - 1, { 
        align: 'end', 
        behavior: 'smooth' 
      });
    }
  }, [displayableMessages.length, flowState.shouldAutoScroll, rowVirtualizer]);

  // Handle new messages
  useEffect(() => {
    const messageCountChanged = messages.length !== lastMessageCountRef.current;
    const newMessagesAdded = messages.length > lastMessageCountRef.current;
    
    if (messageCountChanged) {
      lastMessageCountRef.current = messages.length;
      
      if (newMessagesAdded && autoScrollToBottom && !flowState.userHasScrolled) {
        // Small delay to ensure DOM has updated
        requestAnimationFrame(() => {
          scrollToBottom();
        });
      }
    }
  }, [messages.length, autoScrollToBottom, flowState.userHasScrolled, scrollToBottom]);

  // Handle scroll events to detect user scrolling
  const handleScroll = useCallback(() => {
    if (!parentRef.current) return;
    
    const element = parentRef.current;
    const isNearBottom = element.scrollHeight - element.scrollTop - element.clientHeight < 100;
    
    // Clear any existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    // Set timeout to detect when user stops scrolling
    scrollTimeoutRef.current = setTimeout(() => {
      setFlowState(prev => ({
        ...prev,
        userHasScrolled: !isNearBottom,
        shouldAutoScroll: isNearBottom,
      }));
    }, 150);
  }, []);

  // Attach scroll listener
  useEffect(() => {
    const element = parentRef.current;
    if (element) {
      element.addEventListener('scroll', handleScroll, { passive: true });
      return () => {
        element.removeEventListener('scroll', handleScroll);
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, [handleScroll]);

  // Container styling
  const containerClasses = cn(
    'message-flow',
    'flex-1 overflow-y-auto relative',
    'scroll-smooth',
    modeConfig.containerClass,
    className
  );

  const contentStyle = {
    '--message-flow-bg': modeConfig.backgroundColor,
    '--message-flow-border': modeConfig.borderColor,
  } as React.CSSProperties;

  return (
    <div
      ref={parentRef}
      className={containerClasses}
      style={contentStyle}
      role="log"
      aria-label={`Conversation messages in ${currentMode.name} mode`}
      aria-live="polite"
      aria-busy={isLoading}
    >
      <div
        className="relative w-full max-w-5xl mx-auto px-4 pt-8 pb-40"
        style={{
          height: `${Math.max(rowVirtualizer.getTotalSize(), 200)}px`,
          minHeight: '200px',
        }}
      >
        <AnimatePresence mode="popLayout">
          {rowVirtualizer.getVirtualItems().map((virtualItem) => {
            const message = displayableMessages[virtualItem.index];
            return (
              <motion.div
                key={`${virtualItem.key}-${message.type}`}
                data-index={virtualItem.index}
                ref={(el) => el && rowVirtualizer.measureElement(el)}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{
                  duration: 0.3,
                  ease: [0.4, 0, 0.2, 1],
                }}
                className="absolute inset-x-4 pb-8"
                style={{
                  top: virtualItem.start,
                }}
              >
                <StreamMessage 
                  message={message} 
                  streamMessages={messages}
                  onLinkDetected={onLinkDetected}
                />
              </motion.div>
            );
          })}
        </AnimatePresence>

        {/* Loading indicator */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center justify-center py-8 mt-4"
          >
            <div className="flex items-center space-x-3">
              <div 
                className="rotating-symbol text-primary" 
                style={{ color: modeConfig.borderColor }}
              />
              <span className="text-sm text-muted-foreground">
                {currentMode.name} mode is processing...
              </span>
            </div>
          </motion.div>
        )}

        {/* Error indicator */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className={cn(
              "rounded-lg border border-destructive/50 bg-destructive/10 p-4 text-sm text-destructive",
              "w-full max-w-5xl mx-auto mt-4"
            )}
            role="alert"
            aria-live="assertive"
          >
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 rounded-full bg-destructive" />
              <span>{error}</span>
            </div>
          </motion.div>
        )}

      </div>
    </div>
  );
};

export type { MessageFlowProps, MessageFlowState };
import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { listen, type UnlistenFn } from '@tauri-apps/api/event';
import { api, type Session } from '@/lib/api';
import type { ClaudeStreamMessage } from '../AgentExecution';

/**
 * Core session state interface
 */
interface SessionState {
  // Project and session management
  projectPath: string;
  claudeSessionId: string | null;
  extractedSessionInfo: { sessionId: string; projectId: string } | null;
  
  // Message state
  messages: ClaudeStreamMessage[];
  rawJsonlOutput: string[];
  
  // Loading and error state
  isLoading: boolean;
  error: string | null;
  
  // Session metadata
  totalTokens: number;
  isFirstPrompt: boolean;
  
  // Queued prompts
  queuedPrompts: Array<{ id: string; prompt: string; model: 'sonnet' | 'opus' }>;
  queuedPromptsCollapsed: boolean;
}

/**
 * UI state interface
 */
interface UIState {
  // Dialog states
  showTimeline: boolean;
  showSettings: boolean;
  showForkDialog: boolean;
  showSlashCommandsSettings: boolean;
  showPreview: boolean;
  showPreviewPrompt: boolean;
  
  // Preview state
  previewUrl: string;
  splitPosition: number;
  isPreviewMaximized: boolean;
  
  // Fork state
  forkCheckpointId: string | null;
  forkSessionName: string;
  
  // UI controls
  copyPopoverOpen: boolean;
  timelineVersion: number;
}

/**
 * Session management hook configuration
 */
interface UseClaudeSessionConfig {
  session?: Session;
  initialProjectPath?: string;
  onStreamingChange?: (isStreaming: boolean, sessionId: string | null) => void;
}

/**
 * Hook return interface
 */
interface UseClaudeSessionReturn extends SessionState, UIState {
  // Computed values
  effectiveSession: Session | null;
  displayableMessages: ClaudeStreamMessage[];
  
  // Actions
  setProjectPath: (path: string) => void;
  setError: (error: string | null) => void;
  handleSendPrompt: (prompt: string, model: 'sonnet' | 'opus') => Promise<void>;
  handleSelectPath: () => Promise<void>;
  
  // UI actions
  setShowTimeline: (show: boolean) => void;
  setShowSettings: (show: boolean) => void;
  setShowForkDialog: (show: boolean) => void;
  setShowSlashCommandsSettings: (show: boolean) => void;
  setShowPreview: (show: boolean) => void;
  setShowPreviewPrompt: (show: boolean) => void;
  setPreviewUrl: (url: string) => void;
  setSplitPosition: (position: number) => void;
  setIsPreviewMaximized: (maximized: boolean) => void;
  setForkCheckpointId: (id: string | null) => void;
  setForkSessionName: (name: string) => void;
  setCopyPopoverOpen: (open: boolean) => void;
  setTimelineVersion: (version: number) => void;
  setQueuedPromptsCollapsed: (collapsed: boolean) => void;
  
  // Session management
  reconnectToSession: (sessionId: string) => Promise<void>;
  checkForActiveSession: () => Promise<void>;
  
  // Cleanup
  cleanup: () => void;
}

/**
 * Comprehensive Claude session state management hook
 * 
 * Extracts all session logic from the monolithic ClaudeCodeSession component
 * into a reusable, testable hook with clear separation of concerns.
 */
export function useClaudeSession({
  session,
  initialProjectPath = '',
  onStreamingChange,
}: UseClaudeSessionConfig = {}): UseClaudeSessionReturn {
  
  // ===========================
  // CORE SESSION STATE
  // ===========================
  
  const [projectPath, setProjectPath] = useState(initialProjectPath || session?.project_path || '');
  const [claudeSessionId, setClaudeSessionId] = useState<string | null>(null);
  const [extractedSessionInfo, setExtractedSessionInfo] = useState<{ sessionId: string; projectId: string } | null>(null);
  
  // ===========================
  // MESSAGE STATE
  // ===========================
  
  const [messages, setMessages] = useState<ClaudeStreamMessage[]>([]);
  const [rawJsonlOutput, setRawJsonlOutput] = useState<string[]>([]);
  
  // ===========================
  // LOADING AND ERROR STATE
  // ===========================
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // ===========================
  // SESSION METADATA
  // ===========================
  
  const [totalTokens, setTotalTokens] = useState(0);
  const [isFirstPrompt, setIsFirstPrompt] = useState(!session);
  
  // ===========================
  // QUEUED PROMPTS STATE
  // ===========================
  
  const [queuedPrompts, setQueuedPrompts] = useState<Array<{ id: string; prompt: string; model: 'sonnet' | 'opus' }>>([]);
  const [queuedPromptsCollapsed, setQueuedPromptsCollapsed] = useState(false);
  
  // ===========================
  // UI STATE
  // ===========================
  
  const [showTimeline, setShowTimeline] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showForkDialog, setShowForkDialog] = useState(false);
  const [showSlashCommandsSettings, setShowSlashCommandsSettings] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [showPreviewPrompt, setShowPreviewPrompt] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [splitPosition, setSplitPosition] = useState(50);
  const [isPreviewMaximized, setIsPreviewMaximized] = useState(false);
  const [forkCheckpointId, setForkCheckpointId] = useState<string | null>(null);
  const [forkSessionName, setForkSessionName] = useState('');
  const [copyPopoverOpen, setCopyPopoverOpen] = useState(false);
  const [timelineVersion, setTimelineVersion] = useState(0);
  
  // ===========================
  // REFS FOR LIFECYCLE MANAGEMENT
  // ===========================
  
  const unlistenRefs = useRef<UnlistenFn[]>([]);
  const hasActiveSessionRef = useRef(false);
  const queuedPromptsRef = useRef<Array<{ id: string; prompt: string; model: 'sonnet' | 'opus' }>>([]);
  const isMountedRef = useRef(true);
  const isListeningRef = useRef(false);
  
  // ===========================
  // COMPUTED VALUES
  // ===========================
  
  // Get effective session info (from prop or extracted)
  const effectiveSession = useMemo(() => {
    if (session) return session;
    if (extractedSessionInfo) {
      return {
        id: extractedSessionInfo.sessionId,
        project_id: extractedSessionInfo.projectId,
        project_path: projectPath,
        created_at: Date.now(),
      } as Session;
    }
    return null;
  }, [session, extractedSessionInfo, projectPath]);
  
  // Filter messages for display (remove meta messages without content)
  const displayableMessages = useMemo(() => {
    return messages.filter((message, index) => {
      // Skip meta messages that don't have meaningful content
      if (message.isMeta && !message.leafUuid && !message.summary) {
        return false;
      }

      // Skip user messages that only contain tool results already displayed
      if (message.type === 'user' && message.message) {
        if (message.isMeta) return false;

        const msg = message.message;
        if (!msg.content || (Array.isArray(msg.content) && msg.content.length === 0)) {
          return false;
        }

        if (Array.isArray(msg.content)) {
          let hasVisibleContent = false;
          for (const content of msg.content) {
            if (content.type === 'text') {
              hasVisibleContent = true;
              break;
            }
            if (content.type === 'tool_result') {
              let willBeSkipped = false;
              if (content.tool_use_id) {
                // Look for matching tool_use in previous assistant messages
                for (let i = index - 1; i >= 0; i--) {
                  const prevMsg = messages[i];
                  if (prevMsg.type === 'assistant' && prevMsg.message?.content && Array.isArray(prevMsg.message.content)) {
                    const toolUse = prevMsg.message.content.find((c: any) => 
                      c.type === 'tool_use' && c.id === content.tool_use_id
                    );
                    if (toolUse) {
                      const toolName = toolUse.name?.toLowerCase();
                      const toolsWithWidgets = [
                        'task', 'edit', 'multiedit', 'todowrite', 'ls', 'read', 
                        'glob', 'bash', 'write', 'grep'
                      ];
                      if (toolsWithWidgets.includes(toolName) || toolUse.name?.startsWith('mcp__')) {
                        willBeSkipped = true;
                      }
                      break;
                    }
                  }
                }
              }
              if (!willBeSkipped) {
                hasVisibleContent = true;
                break;
              }
            }
          }
          if (!hasVisibleContent) {
            return false;
          }
        }
      }
      return true;
    });
  }, [messages]);
  
  // ===========================
  // SESSION MANAGEMENT FUNCTIONS
  // ===========================
  
  const checkForActiveSession = useCallback(async () => {
    if (session) {
      try {
        const activeSessions = await api.listRunningClaudeSessions();
        const activeSession = activeSessions.find((s: any) => {
          if ('process_type' in s && s.process_type && 'ClaudeSession' in s.process_type) {
            return (s.process_type as any).ClaudeSession.session_id === session.id;
          }
          return false;
        });
        
        if (activeSession) {
          console.log('[useClaudeSession] Found active session, reconnecting:', session.id);
          setClaudeSessionId(session.id);
          await reconnectToSession(session.id);
        }
      } catch (err) {
        console.error('Failed to check for active sessions:', err);
      }
    }
  }, [session]);
  
  const reconnectToSession = useCallback(async (sessionId: string) => {
    console.log('[useClaudeSession] Reconnecting to session:', sessionId);
    
    // Prevent duplicate listeners
    if (isListeningRef.current) {
      console.log('[useClaudeSession] Already listening to session, skipping reconnect');
      return;
    }
    
    // Clean up previous listeners
    unlistenRefs.current.forEach(unlisten => unlisten());
    unlistenRefs.current = [];
    
    // Set the session ID before setting up listeners
    setClaudeSessionId(sessionId);
    
    // Mark as listening
    isListeningRef.current = true;
    
    // Set up session-specific listeners
    const outputUnlisten = await listen<string>(`claude-output:${sessionId}`, async (event) => {
      try {
        console.log('[useClaudeSession] Received claude-output on reconnect:', event.payload);
        
        if (!isMountedRef.current) return;
        
        // Store raw JSONL
        setRawJsonlOutput(prev => [...prev, event.payload]);
        
        // Parse and display
        const message = JSON.parse(event.payload) as ClaudeStreamMessage;
        setMessages(prev => [...prev, message]);
      } catch (err) {
        console.error('Failed to parse message:', err, event.payload);
      }
    });

    const errorUnlisten = await listen<string>(`claude-error:${sessionId}`, (event) => {
      console.error('Claude error:', event.payload);
      if (isMountedRef.current) {
        setError(event.payload);
      }
    });

    const completeUnlisten = await listen<boolean>(`claude-complete:${sessionId}`, async (event) => {
      console.log('[useClaudeSession] Received claude-complete on reconnect:', event.payload);
      if (isMountedRef.current) {
        setIsLoading(false);
        hasActiveSessionRef.current = false;
      }
    });

    unlistenRefs.current = [outputUnlisten, errorUnlisten, completeUnlisten];
    
    // Mark as loading to show the session is active
    if (isMountedRef.current) {
      setIsLoading(true);
      hasActiveSessionRef.current = true;
    }
  }, []);
  
  // ===========================
  // USER ACTIONS
  // ===========================
  
  const handleSelectPath = useCallback(async () => {
    try {
      const { open } = await import('@tauri-apps/plugin-dialog');
      const selected = await open({
        directory: true,
        multiple: false,
        title: 'Select Project Directory'
      });
      
      if (selected) {
        setProjectPath(selected as string);
        setError(null);
      }
    } catch (err) {
      console.error('Failed to select directory:', err);
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(`Failed to select directory: ${errorMessage}`);
    }
  }, []);
  
  const handleSendPrompt = useCallback(async (prompt: string, model: 'sonnet' | 'opus') => {
    console.log('[useClaudeSession] handleSendPrompt called with:', { prompt, model, projectPath, claudeSessionId, effectiveSession });
    
    if (!projectPath) {
      setError('Please select a project directory first');
      return;
    }

    // If already loading, queue the prompt
    if (isLoading) {
      const newPrompt = {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        prompt,
        model
      };
      setQueuedPrompts(prev => [...prev, newPrompt]);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      hasActiveSessionRef.current = true;
      
      // For resuming sessions, ensure we have the session ID
      if (effectiveSession && !claudeSessionId) {
        setClaudeSessionId(effectiveSession.id);
      }
      
      // Only clean up and set up new listeners if not already listening
      if (!isListeningRef.current) {
        // Clean up previous listeners
        unlistenRefs.current.forEach(unlisten => unlisten());
        unlistenRefs.current = [];
        
        // Mark as setting up listeners
        isListeningRef.current = true;
        
        // Set up event listeners for new session
        const outputUnlisten = await listen<string>('claude-output', async (event) => {
          try {
            if (!isMountedRef.current) return;
            
            setRawJsonlOutput(prev => [...prev, event.payload]);
            const message = JSON.parse(event.payload) as ClaudeStreamMessage;
            setMessages(prev => [...prev, message]);
          } catch (err) {
            console.error('Failed to parse message:', err, event.payload);
          }
        });

        const errorUnlisten = await listen<string>('claude-error', (event) => {
          console.error('Claude error:', event.payload);
          if (isMountedRef.current) {
            setError(event.payload);
          }
        });

        const completeUnlisten = await listen<boolean>('claude-complete', async (event) => {
          console.log('[useClaudeSession] Received claude-complete:', event.payload);
          if (isMountedRef.current) {
            setIsLoading(false);
            hasActiveSessionRef.current = false;
            
            // Process next queued prompt if any
            if (queuedPromptsRef.current.length > 0) {
              const nextPrompt = queuedPromptsRef.current[0];
              setQueuedPrompts(prev => prev.slice(1));
              setTimeout(() => {
                handleSendPrompt(nextPrompt.prompt, nextPrompt.model);
              }, 100);
            }
          }
        });

        unlistenRefs.current = [outputUnlisten, errorUnlisten, completeUnlisten];
      }
      
      // Send the prompt
      const response = await api.sendClaudePrompt({
        prompt,
        model,
        project_path: projectPath,
        session_id: effectiveSession?.id || null,
      });

      console.log('[useClaudeSession] Prompt sent successfully:', response);
      
      // Extract session info from response if it's a new session
      if (response.session_id && response.project_id && !effectiveSession) {
        setExtractedSessionInfo({
          sessionId: response.session_id,
          projectId: response.project_id,
        });
        setClaudeSessionId(response.session_id);
      }
      
      setIsFirstPrompt(false);
      
    } catch (err) {
      console.error('Failed to send prompt:', err);
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(`Failed to send prompt: ${errorMessage}`);
      setIsLoading(false);
      hasActiveSessionRef.current = false;
    }
  }, [projectPath, claudeSessionId, effectiveSession, isLoading]);
  
  // ===========================
  // CLEANUP FUNCTION
  // ===========================
  
  const cleanup = useCallback(() => {
    console.log('[useClaudeSession] Cleaning up session hook');
    isMountedRef.current = false;
    isListeningRef.current = false;
    
    // Clean up listeners
    unlistenRefs.current.forEach(unlisten => unlisten());
    unlistenRefs.current = [];
    
    // Clear checkpoint manager when session ends
    if (effectiveSession) {
      api.clearCheckpointManager(effectiveSession.id).catch(err => {
        console.error('Failed to clear checkpoint manager:', err);
      });
    }
  }, [effectiveSession]);
  
  // ===========================
  // EFFECTS
  // ===========================
  
  // Keep queuedPromptsRef in sync with state
  useEffect(() => {
    queuedPromptsRef.current = queuedPrompts;
  }, [queuedPrompts]);
  
  // Set session ID when session prop changes
  useEffect(() => {
    if (session) {
      setClaudeSessionId(session.id);
      checkForActiveSession();
    }
  }, [session, checkForActiveSession]);
  
  // Calculate total tokens
  useEffect(() => {
    const tokens = messages.reduce((total, msg) => {
      if (msg.message?.usage) {
        return total + msg.message.usage.input_tokens + msg.message.usage.output_tokens;
      }
      return total;
    }, 0);
    setTotalTokens(tokens);
  }, [messages]);
  
  // Notify parent of streaming state changes
  useEffect(() => {
    onStreamingChange?.(isLoading, claudeSessionId);
  }, [isLoading, claudeSessionId, onStreamingChange]);
  
  // Component mount/unmount lifecycle
  useEffect(() => {
    isMountedRef.current = true;
    
    return cleanup;
  }, [cleanup]);
  
  // ===========================
  // RETURN HOOK INTERFACE
  // ===========================
  
  return {
    // Core session state
    projectPath,
    claudeSessionId,
    extractedSessionInfo,
    messages,
    rawJsonlOutput,
    isLoading,
    error,
    totalTokens,
    isFirstPrompt,
    queuedPrompts,
    queuedPromptsCollapsed,
    
    // UI state
    showTimeline,
    showSettings,
    showForkDialog,
    showSlashCommandsSettings,
    showPreview,
    showPreviewPrompt,
    previewUrl,
    splitPosition,
    isPreviewMaximized,
    forkCheckpointId,
    forkSessionName,
    copyPopoverOpen,
    timelineVersion,
    
    // Computed values
    effectiveSession,
    displayableMessages,
    
    // Actions
    setProjectPath,
    setError,
    handleSendPrompt,
    handleSelectPath,
    
    // UI actions
    setShowTimeline,
    setShowSettings,
    setShowForkDialog,
    setShowSlashCommandsSettings,
    setShowPreview,
    setShowPreviewPrompt,
    setPreviewUrl,
    setSplitPosition,
    setIsPreviewMaximized,
    setForkCheckpointId,
    setForkSessionName,
    setCopyPopoverOpen,
    setTimelineVersion,
    setQueuedPromptsCollapsed,
    
    // Session management
    reconnectToSession,
    checkForActiveSession,
    
    // Cleanup
    cleanup,
  };
}

export type { UseClaudeSessionReturn, UseClaudeSessionConfig, SessionState, UIState };
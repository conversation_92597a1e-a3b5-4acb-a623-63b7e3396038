/**
 * Session Container Styles
 * Carbon Grid System + Mode-aware styling
 */

/* Base Grid System (Carbon-inspired) */
.cds--grid {
  margin-left: auto;
  margin-right: auto;
  max-width: 99rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

.cds--grid--full-width {
  max-width: 100%;
  padding-left: 0;
  padding-right: 0;
}

.cds--grid--narrow {
  max-width: 42rem;
}

.cds--grid--condensed {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.cds--row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -1rem;
  margin-right: -1rem;
}

.cds--row--no-gutter {
  margin-left: 0;
  margin-right: 0;
}

.cds--row--no-gutter > .cds--col {
  padding-left: 0;
  padding-right: 0;
}

/* Column Base */
.cds--col {
  flex: 1 1 0%;
  min-width: 0;
  padding-left: 1rem;
  padding-right: 1rem;
}

.cds--col--auto {
  flex: 0 0 auto;
  width: auto;
}

/* Small Breakpoint (320px+) */
@media (min-width: 20rem) {
  .cds--col-sm-1 { flex: 0 0 25%; max-width: 25%; }
  .cds--col-sm-2 { flex: 0 0 50%; max-width: 50%; }
  .cds--col-sm-3 { flex: 0 0 75%; max-width: 75%; }
  .cds--col-sm-4 { flex: 0 0 100%; max-width: 100%; }
  .cds--col-sm-0 { display: none; }
  
  .cds--offset-sm-1 { margin-left: 25%; }
  .cds--offset-sm-2 { margin-left: 50%; }
  .cds--offset-sm-3 { margin-left: 75%; }
}

/* Medium Breakpoint (672px+) */
@media (min-width: 42rem) {
  .cds--grid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .cds--col-md-1 { flex: 0 0 12.5%; max-width: 12.5%; }
  .cds--col-md-2 { flex: 0 0 25%; max-width: 25%; }
  .cds--col-md-3 { flex: 0 0 37.5%; max-width: 37.5%; }
  .cds--col-md-4 { flex: 0 0 50%; max-width: 50%; }
  .cds--col-md-5 { flex: 0 0 62.5%; max-width: 62.5%; }
  .cds--col-md-6 { flex: 0 0 75%; max-width: 75%; }
  .cds--col-md-7 { flex: 0 0 87.5%; max-width: 87.5%; }
  .cds--col-md-8 { flex: 0 0 100%; max-width: 100%; }
  .cds--col-md-0 { display: none; }
  
  .cds--offset-md-1 { margin-left: 12.5%; }
  .cds--offset-md-2 { margin-left: 25%; }
  .cds--offset-md-3 { margin-left: 37.5%; }
  .cds--offset-md-4 { margin-left: 50%; }
  .cds--offset-md-5 { margin-left: 62.5%; }
  .cds--offset-md-6 { margin-left: 75%; }
  .cds--offset-md-7 { margin-left: 87.5%; }
}

/* Large Breakpoint (1056px+) */
@media (min-width: 66rem) {
  .cds--grid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .cds--col-lg-1 { flex: 0 0 6.25%; max-width: 6.25%; }
  .cds--col-lg-2 { flex: 0 0 12.5%; max-width: 12.5%; }
  .cds--col-lg-3 { flex: 0 0 18.75%; max-width: 18.75%; }
  .cds--col-lg-4 { flex: 0 0 25%; max-width: 25%; }
  .cds--col-lg-5 { flex: 0 0 31.25%; max-width: 31.25%; }
  .cds--col-lg-6 { flex: 0 0 37.5%; max-width: 37.5%; }
  .cds--col-lg-7 { flex: 0 0 43.75%; max-width: 43.75%; }
  .cds--col-lg-8 { flex: 0 0 50%; max-width: 50%; }
  .cds--col-lg-9 { flex: 0 0 56.25%; max-width: 56.25%; }
  .cds--col-lg-10 { flex: 0 0 62.5%; max-width: 62.5%; }
  .cds--col-lg-11 { flex: 0 0 68.75%; max-width: 68.75%; }
  .cds--col-lg-12 { flex: 0 0 75%; max-width: 75%; }
  .cds--col-lg-13 { flex: 0 0 81.25%; max-width: 81.25%; }
  .cds--col-lg-14 { flex: 0 0 87.5%; max-width: 87.5%; }
  .cds--col-lg-15 { flex: 0 0 93.75%; max-width: 93.75%; }
  .cds--col-lg-16 { flex: 0 0 100%; max-width: 100%; }
  .cds--col-lg-0 { display: none; }
  
  .cds--offset-lg-1 { margin-left: 6.25%; }
  .cds--offset-lg-2 { margin-left: 12.5%; }
  .cds--offset-lg-3 { margin-left: 18.75%; }
  .cds--offset-lg-4 { margin-left: 25%; }
  .cds--offset-lg-5 { margin-left: 31.25%; }
  .cds--offset-lg-6 { margin-left: 37.5%; }
  .cds--offset-lg-7 { margin-left: 43.75%; }
  .cds--offset-lg-8 { margin-left: 50%; }
  .cds--offset-lg-9 { margin-left: 56.25%; }
  .cds--offset-lg-10 { margin-left: 62.5%; }
  .cds--offset-lg-11 { margin-left: 68.75%; }
  .cds--offset-lg-12 { margin-left: 75%; }
  .cds--offset-lg-13 { margin-left: 81.25%; }
  .cds--offset-lg-14 { margin-left: 87.5%; }
  .cds--offset-lg-15 { margin-left: 93.75%; }
}

/* Session Container Base Styles */
.session-container {
  position: relative;
  background-color: var(--session-bg-color, hsl(var(--background)));
  transition: background-color 0.3s ease;
}

/* Layout Density Variations */
.session-container--compact {
  --session-spacing-unit: 0.5rem;
  --session-border-radius: 0.25rem;
}

.session-container--standard {
  --session-spacing-unit: 1rem;
  --session-border-radius: 0.5rem;
}

.session-container--relaxed {
  --session-spacing-unit: 1.5rem;
  --session-border-radius: 0.75rem;
}

/* Primary Content Area */
.session-container__primary-content {
  position: relative;
  min-height: 100vh;
  background-color: hsl(var(--background));
  border-radius: var(--session-border-radius);
  transition: all 0.3s ease;
}

/* Widget Panel */
.session-container__widget-panel {
  position: relative;
  background-color: hsl(var(--card));
  border-left: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
}

/* Mode-Specific Styling */
.session-container--architect {
  --mode-primary: #3b82f6;
  --mode-primary-rgb: 59, 130, 246;
  --mode-accent: #dbeafe;
}

.session-container--architect .session-container__primary-content {
  border-left: 3px solid var(--mode-primary);
}

.session-container--debug {
  --mode-primary: #ef4444;
  --mode-primary-rgb: 239, 68, 68;
  --mode-accent: #fee2e2;
}

.session-container--debug .session-container__primary-content {
  border-left: 3px solid var(--mode-primary);
}

.session-container--review {
  --mode-primary: #0ea5e9;
  --mode-primary-rgb: 14, 165, 233;
  --mode-accent: #e0f2fe;
}

.session-container--review .session-container__primary-content {
  border-left: 3px solid var(--mode-primary);
}

.session-container--deploy {
  --mode-primary: #22c55e;
  --mode-primary-rgb: 34, 197, 94;
  --mode-accent: #dcfce7;
}

.session-container--deploy .session-container__primary-content {
  border-left: 3px solid var(--mode-primary);
}

.session-container--experiment {
  --mode-primary: #a855f7;
  --mode-primary-rgb: 168, 85, 247;
  --mode-accent: #f3e8ff;
}

.session-container--experiment .session-container__primary-content {
  border-left: 3px solid var(--mode-primary);
}

.session-container--learn {
  --mode-primary: #f59e0b;
  --mode-primary-rgb: 245, 158, 11;
  --mode-accent: #fef3c7;
}

.session-container--learn .session-container__primary-content {
  border-left: 3px solid var(--mode-primary);
}

/* Responsive Behavior */
@media (max-width: 41.9rem) {
  .session-container__widget-panel {
    display: none; /* Hide widget panel on mobile */
  }
  
  .session-container__primary-content {
    max-width: 100%;
  }
}

@media (min-width: 42rem) and (max-width: 65.9rem) {
  .session-container__widget-panel {
    width: 16rem; /* Fixed width on tablet */
    flex: none;
  }
}

@media (min-width: 66rem) {
  .session-container__widget-panel {
    min-width: 20rem; /* Minimum width on desktop */
  }
}

/* Accessibility */
.session-container:focus-within {
  outline: 2px solid var(--mode-primary);
  outline-offset: 2px;
}

/* Animation Utilities */
.session-container * {
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

/* Print Styles */
@media print {
  .session-container {
    background: white !important;
    color: black !important;
  }
  
  .session-container__widget-panel {
    display: none !important;
  }
}

/* ===========================
   CARBON DESIGN SYSTEM RESPONSIVE EXTENSIONS
   =========================== */

/* Design Tokens - Responsive Spacing Scale */
:root {
  /* Carbon Spacing Scale */
  --cds-spacing-01: 0.125rem;  /* 2px */
  --cds-spacing-02: 0.25rem;   /* 4px */
  --cds-spacing-03: 0.5rem;    /* 8px */
  --cds-spacing-04: 0.75rem;   /* 12px */
  --cds-spacing-05: 1rem;      /* 16px */
  --cds-spacing-06: 1.5rem;    /* 24px */
  --cds-spacing-07: 2rem;      /* 32px */
  --cds-spacing-08: 2.5rem;    /* 40px */
  --cds-spacing-09: 3rem;      /* 48px */
  --cds-spacing-10: 4rem;      /* 64px */
  --cds-spacing-11: 5rem;      /* 80px */
  --cds-spacing-12: 6rem;      /* 96px */
  --cds-spacing-13: 10rem;     /* 160px */

  /* Carbon Layout Scale */
  --cds-layout-01: 1rem;       /* 16px */
  --cds-layout-02: 1.5rem;     /* 24px */
  --cds-layout-03: 2rem;       /* 32px */
  --cds-layout-04: 3rem;       /* 48px */
  --cds-layout-05: 4rem;       /* 64px */
  --cds-layout-06: 6rem;       /* 96px */
  --cds-layout-07: 10rem;      /* 160px */

  /* Breakpoint values */
  --cds-breakpoint-sm: 20rem;    /* 320px */
  --cds-breakpoint-md: 42rem;    /* 672px */
  --cds-breakpoint-lg: 66rem;    /* 1056px */
  --cds-breakpoint-xl: 82rem;    /* 1312px */
  --cds-breakpoint-max: 99rem;   /* 1584px */

  /* Responsive type scale */
  --cds-type-scale-sm: 0.875rem;  /* 14px */
  --cds-type-scale-md: 1rem;      /* 16px */
  --cds-type-scale-lg: 1.125rem;  /* 18px */
  --cds-type-scale-xl: 1.25rem;   /* 20px */
}

/* ===========================
   RESPONSIVE TYPOGRAPHY UTILITIES
   =========================== */

/* Typography Scale - Mobile First */
.cds-type--body-compact-01 { font-size: 0.875rem; line-height: 1.125rem; }
.cds-type--body-compact-02 { font-size: 1rem; line-height: 1.375rem; }
.cds-type--body-01 { font-size: 0.875rem; line-height: 1.25rem; }
.cds-type--body-02 { font-size: 1rem; line-height: 1.5rem; }
.cds-type--heading-01 { font-size: 0.875rem; line-height: 1.125rem; font-weight: 600; }
.cds-type--heading-02 { font-size: 1rem; line-height: 1.375rem; font-weight: 600; }
.cds-type--heading-03 { font-size: 1.25rem; line-height: 1.625rem; font-weight: 400; }
.cds-type--heading-04 { font-size: 1.75rem; line-height: 2.25rem; font-weight: 400; }
.cds-type--heading-05 { font-size: 2rem; line-height: 2.5rem; font-weight: 300; }

/* Responsive Typography */
@media (min-width: 42rem) {
  .cds-type--heading-03 { font-size: 1.25rem; line-height: 1.75rem; }
  .cds-type--heading-04 { font-size: 2rem; line-height: 2.5rem; }
  .cds-type--heading-05 { font-size: 2.25rem; line-height: 2.75rem; }
}

@media (min-width: 66rem) {
  .cds-type--heading-04 { font-size: 2.25rem; line-height: 2.75rem; }
  .cds-type--heading-05 { font-size: 2.625rem; line-height: 3.125rem; }
}

/* ===========================
   RESPONSIVE SPACING UTILITIES
   =========================== */

/* Margin Utilities - Mobile First */
.cds-m-01 { margin: var(--cds-spacing-01); }
.cds-m-02 { margin: var(--cds-spacing-02); }
.cds-m-03 { margin: var(--cds-spacing-03); }
.cds-m-04 { margin: var(--cds-spacing-04); }
.cds-m-05 { margin: var(--cds-spacing-05); }
.cds-m-06 { margin: var(--cds-spacing-06); }
.cds-m-07 { margin: var(--cds-spacing-07); }

.cds-mb-01 { margin-bottom: var(--cds-spacing-01); }
.cds-mb-02 { margin-bottom: var(--cds-spacing-02); }
.cds-mb-03 { margin-bottom: var(--cds-spacing-03); }
.cds-mb-04 { margin-bottom: var(--cds-spacing-04); }
.cds-mb-05 { margin-bottom: var(--cds-spacing-05); }
.cds-mb-06 { margin-bottom: var(--cds-spacing-06); }
.cds-mb-07 { margin-bottom: var(--cds-spacing-07); }

.cds-mt-01 { margin-top: var(--cds-spacing-01); }
.cds-mt-02 { margin-top: var(--cds-spacing-02); }
.cds-mt-03 { margin-top: var(--cds-spacing-03); }
.cds-mt-04 { margin-top: var(--cds-spacing-04); }
.cds-mt-05 { margin-top: var(--cds-spacing-05); }
.cds-mt-06 { margin-top: var(--cds-spacing-06); }
.cds-mt-07 { margin-top: var(--cds-spacing-07); }

/* Padding Utilities */
.cds-p-01 { padding: var(--cds-spacing-01); }
.cds-p-02 { padding: var(--cds-spacing-02); }
.cds-p-03 { padding: var(--cds-spacing-03); }
.cds-p-04 { padding: var(--cds-spacing-04); }
.cds-p-05 { padding: var(--cds-spacing-05); }
.cds-p-06 { padding: var(--cds-spacing-06); }
.cds-p-07 { padding: var(--cds-spacing-07); }

/* Responsive Spacing - Medium */
@media (min-width: 42rem) {
  .cds-m-md-05 { margin: var(--cds-spacing-05); }
  .cds-m-md-06 { margin: var(--cds-spacing-06); }
  .cds-m-md-07 { margin: var(--cds-spacing-07); }
  .cds-mb-md-05 { margin-bottom: var(--cds-spacing-05); }
  .cds-mb-md-06 { margin-bottom: var(--cds-spacing-06); }
  .cds-mb-md-07 { margin-bottom: var(--cds-spacing-07); }
  .cds-p-md-05 { padding: var(--cds-spacing-05); }
  .cds-p-md-06 { padding: var(--cds-spacing-06); }
  .cds-p-md-07 { padding: var(--cds-spacing-07); }
}

/* Responsive Spacing - Large */
@media (min-width: 66rem) {
  .cds-m-lg-06 { margin: var(--cds-spacing-06); }
  .cds-m-lg-07 { margin: var(--cds-spacing-07); }
  .cds-m-lg-08 { margin: var(--cds-spacing-08); }
  .cds-mb-lg-06 { margin-bottom: var(--cds-spacing-06); }
  .cds-mb-lg-07 { margin-bottom: var(--cds-spacing-07); }
  .cds-mb-lg-08 { margin-bottom: var(--cds-spacing-08); }
  .cds-p-lg-06 { padding: var(--cds-spacing-06); }
  .cds-p-lg-07 { padding: var(--cds-spacing-07); }
  .cds-p-lg-08 { padding: var(--cds-spacing-08); }
}

/* ===========================
   RESPONSIVE LAYOUT UTILITIES
   =========================== */

/* Display Utilities */
.cds-hidden { display: none; }
.cds-visible { display: block; }

@media (max-width: 41.9rem) {
  .cds-hidden-sm { display: none; }
  .cds-visible-sm { display: block; }
}

@media (min-width: 42rem) and (max-width: 65.9rem) {
  .cds-hidden-md { display: none; }
  .cds-visible-md { display: block; }
}

@media (min-width: 66rem) {
  .cds-hidden-lg { display: none; }
  .cds-visible-lg { display: block; }
}

/* Flex Utilities */
.cds-flex { display: flex; }
.cds-flex-column { flex-direction: column; }
.cds-flex-row { flex-direction: row; }
.cds-flex-wrap { flex-wrap: wrap; }
.cds-flex-nowrap { flex-wrap: nowrap; }
.cds-justify-start { justify-content: flex-start; }
.cds-justify-center { justify-content: center; }
.cds-justify-end { justify-content: flex-end; }
.cds-justify-between { justify-content: space-between; }
.cds-align-start { align-items: flex-start; }
.cds-align-center { align-items: center; }
.cds-align-end { align-items: flex-end; }

/* ===========================
   MODE-AWARE RESPONSIVE PATTERNS
   =========================== */

/* Architect Mode - Structured Layout */
.session-container--architect .cds-layout-structured {
  display: grid;
  gap: var(--cds-spacing-05);
}

@media (min-width: 42rem) {
  .session-container--architect .cds-layout-structured {
    grid-template-columns: 1fr 240px;
    gap: var(--cds-spacing-06);
  }
}

@media (min-width: 66rem) {
  .session-container--architect .cds-layout-structured {
    grid-template-columns: 1fr 320px;
    gap: var(--cds-spacing-07);
  }
}

/* Debug Mode - Dense Information Layout */
.session-container--debug .cds-layout-dense {
  padding: var(--cds-spacing-03);
  font-size: 0.875rem;
}

@media (min-width: 42rem) {
  .session-container--debug .cds-layout-dense {
    padding: var(--cds-spacing-04);
    font-size: 1rem;
  }
}

@media (min-width: 66rem) {
  .session-container--debug .cds-layout-dense {
    padding: var(--cds-spacing-05);
  }
}

/* Review Mode - Reading-Optimized Layout */
.session-container--review .cds-layout-reading {
  max-width: 65ch;
  line-height: 1.6;
  padding: var(--cds-spacing-04);
}

@media (min-width: 42rem) {
  .session-container--review .cds-layout-reading {
    max-width: 75ch;
    padding: var(--cds-spacing-05);
  }
}

@media (min-width: 66rem) {
  .session-container--review .cds-layout-reading {
    max-width: 85ch;
    padding: var(--cds-spacing-06);
  }
}

/* Deploy Mode - Action-Focused Layout */
.session-container--deploy .cds-layout-action {
  display: flex;
  flex-direction: column;
  gap: var(--cds-spacing-04);
}

@media (min-width: 42rem) {
  .session-container--deploy .cds-layout-action {
    flex-direction: row;
    gap: var(--cds-spacing-05);
  }
}

/* ===========================
   RESPONSIVE COMPONENT PATTERNS
   =========================== */

/* Card Responsive Behavior */
.cds-card {
  padding: var(--cds-spacing-04);
  margin-bottom: var(--cds-spacing-04);
  border-radius: 0.25rem;
  border: 1px solid hsl(var(--border));
}

@media (min-width: 42rem) {
  .cds-card {
    padding: var(--cds-spacing-05);
    margin-bottom: var(--cds-spacing-05);
  }
}

@media (min-width: 66rem) {
  .cds-card {
    padding: var(--cds-spacing-06);
    margin-bottom: var(--cds-spacing-06);
  }
}

/* Button Responsive Sizes */
.cds-btn {
  padding: var(--cds-spacing-03) var(--cds-spacing-04);
  font-size: 0.875rem;
  line-height: 1;
  min-height: 2rem;
}

.cds-btn--sm {
  padding: var(--cds-spacing-02) var(--cds-spacing-03);
  font-size: 0.75rem;
  min-height: 1.5rem;
}

.cds-btn--lg {
  padding: var(--cds-spacing-04) var(--cds-spacing-05);
  font-size: 1rem;
  min-height: 2.5rem;
}

@media (min-width: 42rem) {
  .cds-btn {
    padding: var(--cds-spacing-03) var(--cds-spacing-05);
    min-height: 2.25rem;
  }
  
  .cds-btn--lg {
    padding: var(--cds-spacing-04) var(--cds-spacing-06);
    min-height: 3rem;
  }
}

/* ===========================
   ACCESSIBILITY & MOTION
   =========================== */

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast */
@media (prefers-contrast: high) {
  .session-container {
    border: 2px solid;
  }
  
  .cds-card {
    border-width: 2px;
  }
}

/* Focus Indicators */
.cds-focus {
  outline: 2px solid var(--mode-primary, #0066cc);
  outline-offset: 2px;
}
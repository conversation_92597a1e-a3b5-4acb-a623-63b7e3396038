import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Code2, 
  Bug, 
  G<PERSON>B<PERSON><PERSON>, 
  Rocket, 
  ArrowRight, 
  Loader2,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
// import { Progress } from '@/components/ui/progress';
// import { useMode } from '@/intelligent-mode-system/hooks/useMode';
import { Mode } from '@/intelligent-mode-system/types/mode.types';
import { useReducedMotionPreference } from './AccessibilityEnhancedUI';

// Mode transition state management
interface ModeTransitionState {
  from: Mode | null;
  to: Mode | null;
  progress: number;
  status: 'idle' | 'preparing' | 'transitioning' | 'completing' | 'complete' | 'error';
  preservedContext?: Record<string, any>;
  error?: string;
}

// Context preservation hook
const useContextPreservation = () => {
  const [preservedContext, setPreservedContext] = useState<Record<string, any>>({});
  
  const preserveContext = useCallback((mode: Mode, context: Record<string, any>) => {
    setPreservedContext(prev => ({
      ...prev,
      [mode as unknown as string]: {
        ...context,
        timestamp: Date.now(),
        scrollPosition: window.scrollY,
        activeElement: document.activeElement?.id || null
      }
    }));
  }, []);
  
  const restoreContext = useCallback((mode: Mode) => {
    const context = preservedContext[mode as unknown as string];
    if (context) {
      // Restore scroll position
      if (typeof context.scrollPosition === 'number') {
        window.scrollTo({ top: context.scrollPosition, behavior: 'smooth' });
      }
      
      // Restore focus
      if (context.activeElement) {
        const element = document.getElementById(context.activeElement);
        element?.focus();
      }
      
      return context;
    }
    return null;
  }, [preservedContext]);
  
  const clearContext = useCallback((mode: Mode) => {
    setPreservedContext(prev => {
      const { [mode as unknown as string]: _, ...rest } = prev;
      return rest;
    });
  }, []);
  
  return { preserveContext, restoreContext, clearContext, preservedContext };
};

// Mode transition manager hook
const useModeTransition = () => {
  // Mock implementation - replace with actual useMode hook
  const currentMode = 'architect' as unknown as Mode;
  const switchMode = async (mode: Mode) => {
    console.log('Switching to mode:', mode);
  };
  const { preserveContext, restoreContext } = useContextPreservation();
  const [transitionState, setTransitionState] = useState<ModeTransitionState>({
    from: null,
    to: null,
    progress: 0,
    status: 'idle'
  });
  
  const initiateTransition = useCallback(async (targetMode: Mode, context?: Record<string, any>) => {
    if (transitionState.status !== 'idle') return;
    
    setTransitionState({
      from: currentMode,
      to: targetMode,
      progress: 0,
      status: 'preparing',
      preservedContext: context
    });
    
    try {
      // Phase 1: Prepare transition (preserve current context)
      if (currentMode && context) {
        preserveContext(currentMode, context);
      }
      
      setTransitionState(prev => ({ ...prev, progress: 25, status: 'transitioning' }));
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Phase 2: Execute mode switch
      await switchMode(targetMode);
      
      setTransitionState(prev => ({ ...prev, progress: 75 }));
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Phase 3: Restore context for target mode
      const restoredContext = restoreContext(targetMode);
      
      setTransitionState(prev => ({ 
        ...prev, 
        progress: 100, 
        status: 'completing',
        preservedContext: restoredContext || undefined
      }));
      
      await new Promise(resolve => setTimeout(resolve, 300));
      
      setTransitionState({
        from: null,
        to: null,
        progress: 0,
        status: 'idle'
      });
      
    } catch (error) {
      setTransitionState(prev => ({
        ...prev,
        status: 'error',
        error: error instanceof Error ? error.message : 'Transition failed'
      }));
      
      // Reset after error
      setTimeout(() => {
        setTransitionState({
          from: null,
          to: null,
          progress: 0,
          status: 'idle'
        });
      }, 2000);
    }
  }, [currentMode, switchMode, preserveContext, restoreContext, transitionState.status]);
  
  return {
    transitionState,
    initiateTransition,
    isTransitioning: transitionState.status !== 'idle'
  };
};

// Mode icon component
const ModeIcon: React.FC<{ mode: Mode; className?: string }> = ({ mode, className }) => {
  const iconProps = { className: cn('h-5 w-5', className) };
  
  switch (mode as unknown as string) {
    case 'architect':
      return <Code2 {...iconProps} />;
    case 'debug':
      return <Bug {...iconProps} />;
    case 'review':
      return <GitBranch {...iconProps} />;
    case 'deploy':
      return <Rocket {...iconProps} />;
    default:
      return <Code2 {...iconProps} />;
  }
};

// Transition progress indicator
interface TransitionProgressProps {
  state: ModeTransitionState;
  className?: string;
}

const TransitionProgress: React.FC<TransitionProgressProps> = ({ state, className }) => {
  const prefersReducedMotion = useReducedMotionPreference();
  
  if (state.status === 'idle') return null;
  
  
  const getStatusIcon = () => {
    switch (state.status) {
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'complete':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'preparing':
      case 'transitioning':
      case 'completing':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return null;
    }
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: prefersReducedMotion ? 0 : 0.3 }}
      className={cn(
        'fixed top-4 right-4 z-50',
        'bg-white border border-gray-200 rounded-lg shadow-lg',
        'p-4 min-w-[300px]',
        className
      )}
      role="status"
      aria-live="polite"
      aria-label="Mode transition progress"
    >
      <div className="flex items-center gap-3 mb-3">
        {getStatusIcon()}
        <div className="flex-1">
          <div className="flex items-center gap-2 text-sm font-medium">
            {state.from && (
              <>
                <ModeIcon mode={state.from} className="h-4 w-4" />
                <span className="capitalize">{state.from as unknown as string}</span>
              </>
            )}
            {state.from && state.to && (
              <ArrowRight className="h-3 w-3 text-gray-400" />
            )}
            {state.to && (
              <>
                <ModeIcon mode={state.to} className="h-4 w-4" />
                <span className="capitalize">{state.to as unknown as string}</span>
              </>
            )}
          </div>
          <div className="text-xs text-gray-500 capitalize">
            {state.status === 'preparing' && 'Preparing transition...'}
            {state.status === 'transitioning' && 'Switching modes...'}
            {state.status === 'completing' && 'Finalizing...'}
            {state.status === 'complete' && 'Transition complete'}
            {state.status === 'error' && `Error: ${state.error}`}
          </div>
        </div>
      </div>
      
      {state.status !== 'error' && (
        <div className="space-y-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${state.progress}%` }}
              aria-label={`Transition progress: ${state.progress}%`}
            />
          </div>
          <div className="text-xs text-gray-500 text-right">
            {state.progress}%
          </div>
        </div>
      )}
    </motion.div>
  );
};

// Mode transition button with smart suggestions
interface ModeTransitionButtonProps {
  targetMode: Mode;
  currentMode: Mode;
  confidence?: number;
  reason?: string;
  context?: Record<string, any>;
  onTransition?: (mode: Mode) => void;
  className?: string;
  disabled?: boolean;
}

export const ModeTransitionButton: React.FC<ModeTransitionButtonProps> = ({
  targetMode,
  currentMode,
  confidence = 0,
  reason,
  context,
  onTransition,
  className,
  disabled = false
}) => {
  const { initiateTransition, isTransitioning } = useModeTransition();
  const prefersReducedMotion = useReducedMotionPreference();
  
  const handleTransition = useCallback(async () => {
    if (disabled || isTransitioning || targetMode === currentMode) return;
    
    await initiateTransition(targetMode, context);
    onTransition?.(targetMode);
  }, [disabled, isTransitioning, targetMode, currentMode, initiateTransition, context, onTransition]);
  
  const getConfidenceColor = () => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800 border-green-200';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-gray-100 text-gray-800 border-gray-200';
  };
  
  return (
    <motion.div
      whileHover={!prefersReducedMotion ? { scale: 1.02 } : {}}
      whileTap={!prefersReducedMotion ? { scale: 0.98 } : {}}
      className={className}
    >
      <Card className={cn(
        'transition-all duration-200',
        {
          'opacity-50 cursor-not-allowed': disabled || isTransitioning,
          'hover:shadow-md': !disabled && !isTransitioning,
          'border-blue-200 bg-blue-50': targetMode === currentMode
        }
      )}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <ModeIcon mode={targetMode} />
              <span className="font-medium capitalize">{targetMode as unknown as string}</span>
              {targetMode === currentMode && (
                <Badge variant="secondary" className="text-xs">
                  Current
                </Badge>
              )}
            </div>
            
            {confidence > 0 && (
              <Badge 
                className={cn('text-xs', getConfidenceColor())}
                aria-label={`Confidence: ${Math.round(confidence * 100)}%`}
              >
                {Math.round(confidence * 100)}%
              </Badge>
            )}
          </div>
          
          {reason && (
            <p className="text-sm text-gray-600 mb-3">
              {reason}
            </p>
          )}
          
          <Button
            onClick={handleTransition}
            disabled={disabled || isTransitioning || targetMode === currentMode}
            className="w-full"
            size="sm"
            aria-label={`Switch to ${targetMode as unknown as string} mode`}
          >
            {isTransitioning ? (
              <>
                <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                Switching...
              </>
            ) : targetMode === currentMode ? (
              'Current Mode'
            ) : (
              <>
                Switch to {targetMode as unknown as string}
                <ArrowRight className="h-3 w-3 ml-2" />
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Smart mode suggestions component
interface SmartModeSuggestionsProps {
  suggestions: Array<{
    mode: Mode;
    confidence: number;
    reason: string;
    context?: Record<string, any>;
  }>;
  currentMode: Mode;
  onTransition?: (mode: Mode) => void;
  className?: string;
}

export const SmartModeSuggestions: React.FC<SmartModeSuggestionsProps> = ({
  suggestions,
  currentMode,
  onTransition,
  className
}) => {
  const prefersReducedMotion = useReducedMotionPreference();
  
  if (suggestions.length === 0) return null;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: prefersReducedMotion ? 0 : 0.3 }}
      className={cn('space-y-3', className)}
      role="region"
      aria-label="Mode suggestions"
    >
      <h3 className="text-sm font-medium text-gray-700 mb-3">
        Suggested Mode Transitions
      </h3>
      
      <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
        {suggestions.map((suggestion, index) => (
          <ModeTransitionButton
            key={`${suggestion.mode}-${index}`}
            targetMode={suggestion.mode}
            currentMode={currentMode}
            confidence={suggestion.confidence}
            reason={suggestion.reason}
            context={suggestion.context}
            onTransition={onTransition}
          />
        ))}
      </div>
    </motion.div>
  );
};

// Main transition overlay component
interface ModeTransitionOverlayProps {
  className?: string;
}

export const ModeTransitionOverlay: React.FC<ModeTransitionOverlayProps> = ({ className }) => {
  const { transitionState } = useModeTransition();
  
  return (
    <AnimatePresence>
      {transitionState.status !== 'idle' && (
        <TransitionProgress 
          state={transitionState} 
          className={className}
        />
      )}
    </AnimatePresence>
  );
};

export {
  useModeTransition,
  useContextPreservation,
  ModeIcon
};
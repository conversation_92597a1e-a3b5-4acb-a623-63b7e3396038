import React, { useRef, useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronDown, 
  ChevronRight, 
  X, 
  Maximize2, 
  Minimize2, 
  Pin, 
  PinOff,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Focus management hook
const useFocusManagement = () => {
  const focusableElementsSelector = 
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
  
  const trapFocus = useCallback((container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(focusableElementsSelector);
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
    
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;
      
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };
    
    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();
    
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }, [focusableElementsSelector]);
  
  const restoreFocus = useCallback((previouslyFocusedElement: HTMLElement | null) => {
    if (previouslyFocusedElement) {
      previouslyFocusedElement.focus();
    }
  }, []);
  
  return { trapFocus, restoreFocus };
};

// Keyboard navigation hook
const useKeyboardNavigation = (onEscape?: () => void, onEnter?: () => void) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          onEscape?.();
          break;
        case 'Enter':
          if (e.target === e.currentTarget) {
            onEnter?.();
          }
          break;
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onEscape, onEnter]);
};

// Accessible context widget with full ARIA support
interface AccessibleContextWidgetProps {
  id: string;
  title: string;
  content: React.ReactNode;
  priority: number;
  type?: 'info' | 'warning' | 'error' | 'success';
  isCollapsed?: boolean;
  isPinned?: boolean;
  isMaximized?: boolean;
  isDismissible?: boolean;
  onToggle?: () => void;
  onPin?: () => void;
  onMaximize?: () => void;
  onDismiss?: () => void;
  className?: string;
  ariaLabel?: string;
  ariaDescription?: string;
}

export const AccessibleContextWidget: React.FC<AccessibleContextWidgetProps> = ({
  id,
  title,
  content,
  priority,
  type = 'info',
  isCollapsed = false,
  isPinned = false,
  isMaximized = false,
  isDismissible = true,
  onToggle,
  onPin,
  onMaximize,
  onDismiss,
  className,
  ariaLabel,
  ariaDescription
}) => {
  const widgetRef = useRef<HTMLDivElement>(null);
  const { trapFocus, restoreFocus } = useFocusManagement();
  const [previouslyFocusedElement, setPreviouslyFocusedElement] = useState<HTMLElement | null>(null);
  
  useKeyboardNavigation(
    () => {
      if (isMaximized && onMaximize) {
        onMaximize();
      } else if (isDismissible && onDismiss) {
        onDismiss();
      }
    },
    () => {
      if (onToggle) {
        onToggle();
      }
    }
  );
  
  useEffect(() => {
    if (isMaximized && widgetRef.current) {
      setPreviouslyFocusedElement(document.activeElement as HTMLElement);
      const cleanup = trapFocus(widgetRef.current);
      return cleanup;
    } else if (!isMaximized && previouslyFocusedElement) {
      restoreFocus(previouslyFocusedElement);
      setPreviouslyFocusedElement(null);
    }
  }, [isMaximized, trapFocus, restoreFocus, previouslyFocusedElement]);
  
  const getTypeIcon = () => {
    switch (type) {
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" aria-hidden="true" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" aria-hidden="true" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" aria-hidden="true" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" aria-hidden="true" />;
    }
  };
  
  const getTypeColor = () => {
    switch (type) {
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };
  
  return (
    <div
      ref={widgetRef}
      className={cn(
        'accessible-context-widget',
        getTypeColor(),
        {
          'fixed inset-4 z-50': isMaximized,
          'relative': !isMaximized
        },
        className
      )}
      role="region"
      aria-labelledby={`widget-title-${id}`}
      aria-describedby={ariaDescription ? `widget-desc-${id}` : undefined}
      aria-label={ariaLabel}
      tabIndex={-1}
    >
      <Card className="border-0 shadow-sm h-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getTypeIcon()}
              <CardTitle 
                id={`widget-title-${id}`}
                className="text-sm font-medium"
              >
                {title}
              </CardTitle>
              <Badge 
                variant="outline" 
                className="text-xs"
                aria-label={`Priority ${priority}`}
              >
                {priority}
              </Badge>
            </div>
            
            <div className="flex items-center gap-1">
              {onPin && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={onPin}
                  aria-label={isPinned ? 'Unpin widget' : 'Pin widget'}
                  aria-pressed={isPinned}
                >
                  {isPinned ? (
                    <PinOff className="h-3 w-3" aria-hidden="true" />
                  ) : (
                    <Pin className="h-3 w-3" aria-hidden="true" />
                  )}
                </Button>
              )}
              
              {onMaximize && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={onMaximize}
                  aria-label={isMaximized ? 'Minimize widget' : 'Maximize widget'}
                  aria-pressed={isMaximized}
                >
                  {isMaximized ? (
                    <Minimize2 className="h-3 w-3" aria-hidden="true" />
                  ) : (
                    <Maximize2 className="h-3 w-3" aria-hidden="true" />
                  )}
                </Button>
              )}
              
              {onToggle && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={onToggle}
                  aria-label={isCollapsed ? 'Expand widget' : 'Collapse widget'}
                  aria-expanded={!isCollapsed}
                  aria-controls={`widget-content-${id}`}
                >
                  {isCollapsed ? (
                    <ChevronRight className="h-3 w-3" aria-hidden="true" />
                  ) : (
                    <ChevronDown className="h-3 w-3" aria-hidden="true" />
                  )}
                </Button>
              )}
              
              {isDismissible && onDismiss && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={onDismiss}
                  aria-label="Dismiss widget"
                >
                  <X className="h-3 w-3" aria-hidden="true" />
                </Button>
              )}
            </div>
          </div>
          
          {ariaDescription && (
            <div 
              id={`widget-desc-${id}`}
              className="sr-only"
            >
              {ariaDescription}
            </div>
          )}
        </CardHeader>
        
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              id={`widget-content-${id}`}
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2, ease: 'easeOut' }}
              role="tabpanel"
              aria-labelledby={`widget-title-${id}`}
            >
              <CardContent className="pt-0">
                {content}
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
      
      {/* Screen reader announcements */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {isCollapsed ? 'Widget collapsed' : 'Widget expanded'}
        {isPinned ? 'Widget pinned' : ''}
        {isMaximized ? 'Widget maximized' : ''}
      </div>
    </div>
  );
};

// Accessible grid layout with keyboard navigation
interface AccessibleGridLayoutProps {
  children: React.ReactNode;
  columns?: number;
  gap?: string;
  className?: string;
  ariaLabel?: string;
  onNavigate?: (direction: 'up' | 'down' | 'left' | 'right') => void;
}

export const AccessibleGridLayout: React.FC<AccessibleGridLayoutProps> = ({
  children,
  columns = 3,
  gap = '1rem',
  className,
  ariaLabel = 'Widget grid',
  onNavigate
}) => {
  const gridRef = useRef<HTMLDivElement>(null);
  const [focusedIndex, setFocusedIndex] = useState(0);
  
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!gridRef.current) return;
    
    const items = Array.from(gridRef.current.children) as HTMLElement[];
    const currentIndex = focusedIndex;
    let newIndex = currentIndex;
    
    switch (e.key) {
      case 'ArrowRight':
        e.preventDefault();
        newIndex = Math.min(currentIndex + 1, items.length - 1);
        onNavigate?.('right');
        break;
      case 'ArrowLeft':
        e.preventDefault();
        newIndex = Math.max(currentIndex - 1, 0);
        onNavigate?.('left');
        break;
      case 'ArrowDown':
        e.preventDefault();
        newIndex = Math.min(currentIndex + columns, items.length - 1);
        onNavigate?.('down');
        break;
      case 'ArrowUp':
        e.preventDefault();
        newIndex = Math.max(currentIndex - columns, 0);
        onNavigate?.('up');
        break;
      case 'Home':
        e.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        e.preventDefault();
        newIndex = items.length - 1;
        break;
    }
    
    if (newIndex !== currentIndex) {
      setFocusedIndex(newIndex);
      const targetElement = items[newIndex]?.querySelector('[tabindex="0"]') as HTMLElement;
      targetElement?.focus();
    }
  }, [focusedIndex, columns, onNavigate]);
  
  return (
    <div
      ref={gridRef}
      className={cn(
        'accessible-grid-layout',
        'grid',
        className
      )}
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap
      }}
      role="grid"
      aria-label={ariaLabel}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      {React.Children.map(children, (child, index) => (
        <div
          role="gridcell"
          tabIndex={index === focusedIndex ? 0 : -1}
          aria-posinset={index + 1}
          aria-setsize={React.Children.count(children)}
        >
          {child}
        </div>
      ))}
    </div>
  );
};

// Skip link component for keyboard navigation
interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

export const SkipLink: React.FC<SkipLinkProps> = ({
  href,
  children,
  className
}) => {
  return (
    <a
      href={href}
      className={cn(
        'skip-link',
        'sr-only focus:not-sr-only',
        'fixed top-4 left-4 z-50',
        'bg-blue-600 text-white px-4 py-2 rounded',
        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
        className
      )}
    >
      {children}
    </a>
  );
};

// Accessible announcement component for screen readers
interface AnnouncementProps {
  message: string;
  priority?: 'polite' | 'assertive';
  id?: string;
}

export const Announcement: React.FC<AnnouncementProps> = ({
  message,
  priority = 'polite',
  id
}) => {
  return (
    <div
      id={id}
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
    >
      {message}
    </div>
  );
};

// High contrast mode detector
export const useHighContrastMode = () => {
  const [isHighContrast, setIsHighContrast] = useState(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setIsHighContrast(mediaQuery.matches);
    
    const handleChange = (e: MediaQueryListEvent) => {
      setIsHighContrast(e.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);
  
  return isHighContrast;
};

// Reduced motion detector
export const useReducedMotionPreference = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);
    
    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);
  
  return prefersReducedMotion;
};

export {
  useFocusManagement,
  useKeyboardNavigation
};
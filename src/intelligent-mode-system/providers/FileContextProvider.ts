import { FileContext } from '../types';
import { BaseProvider } from './BaseProvider';
import { memoryCache } from '../core/CacheSystem';

export class FileContextProvider extends BaseProvider<FileContext> {
  id = 'file-context';
  priority = 100;

  private currentFile: string = '';
  private fileWatcher: any = null; // Would use file system watcher in real implementation

  async analyzeInternal(): Promise<FileContext> {
    // In a real implementation, this would:
    // 1. Get current active file from editor
    // 2. Analyze file content for errors/warnings
    // 3. Extract symbols and imports
    // 4. Check file type and language

    // For now, return mock data based on current file
    const filePath = this.currentFile || 'src/index.ts';
    console.log(`FileContextProvider: analyzeInternal called, currentFile: ${this.currentFile}, using filePath: ${filePath}`);
    const fileContext: FileContext = {
      path: filePath,
      type: this.getFileType(filePath),
      language: this.getLanguage(filePath),
      hasErrors: false,
      hasWarnings: false,
      symbols: [],
      imports: [],
      lastModified: Date.now(),
    };

    // Simulate error detection
    if (filePath.includes('error') || filePath.includes('bug')) {
      fileContext.hasErrors = true;
    }

    // Simulate different file types - use filePath instead of this.currentFile
    if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
      fileContext.language = 'typescript';
      fileContext.imports = ['react', '@/types', './components'];
      fileContext.symbols = [
        { name: 'App', kind: 'function', line: 10, column: 0 },
        { name: 'useState', kind: 'function', line: 15, column: 8 },
      ];
    }

    return fileContext;
  }

  setCurrentFile(path: string): void {
    const oldFile = this.currentFile;
    this.currentFile = path;
    console.log(`FileContextProvider: setCurrentFile called with path: ${path}, old: ${oldFile}`);
    
    if (oldFile !== path) {
      // Clear the cache to ensure fresh analysis
      memoryCache.invalidate(`provider.${this.id}`).catch(console.error);
      // Trigger re-analysis on file change
      this.analyze().catch(console.error);
    }
  }

  private getFileType(path: string): string {
    if (!path) return 'unknown';
    
    const extension = path.split('.').pop()?.toLowerCase();
    const typeMap: Record<string, string> = {
      ts: 'typescript',
      tsx: 'typescript-react',
      js: 'javascript',
      jsx: 'javascript-react',
      py: 'python',
      rs: 'rust',
      go: 'go',
      java: 'java',
      cpp: 'cpp',
      c: 'c',
      md: 'markdown',
      json: 'json',
      yaml: 'yaml',
      yml: 'yaml',
      html: 'html',
      css: 'css',
      scss: 'scss',
    };

    return typeMap[extension || ''] || 'text';
  }

  private getLanguage(path: string): string | undefined {
    const type = this.getFileType(path);
    const languageMap: Record<string, string> = {
      'typescript': 'typescript',
      'typescript-react': 'typescript',
      'javascript': 'javascript',
      'javascript-react': 'javascript',
      'python': 'python',
      'rust': 'rust',
      'go': 'go',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
    };

    return languageMap[type];
  }

  async detectErrors(content: string): Promise<{ errors: number; warnings: number }> {
    // In a real implementation, this would use language servers
    // For now, simple pattern matching
    let errors = 0;
    let warnings = 0;

    // Simple error patterns
    if (content.includes('undefined is not')) errors++;
    if (content.includes('Cannot read property')) errors++;
    if (content.includes('SyntaxError')) errors++;
    
    // Simple warning patterns
    if (content.includes('console.log')) warnings++;
    if (content.includes('// TODO')) warnings++;
    if (content.includes('deprecated')) warnings++;

    return { errors, warnings };
  }
}
import { Con<PERSON><PERSON><PERSON>ider, ContextSnapshot } from '../../types/context.types';
import { ModeContextProvider, ModeContextData, ModeSignal } from '../../types/mode-features.types';

export class DebugContextProvider implements Context<PERSON><PERSON><PERSON>, ModeContextProvider {
  id = 'debug-context-provider';
  name = 'DebugContextProvider';
  modeId = 'debug';
  weight = 0.9; // High weight for error detection
  priority = 85;

  async analyze(context: ContextSnapshot): Promise<ModeContextData> {
    const signals: ModeSignal[] = [];
    let confidence = 0;

    // Check for errors in current file
    if (context.fileContext.hasErrors) {
      signals.push({
        type: 'file-errors',
        strength: 1.0,
        source: 'file-analysis',
        description: `File contains ${context.fileContext.errorCount || 'multiple'} errors`,
      });
      confidence += 0.4;
    }

    // Check for debug-related actions
    if (this.hasDebugActions(context)) {
      signals.push({
        type: 'debug-actions',
        strength: 0.9,
        source: 'user-behavior',
        description: 'Recent debugging activities detected',
      });
      confidence += 0.3;
    }

    // Check for test file context
    if (context.fileContext.isTest) {
      signals.push({
        type: 'test-file',
        strength: 0.7,
        source: 'file-type',
        description: 'Working with test files',
      });
      confidence += 0.2;
    }

    // Check for console/logging activity
    const loggingActivity = this.analyzeLoggingActivity(context);
    if (loggingActivity > 0.5) {
      signals.push({
        type: 'logging-activity',
        strength: loggingActivity,
        source: 'action-analysis',
        description: 'High console/logging activity',
      });
      confidence += 0.2;
    }

    // Check for error patterns in git status
    if (this.hasRecentErrors(context)) {
      signals.push({
        type: 'recent-errors',
        strength: 0.8,
        source: 'git-analysis',
        description: 'Recent error-related commits or changes',
      });
      confidence += 0.1;
    }

    // Generate recommendations
    const recommendations = this.generateRecommendations(signals, context);

    return {
      confidence: Math.min(confidence, 1.0),
      signals,
      recommendations,
    };
  }

  getContext(): Record<string, any> {
    return {
      activeBreakpoints: this.getActiveBreakpoints(),
      errorPatterns: this.detectErrorPatterns(),
      performanceMetrics: this.getPerformanceMetrics(),
    };
  }

  startWatching(): void {
    // Watch for error events and debugging activity
    console.log('Debug context provider started watching');
  }

  stopWatching(): void {
    // Stop watching
    console.log('Debug context provider stopped watching');
  }

  private hasDebugActions(context: ContextSnapshot): boolean {
    const debugKeywords = ['debug', 'breakpoint', 'error', 'exception', 'trace', 'inspect'];
    return context.userContext.recentActions.some(action =>
      action.type === 'debug' ||
      debugKeywords.some(keyword => 
        action.target?.toLowerCase().includes(keyword) ||
        action.metadata?.description?.toLowerCase().includes(keyword)
      )
    );
  }

  private analyzeLoggingActivity(context: ContextSnapshot): number {
    const consoleActions = context.userContext.recentActions.filter(action =>
      action.target?.includes('console') ||
      action.target?.includes('log') ||
      action.type === 'command'
    );
    
    const totalActions = context.userContext.recentActions.length;
    if (totalActions === 0) return 0;
    
    const ratio = consoleActions.length / totalActions;
    return Math.min(ratio * 2, 1.0); // Scale up the ratio
  }

  private hasRecentErrors(context: ContextSnapshot): boolean {
    const gitMessage = context.environmentContext.gitStatus?.lastCommitMessage || '';
    const errorKeywords = ['fix', 'error', 'bug', 'issue', 'crash', 'exception'];
    return errorKeywords.some(keyword => gitMessage.toLowerCase().includes(keyword));
  }

  private generateRecommendations(signals: ModeSignal[], context: ContextSnapshot): string[] {
    const recommendations: string[] = [];

    if (signals.some(s => s.type === 'file-errors')) {
      recommendations.push('Enable real-time error detection for immediate feedback');
      recommendations.push('Use stack trace enhancement for better error understanding');
      if (context.fileContext.errorCount && context.fileContext.errorCount > 3) {
        recommendations.push('Consider running automated error pattern analysis');
      }
    }

    if (signals.some(s => s.type === 'logging-activity')) {
      recommendations.push('Use the variable inspector to examine values in real-time');
      recommendations.push('Set up conditional breakpoints for targeted debugging');
    }

    if (signals.some(s => s.type === 'test-file')) {
      recommendations.push('Enable test debugging features for better test insight');
      recommendations.push('Use performance profiler to identify slow tests');
    }

    if (signals.some(s => s.type === 'recent-errors')) {
      recommendations.push('Review recent error patterns to prevent recurrence');
      recommendations.push('Consider setting up error monitoring dashboards');
    }

    return recommendations;
  }

  private getActiveBreakpoints(): any[] {
    // Simulated breakpoint data
    return [
      { file: 'app.ts', line: 42, condition: null, enabled: true },
      { file: 'utils.ts', line: 15, condition: 'value > 100', enabled: true },
    ];
  }

  private detectErrorPatterns(): string[] {
    // Simulated error pattern detection
    return [
      'NullPointerException',
      'Async/Await misuse',
      'Memory leak pattern',
    ];
  }

  private getPerformanceMetrics(): Record<string, any> {
    // Simulated performance metrics
    return {
      cpuUsage: 45,
      memoryUsage: 67,
      responseTime: 234,
      errorRate: 0.02,
    };
  }

  // Stub method for ContextProvider interface compatibility
  subscribe(listener: (data: any) => void): () => void {
    // Mode context providers don't need real-time updates
    return () => {};
  }
}

export const debugContextProvider = new DebugContextProvider();
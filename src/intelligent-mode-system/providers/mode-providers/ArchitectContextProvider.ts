import { Context<PERSON><PERSON>ider, ContextSnapshot } from '../../types/context.types';
import { ModeContextProvider, ModeContextData, ModeSignal } from '../../types/mode-features.types';

export class ArchitectContextProvider implements ContextP<PERSON>ider, ModeContextProvider {
  id = 'architect-context-provider';
  name = 'ArchitectContextProvider';
  modeId = 'architect';
  weight = 0.8;
  priority = 80;

  async analyze(context: ContextSnapshot): Promise<ModeContextData> {
    const signals: ModeSignal[] = [];
    let confidence = 0;

    // Check for architecture files
    if (this.isArchitectureFile(context.fileContext.path || '')) {
      signals.push({
        type: 'architecture-file',
        strength: 0.9,
        source: 'file-pattern',
        description: 'Working with architecture documentation or diagrams',
      });
      confidence += 0.3;
    }

    // Check for large project structure
    if (this.isLargeProject(context)) {
      signals.push({
        type: 'large-project',
        strength: 0.7,
        source: 'project-analysis',
        description: 'Large project requiring architectural oversight',
      });
      confidence += 0.2;
    }

    // Check for dependency complexity
    const depComplexity = this.analyzeDependencyComplexity(context);
    if (depComplexity > 0.6) {
      signals.push({
        type: 'complex-dependencies',
        strength: depComplexity,
        source: 'dependency-analysis',
        description: 'Complex dependency structure requiring management',
      });
      confidence += 0.25;
    }

    // Check for architectural patterns in recent actions
    if (this.hasArchitecturalActions(context)) {
      signals.push({
        type: 'architectural-actions',
        strength: 0.8,
        source: 'user-behavior',
        description: 'Recent actions indicate architectural work',
      });
      confidence += 0.25;
    }

    // Generate recommendations
    const recommendations = this.generateRecommendations(signals, context);

    return {
      confidence: Math.min(confidence, 1.0),
      signals,
      recommendations,
    };
  }

  getContext(): Record<string, any> {
    return {
      architecturalPatterns: this.detectPatterns(),
      componentStructure: this.analyzeComponentStructure(),
      dependencyGraph: this.buildDependencyGraph(),
    };
  }

  startWatching(): void {
    // Watch for changes in project structure
    console.log('Architect context provider started watching');
  }

  stopWatching(): void {
    // Stop watching
    console.log('Architect context provider stopped watching');
  }

  private isArchitectureFile(path: string): boolean {
    const archPatterns = [
      'architecture',
      'design',
      'structure',
      'diagram',
      '.puml',
      '.drawio',
      'components',
      'modules',
    ];
    const lowerPath = path.toLowerCase();
    return archPatterns.some(pattern => lowerPath.includes(pattern));
  }

  private isLargeProject(context: ContextSnapshot): boolean {
    const deps = Object.keys(context.projectContext.dependencies || {});
    const hasComplexStructure = context.projectContext.type === 'monorepo' || 
                                context.projectContext.type === 'enterprise';
    return deps.length > 20 || hasComplexStructure;
  }

  private analyzeDependencyComplexity(context: ContextSnapshot): number {
    const deps = context.projectContext.dependencies || {};
    const depCount = Object.keys(deps).length;
    
    // Simple complexity calculation
    if (depCount < 10) return 0.2;
    if (depCount < 20) return 0.4;
    if (depCount < 50) return 0.6;
    if (depCount < 100) return 0.8;
    return 1.0;
  }

  private hasArchitecturalActions(context: ContextSnapshot): boolean {
    const archKeywords = ['component', 'module', 'service', 'layer', 'pattern', 'structure'];
    return context.userContext.recentActions.some(action =>
      archKeywords.some(keyword => 
        action.target?.toLowerCase().includes(keyword) ||
        action.metadata?.description?.toLowerCase().includes(keyword)
      )
    );
  }

  private generateRecommendations(signals: ModeSignal[], context: ContextSnapshot): string[] {
    const recommendations: string[] = [];

    if (signals.some(s => s.type === 'complex-dependencies')) {
      recommendations.push('Consider using the dependency analyzer to identify circular dependencies');
      recommendations.push('Review dependency graph for optimization opportunities');
    }

    if (signals.some(s => s.type === 'large-project')) {
      recommendations.push('Use component mapper to visualize project structure');
      recommendations.push('Consider implementing architectural patterns like microservices or modular monolith');
    }

    if (signals.some(s => s.type === 'architecture-file')) {
      recommendations.push('Enable system designer for visual architecture modeling');
      recommendations.push('Export diagrams in multiple formats for documentation');
    }

    return recommendations;
  }

  private detectPatterns(): string[] {
    // Simplified pattern detection
    return ['MVC', 'Layered Architecture', 'Event-Driven'];
  }

  private analyzeComponentStructure(): Record<string, any> {
    // Simplified component analysis
    return {
      layers: ['presentation', 'business', 'data'],
      components: 15,
      connections: 23,
    };
  }

  private buildDependencyGraph(): Record<string, string[]> {
    // Simplified dependency graph
    return {
      'ui-components': ['utils', 'api-client'],
      'api-client': ['utils', 'types'],
      'business-logic': ['api-client', 'utils', 'types'],
    };
  }

  // Stub method for ContextProvider interface compatibility
  subscribe(listener: (data: any) => void): () => void {
    // Mode context providers don't need real-time updates
    return () => {};
  }
}

export const architectContextProvider = new ArchitectContextProvider();
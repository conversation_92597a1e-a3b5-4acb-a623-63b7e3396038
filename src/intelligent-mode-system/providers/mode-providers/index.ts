// Export all mode-specific context providers
export { architectContextProvider } from './ArchitectContextProvider';
export { debugContextProvider } from './DebugContextProvider';
export { reviewContextProvider } from './ReviewContextProvider';
export { deployContextProvider } from './DeployContextProvider';

// Import all providers
import { architectContextProvider } from './ArchitectContextProvider';
import { debugContextProvider } from './DebugContextProvider';
import { reviewContextProvider } from './ReviewContextProvider';
import { deployContextProvider } from './DeployContextProvider';

// Export as array for easy registration
export const modeContextProviders = [
  architectContextProvider,
  debugContextProvider,
  reviewContextProvider,
  deployContextProvider,
];

// Export as map for easy access
export const modeContextProviderMap = {
  architect: architectContextProvider,
  debug: debugContextProvider,
  review: reviewContextProvider,
  deploy: deployContextProvider,
} as const;
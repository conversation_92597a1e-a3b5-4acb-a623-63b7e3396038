export { BaseProvider } from './BaseProvider';
export { FileContextProvider } from './FileContextProvider';
export { ProjectContextProvider } from './ProjectContextProvider';
export { GitContextProvider } from './GitContextProvider';
export { UserContextProvider } from './UserContextProvider';

// Export mode-specific providers
export * from './mode-providers';

import { FileContextProvider } from './FileContextProvider';
import { ProjectContextProvider } from './ProjectContextProvider';
import { GitContextProvider } from './GitContextProvider';
import { UserContextProvider } from './UserContextProvider';
import { modeContextProviders } from './mode-providers';
import { contextInferenceEngine } from '../core/ContextInferenceEngine';

// Create singleton instances
export const fileContextProvider = new FileContextProvider();
export const projectContextProvider = new ProjectContextProvider();
export const gitContextProvider = new GitContextProvider();
export const userContextProvider = new UserContextProvider();

// Register all providers with the inference engine
export function registerAllProviders(): void {
  // Register core context providers (these extend BaseProvider and have subscribe)
  contextInferenceEngine.registerProvider(fileContextProvider);
  contextInferenceEngine.registerProvider(projectContextProvider);
  contextInferenceEngine.registerProvider(gitContextProvider);
  contextInferenceEngine.registerProvider(userContextProvider);
  
  // Mode-specific context providers are not registered here
  // They are used differently - they analyze existing context rather than generating it
}
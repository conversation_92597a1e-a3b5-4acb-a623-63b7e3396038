import { UserContext, UserAction, UserPattern, UserPreferences } from '../types';
import { BaseProvider } from './BaseProvider';
import { memoryCache } from '../core/CacheSystem';

export class UserContextProvider extends BaseProvider<UserContext> {
  id = 'user-context';
  priority = 70;

  private actions: UserAction[] = [];
  private patterns: Map<string, UserPattern> = new Map();
  private sessionStartTime = Date.now();
  private preferences: UserPreferences = this.getDefaultPreferences();

  async analyzeInternal(): Promise<UserContext> {
    // Analyze recent actions for patterns
    this.detectPatterns();

    const userContext: UserContext = {
      recentActions: this.actions.slice(-20), // Last 20 actions
      preferences: this.preferences,
      patterns: Array.from(this.patterns.values()),
      sessionDuration: Date.now() - this.sessionStartTime,
      lastActivity: this.actions.length > 0 
        ? this.actions[this.actions.length - 1].timestamp 
        : Date.now(),
    };

    return userContext;
  }

  recordAction(action: Omit<UserAction, 'timestamp'>): void {
    const fullAction: UserAction = {
      ...action,
      timestamp: Date.now(),
    };

    this.actions.push(fullAction);

    // Keep only last 100 actions
    if (this.actions.length > 100) {
      this.actions = this.actions.slice(-100);
    }

    // Trigger analysis on significant actions
    if (this.isSignificantAction(fullAction)) {
      this.analyze().catch(console.error);
    }
  }

  updatePreferences(updates: Partial<UserPreferences>): void {
    this.preferences = {
      ...this.preferences,
      ...updates,
    };

    // Persist preferences
    memoryCache.set('user.preferences', this.preferences).catch(console.error);

    // Notify listeners
    this.analyze().catch(console.error);
  }

  async loadPreferences(): Promise<void> {
    const saved = await memoryCache.get<UserPreferences>('user.preferences');
    if (saved) {
      this.preferences = saved;
    }
  }

  private detectPatterns(): void {
    // Simple pattern detection based on action sequences
    
    // Detect debugging pattern
    const debugActions = this.actions.filter(a => 
      a.type === 'debug' || 
      (a.type === 'edit' && a.target?.includes('console.log'))
    );
    
    if (debugActions.length >= 2) {
      this.updatePattern('debug-workflow', 'workflow', debugActions.length / this.actions.length);
    }

    // Detect rapid editing pattern
    const recentEdits = this.actions.slice(-10).filter(a => a.type === 'edit');
    if (recentEdits.length > 7) {
      this.updatePattern('rapid-editing', 'editing', 0.8);
    }

    // Detect file navigation pattern
    const navigationActions = this.actions.filter(a => a.type === 'navigate');
    const uniqueFiles = new Set(navigationActions.map(a => a.target));
    
    if (uniqueFiles.size > 10 && navigationActions.length > 15) {
      this.updatePattern('explorer-pattern', 'navigation', 0.7);
    }

    // Detect search-heavy pattern
    const searchActions = this.actions.filter(a => a.type === 'search');
    if (searchActions.length > 5) {
      this.updatePattern('search-heavy', 'workflow', searchActions.length / this.actions.length);
    }
  }

  private updatePattern(id: string, type: UserPattern['type'], confidence: number): void {
    const existing = this.patterns.get(id);
    
    if (existing) {
      existing.frequency++;
      existing.confidence = (existing.confidence + confidence) / 2;
      existing.lastSeen = Date.now();
    } else {
      this.patterns.set(id, {
        id,
        type,
        frequency: 1,
        lastSeen: Date.now(),
        confidence: Math.min(1, confidence),
      });
    }
  }

  private isSignificantAction(action: UserAction): boolean {
    // Define what constitutes a significant action that should trigger analysis
    const significantTypes: UserAction['type'][] = ['debug', 'command'];
    
    if (significantTypes.includes(action.type)) {
      return true;
    }

    // Switching files is significant
    if (action.type === 'navigate') {
      const lastNavigate = this.actions
        .slice(0, -1)
        .reverse()
        .find(a => a.type === 'navigate');
      
      return !lastNavigate || lastNavigate.target !== action.target;
    }

    return false;
  }

  clearActions(): void {
    this.actions = [];
    this.patterns.clear();
  }

  private getDefaultPreferences(): UserPreferences {
    return {
      theme: 'dark',
      shortcuts: {
        'switchMode': 'cmd+shift+m',
        'openPalette': 'cmd+p',
        'quickSearch': 'cmd+k',
      },
      defaultMode: 'architect',
      autoTransition: true,
      suggestionLevel: 'standard',
    };
  }

  getActionHistory(type?: UserAction['type'], limit?: number): UserAction[] {
    let actions = this.actions;
    
    if (type) {
      actions = actions.filter(a => a.type === type);
    }

    if (limit) {
      actions = actions.slice(-limit);
    }

    return actions;
  }

  getMostUsedFiles(limit: number = 10): Array<{ file: string; count: number }> {
    const fileCounts = new Map<string, number>();
    
    this.actions
      .filter(a => a.type === 'navigate' && a.target)
      .forEach(action => {
        const file = action.target!;
        fileCounts.set(file, (fileCounts.get(file) || 0) + 1);
      });

    return Array.from(fileCounts.entries())
      .map(([file, count]) => ({ file, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  getActivityLevel(): 'low' | 'medium' | 'high' {
    const recentActions = this.actions.filter(
      a => Date.now() - a.timestamp < 5 * 60 * 1000 // Last 5 minutes
    );

    if (recentActions.length < 5) return 'low';
    if (recentActions.length < 20) return 'medium';
    return 'high';
  }
}
import { ContextProvider } from '../core/ContextInferenceEngine';
import { eventBus } from '../core/EventBus';
import { memoryCache } from '../core/CacheSystem';

export abstract class BaseProvider<T> implements ContextProvider<T> {
  abstract id: string;
  abstract priority: number;
  cache = true;
  
  private listeners: Set<(data: T) => void> = new Set();
  private lastData: T | null = null;
  private updateInterval: NodeJS.Timeout | null = null;

  abstract analyzeInternal(): Promise<T>;

  async analyze(): Promise<T> {
    // Check cache if enabled
    if (this.cache) {
      const cached = await memoryCache.get<T>(`provider.${this.id}`);
      if (cached) {
        return cached;
      }
    }

    // Perform analysis
    const data = await this.analyzeInternal();
    
    // Cache result
    if (this.cache) {
      await memoryCache.set(`provider.${this.id}`, data, 5000); // 5 second TTL
    }

    // Check for changes
    if (this.hasChanged(data, this.lastData)) {
      this.lastData = data;
      this.notifyListeners(data);
    }

    return data;
  }

  subscribe(listener: (data: T) => void): () => void {
    this.listeners.add(listener);
    
    // Send current data if available
    if (this.lastData) {
      listener(this.lastData);
    }

    return () => {
      this.listeners.delete(listener);
    };
  }

  startWatching(intervalMs: number = 1000): void {
    if (this.updateInterval) {
      this.stopWatching();
    }

    this.updateInterval = setInterval(async () => {
      try {
        await this.analyze();
      } catch (error) {
        console.error(`Provider ${this.id} watch error:`, error);
      }
    }, intervalMs);
  }

  stopWatching(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  protected notifyListeners(data: T): void {
    this.listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error(`Provider ${this.id} listener error:`, error);
      }
    });

    // Emit event
    eventBus.emit({
      type: 'context.update',
      payload: { provider: this.id, data },
      source: `Provider.${this.id}`,
    });
  }

  protected hasChanged(newData: T, oldData: T | null): boolean {
    if (!oldData) return true;
    // Simple deep equality check - override for better performance
    return JSON.stringify(newData) !== JSON.stringify(oldData);
  }
}
import { EnvironmentContext, GitStatus, Process } from '../types';
import { BaseProvider } from './BaseProvider';

export class GitContextProvider extends BaseProvider<Partial<EnvironmentContext>> {
  id = 'git-context';
  priority = 80;
  cache = false; // Git status changes frequently

  async analyzeInternal(): Promise<Partial<EnvironmentContext>> {
    const gitStatus = await this.getGitStatus();
    const runningProcesses = await this.getRunningProcesses();
    
    return {
      gitStatus,
      runningProcesses,
    };
  }

  private async getGitStatus(): Promise<GitStatus | undefined> {
    // In real implementation, would use git commands or library
    // For now, return mock data

    const mockScenarios = [
      // Clean state
      {
        branch: 'main',
        ahead: 0,
        behind: 0,
        modified: [],
        staged: [],
        untracked: [],
        hasConflicts: false,
      },
      // Active development
      {
        branch: 'feature/intelligent-modes',
        ahead: 3,
        behind: 0,
        modified: ['src/modes/architect.ts', 'src/components/ModeSelector.tsx'],
        staged: ['src/types/mode.types.ts'],
        untracked: ['src/test.ts'],
        hasConflicts: false,
      },
      // Merge conflict scenario
      {
        branch: 'feature/merge-conflict',
        ahead: 2,
        behind: 5,
        modified: ['src/index.ts'],
        staged: [],
        untracked: [],
        hasConflicts: true,
      },
    ];

    // Randomly select a scenario for demonstration
    const scenario = mockScenarios[Math.floor(Math.random() * mockScenarios.length)];
    
    // Always return the active development scenario for consistency
    return {
      branch: 'feature/intelligent-modes',
      ahead: 3,
      behind: 0,
      modified: ['src/modes/architect.ts', 'src/components/ModeSelector.tsx'],
      staged: ['src/types/mode.types.ts'],
      untracked: ['src/test.ts'],
      hasConflicts: false,
    };
  }

  private async getRunningProcesses(): Promise<Process[]> {
    // In real implementation, would check system processes
    // For now, return mock data
    
    const processes: Process[] = [
      {
        name: 'vite',
        pid: 12345,
        type: 'server',
        port: 5173,
      },
      {
        name: 'typescript',
        pid: 12346,
        type: 'build',
      },
    ];

    // Randomly add test or debug processes
    if (Math.random() > 0.5) {
      processes.push({
        name: 'jest',
        pid: 12347,
        type: 'test',
      });
    }

    return processes;
  }

  async getCurrentBranch(): Promise<string> {
    const status = await this.getGitStatus();
    return status?.branch || 'main';
  }

  async hasUncommittedChanges(): Promise<boolean> {
    const status = await this.getGitStatus();
    if (!status) return false;
    
    return status.modified.length > 0 || 
           status.staged.length > 0 || 
           status.untracked.length > 0;
  }

  async isInConflict(): Promise<boolean> {
    const status = await this.getGitStatus();
    return status?.hasConflicts || false;
  }

  protected hasChanged(newData: Partial<EnvironmentContext>, oldData: Partial<EnvironmentContext> | null): boolean {
    if (!oldData || !oldData.gitStatus || !newData.gitStatus) {
      return true;
    }

    const oldGit = oldData.gitStatus;
    const newGit = newData.gitStatus;

    // Check for significant changes
    return (
      oldGit.branch !== newGit.branch ||
      oldGit.hasConflicts !== newGit.hasConflicts ||
      oldGit.modified.length !== newGit.modified.length ||
      oldGit.staged.length !== newGit.staged.length ||
      JSON.stringify(oldGit.modified) !== JSON.stringify(newGit.modified)
    );
  }
}
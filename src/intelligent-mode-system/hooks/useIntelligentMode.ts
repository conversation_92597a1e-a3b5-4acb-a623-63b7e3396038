import { useState, useEffect, useCallback } from 'react';
import { Mode, ModeId, ModeProbabilities, ContextSnapshot } from '../types';
import { intelligentModeSystem } from '../index';
import { modeRegistry } from '../core/ModeRegistry';
import { contextInferenceEngine } from '../core/ContextInferenceEngine';
import { eventBus } from '../core/EventBus';

export interface UseIntelligentModeReturn {
  // Current state
  currentMode: Mode | null;
  recommendedMode: ModeId | null;
  modeProbabilities: ModeProbabilities;
  isTransitioning: boolean;
  
  // Actions
  switchMode: (modeId: ModeId) => Promise<void>;
  recordAction: (action: any) => void;
  
  // Mode information
  availableModes: Mode[];
  getModeById: (modeId: ModeId) => Mode | null;
  
  // Context
  context: ContextSnapshot | null;
  refreshContext: () => Promise<void>;
}

export function useIntelligentMode(): UseIntelligentModeReturn {
  const [currentMode, setCurrentMode] = useState<Mode | null>(null);
  const [recommendedMode, setRecommendedMode] = useState<ModeId | null>(null);
  const [modeProbabilities, setModeProbabilities] = useState<ModeProbabilities>({});
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [availableModes, setAvailableModes] = useState<Mode[]>([]);
  const [context, setContext] = useState<ContextSnapshot | null>(null);

  // Initialize system on mount
  useEffect(() => {
    const initializeSystem = async () => {
      try {
        await intelligentModeSystem.initialize();
        
        // Set initial state
        setCurrentMode(modeRegistry.getActiveMode());
        setAvailableModes(modeRegistry.getAllModes());
        
        // Get initial context and recommendations
        const initialContext = await contextInferenceEngine.analyze();
        setContext(initialContext);
        setRecommendedMode(contextInferenceEngine.getRecommendedMode(initialContext));
        setModeProbabilities(contextInferenceEngine.getProbabilities(initialContext));
      } catch (error) {
        console.error('Failed to initialize Intelligent Mode System:', error);
      }
    };

    initializeSystem();

    // Set up event listeners
    const unsubscribeMode = eventBus.on('mode.transition.complete', () => {
      setCurrentMode(modeRegistry.getActiveMode());
      setIsTransitioning(false);
    });

    const unsubscribeTransitionStart = eventBus.on('mode.transition.start', () => {
      setIsTransitioning(true);
    });

    const unsubscribeContext = contextInferenceEngine.subscribe((snapshot) => {
      setContext(snapshot);
      setRecommendedMode(contextInferenceEngine.getRecommendedMode(snapshot));
      setModeProbabilities(contextInferenceEngine.getProbabilities(snapshot));
    });

    // Cleanup
    return () => {
      unsubscribeMode();
      unsubscribeTransitionStart();
      unsubscribeContext();
    };
  }, []);

  const switchMode = useCallback(async (modeId: ModeId) => {
    try {
      setIsTransitioning(true);
      await intelligentModeSystem.switchMode(modeId);
    } catch (error) {
      console.error('Failed to switch mode:', error);
      setIsTransitioning(false);
    }
  }, []);

  const recordAction = useCallback((action: any) => {
    intelligentModeSystem.recordUserAction(action);
  }, []);

  const getModeById = useCallback((modeId: ModeId): Mode | null => {
    return modeRegistry.getMode(modeId);
  }, []);

  const refreshContext = useCallback(async () => {
    const newContext = await contextInferenceEngine.analyze();
    setContext(newContext);
    setRecommendedMode(contextInferenceEngine.getRecommendedMode(newContext));
    setModeProbabilities(contextInferenceEngine.getProbabilities(newContext));
  }, []);

  return {
    currentMode,
    recommendedMode,
    modeProbabilities,
    isTransitioning,
    switchMode,
    recordAction,
    availableModes,
    getModeById,
    context,
    refreshContext,
  };
}
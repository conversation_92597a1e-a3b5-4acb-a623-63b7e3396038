import { useState, useEffect, useCallback, useMemo } from 'react';
import { ContextSnapshot } from '../types';
import { Mode } from '../types/mode.types';
import { 
  SmartSuggestion, 
  PromptAnalytics, 
  SmartPromptConfig,
  defaultSmartPromptConfig 
} from '../types/smart-prompt.types';
import { enhancedContextInferenceEngine } from '../core/EnhancedContextInferenceEngine';
import { smartSuggestionEngine } from '../services/SmartSuggestionEngine';
import { contextHistoryTracker } from '../services/ContextHistoryTracker';
import { eventBus } from '../core/EventBus';

interface UseEnhancedSmartPromptOptions {
  context: ContextSnapshot;
  currentMode: Mode;
  config?: Partial<SmartPromptConfig>;
  onSubmit?: (prompt: string, metadata?: any) => void;
  enableMLFeatures?: boolean;
}

interface UseEnhancedSmartPromptReturn {
  // Core data
  suggestions: SmartSuggestion[];
  recentPrompts: string[];
  isLoading: boolean;
  analytics: PromptAnalytics[];
  config: SmartPromptConfig;
  
  // Enhanced features
  predictions: {
    likelyMode: string | null;
    likelyActions: string[];
    confidence: number;
  } | null;
  insights: Array<{
    type: 'productivity' | 'workflow' | 'preference' | 'issue';
    description: string;
    confidence: number;
    recommendations: string[];
  }>;
  modeRecommendation: {
    mode: string;
    confidence: number;
    reasoning: string[];
  } | null;
  
  // Actions
  submitPrompt: (prompt: string, metadata?: any) => void;
  addToRecent: (prompt: string) => void;
  clearRecent: () => void;
  updateConfig: (updates: Partial<SmartPromptConfig>) => void;
  trackUsage: (prompt: string, metadata?: any) => void;
  
  // Enhanced actions
  rateSuggestion: (suggestionId: string, rating: number) => void;
  dismissSuggestion: (suggestionId: string) => void;
  refreshSuggestions: () => Promise<void>;
  generateSuggestions: (input?: string) => Promise<SmartSuggestion[]>;
  filterSuggestions: (category?: string, query?: string) => SmartSuggestion[];
}

const STORAGE_KEYS = {
  RECENT_PROMPTS: 'enhancedSmartPrompt.recentPrompts',
  ANALYTICS: 'enhancedSmartPrompt.analytics',
  CONFIG: 'enhancedSmartPrompt.config',
} as const;

export const useEnhancedSmartPrompt = ({
  context,
  currentMode,
  config: userConfig = {},
  onSubmit,
  enableMLFeatures = true
}: UseEnhancedSmartPromptOptions): UseEnhancedSmartPromptReturn => {
  // Merge user config with defaults
  const config = useMemo(() => ({
    ...defaultSmartPromptConfig,
    ...userConfig
  }), [userConfig]);

  // State
  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([]);
  const [recentPrompts, setRecentPrompts] = useState<string[]>([]);
  const [analytics, setAnalytics] = useState<PromptAnalytics[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [predictions, setPredictions] = useState<UseEnhancedSmartPromptReturn['predictions']>(null);
  const [insights, setInsights] = useState<UseEnhancedSmartPromptReturn['insights']>([]);
  const [modeRecommendation, setModeRecommendation] = useState<UseEnhancedSmartPromptReturn['modeRecommendation']>(null);

  // Load persisted data on mount
  useEffect(() => {
    loadPersistedData();
  }, []);

  // Load ML-based insights and predictions
  useEffect(() => {
    if (enableMLFeatures) {
      loadMLFeatures();
    }
  }, [enableMLFeatures, context, currentMode]);

  // Generate suggestions when context or mode changes
  useEffect(() => {
    generateSuggestions();
  }, [context, currentMode, config]);

  // Save data when it changes
  useEffect(() => {
    saveToStorage(STORAGE_KEYS.RECENT_PROMPTS, recentPrompts);
  }, [recentPrompts]);

  useEffect(() => {
    if (config.analytics.enabled) {
      saveToStorage(STORAGE_KEYS.ANALYTICS, analytics);
    }
  }, [analytics, config.analytics.enabled]);

  const loadPersistedData = useCallback(() => {
    try {
      // Load recent prompts
      const storedRecent = loadFromStorage<string[]>(STORAGE_KEYS.RECENT_PROMPTS);
      if (storedRecent) {
        setRecentPrompts(storedRecent.slice(0, config.maxRecentPrompts));
      }

      // Load analytics
      if (config.analytics.enabled) {
        const storedAnalytics = loadFromStorage<PromptAnalytics[]>(STORAGE_KEYS.ANALYTICS);
        if (storedAnalytics) {
          setAnalytics(storedAnalytics.slice(-1000)); // Keep last 1000 entries
        }
      }
    } catch (error) {
      console.warn('Failed to load smart prompt data:', error);
    }
  }, [config.maxRecentPrompts, config.analytics.enabled]);

  const loadMLFeatures = useCallback(async () => {
    try {
      // Load predictions
      const prediction = contextHistoryTracker.predictNext(context);
      setPredictions(prediction);

      // Load insights
      const behaviorInsights = contextHistoryTracker.getInsights();
      setInsights(behaviorInsights);

      // Load mode recommendation
      const modeRec = await enhancedContextInferenceEngine.getRecommendedMode(context);
      setModeRecommendation(modeRec);
    } catch (error) {
      console.warn('Failed to load ML features:', error);
    }
  }, [context]);

  // Generate suggestions using enhanced engine
  const generateSuggestions = useCallback(async (input?: string): Promise<SmartSuggestion[]> => {
    setIsLoading(true);
    
    try {
      const newSuggestions = await enhancedContextInferenceEngine.getSmartSuggestions(input);
      setSuggestions(newSuggestions);
      return newSuggestions;
    } catch (error) {
      console.error('Failed to generate suggestions:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Refresh suggestions
  const refreshSuggestions = useCallback(async (): Promise<void> => {
    await generateSuggestions();
  }, [generateSuggestions]);

  // Filter suggestions
  const filterSuggestions = useCallback((category?: string, query?: string): SmartSuggestion[] => {
    return suggestions.filter(suggestion => {
      // Category filter
      if (category && category !== 'all' && suggestion.category !== category) {
        return false;
      }
      
      // Query filter
      if (query) {
        const lowerQuery = query.toLowerCase();
        return (
          suggestion.text.toLowerCase().includes(lowerQuery) ||
          suggestion.description?.toLowerCase().includes(lowerQuery) ||
          suggestion.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
        );
      }
      
      return true;
    });
  }, [suggestions]);

  // Submit prompt
  const submitPrompt = useCallback((prompt: string, metadata?: any) => {
    if (!prompt.trim()) return;

    // Add to recent prompts
    addToRecent(prompt);

    // Track usage
    if (config.analytics.trackUsage) {
      trackUsage(prompt, metadata);
    }

    // Call external handler
    onSubmit?.(prompt, metadata);

    // Emit event for ML learning
    eventBus.emit({
      type: 'prompt.submitted',
      payload: { prompt, metadata, context, mode: currentMode.id },
      source: 'useEnhancedSmartPrompt',
    });
  }, [config.analytics.trackUsage, onSubmit, context, currentMode]);

  // Add to recent prompts
  const addToRecent = useCallback((prompt: string) => {
    setRecentPrompts(prev => {
      const filtered = prev.filter(p => p !== prompt);
      return [prompt, ...filtered].slice(0, config.maxRecentPrompts);
    });
  }, [config.maxRecentPrompts]);

  // Clear recent prompts
  const clearRecent = useCallback(() => {
    setRecentPrompts([]);
    removeFromStorage(STORAGE_KEYS.RECENT_PROMPTS);
  }, []);

  // Update configuration
  const updateConfig = useCallback((updates: Partial<SmartPromptConfig>) => {
    // This would typically update a global config state or context
    console.log('Config update requested:', updates);
  }, []);

  // Track usage analytics
  const trackUsage = useCallback((prompt: string, metadata?: any) => {
    if (!config.analytics.enabled) return;

    const analyticsEntry: PromptAnalytics = {
      prompt,
      timestamp: Date.now(),
      mode: currentMode.id,
      context: {
        file: context.fileContext.path ? context.fileContext.path.split('/').pop() : undefined,
        project: context.projectContext.type,
        branch: context.environmentContext.gitStatus?.branch,
      },
      metadata,
    };

    setAnalytics(prev => [...prev.slice(-999), analyticsEntry]);
  }, [config.analytics.enabled, currentMode.id, context]);

  // Rate suggestion
  const rateSuggestion = useCallback((suggestionId: string, rating: number) => {
    if (!config.analytics.trackEffectiveness) return;

    // Use enhanced suggestion engine for rating
    smartSuggestionEngine.rateSuggestion(suggestionId, rating);

    // Emit event
    eventBus.emit({
      type: 'suggestion.rated',
      payload: { suggestionId, rating },
      source: 'useEnhancedSmartPrompt',
    });
  }, [config.analytics.trackEffectiveness]);

  // Dismiss suggestion
  const dismissSuggestion = useCallback((suggestionId: string) => {
    // Track dismissal with enhanced engine
    smartSuggestionEngine.trackSuggestionUsage(suggestionId, 'dismiss', context);

    // Remove from current suggestions
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));

    // Emit event
    eventBus.emit({
      type: 'suggestion.dismissed',
      payload: { suggestionId, context },
      source: 'useEnhancedSmartPrompt',
    });
  }, [context]);

  return {
    // Core data
    suggestions,
    recentPrompts,
    isLoading,
    analytics,
    config,
    
    // Enhanced features
    predictions,
    insights,
    modeRecommendation,
    
    // Actions
    submitPrompt,
    addToRecent,
    clearRecent,
    updateConfig,
    trackUsage,
    
    // Enhanced actions
    rateSuggestion,
    dismissSuggestion,
    refreshSuggestions,
    generateSuggestions,
    filterSuggestions,
  };
};

// Storage utilities
function saveToStorage<T>(key: string, data: T): void {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.warn(`Failed to save to storage (${key}):`, error);
  }
}

function loadFromStorage<T>(key: string): T | null {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : null;
  } catch (error) {
    console.warn(`Failed to load from storage (${key}):`, error);
    return null;
  }
}

function removeFromStorage(key: string): void {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.warn(`Failed to remove from storage (${key}):`, error);
  }
}

export default useEnhancedSmartPrompt;
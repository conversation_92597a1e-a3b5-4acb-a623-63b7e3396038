import { useState, useEffect, useCallback, useMemo } from 'react';
import { ContextSnapshot } from '../types';
import { Mode } from '../types/mode.types';
import { 
  SmartSuggestion, 
  PromptAnalytics, 
  SmartPromptConfig,
  defaultSmartPromptConfig 
} from '../types/smart-prompt.types';

interface UseSmartPromptOptions {
  context: ContextSnapshot;
  currentMode: Mode;
  config?: Partial<SmartPromptConfig>;
  onSubmit?: (prompt: string, metadata?: any) => void;
}

interface UseSmartPromptReturn {
  suggestions: SmartSuggestion[];
  recentPrompts: string[];
  isLoading: boolean;
  analytics: PromptAnalytics[];
  config: SmartPromptConfig;
  
  // Actions
  submitPrompt: (prompt: string, metadata?: any) => void;
  addToRecent: (prompt: string) => void;
  clearRecent: () => void;
  updateConfig: (updates: Partial<SmartPromptConfig>) => void;
  trackUsage: (prompt: string, metadata?: any) => void;
  
  // Suggestion management
  generateSuggestions: (input?: string) => SmartSuggestion[];
  filterSuggestions: (category?: string, query?: string) => SmartSuggestion[];
  rateSuggestion: (suggestionId: string, rating: number) => void;
}

const STORAGE_KEYS = {
  RECENT_PROMPTS: 'smartPrompt.recentPrompts',
  ANALYTICS: 'smartPrompt.analytics',
  CONFIG: 'smartPrompt.config',
  SUGGESTIONS_CACHE: 'smartPrompt.suggestionsCache',
} as const;

export const useSmartPrompt = ({
  context,
  currentMode,
  config: userConfig = {},
  onSubmit
}: UseSmartPromptOptions): UseSmartPromptReturn => {
  // Merge user config with defaults
  const config = useMemo(() => ({
    ...defaultSmartPromptConfig,
    ...userConfig
  }), [userConfig]);

  // State
  const [recentPrompts, setRecentPrompts] = useState<string[]>([]);
  const [analytics, setAnalytics] = useState<PromptAnalytics[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [suggestionsCache, setSuggestionsCache] = useState<Map<string, SmartSuggestion[]>>(new Map());

  // Load persisted data on mount
  useEffect(() => {
    loadPersistedData();
  }, []);

  // Save data when it changes
  useEffect(() => {
    saveToStorage(STORAGE_KEYS.RECENT_PROMPTS, recentPrompts);
  }, [recentPrompts]);

  useEffect(() => {
    if (config.analytics.enabled) {
      saveToStorage(STORAGE_KEYS.ANALYTICS, analytics);
    }
  }, [analytics, config.analytics.enabled]);

  const loadPersistedData = useCallback(() => {
    try {
      // Load recent prompts
      const storedRecent = loadFromStorage<string[]>(STORAGE_KEYS.RECENT_PROMPTS);
      if (storedRecent) {
        setRecentPrompts(storedRecent.slice(0, config.maxRecentPrompts));
      }

      // Load analytics
      if (config.analytics.enabled) {
        const storedAnalytics = loadFromStorage<PromptAnalytics[]>(STORAGE_KEYS.ANALYTICS);
        if (storedAnalytics) {
          setAnalytics(storedAnalytics.slice(-1000)); // Keep last 1000 entries
        }
      }

      // Load suggestions cache
      const storedCache = loadFromStorage<Array<[string, SmartSuggestion[]]>>(STORAGE_KEYS.SUGGESTIONS_CACHE);
      if (storedCache) {
        setSuggestionsCache(new Map(storedCache));
      }
    } catch (error) {
      console.warn('Failed to load smart prompt data:', error);
    }
  }, [config.maxRecentPrompts, config.analytics.enabled]);

  // Generate contextual suggestions
  const generateSuggestions = useCallback((input?: string): SmartSuggestion[] => {
    const cacheKey = `${currentMode.id}-${input || ''}-${context.fileContext.path || ''}`;
    
    // Check cache first
    if (suggestionsCache.has(cacheKey)) {
      return suggestionsCache.get(cacheKey)!;
    }

    setIsLoading(true);
    
    try {
      const suggestions: SmartSuggestion[] = [];

      // Mode-specific templates
      if (config.enableTemplates) {
        const templates = getModeTemplates(currentMode, context);
        suggestions.push(...templates);
      }

      // Context-aware suggestions
      if (config.enableContextSuggestions) {
        const contextSuggestions = getContextualSuggestions(context, currentMode);
        suggestions.push(...contextSuggestions);
      }

      // Smart completions
      if (config.enableSmartCompletions && input && input.length > 2) {
        const completions = getSmartCompletions(input, context, currentMode);
        suggestions.push(...completions);
      }

      // Recent prompts as suggestions
      if (config.enableRecentPrompts) {
        const recentSuggestions = getRecentPromptSuggestions(recentPrompts, input);
        suggestions.push(...recentSuggestions);
      }

      // Sort by priority and limit
      const sortedSuggestions = suggestions
        .sort((a, b) => b.priority - a.priority)
        .slice(0, config.maxSuggestions);

      // Cache the results
      setSuggestionsCache(prev => {
        const newCache = new Map(prev);
        newCache.set(cacheKey, sortedSuggestions);
        
        // Limit cache size
        if (newCache.size > 100) {
          const firstKey = newCache.keys().next().value;
          if (firstKey) {
            newCache.delete(firstKey);
          }
        }
        
        return newCache;
      });

      return sortedSuggestions;
    } finally {
      setIsLoading(false);
    }
  }, [currentMode, context, config, recentPrompts, suggestionsCache]);

  // Filter suggestions
  const filterSuggestions = useCallback((category?: string, query?: string): SmartSuggestion[] => {
    const allSuggestions = generateSuggestions(query);
    
    return allSuggestions.filter(suggestion => {
      // Category filter
      if (category && category !== 'all' && suggestion.category !== category) {
        return false;
      }
      
      // Query filter
      if (query) {
        const lowerQuery = query.toLowerCase();
        return (
          suggestion.text.toLowerCase().includes(lowerQuery) ||
          suggestion.description?.toLowerCase().includes(lowerQuery) ||
          suggestion.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
        );
      }
      
      return true;
    });
  }, [generateSuggestions]);

  // Submit prompt
  const submitPrompt = useCallback((prompt: string, metadata?: any) => {
    if (!prompt.trim()) return;

    // Add to recent prompts
    addToRecent(prompt);

    // Track usage
    if (config.analytics.trackUsage) {
      trackUsage(prompt, metadata);
    }

    // Call external handler
    onSubmit?.(prompt, metadata);
  }, [config.analytics.trackUsage, onSubmit]);

  // Add to recent prompts
  const addToRecent = useCallback((prompt: string) => {
    setRecentPrompts(prev => {
      const filtered = prev.filter(p => p !== prompt);
      return [prompt, ...filtered].slice(0, config.maxRecentPrompts);
    });
  }, [config.maxRecentPrompts]);

  // Clear recent prompts
  const clearRecent = useCallback(() => {
    setRecentPrompts([]);
    removeFromStorage(STORAGE_KEYS.RECENT_PROMPTS);
  }, []);

  // Update configuration
  const updateConfig = useCallback((updates: Partial<SmartPromptConfig>) => {
    // This would typically update a global config state or context
    console.log('Config update requested:', updates);
  }, []);

  // Track usage analytics
  const trackUsage = useCallback((prompt: string, metadata?: any) => {
    if (!config.analytics.enabled) return;

    const analyticsEntry: PromptAnalytics = {
      prompt,
      timestamp: Date.now(),
      mode: currentMode.id,
      context: {
        file: context.fileContext.path ? context.fileContext.path.split('/').pop() : undefined,
        project: context.projectContext.type,
        branch: context.environmentContext.gitStatus?.branch,
      },
      metadata,
    };

    setAnalytics(prev => [...prev.slice(-999), analyticsEntry]);
  }, [config.analytics.enabled, currentMode.id, context]);

  // Rate suggestion
  const rateSuggestion = useCallback((suggestionId: string, rating: number) => {
    if (!config.analytics.trackEffectiveness) return;

    // This would typically update suggestion metadata
    console.log(`Rating suggestion ${suggestionId}: ${rating}`);
  }, [config.analytics.trackEffectiveness]);

  // Generate current suggestions
  const suggestions = useMemo(() => {
    return generateSuggestions();
  }, [generateSuggestions]);

  return {
    suggestions,
    recentPrompts,
    isLoading,
    analytics,
    config,
    
    // Actions
    submitPrompt,
    addToRecent,
    clearRecent,
    updateConfig,
    trackUsage,
    
    // Suggestion management
    generateSuggestions,
    filterSuggestions,
    rateSuggestion,
  };
};

// Helper functions
function getModeTemplates(mode: Mode, context: ContextSnapshot): SmartSuggestion[] {
  // This would typically come from a more comprehensive template system
  const baseTemplates: Record<string, SmartSuggestion[]> = {
    architect: [
      {
        id: 'arch-review',
        text: 'Review the architecture and suggest improvements',
        category: 'template',
        priority: 10,
        tags: ['architecture', 'review'],
        description: 'Comprehensive architecture analysis',
      },
    ],
    debug: [
      {
        id: 'debug-analyze',
        text: 'Analyze this error and suggest fixes',
        category: 'template',
        priority: 10,
        tags: ['debug', 'error'],
        description: 'Error analysis and resolution',
      },
    ],
    // Add more modes as needed
  };

  return baseTemplates[mode.id] || [];
}

function getContextualSuggestions(context: ContextSnapshot, mode: Mode): SmartSuggestion[] {
  const suggestions: SmartSuggestion[] = [];

  // File context suggestions
  if (context.fileContext.path) {
    const fileName = context.fileContext.path.split('/').pop();
    const ext = fileName?.split('.').pop()?.toLowerCase();
    
    if (ext === 'ts' || ext === 'tsx') {
      suggestions.push({
        id: 'ctx-typescript',
        text: 'Review TypeScript types and interfaces',
        category: 'context',
        priority: 8,
        tags: ['typescript', 'types'],
        description: 'TypeScript-specific analysis',
      });
    }
  }

  return suggestions;
}

function getSmartCompletions(input: string, context: ContextSnapshot, mode: Mode): SmartSuggestion[] {
  const suggestions: SmartSuggestion[] = [];
  const lowerInput = input.toLowerCase();

  // Common completion patterns
  if (lowerInput.includes('how')) {
    suggestions.push({
      id: 'completion-how',
      text: input + ' step by step?',
      category: 'completion',
      priority: 6,
      tags: ['how-to', 'guide'],
      description: 'Step-by-step guidance',
    });
  }

  return suggestions;
}

function getRecentPromptSuggestions(recentPrompts: string[], input?: string): SmartSuggestion[] {
  return recentPrompts
    .filter(prompt => !input || prompt.toLowerCase().includes(input.toLowerCase()))
    .slice(0, 3)
    .map((prompt, index) => ({
      id: `recent-${index}`,
      text: prompt,
      category: 'recent' as const,
      priority: 5 - index,
      tags: ['recent'],
      description: 'Recently used prompt',
    }));
}

// Storage utilities
function saveToStorage<T>(key: string, data: T): void {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.warn(`Failed to save to storage (${key}):`, error);
  }
}

function loadFromStorage<T>(key: string): T | null {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : null;
  } catch (error) {
    console.warn(`Failed to load from storage (${key}):`, error);
    return null;
  }
}

function removeFromStorage(key: string): void {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.warn(`Failed to remove from storage (${key}):`, error);
  }
}

export default useSmartPrompt;
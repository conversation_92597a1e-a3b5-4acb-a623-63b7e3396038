export interface SystemEvent {
  id: string;
  type: EventType;
  payload: any;
  timestamp: number;
  source: string;
  metadata?: EventMetadata;
}

export type EventType = 
  | 'mode.change'
  | 'mode.transition.start'
  | 'mode.transition.complete'
  | 'mode.feature.activate'
  | 'mode.feature.deactivate'
  | 'context.update'
  | 'context.analyzed'
  | 'suggestion.generated'
  | 'suggestion.accepted'
  | 'suggestion.rejected'
  | 'error.occurred'
  | 'performance.metric'
  | 'cache.hit'
  | 'cache.miss';

export interface EventMetadata {
  correlationId?: string;
  userId?: string;
  sessionId?: string;
  version?: string;
}

export type EventHandler = (event: SystemEvent) => void | Promise<void>;

export type Unsubscribe = () => void;

export interface EventBus {
  emit(event: Omit<SystemEvent, 'id' | 'timestamp'>): void;
  on(eventType: EventType | EventType[], handler: EventHandler): Unsubscribe;
  once(eventType: EventType, handler: EventHandler): Unsubscribe;
  off(eventType: EventType, handler?: EventHandler): void;
  clear(): void;
}

export interface EventFilter {
  types?: EventType[];
  source?: string | string[];
  timeRange?: { start: number; end: number };
  metadata?: Partial<EventMetadata>;
}
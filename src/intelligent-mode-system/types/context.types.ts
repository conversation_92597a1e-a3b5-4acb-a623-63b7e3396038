export interface FileContext {
  path: string;
  type: string;
  language?: string;
  content?: string;
  hasErrors: boolean;
  hasWarnings: boolean;
  symbols?: Symbol[];
  imports?: string[];
  lastModified: number;
}

export interface ProjectContext {
  type: 'node' | 'python' | 'rust' | 'go' | 'unknown';
  rootPath: string;
  dependencies: Record<string, string>;
  scripts?: Record<string, string>;
  structure: ProjectStructure;
  configuration: Record<string, any>;
}

export interface UserContext {
  recentActions: UserAction[];
  preferences: UserPreferences;
  patterns: UserPattern[];
  sessionDuration: number;
  lastActivity: number;
}

export interface EnvironmentContext {
  gitStatus?: GitStatus;
  runningProcesses: Process[];
  systemResources: SystemResources;
  openFiles: string[];
  activeTerminals: number;
}

export interface ContextSnapshot {
  fileContext: FileContext;
  projectContext: ProjectContext;
  userContext: UserContext;
  environmentContext: EnvironmentContext;
  timestamp: number;
}

export interface UserAction {
  type: 'edit' | 'navigate' | 'command' | 'search' | 'debug';
  target?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface UserPreferences {
  theme: string;
  shortcuts: Record<string, string>;
  defaultMode?: string;
  autoTransition: boolean;
  suggestionLevel: 'minimal' | 'standard' | 'aggressive';
}

export interface UserPattern {
  id: string;
  type: 'workflow' | 'navigation' | 'editing';
  frequency: number;
  lastSeen: number;
  confidence: number;
}

export interface GitStatus {
  branch: string;
  ahead: number;
  behind: number;
  modified: string[];
  staged: string[];
  untracked: string[];
  hasConflicts: boolean;
}

export interface Process {
  name: string;
  pid: number;
  type: 'server' | 'build' | 'test' | 'other';
  port?: number;
}

export interface SystemResources {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
}

export interface ProjectStructure {
  directories: string[];
  fileCount: number;
  totalSize: number;
  depth: number;
  hasTests: boolean;
  hasDocs: boolean;
}

export interface Symbol {
  name: string;
  kind: 'class' | 'function' | 'variable' | 'interface' | 'type';
  line: number;
  column: number;
}
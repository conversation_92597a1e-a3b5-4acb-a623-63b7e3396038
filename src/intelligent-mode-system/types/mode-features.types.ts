import { ContextSnapshot } from './context.types';
import { KeyboardShortcut } from './mode.types';

// Base feature interface extended for mode-specific features
export interface BaseModeFeature {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  shortcuts?: KeyboardShortcut[];
}

// Architect Mode Features
export interface ArchitectFeatures {
  systemDesigner: {
    enabled: boolean;
    diagramTypes: ('component' | 'sequence' | 'class' | 'flow')[];
    exportFormats: ('svg' | 'png' | 'mermaid')[];
  };
  dependencyAnalyzer: {
    enabled: boolean;
    visualizationEnabled: boolean;
    circularDependencyDetection: boolean;
    unusedDependencyDetection: boolean;
  };
  componentMapper: {
    enabled: boolean;
    autoDiscovery: boolean;
    relationshipTracking: boolean;
  };
  architecturePatterns: {
    enabled: boolean;
    suggestPatterns: boolean;
    patternLibrary: string[];
  };
}

// Debug Mode Features
export interface DebugFeatures {
  breakpointManager: {
    enabled: boolean;
    conditionalBreakpoints: boolean;
    logPoints: boolean;
    maxBreakpoints: number;
  };
  errorDetector: {
    enabled: boolean;
    realTimeAnalysis: boolean;
    errorPatternRecognition: boolean;
    stackTraceEnhancement: boolean;
  };
  performanceProfiler: {
    enabled: boolean;
    cpuProfiling: boolean;
    memoryProfiling: boolean;
    networkAnalysis: boolean;
  };
  variableInspector: {
    enabled: boolean;
    watchExpressions: boolean;
    deepInspection: boolean;
    historyTracking: boolean;
  };
}

// Review Mode Features
export interface ReviewFeatures {
  codeAnalyzer: {
    enabled: boolean;
    complexityAnalysis: boolean;
    duplicateDetection: boolean;
    coverageReporting: boolean;
  };
  qualityChecker: {
    enabled: boolean;
    lintingRules: string[];
    customRules: boolean;
    autoFix: boolean;
  };
  documentationVerifier: {
    enabled: boolean;
    missingDocsDetection: boolean;
    qualityScoring: boolean;
    exampleValidation: boolean;
  };
  diffViewer: {
    enabled: boolean;
    sideBySide: boolean;
    inlineComments: boolean;
    conflictResolution: boolean;
  };
}

// Deploy Mode Features
export interface DeployFeatures {
  pipelineManager: {
    enabled: boolean;
    pipelineTypes: ('ci' | 'cd' | 'release')[];
    parallelExecution: boolean;
    rollbackSupport: boolean;
  };
  environmentConfigurator: {
    enabled: boolean;
    environments: string[];
    secretsManagement: boolean;
    configValidation: boolean;
  };
  releaseAutomator: {
    enabled: boolean;
    versioningStrategy: 'semantic' | 'calendar' | 'custom';
    changelogGeneration: boolean;
    tagManagement: boolean;
  };
  monitoringIntegration: {
    enabled: boolean;
    metricsCollection: boolean;
    alertConfiguration: boolean;
    dashboardCreation: boolean;
  };
}

// Experiment Mode Features
export interface ExperimentFeatures {
  scratchPad: {
    enabled: boolean;
    autoSave: boolean;
    syntaxHighlighting: boolean;
    multiLanguageSupport: boolean;
  };
  rapidPrototyper: {
    enabled: boolean;
    livePreview: boolean;
    hotReload: boolean;
    mockDataGeneration: boolean;
  };
  sandboxEnvironment: {
    enabled: boolean;
    isolation: boolean;
    resourceLimits: boolean;
    snapshotting: boolean;
  };
  codePlayground: {
    enabled: boolean;
    interactiveExecution: boolean;
    shareableLinks: boolean;
    embeddedDocs: boolean;
  };
}

// Learn Mode Features
export interface LearnFeatures {
  documentationHub: {
    enabled: boolean;
    sources: string[];
    offlineCache: boolean;
    contextualDocs: boolean;
  };
  tutorialEngine: {
    enabled: boolean;
    interactiveTutorials: boolean;
    progressTracking: boolean;
    certifications: boolean;
  };
  knowledgeBase: {
    enabled: boolean;
    searchEnabled: boolean;
    aiAssistant: boolean;
    bookmarking: boolean;
  };
  codeExamples: {
    enabled: boolean;
    runnable: boolean;
    categorized: boolean;
    communityContributed: boolean;
  };
}

// Removed duplicate KeyboardShortcut interface (now imported from mode.types)

// Mode transition rules
export interface ModeTransitionRule {
  fromMode: string;
  toMode: string;
  conditions: TransitionCondition[];
  priority: number;
  automatic: boolean;
}

export interface TransitionCondition {
  type: 'context' | 'user' | 'time' | 'event';
  evaluate: (context: ContextSnapshot, data?: any) => boolean;
  description: string;
}

// Mode-specific context providers
export interface ModeContextProvider {
  modeId: string;
  analyze: (context: ContextSnapshot) => Promise<ModeContextData>;
  weight: number;
}

export interface ModeContextData {
  confidence: number;
  signals: ModeSignal[];
  recommendations: string[];
}

export interface ModeSignal {
  type: string;
  strength: number;
  source: string;
  description: string;
}

// Mode enhancement suggestions
export interface ModeEnhancement {
  id: string;
  mode: string;
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  category: 'feature' | 'performance' | 'ux' | 'integration';
}

// Mode metrics for analytics
export interface ModeMetrics {
  modeId: string;
  activationCount: number;
  totalDuration: number;
  averageDuration: number;
  featureUsage: Record<string, number>;
  transitionPatterns: Record<string, number>;
  userSatisfaction?: number;
}

// Export all mode feature types
export type ModeFeatures = 
  | ArchitectFeatures
  | DebugFeatures
  | ReviewFeatures
  | DeployFeatures
  | ExperimentFeatures
  | LearnFeatures;

// Mode feature configuration
export interface ModeFeatureConfig<T extends ModeFeatures> {
  modeId: string;
  features: T;
  enabledByDefault: (keyof T)[];
  requiredFeatures: (keyof T)[];
  experimentalFeatures?: (keyof T)[];
}
export interface SmartSuggestion {
  id: string;
  text: string;
  type: 'completion' | 'template' | 'command' | 'question';
  confidence: number;
  category: string;
  metadata?: Record<string, any>;
}

export interface PromptTemplate {
  id: string;
  name: string;
  template: string;
  description: string;
  mode: string;
  category: string;
  variables: string[];
  tags: string[];
  examples?: string[];
  priority: number;
}

export interface ContextVariable {
  name: string;
  value: string;
  type: 'file' | 'project' | 'git' | 'user' | 'env';
  description?: string;
}

export interface PromptAnalytics {
  id: string;
  prompt: string;
  timestamp: number;
  mode: string;
  responseTime: number;
  success: boolean;
  tokens: number;
  satisfaction?: number;
}

export interface SmartPromptConfig {
  maxSuggestions: number;
  minConfidence: number;
  enableMLFeatures: boolean;
  enableAnalytics: boolean;
  cacheSize: number;
  analysisInterval: number;
}

export interface PromptModeConfig {
  [modeId: string]: {
    templates: PromptTemplate[];
    shortcuts: KeyboardShortcut[];
    contextRules: ContextRule[];
    suggestions: {
      enabled: boolean;
      categories: string[];
      maxSuggestions: number;
    };
  };
}

export interface KeyboardShortcut {
  key: string;
  modifiers: ('ctrl' | 'cmd' | 'alt' | 'shift')[];
  action: string;
  description: string;
  context?: string;
}

export interface ContextRule {
  id: string;
  condition: (context: any) => boolean;
  suggestions: SmartSuggestion[];
  priority: number;
}

// Default configuration
export const defaultSmartPromptConfig: SmartPromptConfig = {
  maxSuggestions: 6,
  enableTemplates: true,
  enableContextSuggestions: true,
  enableSmartCompletions: true,
  enableRecentPrompts: true,
  maxRecentPrompts: 10,
  suggestionCategories: ['template', 'context', 'smart', 'recent', 'completion'],
  analytics: {
    enabled: true,
    trackUsage: true,
    trackEffectiveness: false,
  },
};

// Prompt categories with metadata
export const promptCategories = {
  template: {
    name: 'Templates',
    description: 'Pre-defined prompt templates for common tasks',
    icon: 'FileText',
    color: '#3B82F6',
  },
  context: {
    name: 'Context',
    description: 'Suggestions based on your current context',
    icon: 'Brain',
    color: '#8B5CF6',
  },
  smart: {
    name: 'Smart',
    description: 'AI-powered intelligent suggestions',
    icon: 'Sparkles',
    color: '#10B981',
  },
  recent: {
    name: 'Recent',
    description: 'Your recently used prompts',
    icon: 'History',
    color: '#F59E0B',
  },
  completion: {
    name: 'Completion',
    description: 'Auto-completion suggestions',
    icon: 'Wand2',
    color: '#EF4444',
  },
};

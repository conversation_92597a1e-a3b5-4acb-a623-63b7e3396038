export * from './context.types';
export * from './mode.types';
export * from './events.types';
export * from './mode-features.types';

// Cache types
export interface CacheStrategy {
  type: 'memory' | 'indexed-db' | 'session';
  ttl: number;
  maxSize: number;
  evictionPolicy: 'lru' | 'lfu' | 'fifo';
}

export interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  itemCount: number;
  evictions: number;
}

// Performance types
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  timestamp: number;
  tags?: Record<string, string>;
}

export interface PerformanceThreshold {
  metric: string;
  warning: number;
  critical: number;
}
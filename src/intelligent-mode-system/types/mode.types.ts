import { ContextSnapshot } from './context.types';

export type ModeId = 'architect' | 'debug' | 'review' | 'deploy' | 'experiment' | 'learn';

export type ModeType = 'focused' | 'creative' | 'analytical';

export interface Mode {
  id: ModeId;
  name: string;
  description: string;
  icon: string;
  color: string;
  isActive: boolean;
  features: ModeFeature[];
  shortcuts: KeyboardShortcut[];
  lastActivated?: number;
  // Enhanced properties from IntelligentMode
  type: ModeType;
  capabilities: string[];
  configuration: ModeConfiguration;
  theme?: ModeTheme;
  priority: number;
}

export interface ModeConfiguration {
  aiModel: string;
  contextWindow: number;
  temperature: number;
  systemPrompt: string;
  maxTokens: number;
  tools: string[];
  customSettings?: Record<string, any>;
}

export interface ModeTheme {
  // Core colors
  primary: string;
  primaryLight: string;
  primaryDark: string;
  primaryForeground: string;
  
  // Secondary colors
  secondary: string;
  secondaryLight: string;
  secondaryDark: string;
  secondaryForeground: string;
  
  // Accent colors
  accent: string;
  accentLight: string;
  accentDark: string;
  accentForeground: string;
  
  // Background colors
  background: string;
  backgroundSecondary: string;
  backgroundTertiary: string;
  
  // Surface colors
  surface: string;
  surfaceSecondary: string;
  surfaceTertiary: string;
  
  // Text colors
  text: string;
  textSecondary: string;
  textTertiary: string;
  textMuted: string;
  
  // Border colors
  border: string;
  borderSecondary: string;
  borderFocus: string;
  
  // Status colors
  success: string;
  successForeground: string;
  warning: string;
  warningForeground: string;
  error: string;
  errorForeground: string;
  info: string;
  infoForeground: string;
  
  // Interactive states
  hover: string;
  active: string;
  disabled: string;
  
  // Special elements
  codeBackground: string;
  codeBorder: string;
  tooltipBackground: string;
  tooltipForeground: string;
}

export interface ModeDefinition extends Omit<Mode, 'isActive' | 'lastActivated'> {
  contextRules: ContextRule[];
  activate?: (context: ContextSnapshot) => Promise<void>;
  deactivate?: () => Promise<void>;
}

export interface ModeFeature {
  id: string;
  name: string;
  type: 'widget' | 'tool' | 'panel' | 'command';
  component?: React.ComponentType<any>;
  position?: FeaturePosition;
  config?: Record<string, any>;
}

export interface ContextRule {
  id: string;
  type: 'pattern' | 'keyword' | 'state' | 'file';
  condition: RuleCondition;
  weight: number;
  confidence: number;
}

export interface RuleCondition {
  evaluate: (context: ContextSnapshot) => boolean;
  description: string;
}

export interface KeyboardShortcut {
  key: string;
  modifiers?: ('ctrl' | 'cmd' | 'shift' | 'alt')[];
  action: string;
  description: string;
}

export interface FeaturePosition {
  area: 'left' | 'right' | 'top' | 'bottom' | 'center' | 'floating';
  order?: number;
  size?: { width?: number; height?: number };
}

export interface ModeProbabilities {
  [modeId: string]: number;
}

export interface ModeTransition {
  from: ModeId;
  to: ModeId;
  reason: string;
  timestamp: number;
  automatic: boolean;
}

export interface ModeHistory {
  current: ModeId;
  previous?: ModeId;
  transitions: ModeTransition[];
  duration: Record<ModeId, number>;
}
{
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint", "react", "react-hooks"],
  "parserOptions": {
    "ecmaVersion": 2022,
    "sourceType": "module",
    "project": "./tsconfig.json",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "env": {
    "browser": true,
    "node": true,
    "es2022": true,
    "vitest/globals": true
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "rules": {
    // TypeScript specific rules
    "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-inferrable-types": "off",
    "@typescript-eslint/prefer-const": "error",
    "@typescript-eslint/no-var-requires": "error",
    "@typescript-eslint/consistent-type-imports": ["error", { "prefer": "type-imports" }],
    
    // React specific rules
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    
    // General code quality rules
    "no-console": ["warn", { "allow": ["warn", "error", "info"] }],
    "no-debugger": "error",
    "no-duplicate-imports": "error",
    "no-unused-expressions": "error",
    "prefer-const": "error",
    "no-var": "error",
    "object-shorthand": "error",
    "prefer-arrow-callback": "error",
    "prefer-template": "error",
    "eqeqeq": ["error", "always"],
    "curly": ["error", "all"],
    
    // Code organization
    "import/order": "off", // Let prettier handle this
    "sort-imports": "off", // Let prettier handle this
    
    // Performance
    "no-await-in-loop": "warn",
    "require-atomic-updates": "error",
    
    // Security
    "no-eval": "error",
    "no-implied-eval": "error",
    "no-new-func": "error",
    "no-script-url": "error",
    
    // Accessibility (basic)
    "jsx-a11y/alt-text": "off", // Not using jsx-a11y plugin
    
    // Comments and documentation
    "spaced-comment": ["error", "always", { "exceptions": ["-", "+"] }],
    "multiline-comment-style": ["error", "starred-block"]
  },
  "overrides": [
    {
      "files": ["**/__tests__/**/*.ts", "**/*.test.ts", "**/*.spec.ts"],
      "env": {
        "vitest/globals": true
      },
      "rules": {
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-non-null-assertion": "off",
        "no-console": "off"
      }
    },
    {
      "files": ["*.js"],
      "rules": {
        "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/explicit-function-return-type": "off"
      }
    },
    {
      "files": ["vite.config.ts", "vitest.config.ts", ".eslintrc.js"],
      "rules": {
        "import/no-default-export": "off"
      }
    }
  ],
  "ignorePatterns": [
    "dist",
    "build",
    "coverage",
    "node_modules",
    "*.config.js",
    "*.d.ts"
  ]
}
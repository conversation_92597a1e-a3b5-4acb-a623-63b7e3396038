/**
 * Example Custom Analyzer Plugin
 * 
 * Demonstrates how to create a custom context analyzer that provides
 * specialized analysis capabilities for the intelligent mode system.
 */

import { BaseAnalyzerPlugin } from '../BasePlugin';
import { ContextAnalyzer, PluginManifest, AnalysisResult } from '../../core/PluginRegistry';
import { ContextSnapshot } from '../../types';

// Plugin manifest
const manifest: PluginManifest = {
  id: 'complexity-analyzer',
  name: 'Code Complexity Analyzer',
  version: '1.0.0',
  description: 'Analyzes code complexity and provides refactoring suggestions',
  author: 'Example Developer',
  type: 'analyzer',
  dependencies: [],
  optionalDependencies: [],
  permissions: [
    {
      type: 'context.read',
      description: 'Read file content and project structure for complexity analysis',
    },
    {
      type: 'file.access',
      description: 'Access file content for detailed analysis',
    },
  ],
  apiVersion: '1.0.0',
  config: {
    complexityThreshold: 10,
    enableDeepAnalysis: true,
    trackHistory: true,
    maxFileSize: 1024 * 1024, // 1MB
  },
};

/**
 * Code Complexity Analyzer Plugin
 */
export class ComplexityAnalyzerPlugin extends BaseAnalyzerPlugin {
  private analysisHistory: Map<string, AnalysisResult[]> = new Map();
  
  constructor() {
    super(manifest);
  }
  
  protected async onInitialize(): Promise<void> {
    console.log('Complexity Analyzer Plugin initialized');
    
    // Set up event listeners
    this.on('file.saved', this.handleFileSaved.bind(this));
    this.on('file.changed', this.handleFileChanged.bind(this));
  }
  
  protected async onDestroy(): Promise<void> {
    console.log('Complexity Analyzer Plugin destroyed');
    this.analysisHistory.clear();
  }
  
  protected createAnalyzer(): ContextAnalyzer {
    return {
      id: 'complexity-analyzer',
      name: 'Code Complexity Analyzer',
      priority: 8, // High priority analyzer
      
      analyze: this.analyze.bind(this),
      getFeatures: this.getFeatures.bind(this),
    };
  }
  
  /**
   * Perform comprehensive complexity analysis
   */
  private async analyze(context: ContextSnapshot): Promise<AnalysisResult> {
    const startTime = performance.now();
    
    try {
      // Skip non-code files
      if (!this.isCodeFile(context.fileContext.path)) {
        return {
          score: 0,
          confidence: 1,
          features: {},
          metadata: { reason: 'not_code_file' },
        };
      }
      
      // Perform various complexity analyses
      const cyclomaticComplexity = this.calculateCyclomaticComplexity(context);
      const cognitiveComplexity = this.calculateCognitiveComplexity(context);
      const structuralComplexity = this.calculateStructuralComplexity(context);
      const maintenanceComplexity = this.calculateMaintenanceComplexity(context);
      
      // Calculate overall complexity score
      const complexityScore = this.calculateOverallComplexity({
        cyclomatic: cyclomaticComplexity,
        cognitive: cognitiveComplexity,
        structural: structuralComplexity,
        maintenance: maintenanceComplexity,
      });
      
      // Generate analysis features
      const features = this.generateComplexityFeatures(context, {
        cyclomatic: cyclomaticComplexity,
        cognitive: cognitiveComplexity,
        structural: structuralComplexity,
        maintenance: maintenanceComplexity,
      });
      
      // Create analysis result
      const result: AnalysisResult = {\n        score: complexityScore,\n        confidence: this.calculateConfidence(context),\n        features,\n        metadata: {\n          cyclomaticComplexity,\n          cognitiveComplexity,\n          structuralComplexity,\n          maintenanceComplexity,\n          analysisTime: performance.now() - startTime,\n          suggestions: this.generateSuggestions(complexityScore, {\n            cyclomatic: cyclomaticComplexity,\n            cognitive: cognitiveComplexity,\n            structural: structuralComplexity,\n            maintenance: maintenanceComplexity,\n          }),\n        },\n      };\n      \n      // Store in history if enabled\n      if (this.config.trackHistory) {\n        this.updateAnalysisHistory(context.fileContext.path, result);\n      }\n      \n      // Emit analysis event\n      this.emit('complexity.analyzed', {\n        filePath: context.fileContext.path,\n        complexity: complexityScore,\n        result,\n      });\n      \n      return result;\n      \n    } catch (error) {\n      console.error('Complexity analysis failed:', error);\n      return {\n        score: 0,\n        confidence: 0,\n        features: {},\n        metadata: { error: error.message },\n      };\n    }\n  }\n  \n  /**\n   * Extract complexity-related features\n   */\n  private getFeatures(context: ContextSnapshot): Record<string, number> {\n    const features: Record<string, number> = {};\n    \n    if (!this.isCodeFile(context.fileContext.path)) {\n      return features;\n    }\n    \n    // File-based features\n    features['file_size_kb'] = this.getFileSize(context) / 1024;\n    features['line_count'] = this.getLineCount(context);\n    features['function_count'] = this.getFunctionCount(context);\n    features['class_count'] = this.getClassCount(context);\n    \n    // Syntax features\n    features['nesting_depth'] = this.getMaxNestingDepth(context);\n    features['conditional_statements'] = this.getConditionalCount(context);\n    features['loop_statements'] = this.getLoopCount(context);\n    features['try_catch_blocks'] = this.getTryCatchCount(context);\n    \n    // Structure features\n    features['import_count'] = this.getImportCount(context);\n    features['export_count'] = this.getExportCount(context);\n    features['comment_ratio'] = this.getCommentRatio(context);\n    \n    // Quality features\n    features['has_errors'] = context.fileContext.hasErrors ? 1 : 0;\n    features['error_count'] = this.getErrorCount(context);\n    features['warning_count'] = this.getWarningCount(context);\n    \n    // Historical features\n    const history = this.analysisHistory.get(context.fileContext.path);\n    if (history && history.length > 1) {\n      const recent = history.slice(-5); // Last 5 analyses\n      features['complexity_trend'] = this.calculateComplexityTrend(recent);\n      features['analysis_frequency'] = recent.length / 7; // Per week\n    }\n    \n    return features;\n  }\n  \n  /**\n   * Complexity calculation methods\n   */\n  \n  private calculateCyclomaticComplexity(context: ContextSnapshot): number {\n    // Simplified cyclomatic complexity calculation\n    // In reality, this would parse AST and count decision points\n    \n    const content = this.getFileContent(context);\n    if (!content) return 0;\n    \n    let complexity = 1; // Base complexity\n    \n    // Count decision points\n    const decisionKeywords = [\n      'if', 'else if', 'while', 'for', 'switch', 'case', \n      'catch', '&&', '||', '?', 'forEach', 'map', 'filter'\n    ];\n    \n    decisionKeywords.forEach(keyword => {\n      const regex = new RegExp(`\\\\b${keyword}\\\\b`, 'g');\n      const matches = content.match(regex);\n      if (matches) {\n        complexity += matches.length;\n      }\n    });\n    \n    return complexity;\n  }\n  \n  private calculateCognitiveComplexity(context: ContextSnapshot): number {\n    // Simplified cognitive complexity calculation\n    // Considers nesting levels and structural complexity\n    \n    const content = this.getFileContent(context);\n    if (!content) return 0;\n    \n    let complexity = 0;\n    const lines = content.split('\\n');\n    let nestingLevel = 0;\n    \n    lines.forEach(line => {\n      const trimmed = line.trim();\n      \n      // Track nesting level\n      const openBraces = (line.match(/\\{/g) || []).length;\n      const closeBraces = (line.match(/\\}/g) || []).length;\n      nestingLevel += openBraces - closeBraces;\n      \n      // Add complexity for control structures\n      if (/\\b(if|while|for|switch)\\b/.test(trimmed)) {\n        complexity += 1 + nestingLevel; // Base + nesting penalty\n      }\n      \n      // Add complexity for logical operators\n      const logicalOps = (trimmed.match(/&&|\\|\\|/g) || []).length;\n      complexity += logicalOps;\n      \n      // Add complexity for ternary operators\n      const ternaryOps = (trimmed.match(/\\?.*:/g) || []).length;\n      complexity += ternaryOps * 2;\n    });\n    \n    return complexity;\n  }\n  \n  private calculateStructuralComplexity(context: ContextSnapshot): number {\n    // Analyze structural complexity based on code organization\n    \n    const features = this.getFeatures(context);\n    let complexity = 0;\n    \n    // File size impact\n    if (features.line_count > 500) complexity += 2;\n    if (features.line_count > 1000) complexity += 3;\n    \n    // Function count impact\n    if (features.function_count > 20) complexity += 2;\n    if (features.function_count > 50) complexity += 4;\n    \n    // Nesting depth impact\n    complexity += Math.max(0, features.nesting_depth - 3) * 2;\n    \n    // Import complexity\n    if (features.import_count > 15) complexity += 1;\n    if (features.import_count > 30) complexity += 2;\n    \n    return complexity;\n  }\n  \n  private calculateMaintenanceComplexity(context: ContextSnapshot): number {\n    // Analyze factors that affect maintainability\n    \n    const features = this.getFeatures(context);\n    let complexity = 0;\n    \n    // Error and warning impact\n    complexity += features.error_count * 3;\n    complexity += features.warning_count * 1;\n    \n    // Comment ratio impact (low comments = higher maintenance complexity)\n    if (features.comment_ratio < 0.1) complexity += 3;\n    if (features.comment_ratio < 0.05) complexity += 2;\n    \n    // Historical complexity trend\n    if (features.complexity_trend > 1.2) complexity += 2; // Increasing complexity\n    \n    return complexity;\n  }\n  \n  private calculateOverallComplexity(complexities: {\n    cyclomatic: number;\n    cognitive: number;\n    structural: number;\n    maintenance: number;\n  }): number {\n    // Weighted combination of different complexity measures\n    const weights = {\n      cyclomatic: 0.3,\n      cognitive: 0.3,\n      structural: 0.2,\n      maintenance: 0.2,\n    };\n    \n    const normalizedScore = (\n      (complexities.cyclomatic / 50) * weights.cyclomatic +\n      (complexities.cognitive / 30) * weights.cognitive +\n      (complexities.structural / 20) * weights.structural +\n      (complexities.maintenance / 15) * weights.maintenance\n    );\n    \n    return Math.min(normalizedScore, 1.0);\n  }\n  \n  /**\n   * Feature extraction helpers\n   */\n  \n  private getFileContent(context: ContextSnapshot): string {\n    // In a real implementation, this would read the actual file content\n    return context.fileContext.content || '';\n  }\n  \n  private getFileSize(context: ContextSnapshot): number {\n    const content = this.getFileContent(context);\n    return new Blob([content]).size;\n  }\n  \n  private getLineCount(context: ContextSnapshot): number {\n    const content = this.getFileContent(context);\n    return content.split('\\n').length;\n  }\n  \n  private getFunctionCount(context: ContextSnapshot): number {\n    const content = this.getFileContent(context);\n    const functionRegex = /\\b(function|=>|\\w+\\s*\\()/g;\n    const matches = content.match(functionRegex);\n    return matches ? matches.length : 0;\n  }\n  \n  private getClassCount(context: ContextSnapshot): number {\n    const content = this.getFileContent(context);\n    const classRegex = /\\bclass\\s+\\w+/g;\n    const matches = content.match(classRegex);\n    return matches ? matches.length : 0;\n  }\n  \n  private getMaxNestingDepth(context: ContextSnapshot): number {\n    const content = this.getFileContent(context);\n    const lines = content.split('\\n');\n    \n    let maxDepth = 0;\n    let currentDepth = 0;\n    \n    lines.forEach(line => {\n      const openBraces = (line.match(/\\{/g) || []).length;\n      const closeBraces = (line.match(/\\}/g) || []).length;\n      currentDepth += openBraces - closeBraces;\n      maxDepth = Math.max(maxDepth, currentDepth);\n    });\n    \n    return maxDepth;\n  }\n  \n  private getConditionalCount(context: ContextSnapshot): number {\n    const content = this.getFileContent(context);\n    const conditionalRegex = /\\b(if|else if|switch|case)\\b/g;\n    const matches = content.match(conditionalRegex);\n    return matches ? matches.length : 0;\n  }\n  \n  private getLoopCount(context: ContextSnapshot): number {\n    const content = this.getFileContent(context);\n    const loopRegex = /\\b(for|while|do|forEach|map|filter|reduce)\\b/g;\n    const matches = content.match(loopRegex);\n    return matches ? matches.length : 0;\n  }\n  \n  private getTryCatchCount(context: ContextSnapshot): number {\n    const content = this.getFileContent(context);\n    const tryCatchRegex = /\\b(try|catch|finally)\\b/g;\n    const matches = content.match(tryCatchRegex);\n    return matches ? matches.length : 0;\n  }\n  \n  private getImportCount(context: ContextSnapshot): number {\n    const content = this.getFileContent(context);\n    const importRegex = /\\b(import|require|from)\\b/g;\n    const matches = content.match(importRegex);\n    return matches ? matches.length : 0;\n  }\n  \n  private getExportCount(context: ContextSnapshot): number {\n    const content = this.getFileContent(context);\n    const exportRegex = /\\b(export|module\\.exports)\\b/g;\n    const matches = content.match(exportRegex);\n    return matches ? matches.length : 0;\n  }\n  \n  private getCommentRatio(context: ContextSnapshot): number {\n    const content = this.getFileContent(context);\n    const lines = content.split('\\n');\n    const commentLines = lines.filter(line => {\n      const trimmed = line.trim();\n      return trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*');\n    });\n    \n    return lines.length > 0 ? commentLines.length / lines.length : 0;\n  }\n  \n  private getErrorCount(context: ContextSnapshot): number {\n    // In a real implementation, this would integrate with language servers\n    return context.fileContext.hasErrors ? 5 : 0; // Simulated\n  }\n  \n  private getWarningCount(context: ContextSnapshot): number {\n    // In a real implementation, this would integrate with linters\n    return Math.floor(Math.random() * 3); // Simulated\n  }\n  \n  /**\n   * Analysis utilities\n   */\n  \n  private generateComplexityFeatures(context: ContextSnapshot, complexities: any): Record<string, number> {\n    const features = this.getFeatures(context);\n    \n    // Add complexity-specific features\n    features['complexity.cyclomatic'] = complexities.cyclomatic;\n    features['complexity.cognitive'] = complexities.cognitive;\n    features['complexity.structural'] = complexities.structural;\n    features['complexity.maintenance'] = complexities.maintenance;\n    \n    return features;\n  }\n  \n  private calculateConfidence(context: ContextSnapshot): number {\n    // Confidence based on file size and content availability\n    const fileSize = this.getFileSize(context);\n    \n    if (fileSize < 100) return 0.3; // Very small files are hard to analyze\n    if (fileSize < 1000) return 0.7;\n    if (fileSize > this.config.maxFileSize) return 0.5; // Very large files may timeout\n    \n    return 0.9;\n  }\n  \n  private generateSuggestions(complexityScore: number, complexities: any): string[] {\n    const suggestions: string[] = [];\n    \n    if (complexityScore > 0.7) {\n      suggestions.push('Consider refactoring this file to reduce complexity');\n    }\n    \n    if (complexities.cyclomatic > 20) {\n      suggestions.push('Break down complex functions into smaller ones');\n    }\n    \n    if (complexities.cognitive > 15) {\n      suggestions.push('Simplify nested conditional logic');\n    }\n    \n    if (complexities.structural > 10) {\n      suggestions.push('Consider splitting this file into multiple modules');\n    }\n    \n    if (complexities.maintenance > 8) {\n      suggestions.push('Add more comments and fix warnings to improve maintainability');\n    }\n    \n    return suggestions;\n  }\n  \n  private calculateComplexityTrend(history: AnalysisResult[]): number {\n    if (history.length < 2) return 1;\n    \n    const first = history[0].score;\n    const last = history[history.length - 1].score;\n    \n    return last / (first || 0.1); // Avoid division by zero\n  }\n  \n  private updateAnalysisHistory(filePath: string, result: AnalysisResult): void {\n    if (!this.analysisHistory.has(filePath)) {\n      this.analysisHistory.set(filePath, []);\n    }\n    \n    const history = this.analysisHistory.get(filePath)!;\n    history.push(result);\n    \n    // Keep only last 50 analyses\n    if (history.length > 50) {\n      history.splice(0, history.length - 50);\n    }\n  }\n  \n  private isCodeFile(filePath: string): boolean {\n    const codeExtensions = [\n      '.js', '.ts', '.jsx', '.tsx', '.vue', '.py', '.java',\n      '.cpp', '.c', '.h', '.cs', '.php', '.rb', '.go', '.rs'\n    ];\n    \n    return codeExtensions.some(ext => filePath.toLowerCase().endsWith(ext));\n  }\n  \n  /**\n   * Event handlers\n   */\n  \n  private handleFileSaved(event: any): void {\n    const { filePath } = event.payload;\n    \n    if (this.isCodeFile(filePath)) {\n      this.emit('complexity.file_changed', {\n        filePath,\n        timestamp: Date.now(),\n        trigger: 'save',\n      });\n    }\n  }\n  \n  private handleFileChanged(event: any): void {\n    const { filePath } = event.payload;\n    \n    if (this.isCodeFile(filePath)) {\n      // Debounced analysis trigger\n      setTimeout(() => {\n        this.emit('complexity.analysis_requested', {\n          filePath,\n          timestamp: Date.now(),\n        });\n      }, 1000);\n    }\n  }\n}\n\n// Export for plugin loader\nexport default ComplexityAnalyzerPlugin;
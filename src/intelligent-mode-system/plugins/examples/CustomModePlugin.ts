/**
 * Example Custom Mode Plugin
 * 
 * Demonstrates how to create a custom mode plugin that adds
 * new intelligent behavior to the mode system.
 */

import { BaseModePlugin } from '../BasePlugin';
import { ModeProvider, PluginManifest, PluginAction } from '../../core/PluginRegistry';
import { ContextSnapshot } from '../../types';

// Plugin manifest
const manifest: PluginManifest = {
  id: 'custom-research-mode',
  name: 'Research Mode',
  version: '1.0.0',
  description: 'Intelligent mode for research and information gathering tasks',
  author: 'Example Developer',
  type: 'mode',
  dependencies: [],
  optionalDependencies: [],
  permissions: [
    {
      type: 'context.read',
      description: 'Read current context to determine applicability',
    },
    {
      type: 'ui.render',
      description: 'Render mode-specific UI components',
    },
  ],
  apiVersion: '1.0.0',
  config: {
    sensitivity: 0.7,
    maxSuggestions: 5,
    enableDeepAnalysis: true,
  },
};

/**
 * Custom Research Mode Plugin
 */
export class CustomResearchModePlugin extends BaseModePlugin {
  constructor() {
    super(manifest);
  }
  
  protected async onInitialize(): Promise<void> {
    console.log('Research Mode Plugin initialized');
    
    // Set up event listeners for research-specific events
    this.on('file.opened', this.handleFileOpened.bind(this));
    this.on('search.performed', this.handleSearchPerformed.bind(this));
  }
  
  protected async onDestroy(): Promise<void> {
    console.log('Research Mode Plugin destroyed');
  }
  
  protected createModeProvider(): ModeProvider {
    return {
      id: 'research' as any,
      displayName: 'Research',
      description: 'Intelligent research and information gathering mode',
      icon: '🔍',
      color: '#4A90E2',
      
      isApplicable: this.isApplicable.bind(this),
      getScore: this.getScore.bind(this),
      getSuggestions: this.getSuggestions.bind(this),
      getActions: this.getActions.bind(this),
    };
  }
  
  /**
   * Determine if research mode is applicable to the current context
   */
  private isApplicable(context: ContextSnapshot): boolean {
    // Research mode is applicable when:
    
    // 1. User is working with documentation files
    const docExtensions = ['.md', '.txt', '.rst', '.wiki'];
    const isDocFile = docExtensions.some(ext => 
      context.fileContext.path.toLowerCase().endsWith(ext)
    );
    
    // 2. User has recently performed search actions
    const hasSearchActions = context.userContext.recentActions.some(action => 
      action.type === 'search' || action.type === 'find'
    );
    
    // 3. Project has research-related directories
    const hasResearchDirs = context.projectContext.structure.directories.some(dir =>
      ['docs', 'research', 'notes', 'references'].includes(dir.toLowerCase())
    );
    
    // 4. Multiple files are open (comparative research)
    const hasMultipleFiles = context.fileContext.openFiles?.length > 3;
    
    return isDocFile || hasSearchActions || hasResearchDirs || hasMultipleFiles;
  }
  
  /**
   * Calculate relevance score for research mode
   */
  private getScore(context: ContextSnapshot): number {
    let score = 0;
    
    // File type scoring
    if (context.fileContext.path.endsWith('.md')) score += 0.4;
    if (context.fileContext.path.endsWith('.txt')) score += 0.3;
    if (context.fileContext.path.includes('README')) score += 0.2;
    
    // User action scoring
    const searchActions = context.userContext.recentActions.filter(action =>
      ['search', 'find', 'grep', 'browse'].includes(action.type)
    );
    score += Math.min(searchActions.length * 0.1, 0.3);
    
    // Project structure scoring
    const researchIndicators = ['docs', 'research', 'notes', 'wiki', 'references'];
    const hasResearchStructure = context.projectContext.structure.directories.some(dir =>
      researchIndicators.some(indicator => dir.toLowerCase().includes(indicator))
    );
    if (hasResearchStructure) score += 0.2;
    
    // Time of day scoring (research often happens during certain hours)
    const hour = new Date().getHours();
    if (hour >= 9 && hour <= 11) score += 0.1; // Morning research
    if (hour >= 14 && hour <= 16) score += 0.1; // Afternoon research
    
    // Collaborative indicators
    const isCollaborativeWork = context.environmentContext.gitStatus?.ahead > 0 ||
      context.environmentContext.gitStatus?.staged.length > 0;
    if (isCollaborativeWork) score += 0.1;
    
    return Math.min(score, 1.0);
  }
  
  /**
   * Generate research-specific suggestions
   */
  private async getSuggestions(context: ContextSnapshot, input?: string): Promise<string[]> {
    const suggestions: string[] = [];
    
    // Content-based suggestions
    if (context.fileContext.path.endsWith('.md')) {
      suggestions.push('Add table of contents');
      suggestions.push('Create cross-references');
      suggestions.push('Add citations and sources');
    }
    
    // Search-based suggestions
    if (input) {
      const searchTerm = input.toLowerCase();
      
      if (searchTerm.includes('how') || searchTerm.includes('what') || searchTerm.includes('why')) {
        suggestions.push(`Research methodology for: ${input}`);
        suggestions.push(`Find academic sources about: ${input}`);
        suggestions.push(`Create research outline for: ${input}`);
      }
      
      if (searchTerm.includes('compare') || searchTerm.includes('vs')) {
        suggestions.push('Create comparison table');
        suggestions.push('Analyze pros and cons');
        suggestions.push('Research alternatives');
      }
    }
    
    // Context-based suggestions
    const recentFiles = context.fileContext.openFiles || [];
    if (recentFiles.length > 1) {
      suggestions.push('Synthesize information from open files');
      suggestions.push('Create summary document');
      suggestions.push('Find common themes');
    }
    
    // Project-based suggestions
    if (context.projectContext.structure.hasTests) {
      suggestions.push('Document testing methodology');
      suggestions.push('Research testing best practices');
    }
    
    // Time-sensitive suggestions
    const hour = new Date().getHours();
    if (hour < 12) {
      suggestions.push('Plan research agenda for today');
    } else {
      suggestions.push('Summarize research findings');
    }
    
    return suggestions.slice(0, this.config.maxSuggestions);
  }
  
  /**
   * Get available actions for research mode
   */
  private getActions(context: ContextSnapshot): PluginAction[] {
    const actions: PluginAction[] = [];
    
    // Documentation actions
    actions.push(this.createAction({
      id: 'create-outline',
      label: 'Create Research Outline',
      description: 'Generate a structured outline for research',
      icon: '📋',
      execute: async (ctx) => {
        this.emit('research.outline_created', { context: ctx });
        // Implementation would create actual outline
      },
    }));
    
    // Citation actions
    actions.push(this.createAction({
      id: 'add-citations',
      label: 'Add Citations',
      description: 'Add citation placeholders and bibliography',
      icon: '📚',
      execute: async (ctx) => {
        this.emit('research.citations_added', { context: ctx });
        // Implementation would add citation format
      },
    }));
    
    // Analysis actions
    actions.push(this.createAction({
      id: 'analyze-sources',
      label: 'Analyze Sources',
      description: 'Analyze and categorize research sources',
      icon: '🔍',
      execute: async (ctx) => {
        this.emit('research.sources_analyzed', { context: ctx });
        // Implementation would analyze source quality and relevance
      },
    }));
    
    // Synthesis actions
    if (context.fileContext.openFiles?.length > 1) {
      actions.push(this.createAction({
        id: 'synthesize-info',
        label: 'Synthesize Information',
        description: 'Combine information from multiple sources',
        icon: '🧠',
        execute: async (ctx) => {
          this.emit('research.info_synthesized', { context: ctx });
          // Implementation would create synthesis document
        },
      }));
    }
    
    // Export actions
    actions.push(this.createAction({
      id: 'export-research',
      label: 'Export Research',
      description: 'Export research in various formats',
      icon: '📤',
      execute: async (ctx) => {
        this.emit('research.exported', { context: ctx });
        // Implementation would handle export formats
      },
    }));
    
    return actions;
  }
  
  /**
   * Event handlers
   */
  private handleFileOpened(event: any): void {
    const { filePath } = event.payload;
    
    // Track research-related file access
    if (this.isResearchFile(filePath)) {
      this.emit('research.file_accessed', { 
        filePath,
        timestamp: Date.now(),
        type: 'research_file'
      });
    }
  }
  
  private handleSearchPerformed(event: any): void {
    const { query, results } = event.payload;
    
    // Analyze search patterns for research insights
    this.emit('research.search_pattern', {
      query,
      resultCount: results?.length || 0,
      timestamp: Date.now(),
    });
  }
  
  /**
   * Helper methods
   */
  private isResearchFile(filePath: string): boolean {
    const researchExtensions = ['.md', '.txt', '.rst', '.wiki', '.tex', '.bib'];
    const researchDirs = ['docs', 'research', 'notes', 'references', 'papers'];
    
    const hasResearchExtension = researchExtensions.some(ext =>
      filePath.toLowerCase().endsWith(ext)
    );
    
    const isInResearchDir = researchDirs.some(dir =>
      filePath.toLowerCase().includes(`/${dir}/`)
    );
    
    return hasResearchExtension || isInResearchDir;
  }
}

// Export for plugin loader
export default CustomResearchModePlugin;
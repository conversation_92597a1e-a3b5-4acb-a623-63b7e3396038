/**
 * Plugin Loader System
 * 
 * Features:
 * - Dynamic plugin loading from multiple sources
 * - Plugin validation and security checking
 * - Dependency resolution and management
 * - Hot reloading and updates
 * - Plugin marketplace integration
 */

import { pluginRegistry, Plugin, PluginManifest, PluginLoadOptions } from '../core/PluginRegistry';
import { eventBus } from '../core/EventBus';
import { performanceMonitor } from '../utils/performance';

export interface PluginSource {
  type: 'file' | 'url' | 'marketplace' | 'builtin';
  location: string;
  metadata?: Record<string, any>;
}

export interface PluginPackage {
  manifest: PluginManifest;
  source: PluginSource;
  code?: string;
  assets?: Record<string, Blob>;
  signature?: string;
}

export interface LoaderConfig {
  trustedSources: string[];
  allowUntrusted: boolean;
  maxPluginSize: number;
  cacheEnabled: boolean;
  hotReloadEnabled: boolean;
  validateSignatures: boolean;
}

export interface PluginUpdateInfo {
  pluginId: string;
  currentVersion: string;
  availableVersion: string;
  changelog?: string;
  breaking: boolean;
}

class PluginLoader {
  private static instance: PluginLoader;
  
  private config: LoaderConfig = {
    trustedSources: ['builtin', 'marketplace'],
    allowUntrusted: false,
    maxPluginSize: 10 * 1024 * 1024, // 10MB
    cacheEnabled: true,
    hotReloadEnabled: true,
    validateSignatures: false,
  };
  
  // Plugin cache
  private packageCache = new Map<string, PluginPackage>();
  private loadedSources = new Set<string>();
  
  // File watching for hot reload
  private watchedFiles = new Map<string, FileSystemWatcher>();
  
  private constructor() {
    this.setupEventListeners();
  }
  
  static getInstance(): PluginLoader {
    if (!PluginLoader.instance) {
      PluginLoader.instance = new PluginLoader();
    }
    return PluginLoader.instance;
  }
  
  /**
   * Load plugins from various sources
   */
  async loadFromSource(source: PluginSource, options: PluginLoadOptions = {}): Promise<Plugin[]> {
    performanceMonitor.startTimer('plugin.load');
    
    try {
      // Validate source
      this.validateSource(source);
      
      // Get plugin packages
      const packages = await this.discoverPlugins(source);
      const loadedPlugins: Plugin[] = [];
      
      for (const pkg of packages) {
        try {
          const plugin = await this.loadPluginFromPackage(pkg, options);
          loadedPlugins.push(plugin);
          
          // Register with registry
          await pluginRegistry.registerPlugin(plugin, options);
          
          // Setup hot reloading if enabled
          if (this.config.hotReloadEnabled && source.type === 'file') {
            this.setupHotReload(pkg, plugin);
          }
          
        } catch (error) {
          console.error(`Failed to load plugin from ${pkg.source.location}:`, error);
          
          eventBus.emit({
            type: 'plugin.load_failed',
            payload: { 
              source: pkg.source, 
              manifest: pkg.manifest,
              error: error.message 
            },
            source: 'PluginLoader',
          });
        }
      }
      
      this.loadedSources.add(source.location);
      
      performanceMonitor.endTimer('plugin.load', { 
        source: source.type,
        count: loadedPlugins.length 
      });
      
      return loadedPlugins;
      
    } catch (error) {
      performanceMonitor.endTimer('plugin.load', { error: 'true' });
      throw error;
    }
  }
  
  /**
   * Load a single plugin from a package
   */
  async loadPluginFromPackage(pkg: PluginPackage, options: PluginLoadOptions = {}): Promise<Plugin> {
    // Validate package
    await this.validatePackage(pkg);
    
    // Check cache
    const cacheKey = `${pkg.manifest.id}@${pkg.manifest.version}`;
    if (this.config.cacheEnabled && this.packageCache.has(cacheKey)) {
      const cachedPkg = this.packageCache.get(cacheKey)!;
      return this.instantiatePlugin(cachedPkg);
    }
    
    // Load and instantiate plugin
    const plugin = await this.instantiatePlugin(pkg);
    
    // Cache package
    if (this.config.cacheEnabled) {
      this.packageCache.set(cacheKey, pkg);
    }
    
    return plugin;
  }
  
  /**
   * Discover available plugin updates
   */
  async checkForUpdates(): Promise<PluginUpdateInfo[]> {
    const updates: PluginUpdateInfo[] = [];
    const activePlugins = pluginRegistry.getAllPlugins();
    
    for (const plugin of activePlugins) {
      try {
        const updateInfo = await this.checkPluginUpdate(plugin);
        if (updateInfo) {
          updates.push(updateInfo);
        }
      } catch (error) {
        console.warn(`Failed to check updates for ${plugin.manifest.id}:`, error);
      }
    }
    
    return updates;
  }
  
  /**
   * Update a plugin to the latest version
   */
  async updatePlugin(pluginId: string, options: PluginLoadOptions = {}): Promise<Plugin> {
    const currentPlugin = pluginRegistry.getPlugin(pluginId);
    if (!currentPlugin) {
      throw new Error(`Plugin ${pluginId} not found`);
    }
    
    // Find update source
    const updateInfo = await this.checkPluginUpdate(currentPlugin);
    if (!updateInfo) {
      throw new Error(`No updates available for ${pluginId}`);
    }
    
    // Load updated plugin
    const source: PluginSource = {
      type: 'marketplace',
      location: `marketplace://${pluginId}@${updateInfo.availableVersion}`,
    };
    
    const packages = await this.discoverPlugins(source);
    if (packages.length === 0) {
      throw new Error(`Updated plugin package not found`);
    }
    
    const newPlugin = await this.loadPluginFromPackage(packages[0], options);
    
    // Hot reload the plugin
    if (this.config.hotReloadEnabled) {
      await pluginRegistry.reloadPlugin(pluginId, newPlugin);
    } else {
      // Manual update - deactivate old, register new
      await pluginRegistry.unregisterPlugin(pluginId);
      await pluginRegistry.registerPlugin(newPlugin, options);
    }
    
    eventBus.emit({
      type: 'plugin.updated',
      payload: { 
        pluginId,
        fromVersion: updateInfo.currentVersion,
        toVersion: updateInfo.availableVersion 
      },
      source: 'PluginLoader',
    });
    
    return newPlugin;
  }
  
  /**
   * Uninstall a plugin completely
   */
  async uninstallPlugin(pluginId: string): Promise<void> {
    // Unregister from registry
    await pluginRegistry.unregisterPlugin(pluginId);
    
    // Clean up cache
    const entries = Array.from(this.packageCache.entries());
    entries.forEach(([key, pkg]) => {
      if (pkg.manifest.id === pluginId) {
        this.packageCache.delete(key);
      }
    });
    
    // Clean up file watchers
    this.watchedFiles.forEach((watcher, path) => {
      if (path.includes(pluginId)) {
        watcher.close();
        this.watchedFiles.delete(path);
      }
    });
    
    eventBus.emit({
      type: 'plugin.uninstalled',
      payload: { pluginId },
      source: 'PluginLoader',
    });
  }
  
  /**
   * Plugin discovery from different sources
   */
  private async discoverPlugins(source: PluginSource): Promise<PluginPackage[]> {
    switch (source.type) {
      case 'file':
        return this.discoverFilePlugins(source.location);
      case 'url':
        return this.discoverUrlPlugins(source.location);
      case 'marketplace':
        return this.discoverMarketplacePlugins(source.location);
      case 'builtin':
        return this.discoverBuiltinPlugins();
      default:
        throw new Error(`Unsupported source type: ${source.type}`);
    }
  }
  
  private async discoverFilePlugins(path: string): Promise<PluginPackage[]> {
    // Simulate file system plugin discovery
    // In a real implementation, this would scan directories for plugin files
    const packages: PluginPackage[] = [];
    
    try {
      // This is a simulation - in reality you'd read from file system
      const manifestText = await this.readFile(`${path}/manifest.json`);
      const manifest: PluginManifest = JSON.parse(manifestText);
      
      const code = await this.readFile(`${path}/index.js`);
      
      packages.push({
        manifest,
        source: { type: 'file', location: path },
        code,
      });
    } catch (error) {
      console.warn(`Failed to discover file plugins at ${path}:`, error);
    }
    
    return packages;
  }
  
  private async discoverUrlPlugins(url: string): Promise<PluginPackage[]> {
    // Simulate network plugin loading
    const packages: PluginPackage[] = [];
    
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      packages.push({
        manifest: data.manifest,
        source: { type: 'url', location: url },
        code: data.code,
        assets: data.assets,
      });
    } catch (error) {
      console.warn(`Failed to discover URL plugins at ${url}:`, error);
    }
    
    return packages;
  }
  
  private async discoverMarketplacePlugins(query: string): Promise<PluginPackage[]> {
    // Simulate marketplace plugin discovery
    const packages: PluginPackage[] = [];
    
    try {
      // This would connect to a real plugin marketplace API
      const marketplaceUrl = `https://api.plugin-marketplace.com/search?q=${encodeURIComponent(query)}`;
      const response = await fetch(marketplaceUrl);
      
      if (response.ok) {
        const results = await response.json();
        
        for (const result of results.plugins) {
          packages.push({
            manifest: result.manifest,
            source: { type: 'marketplace', location: result.downloadUrl },
            signature: result.signature,
          });
        }
      }
    } catch (error) {
      console.warn(`Failed to discover marketplace plugins:`, error);
    }
    
    return packages;
  }
  
  private async discoverBuiltinPlugins(): Promise<PluginPackage[]> {
    // Return built-in plugins that ship with the system
    const packages: PluginPackage[] = [];
    
    // Example built-in plugins would be defined here
    // For now, return empty array as this is a framework
    
    return packages;
  }
  
  /**
   * Plugin instantiation and validation
   */
  private async instantiatePlugin(pkg: PluginPackage): Promise<Plugin> {
    if (!pkg.code) {
      throw new Error('Plugin package missing code');
    }
    
    try {
      // Create isolated execution context
      const pluginModule = this.executePluginCode(pkg.code, pkg.manifest);
      
      // Instantiate plugin class
      if (typeof pluginModule.default === 'function') {
        return new pluginModule.default(pkg.manifest);
      } else if (typeof pluginModule === 'function') {
        return new pluginModule(pkg.manifest);
      } else {
        throw new Error('Plugin must export a constructor function');
      }
    } catch (error) {
      throw new Error(`Failed to instantiate plugin: ${error.message}`);
    }
  }
  
  private executePluginCode(code: string, manifest: PluginManifest): any {
    // Create a sandboxed execution environment
    // In a real implementation, this would use proper sandboxing
    
    const context = {
      require: this.createRequireFunction(manifest),
      exports: {},
      module: { exports: {} },
      console: {
        log: (...args: any[]) => console.log(`[${manifest.id}]`, ...args),
        warn: (...args: any[]) => console.warn(`[${manifest.id}]`, ...args),
        error: (...args: any[]) => console.error(`[${manifest.id}]`, ...args),
      },
    };
    
    // Execute plugin code in context
    const func = new Function('require', 'exports', 'module', 'console', code);
    func(context.require, context.exports, context.module, context.console);
    
    return context.module.exports.default || context.module.exports;
  }
  
  private createRequireFunction(manifest: PluginManifest): (id: string) => any {
    const allowedModules = [
      'react',
      'react-dom',
      ...manifest.dependencies.filter(dep => dep.startsWith('npm:')),
    ];
    
    return (id: string) => {
      if (!allowedModules.includes(id)) {
        throw new Error(`Module '${id}' is not allowed`);
      }
      
      // In a real implementation, this would provide actual module loading
      // For now, return mock modules
      switch (id) {
        case 'react':
          return require('react');
        default:
          throw new Error(`Module '${id}' not found`);
      }
    };
  }
  
  /**
   * Validation and security
   */
  private validateSource(source: PluginSource): void {
    if (!this.config.allowUntrusted && !this.config.trustedSources.includes(source.type)) {
      throw new Error(`Untrusted source type: ${source.type}`);
    }
  }
  
  private async validatePackage(pkg: PluginPackage): Promise<void> {
    // Size validation
    if (pkg.code && pkg.code.length > this.config.maxPluginSize) {
      throw new Error(`Plugin size exceeds limit: ${pkg.code.length} bytes`);
    }
    
    // Signature validation
    if (this.config.validateSignatures && pkg.signature) {
      const isValid = await this.validateSignature(pkg);
      if (!isValid) {
        throw new Error('Invalid plugin signature');
      }
    }
    
    // Manifest validation
    this.validateManifest(pkg.manifest);
  }
  
  private validateManifest(manifest: PluginManifest): void {
    const required = ['id', 'name', 'version', 'type', 'apiVersion'];
    for (const field of required) {
      if (!manifest[field as keyof PluginManifest]) {
        throw new Error(`Manifest missing required field: ${field}`);
      }
    }
    
    // Version format validation
    if (!/^\d+\.\d+\.\d+/.test(manifest.version)) {
      throw new Error(`Invalid version format: ${manifest.version}`);
    }
  }
  
  private async validateSignature(pkg: PluginPackage): Promise<boolean> {
    // Simulate signature validation
    // In a real implementation, this would use proper cryptographic verification
    return pkg.signature === 'valid_signature_hash';
  }
  
  /**
   * Hot reloading
   */
  private setupHotReload(pkg: PluginPackage, plugin: Plugin): void {
    if (pkg.source.type !== 'file') return;
    
    const path = pkg.source.location;
    
    // Create file watcher (simulated)
    const watcher = this.createFileWatcher(path, async () => {
      try {
        console.log(`Hot reloading plugin: ${plugin.manifest.id}`);
        
        // Reload plugin package
        const updatedPackages = await this.discoverFilePlugins(path);
        if (updatedPackages.length > 0) {
          const updatedPlugin = await this.loadPluginFromPackage(updatedPackages[0]);
          await pluginRegistry.reloadPlugin(plugin.manifest.id, updatedPlugin);
        }
      } catch (error) {
        console.error(`Hot reload failed for ${plugin.manifest.id}:`, error);
      }
    });
    
    this.watchedFiles.set(path, watcher);
  }
  
  private createFileWatcher(path: string, callback: () => void): FileSystemWatcher {
    // Simulate file system watcher
    return {
      close: () => {
        // Cleanup watcher
      }
    };
  }
  
  /**
   * Update checking
   */
  private async checkPluginUpdate(plugin: Plugin): Promise<PluginUpdateInfo | null> {
    try {
      // Check marketplace for updates
      const response = await fetch(
        `https://api.plugin-marketplace.com/plugins/${plugin.manifest.id}/latest`
      );
      
      if (!response.ok) return null;
      
      const data = await response.json();
      
      if (this.isNewerVersion(data.version, plugin.manifest.version)) {
        return {
          pluginId: plugin.manifest.id,
          currentVersion: plugin.manifest.version,
          availableVersion: data.version,
          changelog: data.changelog,
          breaking: data.breaking || false,
        };
      }
    } catch (error) {
      console.warn(`Failed to check updates for ${plugin.manifest.id}:`, error);
    }
    
    return null;
  }
  
  private isNewerVersion(available: string, current: string): boolean {
    const parseVersion = (v: string) => v.split('.').map(Number);
    const [aMajor, aMinor, aPatch] = parseVersion(available);
    const [cMajor, cMinor, cPatch] = parseVersion(current);
    
    if (aMajor > cMajor) return true;
    if (aMajor < cMajor) return false;
    if (aMinor > cMinor) return true;
    if (aMinor < cMinor) return false;
    return aPatch > cPatch;
  }
  
  /**
   * Utility methods
   */
  private async readFile(path: string): Promise<string> {
    // Simulate file reading
    // In a real implementation, this would use proper file system APIs
    throw new Error(`File reading not implemented: ${path}`);
  }
  
  private setupEventListeners(): void {
    // Listen for system events
    eventBus.on('system.plugin_update_check', async () => {
      const updates = await this.checkForUpdates();
      if (updates.length > 0) {
        eventBus.emit({
          type: 'plugin.updates_available',
          payload: { updates },
          source: 'PluginLoader',
        });
      }
    });
  }
  
  /**
   * Configuration
   */
  updateConfig(updates: Partial<LoaderConfig>): void {
    this.config = { ...this.config, ...updates };
  }
  
  getConfig(): LoaderConfig {
    return { ...this.config };
  }
}

// Interfaces for file system simulation
interface FileSystemWatcher {
  close(): void;
}

// Export singleton instance
export const pluginLoader = PluginLoader.getInstance();
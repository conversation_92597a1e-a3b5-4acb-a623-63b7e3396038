/**
 * Base Plugin Implementation
 * 
 * Provides a foundation for creating plugins with common functionality,
 * lifecycle management, and utility methods.
 */

import { 
  Plugin, 
  PluginManifest, 
  PluginContext, 
  PluginCapability,
  ModeProvider,
  ContextAnalyzer,
  UIComponent,
  PluginAction
} from '../core/PluginRegistry';
import { ContextSnapshot } from '../types';

export abstract class BasePlugin implements Plugin {
  public readonly manifest: PluginManifest;
  protected context: PluginContext | null = null;
  protected isInitialized = false;
  protected isActive = false;
  
  // Plugin state
  protected config: Record<string, any> = {};
  protected capabilities: PluginCapability[] = [];
  
  constructor(manifest: PluginManifest) {
    this.manifest = manifest;
    this.config = manifest.config || {};
  }
  
  /**
   * Initialize the plugin with system context
   */
  async initialize(context: PluginContext): Promise<void> {
    if (this.isInitialized) {
      return;
    }
    
    this.context = context;
    
    // Request permissions
    for (const permission of this.manifest.permissions) {
      const granted = await context.requestPermission(permission);
      if (!granted) {
        throw new Error(`Permission ${permission.type} denied`);
      }
    }
    
    // Initialize plugin-specific logic
    await this.onInitialize();
    
    this.isInitialized = true;
  }
  
  /**
   * Activate the plugin and register capabilities
   */
  async activate(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Plugin must be initialized before activation');
    }
    
    if (this.isActive) {
      return;
    }
    
    // Activate plugin-specific logic
    await this.onActivate();
    
    // Register capabilities
    this.registerCapabilities();
    
    this.isActive = true;
  }
  
  /**
   * Deactivate the plugin and unregister capabilities
   */
  async deactivate(): Promise<void> {
    if (!this.isActive) {
      return;
    }
    
    // Unregister capabilities
    this.unregisterCapabilities();
    
    // Deactivate plugin-specific logic
    await this.onDeactivate();
    
    this.isActive = false;
  }
  
  /**
   * Destroy the plugin and clean up resources
   */
  async destroy(): Promise<void> {
    if (this.isActive) {
      await this.deactivate();
    }
    
    // Cleanup plugin-specific resources
    await this.onDestroy();
    
    this.context = null;
    this.isInitialized = false;
  }
  
  /**
   * Get plugin capabilities
   */
  getCapabilities(): PluginCapability[] {
    return [...this.capabilities];
  }
  
  /**
   * Update plugin configuration
   */
  updateConfig(updates: Record<string, any>): void {
    this.config = { ...this.config, ...updates };
    this.onConfigUpdate(this.config);
  }
  
  /**
   * Get current configuration
   */
  getConfig(): Record<string, any> {
    return { ...this.config };
  }
  
  /**
   * Abstract methods for plugin implementations
   */
  protected abstract onInitialize(): Promise<void>;
  protected abstract onActivate(): Promise<void>;
  protected abstract onDeactivate(): Promise<void>;
  protected abstract onDestroy(): Promise<void>;
  
  /**
   * Optional lifecycle hooks
   */
  protected onConfigUpdate(config: Record<string, any>): void {
    // Override in subclasses if needed
  }
  
  /**
   * Capability registration helpers
   */
  protected registerCapabilities(): void {
    // Override in subclasses to register specific capabilities
  }
  
  protected unregisterCapabilities(): void {
    // Override in subclasses to unregister specific capabilities
  }
  
  /**
   * Utility methods for common plugin functionality
   */
  protected assertContext(): PluginContext {
    if (!this.context) {
      throw new Error('Plugin context not available');
    }
    return this.context;
  }
  
  protected assertActive(): void {
    if (!this.isActive) {
      throw new Error('Plugin is not active');
    }
  }
  
  protected emit(eventType: string, payload: any): void {
    const context = this.assertContext();
    context.eventBus.emit({
      type: eventType,
      payload,
      source: this.manifest.id,
    });
  }
  
  protected on(eventType: string, handler: (event: any) => void): void {
    const context = this.assertContext();
    context.eventBus.on(eventType, handler);
  }
  
  protected createAction(config: {
    id: string;
    label: string;
    description: string;
    icon?: string;
    shortcut?: string;
    execute: (context: ContextSnapshot) => Promise<void>;
  }): PluginAction {
    return {
      id: `${this.manifest.id}.${config.id}`,
      label: config.label,
      description: config.description,
      icon: config.icon,
      shortcut: config.shortcut,
      execute: config.execute,
    };
  }
}

/**
 * Base Mode Provider Plugin
 */
export abstract class BaseModePlugin extends BasePlugin {
  protected modeProvider: ModeProvider | null = null;
  
  protected registerCapabilities(): void {
    super.registerCapabilities();
    
    if (this.modeProvider) {
      const context = this.assertContext();
      context.registerModeProvider?.(this.modeProvider);
      
      this.capabilities.push({
        type: 'mode',
        name: this.modeProvider.id,
        config: { displayName: this.modeProvider.displayName },
      });
    }
  }
  
  protected abstract createModeProvider(): ModeProvider;
  
  protected async onActivate(): Promise<void> {
    this.modeProvider = this.createModeProvider();
  }
  
  protected async onDeactivate(): Promise<void> {
    this.modeProvider = null;
  }
}

/**
 * Base Context Analyzer Plugin
 */
export abstract class BaseAnalyzerPlugin extends BasePlugin {
  protected analyzer: ContextAnalyzer | null = null;
  
  protected registerCapabilities(): void {
    super.registerCapabilities();
    
    if (this.analyzer) {
      const context = this.assertContext();
      context.registerContextAnalyzer?.(this.analyzer);
      
      this.capabilities.push({
        type: 'analyzer',
        name: this.analyzer.id,
        config: { priority: this.analyzer.priority },
      });
    }
  }
  
  protected abstract createAnalyzer(): ContextAnalyzer;
  
  protected async onActivate(): Promise<void> {
    this.analyzer = this.createAnalyzer();
  }
  
  protected async onDeactivate(): Promise<void> {
    this.analyzer = null;
  }
}

/**
 * Base UI Plugin
 */
export abstract class BaseUIPlugin extends BasePlugin {
  protected uiComponents: UIComponent[] = [];
  
  protected registerCapabilities(): void {
    super.registerCapabilities();
    
    const context = this.assertContext();
    this.uiComponents.forEach(component => {
      context.registerUIComponent?.(component);
      
      this.capabilities.push({
        type: 'ui',
        name: component.id,
        config: { type: component.type },
      });
    });
  }
  
  protected abstract createUIComponents(): UIComponent[];
  
  protected async onActivate(): Promise<void> {
    this.uiComponents = this.createUIComponents();
  }
  
  protected async onDeactivate(): Promise<void> {
    this.uiComponents = [];
  }
}

/**
 * Plugin Factory Utilities
 */
export class PluginFactory {
  static createModePlugin(config: {
    manifest: PluginManifest;
    modeConfig: {
      id: string;
      displayName: string;
      description: string;
      icon: string;
      color: string;
      isApplicable: (context: ContextSnapshot) => boolean;
      getScore: (context: ContextSnapshot) => number;
      getSuggestions: (context: ContextSnapshot, input?: string) => Promise<string[]>;
      getActions: (context: ContextSnapshot) => PluginAction[];
    };
  }): Plugin {
    return new (class extends BaseModePlugin {
      protected async onInitialize(): Promise<void> {
        // Custom initialization logic
      }
      
      protected async onDestroy(): Promise<void> {
        // Custom cleanup logic
      }
      
      protected createModeProvider(): ModeProvider {
        return {
          id: config.modeConfig.id as any,
          displayName: config.modeConfig.displayName,
          description: config.modeConfig.description,
          icon: config.modeConfig.icon,
          color: config.modeConfig.color,
          isApplicable: config.modeConfig.isApplicable,
          getScore: config.modeConfig.getScore,
          getSuggestions: config.modeConfig.getSuggestions,
          getActions: config.modeConfig.getActions,
        };
      }
    })(config.manifest);
  }
  
  static createAnalyzerPlugin(config: {
    manifest: PluginManifest;
    analyzerConfig: {
      id: string;
      name: string;
      priority: number;
      analyze: (context: ContextSnapshot) => Promise<any>;
      getFeatures: (context: ContextSnapshot) => Record<string, number>;
    };
  }): Plugin {
    return new (class extends BaseAnalyzerPlugin {
      protected async onInitialize(): Promise<void> {
        // Custom initialization logic
      }
      
      protected async onDestroy(): Promise<void> {
        // Custom cleanup logic
      }
      
      protected createAnalyzer(): ContextAnalyzer {
        return {
          id: config.analyzerConfig.id,
          name: config.analyzerConfig.name,
          priority: config.analyzerConfig.priority,
          analyze: config.analyzerConfig.analyze,
          getFeatures: config.analyzerConfig.getFeatures,
        };
      }
    })(config.manifest);
  }
}
import { ModeDefinition, ContextRule, ModeFeature } from '../types/mode.types';
import { ContextSnapshot } from '../types/context.types';
import { LearnFeatures } from '../types/mode-features.types';

// Learn Mode: Documentation, tutorials, and knowledge management
export const learnMode: ModeDefinition = {
  id: 'learn',
  name: 'Learn Mode',
  description: 'Documentation access, interactive tutorials, and knowledge base',
  icon: '📚',
  color: '#06B6D4', // Cyan
  
  contextRules: [
    {
      id: 'learn-documentation-files',
      type: 'file',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const filePath = context.fileContext.path?.toLowerCase() || '';
          const fileName = context.fileContext.name?.toLowerCase() || '';
          return (
            filePath.includes('readme') ||
            filePath.includes('docs') ||
            filePath.includes('documentation') ||
            filePath.includes('guide') ||
            filePath.includes('tutorial') ||
            fileName.endsWith('.md') ||
            fileName.endsWith('.mdx') ||
            fileName.endsWith('.rst')
          );
        },
        description: 'Documentation files',
      },
      weight: 0.9,
      confidence: 0.95,
    },
    {
      id: 'learn-search-pattern',
      type: 'pattern',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const recentActions = context.userContext.recentActions;
          const searchActions = recentActions.filter(a => 
            a.type === 'search' || 
            a.target?.includes('documentation')
          );
          return searchActions.length > 2;
        },
        description: 'Documentation search pattern',
      },
      weight: 0.7,
      confidence: 0.8,
    },
    {
      id: 'learn-keywords',
      type: 'keyword',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const recentActions = context.userContext.recentActions;
          const learnKeywords = ['learn', 'tutorial', 'guide', 'docs', 'help', 'how to', 'example'];
          return recentActions.some(action => 
            learnKeywords.some(keyword => 
              action.target?.toLowerCase().includes(keyword)
            )
          );
        },
        description: 'Learning-related keywords',
      },
      weight: 0.6,
      confidence: 0.7,
    },
    {
      id: 'learn-new-technology',
      type: 'state',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const dependencies = context.projectContext.dependencies || {};
          const recentDeps = Object.entries(dependencies).filter(([_, version]) => {
            // Check if dependency was recently added (simplified check)
            return version.includes('^') || version.includes('~');
          });
          return recentDeps.length > 3; // Multiple new dependencies
        },
        description: 'New technologies detected',
      },
      weight: 0.5,
      confidence: 0.6,
    },
  ],
  
  features: [
    {
      id: 'documentation-hub',
      name: 'Documentation Hub',
      type: 'panel',
      position: {
        area: 'right',
        order: 1,
        size: { width: 400 },
      },
      config: {
        sources: ['official', 'community', 'internal'],
        offlineCache: true,
        contextualDocs: true,
        searchEnabled: true,
      },
    },
    {
      id: 'tutorial-engine',
      name: 'Interactive Tutorial Engine',
      type: 'widget',
      position: {
        area: 'center',
        order: 1,
      },
      config: {
        interactiveTutorials: true,
        progressTracking: true,
        certifications: true,
        practiceMode: true,
      },
    },
    {
      id: 'knowledge-base',
      name: 'AI-Powered Knowledge Base',
      type: 'tool',
      config: {
        searchEnabled: true,
        aiAssistant: true,
        bookmarking: true,
        notesTaking: true,
      },
    },
    {
      id: 'code-examples',
      name: 'Code Examples Library',
      type: 'panel',
      position: {
        area: 'bottom',
        order: 1,
        size: { height: 200 },
      },
      config: {
        runnable: true,
        categorized: true,
        communityContributed: true,
        versionSpecific: true,
      },
    },
  ],
  
  shortcuts: [
    {
      key: 'h',
      modifiers: ['cmd', 'shift'],
      action: 'searchDocumentation',
      description: 'Search documentation',
    },
    {
      key: 't',
      modifiers: ['cmd', 'shift'],
      action: 'startTutorial',
      description: 'Start tutorial',
    },
    {
      key: 'g',
      modifiers: ['cmd', 'shift'],
      action: 'openKnowledgeBase',
      description: 'Open knowledge base',
    },
    {
      key: 'u',
      modifiers: ['cmd', 'shift'],
      action: 'browseExamples',
      description: 'Browse code examples',
    },
  ],
  
  activate: async (context: ContextSnapshot) => {
    console.log('Activating Learn Mode');
    
    // Initialize learning features
    await loadDocumentationSources(context);
    await setupTutorialSystem();
    
    // Start features
    initializeKnowledgeBase();
    loadRelevantExamples(context);
  },
  
  deactivate: async () => {
    console.log('Deactivating Learn Mode');
    
    // Save learning progress
    await saveLearningProgress();
    closeDocumentationConnections();
  },
};

// Feature initialization functions
async function loadDocumentationSources(context: ContextSnapshot) {
  // Connect to documentation sources
  // Index available docs
  // Load user bookmarks
}

async function setupTutorialSystem() {
  // Load tutorial content
  // Restore progress
  // Initialize interactive elements
}

function initializeKnowledgeBase() {
  // Connect to AI assistant
  // Load knowledge graph
  // Set up search index
}

async function loadRelevantExamples(context: ContextSnapshot) {
  // Analyze current context
  // Find relevant examples
  // Prepare runnable snippets
}

async function saveLearningProgress() {
  // Save tutorial progress
  // Export bookmarks
  // Store notes
}

function closeDocumentationConnections() {
  // Close API connections
  // Clear cache if needed
  // Release resources
}

// Export feature configuration
export const learnFeatureConfig: LearnFeatures = {
  documentationHub: {
    enabled: true,
    sources: ['official', 'community', 'stackoverflow', 'internal'],
    offlineCache: true,
    contextualDocs: true,
  },
  tutorialEngine: {
    enabled: true,
    interactiveTutorials: true,
    progressTracking: true,
    certifications: true,
  },
  knowledgeBase: {
    enabled: true,
    searchEnabled: true,
    aiAssistant: true,
    bookmarking: true,
  },
  codeExamples: {
    enabled: true,
    runnable: true,
    categorized: true,
    communityContributed: true,
  },
};
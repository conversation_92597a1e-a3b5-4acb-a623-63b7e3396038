import { ModeDefinition, ContextRule, ModeFeature } from '../types/mode.types';
import { ContextSnapshot } from '../types/context.types';
import { DeployFeatures } from '../types/mode-features.types';

// Deploy Mode: Deployment, CI/CD, and infrastructure management
export const deployMode: ModeDefinition = {
  id: 'deploy',
  name: 'Deploy Mode',
  description: 'Deployment automation, CI/CD management, and infrastructure configuration',
  icon: '🚀',
  color: '#F59E0B', // Amber
  
  contextRules: [
    {
      id: 'deploy-config-files',
      type: 'file',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const filePath = context.fileContext.path?.toLowerCase() || '';
          return (
            filePath.includes('dockerfile') ||
            filePath.includes('docker-compose') ||
            filePath.includes('.github/workflows') ||
            filePath.includes('.gitlab-ci') ||
            filePath.includes('jenkins') ||
            filePath.includes('k8s') ||
            filePath.includes('kubernetes') ||
            filePath.endsWith('.yml') ||
            filePath.endsWith('.yaml')
          );
        },
        description: 'Deployment configuration files',
      },
      weight: 0.9,
      confidence: 0.95,
    },
    {
      id: 'deploy-keywords',
      type: 'keyword',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const recentActions = context.userContext.recentActions;
          const deployKeywords = ['deploy', 'release', 'publish', 'build', 'ci', 'cd', 'pipeline'];
          return recentActions.some(action => 
            deployKeywords.some(keyword => 
              action.target?.toLowerCase().includes(keyword) ||
              action.type === 'command' && action.target?.includes(keyword)
            )
          );
        },
        description: 'Deployment-related keywords',
      },
      weight: 0.8,
      confidence: 0.85,
    },
    {
      id: 'deploy-branch-pattern',
      type: 'pattern',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const branch = context.environmentContext.gitStatus?.branch || '';
          return branch.includes('release') || 
                 branch.includes('deploy') || 
                 branch === 'main' || 
                 branch === 'master';
        },
        description: 'Deployment branch patterns',
      },
      weight: 0.6,
      confidence: 0.7,
    },
    {
      id: 'deploy-environment',
      type: 'state',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const buildTool = context.projectContext.buildTool;
          return buildTool !== null && buildTool !== undefined;
        },
        description: 'Project has build configuration',
      },
      weight: 0.5,
      confidence: 0.6,
    },
  ],
  
  features: [
    {
      id: 'pipeline-manager',
      name: 'CI/CD Pipeline Manager',
      type: 'panel',
      position: {
        area: 'center',
        order: 1,
      },
      config: {
        pipelineTypes: ['ci', 'cd', 'release'],
        parallelExecution: true,
        rollbackSupport: true,
        visualPipeline: true,
      },
    },
    {
      id: 'environment-configurator',
      name: 'Environment Configurator',
      type: 'widget',
      position: {
        area: 'right',
        order: 1,
        size: { width: 300 },
      },
      config: {
        environments: ['development', 'staging', 'production'],
        secretsManagement: true,
        configValidation: true,
        variableTemplating: true,
      },
    },
    {
      id: 'release-automator',
      name: 'Release Automation',
      type: 'tool',
      config: {
        versioningStrategy: 'semantic',
        changelogGeneration: true,
        tagManagement: true,
        releaseNotes: true,
      },
    },
    {
      id: 'monitoring-integration',
      name: 'Deployment Monitoring',
      type: 'panel',
      position: {
        area: 'bottom',
        order: 1,
        size: { height: 200 },
      },
      config: {
        metricsCollection: true,
        alertConfiguration: true,
        dashboardCreation: true,
        logAggregation: true,
      },
    },
  ],
  
  shortcuts: [
    {
      key: 'p',
      modifiers: ['cmd', 'alt'],
      action: 'triggerDeployment',
      description: 'Trigger deployment',
    },
    {
      key: 'l',
      modifiers: ['cmd', 'shift'],
      action: 'viewPipeline',
      description: 'View pipeline status',
    },
    {
      key: 'e',
      modifiers: ['cmd', 'shift'],
      action: 'switchEnvironment',
      description: 'Switch environment',
    },
    {
      key: 'k',
      modifiers: ['cmd', 'shift'],
      action: 'rollbackDeployment',
      description: 'Rollback deployment',
    },
  ],
  
  activate: async (context: ContextSnapshot) => {
    console.log('Activating Deploy Mode');
    
    // Initialize deployment features
    await loadDeploymentConfig(context);
    await connectToPipelines();
    
    // Start monitoring
    startDeploymentMonitoring();
    initializeEnvironments();
  },
  
  deactivate: async () => {
    console.log('Deactivating Deploy Mode');
    
    // Save deployment state
    await saveDeploymentState();
    stopDeploymentMonitoring();
  },
};

// Feature initialization functions
async function loadDeploymentConfig(context: ContextSnapshot) {
  // Load CI/CD configurations
  // Parse deployment scripts
  // Identify environments
}

async function connectToPipelines() {
  // Connect to CI/CD services
  // Load pipeline status
  // Set up webhooks
}

function startDeploymentMonitoring() {
  // Monitor deployment status
  // Track build progress
  // Collect metrics
}

function initializeEnvironments() {
  // Load environment configs
  // Validate secrets
  // Prepare deployment targets
}

async function saveDeploymentState() {
  // Save pipeline state
  // Export deployment logs
  // Update deployment history
}

function stopDeploymentMonitoring() {
  // Stop status polling
  // Clear webhooks
  // Release connections
}

// Export feature configuration
export const deployFeatureConfig: DeployFeatures = {
  pipelineManager: {
    enabled: true,
    pipelineTypes: ['ci', 'cd', 'release'],
    parallelExecution: true,
    rollbackSupport: true,
  },
  environmentConfigurator: {
    enabled: true,
    environments: ['development', 'staging', 'production', 'qa'],
    secretsManagement: true,
    configValidation: true,
  },
  releaseAutomator: {
    enabled: true,
    versioningStrategy: 'semantic',
    changelogGeneration: true,
    tagManagement: true,
  },
  monitoringIntegration: {
    enabled: true,
    metricsCollection: true,
    alertConfiguration: true,
    dashboardCreation: true,
  },
};
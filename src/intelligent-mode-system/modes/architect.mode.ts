import { ModeDefinition, ContextRule, ModeFeature } from '../types/mode.types';
import { ContextSnapshot } from '../types/context.types';
import { ArchitectFeatures } from '../types/mode-features.types';

// Architect Mode: System design and architecture planning
export const architectMode: ModeDefinition = {
  id: 'architect',
  name: 'Architect Mode',
  description: 'System design, architecture planning, and structural analysis',
  icon: '🏗️',
  color: '#3B82F6', // Blue
  
  contextRules: [
    {
      id: 'architect-file-pattern',
      type: 'file',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const filePath = context.fileContext.path?.toLowerCase() || '';
          return (
            filePath.includes('architecture') ||
            filePath.includes('design') ||
            filePath.includes('structure') ||
            filePath.includes('diagram') ||
            filePath.endsWith('.puml') ||
            filePath.endsWith('.drawio')
          );
        },
        description: 'Architecture or design related files',
      },
      weight: 0.8,
      confidence: 0.9,
    },
    {
      id: 'architect-keyword-pattern',
      type: 'keyword',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const recentActions = context.userContext.recentActions;
          const keywords = ['component', 'module', 'service', 'layer', 'pattern', 'dependency'];
          return recentActions.some(action => 
            keywords.some(keyword => 
              action.target?.toLowerCase().includes(keyword)
            )
          );
        },
        description: 'Architecture-related keywords in user actions',
      },
      weight: 0.6,
      confidence: 0.7,
    },
    {
      id: 'architect-project-size',
      type: 'state',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const deps = Object.keys(context.projectContext.dependencies || {});
          return deps.length > 20; // Large project
        },
        description: 'Large project with many dependencies',
      },
      weight: 0.4,
      confidence: 0.6,
    },
  ],
  
  features: [
    {
      id: 'system-designer',
      name: 'System Designer',
      type: 'panel',
      position: {
        area: 'center',
        order: 1,
      },
      config: {
        diagramTypes: ['component', 'sequence', 'class', 'flow'],
        exportFormats: ['svg', 'png', 'mermaid'],
        autoLayout: true,
      },
    },
    {
      id: 'dependency-analyzer',
      name: 'Dependency Analyzer',
      type: 'widget',
      position: {
        area: 'right',
        order: 1,
        size: { width: 300 },
      },
      config: {
        showCircular: true,
        showUnused: true,
        groupByType: true,
      },
    },
    {
      id: 'component-mapper',
      name: 'Component Relationship Mapper',
      type: 'tool',
      config: {
        autoDiscovery: true,
        trackRelationships: true,
        visualizeConnections: true,
      },
    },
    {
      id: 'architecture-patterns',
      name: 'Architecture Pattern Suggestions',
      type: 'widget',
      position: {
        area: 'left',
        order: 2,
      },
      config: {
        patterns: [
          'MVC',
          'MVP',
          'MVVM',
          'Microservices',
          'Layered',
          'Event-Driven',
          'Hexagonal',
        ],
      },
    },
  ],
  
  shortcuts: [
    {
      key: 'd',
      modifiers: ['cmd', 'shift'],
      action: 'openSystemDesigner',
      description: 'Open system designer',
    },
    {
      key: 'a',
      modifiers: ['cmd', 'shift'],
      action: 'analyzeDependencies',
      description: 'Analyze dependencies',
    },
    {
      key: 'm',
      modifiers: ['cmd', 'shift'],
      action: 'mapComponents',
      description: 'Map component relationships',
    },
  ],
  
  activate: async (context: ContextSnapshot) => {
    console.log('Activating Architect Mode');
    
    // Initialize architecture-specific features
    await initializeSystemDesigner();
    await loadProjectStructure(context);
    
    // Set up watchers for structural changes
    watchForArchitecturalChanges();
  },
  
  deactivate: async () => {
    console.log('Deactivating Architect Mode');
    
    // Clean up resources
    await saveDesignerState();
    stopArchitecturalWatchers();
  },
};

// Feature initialization functions
async function initializeSystemDesigner() {
  // Initialize the system design canvas
  // Load saved diagrams
  // Set up auto-save
}

async function loadProjectStructure(context: ContextSnapshot) {
  // Analyze project structure
  // Build component graph
  // Identify architectural patterns
}

function watchForArchitecturalChanges() {
  // Watch for file structure changes
  // Monitor dependency updates
  // Track component relationships
}

async function saveDesignerState() {
  // Save current diagrams
  // Persist component mappings
  // Store analysis results
}

function stopArchitecturalWatchers() {
  // Stop file watchers
  // Clear intervals
  // Release resources
}

// Export feature configuration
export const architectFeatureConfig: ArchitectFeatures = {
  systemDesigner: {
    enabled: true,
    diagramTypes: ['component', 'sequence', 'class', 'flow'],
    exportFormats: ['svg', 'png', 'mermaid'],
  },
  dependencyAnalyzer: {
    enabled: true,
    visualizationEnabled: true,
    circularDependencyDetection: true,
    unusedDependencyDetection: true,
  },
  componentMapper: {
    enabled: true,
    autoDiscovery: true,
    relationshipTracking: true,
  },
  architecturePatterns: {
    enabled: true,
    suggestPatterns: true,
    patternLibrary: [
      'MVC',
      'MVP',
      'MVVM',
      'Microservices',
      'Layered',
      'Event-Driven',
      'Hexagonal',
      'Clean Architecture',
      'Domain-Driven Design',
    ],
  },
};
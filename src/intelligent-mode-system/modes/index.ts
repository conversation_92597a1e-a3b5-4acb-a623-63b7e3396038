// Export all mode definitions
export { architectMode, architectFeatureConfig } from './architect.mode';
export { debugMode, debugFeatureConfig } from './debug.mode';
export { reviewMode, reviewFeatureConfig } from './review.mode';
export { deployMode, deployFeatureConfig } from './deploy.mode';
export { experimentMode, experimentFeatureConfig } from './experiment.mode';
export { learnMode, learnFeatureConfig } from './learn.mode';

// Import mode definitions
import { architectMode } from './architect.mode';
import { debugMode } from './debug.mode';
import { reviewMode } from './review.mode';
import { deployMode } from './deploy.mode';
import { experimentMode } from './experiment.mode';
import { learnMode } from './learn.mode';

// Export all modes as an array
export const allModes = [
  architectMode,
  debugMode,
  reviewMode,
  deployMode,
  experimentMode,
  learnMode,
];

// Export mode map for easy access
export const modeMap = {
  architect: architectMode,
  debug: debugMode,
  review: reviewMode,
  deploy: deployMode,
  experiment: experimentMode,
  learn: learnMode,
} as const;
import { ModeDefinition, ContextRule, ModeFeature } from '../types/mode.types';
import { ContextSnapshot } from '../types/context.types';
import { ExperimentFeatures } from '../types/mode-features.types';

// Experiment Mode: Rapid prototyping and experimentation
export const experimentMode: ModeDefinition = {
  id: 'experiment',
  name: 'Experiment Mode',
  description: 'Rapid prototyping, scratch pad functionality, and quick testing',
  icon: '🧪',
  color: '#10B981', // Green
  
  contextRules: [
    {
      id: 'experiment-scratch-files',
      type: 'file',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const filePath = context.fileContext.path?.toLowerCase() || '';
          const fileName = context.fileContext.name?.toLowerCase() || '';
          return (
            filePath.includes('scratch') ||
            filePath.includes('temp') ||
            filePath.includes('test') ||
            filePath.includes('playground') ||
            filePath.includes('sandbox') ||
            fileName.startsWith('test_') ||
            fileName.startsWith('tmp_')
          );
        },
        description: 'Scratch or temporary files',
      },
      weight: 0.9,
      confidence: 0.9,
    },
    {
      id: 'experiment-rapid-edits',
      type: 'pattern',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const recentActions = context.userContext.recentActions;
          const editActions = recentActions.filter(a => a.type === 'edit');
          const timeDiff = editActions.length > 1 ? 
            (editActions[0].timestamp - editActions[editActions.length - 1].timestamp) : 0;
          
          // Rapid editing pattern (many edits in short time)
          return editActions.length > 5 && timeDiff < 60000; // 5+ edits in 1 minute
        },
        description: 'Rapid editing pattern',
      },
      weight: 0.7,
      confidence: 0.8,
    },
    {
      id: 'experiment-keywords',
      type: 'keyword',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const recentActions = context.userContext.recentActions;
          const experimentKeywords = ['test', 'try', 'experiment', 'prototype', 'demo', 'poc'];
          return recentActions.some(action => 
            experimentKeywords.some(keyword => 
              action.target?.toLowerCase().includes(keyword)
            )
          );
        },
        description: 'Experimentation keywords',
      },
      weight: 0.6,
      confidence: 0.7,
    },
    {
      id: 'experiment-new-file',
      type: 'state',
      condition: {
        evaluate: (context: ContextSnapshot) => {
          const fileSize = context.fileContext.size || 0;
          const lastModified = context.fileContext.lastModified || 0;
          const isNew = Date.now() - lastModified < 300000; // Created in last 5 minutes
          return fileSize < 1000 && isNew; // Small, new file
        },
        description: 'New, small file',
      },
      weight: 0.5,
      confidence: 0.6,
    },
  ],
  
  features: [
    {
      id: 'scratch-pad',
      name: 'Multi-language Scratch Pad',
      type: 'panel',
      position: {
        area: 'center',
        order: 1,
      },
      config: {
        autoSave: true,
        syntaxHighlighting: true,
        multiLanguageSupport: true,
        snippetLibrary: true,
      },
    },
    {
      id: 'rapid-prototyper',
      name: 'Rapid Prototyper',
      type: 'widget',
      position: {
        area: 'right',
        order: 1,
        size: { width: 400 },
      },
      config: {
        livePreview: true,
        hotReload: true,
        mockDataGeneration: true,
        templateLibrary: true,
      },
    },
    {
      id: 'sandbox-environment',
      name: 'Isolated Sandbox',
      type: 'tool',
      config: {
        isolation: true,
        resourceLimits: true,
        snapshotting: true,
        resetCapability: true,
      },
    },
    {
      id: 'code-playground',
      name: 'Interactive Playground',
      type: 'panel',
      position: {
        area: 'bottom',
        order: 1,
        size: { height: 250 },
      },
      config: {
        interactiveExecution: true,
        shareableLinks: true,
        embeddedDocs: true,
        outputVisualization: true,
      },
    },
  ],
  
  shortcuts: [
    {
      key: 'n',
      modifiers: ['cmd', 'shift'],
      action: 'newScratchPad',
      description: 'New scratch pad',
    },
    {
      key: 'x',
      modifiers: ['cmd', 'shift'],
      action: 'runExperiment',
      description: 'Run experiment',
    },
    {
      key: 's',
      modifiers: ['cmd', 'shift'],
      action: 'saveSnapshot',
      description: 'Save experiment snapshot',
    },
    {
      key: 'w',
      modifiers: ['cmd', 'shift'],
      action: 'clearSandbox',
      description: 'Clear sandbox',
    },
  ],
  
  activate: async (context: ContextSnapshot) => {
    console.log('Activating Experiment Mode');
    
    // Initialize experimentation features
    await setupSandboxEnvironment();
    await loadScratchPads();
    
    // Start features
    startLivePreview();
    initializePlayground();
  },
  
  deactivate: async () => {
    console.log('Deactivating Experiment Mode');
    
    // Save experiment state
    await saveExperiments();
    cleanupSandbox();
  },
};

// Feature initialization functions
async function setupSandboxEnvironment() {
  // Create isolated environment
  // Set resource limits
  // Initialize snapshot system
}

async function loadScratchPads() {
  // Load saved scratch pads
  // Create default templates
  // Set up auto-save
}

function startLivePreview() {
  // Initialize live preview
  // Set up hot reload
  // Configure mock data
}

function initializePlayground() {
  // Set up code execution
  // Load documentation
  // Configure output display
}

async function saveExperiments() {
  // Save scratch pads
  // Export snapshots
  // Store experiment history
}

function cleanupSandbox() {
  // Clear temporary files
  // Release resources
  // Reset environment
}

// Export feature configuration
export const experimentFeatureConfig: ExperimentFeatures = {
  scratchPad: {
    enabled: true,
    autoSave: true,
    syntaxHighlighting: true,
    multiLanguageSupport: true,
  },
  rapidPrototyper: {
    enabled: true,
    livePreview: true,
    hotReload: true,
    mockDataGeneration: true,
  },
  sandboxEnvironment: {
    enabled: true,
    isolation: true,
    resourceLimits: true,
    snapshotting: true,
  },
  codePlayground: {
    enabled: true,
    interactiveExecution: true,
    shareableLinks: true,
    embeddedDocs: true,
  },
};
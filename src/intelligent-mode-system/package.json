{"name": "intelligent-mode-system", "version": "1.0.0", "description": "Intelligent mode selection and context analysis system", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc && vite build", "dev": "vite", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:performance": "vitest run --reporter=verbose --testNamePattern=\"Performance\"", "test:quality": "vitest run --reporter=verbose --testNamePattern=\"Quality\"", "test:e2e": "vitest run --reporter=verbose --testNamePattern=\"E2E\"", "test:integration": "vitest run --reporter=verbose --testNamePattern=\"Integration\"", "lint": "eslint . --ext .ts,.tsx --fix", "lint:check": "eslint . --ext .ts,.tsx", "typecheck": "tsc --noEmit", "clean": "rimraf dist coverage test-results.json", "prepack": "npm run clean && npm run build", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "validate": "npm run lint:check && npm run typecheck && npm run test && npm run build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.0", "rimraf": "^5.0.0", "typescript": "^5.2.0", "vite": "^5.0.0", "vitest": "^1.0.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "files": ["dist", "README.md", "CHANGELOG.md", "LICENSE"], "keywords": ["intelligent", "mode", "context", "analysis", "ai", "machine-learning", "plugin", "system", "typescript", "react"], "author": "<PERSON> Assistant", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/claudia/intelligent-mode-system.git"}, "bugs": {"url": "https://github.com/claudia/intelligent-mode-system/issues"}, "homepage": "https://github.com/claudia/intelligent-mode-system#readme"}
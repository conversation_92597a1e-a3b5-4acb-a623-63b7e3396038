import { 
  ContextSnapshot, 
  ModeId, 
  ModeProbabilities,
  FileContext,
  ProjectContext,
  UserContext,
  EnvironmentContext,
  UserAction
} from '../types';
import { modeRegistry } from '../core/ModeRegistry';
import { eventBus } from '../core/EventBus';
import { memoryCache } from '../core/CacheSystem';
import { performanceMonitor } from '../utils/performance';

/**
 * Enhanced Context Inference System with Machine Learning-based Pattern Recognition
 * 
 * Key Enhancements:
 * - Weighted multi-factor scoring algorithm
 * - Pattern recognition and learning from user behavior
 * - Time-decay for recent actions
 * - Contextual relationship analysis
 * - Confidence-based recommendations
 */

interface ContextPattern {
  id: string;
  pattern: string;
  modeAffinity: Record<ModeId, number>;
  weight: number;
  frequency: number;
  lastSeen: number;
}

interface ContextFeature {
  name: string;
  value: number;
  weight: number;
  category: 'file' | 'project' | 'user' | 'environment';
}

interface ModeScoreFactors {
  base: number;
  fileContext: number;
  projectContext: number;
  userBehavior: number;
  environmentState: number;
  patternMatch: number;
  temporalBoost: number;
}

export class EnhancedContextInference {
  private static instance: EnhancedContextInference;
  
  // Pattern recognition storage
  private patterns: Map<string, ContextPattern> = new Map();
  private featureWeights: Map<string, number> = new Map();
  private modeTransitionHistory: Array<{ from: ModeId; to: ModeId; context: ContextSnapshot; timestamp: number }> = [];
  
  // Configuration
  private readonly config = {
    minConfidenceThreshold: 0.65,
    patternRetentionDays: 30,
    maxHistorySize: 1000,
    learningRate: 0.1,
    timeDecayFactor: 0.95,
    featureExtraction: {
      maxFeatures: 50,
      minFeatureWeight: 0.01,
    },
  };

  private constructor() {
    this.initializeFeatureWeights();
    this.loadPatterns();
  }

  static getInstance(): EnhancedContextInference {
    if (!EnhancedContextInference.instance) {
      EnhancedContextInference.instance = new EnhancedContextInference();
    }
    return EnhancedContextInference.instance;
  }

  /**
   * Enhanced mode probability calculation with machine learning features
   */
  async calculateModeProbabilities(context: ContextSnapshot): Promise<ModeProbabilities> {
    performanceMonitor.startTimer('enhanced.inference');

    try {
      // Extract features from context
      const features = this.extractFeatures(context);
      
      // Calculate base scores for each mode
      const modeScores = new Map<ModeId, ModeScoreFactors>();
      const modes = modeRegistry.getAllModes();

      for (const mode of modes) {
        const scores = await this.calculateModeScore(mode.id, context, features);
        modeScores.set(mode.id, scores);
      }

      // Apply pattern recognition boost
      this.applyPatternRecognition(modeScores, context);

      // Convert to probabilities
      const probabilities = this.normalizeToProbabilities(modeScores);

      // Learn from this inference
      this.updatePatterns(context, probabilities);

      performanceMonitor.endTimer('enhanced.inference');
      return probabilities;
    } catch (error) {
      performanceMonitor.endTimer('enhanced.inference', { error: 'true' });
      throw error;
    }
  }

  /**
   * Get confident mode recommendation with explanation
   */
  getRecommendation(probabilities: ModeProbabilities): {
    mode: ModeId;
    confidence: number;
    reasoning: string[];
  } | null {
    let topMode: ModeId | null = null;
    let topProbability = 0;
    let secondProbability = 0;

    for (const [modeId, probability] of Object.entries(probabilities)) {
      if (probability > topProbability) {
        secondProbability = topProbability;
        topProbability = probability;
        topMode = modeId as ModeId;
      } else if (probability > secondProbability) {
        secondProbability = probability;
      }
    }

    if (!topMode || topProbability < this.config.minConfidenceThreshold) {
      return null;
    }

    // Calculate confidence based on separation from second choice
    const confidence = topProbability * (1 + (topProbability - secondProbability));
    
    return {
      mode: topMode,
      confidence: Math.min(confidence, 1),
      reasoning: this.generateReasoning(topMode, probabilities),
    };
  }

  /**
   * Learn from user mode transitions
   */
  recordModeTransition(from: ModeId, to: ModeId, context: ContextSnapshot): void {
    // Record transition
    this.modeTransitionHistory.push({
      from,
      to,
      context,
      timestamp: Date.now(),
    });

    // Limit history size
    if (this.modeTransitionHistory.length > this.config.maxHistorySize) {
      this.modeTransitionHistory = this.modeTransitionHistory.slice(-this.config.maxHistorySize);
    }

    // Update patterns based on transition
    this.learnFromTransition(from, to, context);

    // Persist patterns
    this.savePatterns();
  }

  /**
   * Extract features from context for ML processing
   */
  private extractFeatures(context: ContextSnapshot): ContextFeature[] {
    const features: ContextFeature[] = [];

    // File context features
    this.extractFileFeatures(context.fileContext, features);
    
    // Project context features
    this.extractProjectFeatures(context.projectContext, features);
    
    // User behavior features
    this.extractUserFeatures(context.userContext, features);
    
    // Environment features
    this.extractEnvironmentFeatures(context.environmentContext, features);

    // Sort by weight and limit
    return features
      .sort((a, b) => b.weight - a.weight)
      .slice(0, this.config.featureExtraction.maxFeatures);
  }

  private extractFileFeatures(fileContext: FileContext, features: ContextFeature[]): void {
    // File type feature
    const fileExt = fileContext.path.split('.').pop()?.toLowerCase() || '';
    features.push({
      name: `file.ext.${fileExt}`,
      value: 1,
      weight: this.getFeatureWeight(`file.ext.${fileExt}`),
      category: 'file',
    });

    // File path features
    const pathParts = fileContext.path.split('/');
    pathParts.forEach((part, index) => {
      if (part && index < 3) { // Only first 3 levels
        features.push({
          name: `file.path.${index}.${part}`,
          value: 1,
          weight: this.getFeatureWeight(`file.path.${index}.${part}`) * (1 - index * 0.2),
          category: 'file',
        });
      }
    });

    // Error state
    if (fileContext.hasErrors) {
      features.push({
        name: 'file.hasErrors',
        value: 1,
        weight: this.getFeatureWeight('file.hasErrors'),
        category: 'file',
      });
    }

    // File freshness (how recently modified)
    const hoursSinceModified = (Date.now() - fileContext.lastModified) / (1000 * 60 * 60);
    features.push({
      name: 'file.freshness',
      value: Math.exp(-hoursSinceModified / 24), // Exponential decay over 24 hours
      weight: this.getFeatureWeight('file.freshness'),
      category: 'file',
    });
  }

  private extractProjectFeatures(projectContext: ProjectContext, features: ContextFeature[]): void {
    // Project type
    features.push({
      name: `project.type.${projectContext.type}`,
      value: 1,
      weight: this.getFeatureWeight(`project.type.${projectContext.type}`),
      category: 'project',
    });

    // Project size
    const size = projectContext.structure.fileCount;
    const sizeCategory = size < 100 ? 'small' : size < 1000 ? 'medium' : 'large';
    features.push({
      name: `project.size.${sizeCategory}`,
      value: 1,
      weight: this.getFeatureWeight(`project.size.${sizeCategory}`),
      category: 'project',
    });

    // Has tests
    if (projectContext.structure.hasTests) {
      features.push({
        name: 'project.hasTests',
        value: 1,
        weight: this.getFeatureWeight('project.hasTests'),
        category: 'project',
      });
    }

    // Dependencies
    const depCount = Object.keys(projectContext.dependencies).length;
    features.push({
      name: 'project.dependencies',
      value: Math.min(depCount / 50, 1), // Normalize to 0-1
      weight: this.getFeatureWeight('project.dependencies'),
      category: 'project',
    });
  }

  private extractUserFeatures(userContext: UserContext, features: ContextFeature[]): void {
    const recentActions = userContext.recentActions.slice(-20);
    
    // Action type distribution
    const actionCounts = new Map<string, number>();
    recentActions.forEach(action => {
      const count = actionCounts.get(action.type) || 0;
      actionCounts.set(action.type, count + 1);
    });

    actionCounts.forEach((count, type) => {
      features.push({
        name: `user.action.${type}`,
        value: count / recentActions.length,
        weight: this.getFeatureWeight(`user.action.${type}`),
        category: 'user',
      });
    });

    // Action velocity (actions per minute)
    if (recentActions.length > 1) {
      const timeSpan = recentActions[recentActions.length - 1].timestamp - recentActions[0].timestamp;
      const velocity = recentActions.length / (timeSpan / 60000);
      features.push({
        name: 'user.velocity',
        value: Math.min(velocity / 10, 1), // Normalize to 0-1
        weight: this.getFeatureWeight('user.velocity'),
        category: 'user',
      });
    }

    // Session duration
    const sessionMinutes = userContext.sessionDuration / 60000;
    features.push({
      name: 'user.sessionDuration',
      value: Math.min(sessionMinutes / 120, 1), // Normalize to 2 hours
      weight: this.getFeatureWeight('user.sessionDuration'),
      category: 'user',
    });

    // Time since last activity
    const idleMinutes = (Date.now() - userContext.lastActivity) / 60000;
    features.push({
      name: 'user.idleTime',
      value: Math.exp(-idleMinutes / 5), // Exponential decay over 5 minutes
      weight: this.getFeatureWeight('user.idleTime'),
      category: 'user',
    });
  }

  private extractEnvironmentFeatures(envContext: EnvironmentContext, features: ContextFeature[]): void {
    // Running processes
    envContext.runningProcesses.forEach(process => {
      features.push({
        name: `env.process.${process.type}`,
        value: 1,
        weight: this.getFeatureWeight(`env.process.${process.type}`),
        category: 'environment',
      });
    });

    // Git state
    if (envContext.gitStatus) {
      const git = envContext.gitStatus;
      
      if (git.hasConflicts) {
        features.push({
          name: 'env.git.conflicts',
          value: 1,
          weight: this.getFeatureWeight('env.git.conflicts'),
          category: 'environment',
        });
      }

      if (git.staged.length > 0) {
        features.push({
          name: 'env.git.staged',
          value: Math.min(git.staged.length / 10, 1),
          weight: this.getFeatureWeight('env.git.staged'),
          category: 'environment',
        });
      }

      if (git.ahead > 0 || git.behind > 0) {
        features.push({
          name: 'env.git.diverged',
          value: 1,
          weight: this.getFeatureWeight('env.git.diverged'),
          category: 'environment',
        });
      }
    }

    // Resource usage
    const resources = envContext.systemResources;
    if (resources.cpuUsage > 0.8) {
      features.push({
        name: 'env.highCpu',
        value: resources.cpuUsage,
        weight: this.getFeatureWeight('env.highCpu'),
        category: 'environment',
      });
    }
  }

  /**
   * Calculate detailed score for a specific mode
   */
  private async calculateModeScore(
    modeId: ModeId, 
    context: ContextSnapshot, 
    features: ContextFeature[]
  ): Promise<ModeScoreFactors> {
    const scores: ModeScoreFactors = {
      base: 0.1, // Base probability
      fileContext: 0,
      projectContext: 0,
      userBehavior: 0,
      environmentState: 0,
      patternMatch: 0,
      temporalBoost: 0,
    };

    // Calculate category scores based on features
    features.forEach(feature => {
      const modeAffinity = this.getModeAffinity(feature.name, modeId);
      const score = feature.value * feature.weight * modeAffinity;

      switch (feature.category) {
        case 'file':
          scores.fileContext += score;
          break;
        case 'project':
          scores.projectContext += score;
          break;
        case 'user':
          scores.userBehavior += score;
          break;
        case 'environment':
          scores.environmentState += score;
          break;
      }
    });

    // Apply temporal boost for recently used modes
    const recentTransition = this.getRecentTransitionToMode(modeId);
    if (recentTransition) {
      const minutesSince = (Date.now() - recentTransition.timestamp) / 60000;
      scores.temporalBoost = 0.2 * Math.exp(-minutesSince / 30); // Decay over 30 minutes
    }

    return scores;
  }

  /**
   * Apply pattern recognition to boost scores
   */
  private applyPatternRecognition(
    modeScores: Map<ModeId, ModeScoreFactors>, 
    context: ContextSnapshot
  ): void {
    // Generate context signature
    const signature = this.generateContextSignature(context);
    
    // Find matching patterns
    this.patterns.forEach((pattern, patternId) => {
      const similarity = this.calculatePatternSimilarity(signature, pattern.pattern);
      if (similarity > 0.7) {
        // Apply pattern boost to mode affinities
        Object.entries(pattern.modeAffinity).forEach(([modeId, affinity]) => {
          const scores = modeScores.get(modeId as ModeId);
          if (scores) {
            scores.patternMatch += similarity * affinity * pattern.weight;
          }
        });
      }
    });
  }

  /**
   * Normalize scores to probabilities
   */
  private normalizeToProbabilities(modeScores: Map<ModeId, ModeScoreFactors>): ModeProbabilities {
    const probabilities: ModeProbabilities = {};
    let totalScore = 0;

    // Calculate total scores
    modeScores.forEach((factors, modeId) => {
      const totalFactorScore = Object.values(factors).reduce((sum, score) => sum + score, 0);
      probabilities[modeId] = totalFactorScore;
      totalScore += totalFactorScore;
    });

    // Normalize to sum to 1
    if (totalScore > 0) {
      Object.keys(probabilities).forEach(modeId => {
        probabilities[modeId] = probabilities[modeId] / totalScore;
      });
    }

    return probabilities;
  }

  /**
   * Learn from context and update patterns
   */
  private updatePatterns(context: ContextSnapshot, probabilities: ModeProbabilities): void {
    const signature = this.generateContextSignature(context);
    const patternId = this.hashSignature(signature);

    let pattern = this.patterns.get(patternId);
    if (!pattern) {
      pattern = {
        id: patternId,
        pattern: signature,
        modeAffinity: {},
        weight: 0.1,
        frequency: 0,
        lastSeen: Date.now(),
      };
      this.patterns.set(patternId, pattern);
    }

    // Update pattern with current probabilities
    Object.entries(probabilities).forEach(([modeId, probability]) => {
      const currentAffinity = pattern!.modeAffinity[modeId as ModeId] || 0;
      pattern!.modeAffinity[modeId as ModeId] = 
        currentAffinity * (1 - this.config.learningRate) + 
        probability * this.config.learningRate;
    });

    pattern.frequency++;
    pattern.lastSeen = Date.now();
    pattern.weight = Math.min(pattern.weight * 1.05, 1); // Slowly increase weight

    // Clean old patterns
    this.cleanOldPatterns();
  }

  /**
   * Learn from explicit mode transitions
   */
  private learnFromTransition(from: ModeId, to: ModeId, context: ContextSnapshot): void {
    // Boost the pattern that led to this transition
    const signature = this.generateContextSignature(context);
    const patternId = this.hashSignature(signature);

    let pattern = this.patterns.get(patternId);
    if (!pattern) {
      pattern = {
        id: patternId,
        pattern: signature,
        modeAffinity: {},
        weight: 0.2,
        frequency: 1,
        lastSeen: Date.now(),
      };
      this.patterns.set(patternId, pattern);
    }

    // Strongly boost the target mode
    pattern.modeAffinity[to] = (pattern.modeAffinity[to] || 0) * 0.7 + 0.3;
    
    // Slightly decrease the source mode
    pattern.modeAffinity[from] = (pattern.modeAffinity[from] || 0) * 0.9;

    pattern.frequency++;
    pattern.lastSeen = Date.now();
    pattern.weight = Math.min(pattern.weight * 1.1, 1);
  }

  /**
   * Generate a signature string from context
   */
  private generateContextSignature(context: ContextSnapshot): string {
    const parts: string[] = [];

    // File signature
    const fileExt = context.fileContext.path.split('.').pop() || 'unknown';
    const filePath = context.fileContext.path.split('/').slice(-3).join('/');
    parts.push(`file:${fileExt}:${filePath}`);

    // Project signature
    parts.push(`project:${context.projectContext.type}`);

    // Recent actions signature
    const actionTypes = context.userContext.recentActions
      .slice(-5)
      .map(a => a.type)
      .join(',');
    parts.push(`actions:${actionTypes}`);

    // Environment signature
    if (context.environmentContext.gitStatus) {
      parts.push(`git:${context.environmentContext.gitStatus.hasConflicts ? 'conflicts' : 'clean'}`);
    }

    return parts.join('|');
  }

  /**
   * Calculate similarity between two pattern signatures
   */
  private calculatePatternSimilarity(sig1: string, sig2: string): number {
    const parts1 = sig1.split('|');
    const parts2 = sig2.split('|');

    let matches = 0;
    let total = Math.max(parts1.length, parts2.length);

    parts1.forEach((part, index) => {
      if (parts2[index] === part) {
        matches++;
      } else if (parts2[index] && this.partialMatch(part, parts2[index])) {
        matches += 0.5;
      }
    });

    return matches / total;
  }

  private partialMatch(part1: string, part2: string): boolean {
    const [type1, ...values1] = part1.split(':');
    const [type2, ...values2] = part2.split(':');

    if (type1 !== type2) return false;

    // Check if any values match
    return values1.some(v1 => values2.includes(v1));
  }

  private hashSignature(signature: string): string {
    // Simple hash function for pattern ID
    let hash = 0;
    for (let i = 0; i < signature.length; i++) {
      const char = signature.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString(36);
  }

  /**
   * Get feature weight with learning
   */
  private getFeatureWeight(featureName: string): number {
    return this.featureWeights.get(featureName) || 0.05;
  }

  /**
   * Get mode affinity for a feature
   */
  private getModeAffinity(featureName: string, modeId: ModeId): number {
    // This would typically be learned from data
    const affinities: Record<string, Partial<Record<ModeId, number>>> = {
      'file.ext.ts': { architect: 0.8, debug: 0.6 },
      'file.ext.tsx': { architect: 0.7, debug: 0.5, experiment: 0.6 },
      'file.ext.md': { learn: 0.9, review: 0.6 },
      'file.ext.yml': { deploy: 0.8 },
      'file.hasErrors': { debug: 0.9 },
      'project.hasTests': { debug: 0.6, review: 0.7 },
      'user.action.edit': { experiment: 0.6, architect: 0.5 },
      'user.action.search': { learn: 0.7, debug: 0.6 },
      'env.git.conflicts': { review: 0.9 },
      'env.git.staged': { review: 0.7, deploy: 0.6 },
      'env.process.test': { debug: 0.8 },
      'env.process.build': { deploy: 0.8 },
    };

    const featureAffinities = affinities[featureName];
    if (featureAffinities) {
      return featureAffinities[modeId] || 0.1;
    }

    // Check partial matches
    for (const [pattern, modeAffinities] of Object.entries(affinities)) {
      if (featureName.startsWith(pattern.split('.').slice(0, 2).join('.'))) {
        return modeAffinities[modeId] || 0.1;
      }
    }

    return 0.1; // Default affinity
  }

  private getRecentTransitionToMode(modeId: ModeId): typeof this.modeTransitionHistory[0] | null {
    for (let i = this.modeTransitionHistory.length - 1; i >= 0; i--) {
      if (this.modeTransitionHistory[i].to === modeId) {
        return this.modeTransitionHistory[i];
      }
    }
    return null;
  }

  /**
   * Generate reasoning for mode recommendation
   */
  private generateReasoning(mode: ModeId, probabilities: ModeProbabilities): string[] {
    const reasoning: string[] = [];
    const probability = probabilities[mode];

    reasoning.push(`${mode} mode has ${(probability * 100).toFixed(1)}% probability`);

    // Add specific reasoning based on mode
    switch (mode) {
      case 'debug':
        reasoning.push('Detected error conditions or debugging patterns');
        break;
      case 'architect':
        reasoning.push('Working with system structure or configuration files');
        break;
      case 'review':
        reasoning.push('Git changes detected, ready for review');
        break;
      case 'deploy':
        reasoning.push('Deployment configuration or build process active');
        break;
      case 'experiment':
        reasoning.push('High edit frequency suggests experimentation');
        break;
      case 'learn':
        reasoning.push('Documentation or search activity detected');
        break;
    }

    return reasoning;
  }

  /**
   * Initialize default feature weights
   */
  private initializeFeatureWeights(): void {
    // File features
    this.featureWeights.set('file.ext.ts', 0.8);
    this.featureWeights.set('file.ext.tsx', 0.8);
    this.featureWeights.set('file.ext.js', 0.7);
    this.featureWeights.set('file.ext.jsx', 0.7);
    this.featureWeights.set('file.ext.md', 0.6);
    this.featureWeights.set('file.ext.yml', 0.7);
    this.featureWeights.set('file.ext.yaml', 0.7);
    this.featureWeights.set('file.hasErrors', 0.9);
    this.featureWeights.set('file.freshness', 0.6);

    // Project features
    this.featureWeights.set('project.type.node', 0.5);
    this.featureWeights.set('project.type.python', 0.5);
    this.featureWeights.set('project.hasTests', 0.6);
    this.featureWeights.set('project.dependencies', 0.4);

    // User features
    this.featureWeights.set('user.action.edit', 0.7);
    this.featureWeights.set('user.action.navigate', 0.5);
    this.featureWeights.set('user.action.search', 0.8);
    this.featureWeights.set('user.action.debug', 0.9);
    this.featureWeights.set('user.velocity', 0.6);
    this.featureWeights.set('user.sessionDuration', 0.4);
    this.featureWeights.set('user.idleTime', 0.5);

    // Environment features
    this.featureWeights.set('env.process.test', 0.8);
    this.featureWeights.set('env.process.build', 0.8);
    this.featureWeights.set('env.git.conflicts', 0.9);
    this.featureWeights.set('env.git.staged', 0.7);
    this.featureWeights.set('env.git.diverged', 0.6);
    this.featureWeights.set('env.highCpu', 0.7);
  }

  /**
   * Clean patterns older than retention period
   */
  private cleanOldPatterns(): void {
    const cutoffTime = Date.now() - (this.config.patternRetentionDays * 24 * 60 * 60 * 1000);
    
    this.patterns.forEach((pattern, id) => {
      if (pattern.lastSeen < cutoffTime && pattern.frequency < 5) {
        this.patterns.delete(id);
      }
    });
  }

  /**
   * Persistence methods
   */
  private async loadPatterns(): Promise<void> {
    try {
      const cached = await memoryCache.get<Array<[string, ContextPattern]>>('inference.patterns');
      if (cached) {
        this.patterns = new Map(cached);
      }
    } catch (error) {
      console.warn('Failed to load inference patterns:', error);
    }
  }

  private async savePatterns(): Promise<void> {
    try {
      const patternsArray = Array.from(this.patterns.entries());
      await memoryCache.set('inference.patterns', patternsArray, 86400000); // 24 hours
    } catch (error) {
      console.warn('Failed to save inference patterns:', error);
    }
  }
}

// Export singleton instance
export const enhancedContextInference = EnhancedContextInference.getInstance();
import { 
  SmartSuggestion, 
  PromptTemplate,
  ContextSnapshot,
  Mode,
  ModeId,
  UserAction
} from '../types';
import { enhancedContextInference } from './EnhancedContextInference';
import { memoryCache } from '../core/CacheSystem';
import { performanceMonitor } from '../utils/performance';
import { eventBus } from '../core/EventBus';

/**
 * Enhanced Smart Suggestion Engine with Machine Learning-based Ranking
 * 
 * Features:
 * - Context-aware suggestion generation
 * - ML-based ranking algorithm
 * - User behavior learning
 * - Temporal relevance scoring
 * - Collaborative filtering
 * - A/B testing support
 */

interface SuggestionScore {
  relevance: number;
  popularity: number;
  recency: number;
  personalization: number;
  contextMatch: number;
  total: number;
}

interface SuggestionMetrics {
  impressions: number;
  clicks: number;
  completions: number;
  ratings: Map<number, number>; // rating -> count
  lastUsed: number;
  contexts: Map<string, number>; // context signature -> usage count
}

interface UserPreferences {
  preferredCategories: Map<string, number>;
  dismissedSuggestions: Set<string>;
  ratingHistory: Array<{ suggestionId: string; rating: number; timestamp: number }>;
  usagePatterns: Map<string, number>; // pattern -> frequency
}

export class SmartSuggestionEngine {
  private static instance: SmartSuggestionEngine;
  
  // Suggestion storage and metrics
  private suggestionMetrics = new Map<string, SuggestionMetrics>();
  private userPreferences: UserPreferences = {
    preferredCategories: new Map(),
    dismissedSuggestions: new Set(),
    ratingHistory: [],
    usagePatterns: new Map(),
  };
  
  // Template system
  private templates = new Map<string, PromptTemplate[]>();
  private dynamicTemplates = new Map<string, (context: ContextSnapshot) => PromptTemplate[]>();
  
  // Configuration
  private readonly config = {
    maxSuggestions: 10,
    minRelevanceScore: 0.3,
    learningRate: 0.15,
    popularityWeight: 0.2,
    recencyWeight: 0.15,
    personalizationWeight: 0.25,
    contextWeight: 0.4,
    cacheTimeout: 300000, // 5 minutes
    abTestingEnabled: true,
    variantProbability: 0.2,
  };

  private constructor() {
    this.initializeTemplates();
    this.loadUserPreferences();
    this.setupEventListeners();
  }

  static getInstance(): SmartSuggestionEngine {
    if (!SmartSuggestionEngine.instance) {
      SmartSuggestionEngine.instance = new SmartSuggestionEngine();
    }
    return SmartSuggestionEngine.instance;
  }

  /**
   * Generate ranked suggestions for current context
   */
  async generateSuggestions(
    context: ContextSnapshot,
    currentMode: Mode,
    input?: string
  ): Promise<SmartSuggestion[]> {
    performanceMonitor.startTimer('suggestion.generate');

    try {
      // Check cache first
      const cacheKey = this.getCacheKey(context, currentMode, input);
      const cached = await this.getCachedSuggestions(cacheKey);
      if (cached) {
        performanceMonitor.endTimer('suggestion.generate', { cache: 'hit' });
        return cached;
      }

      // Generate base suggestions
      const baseSuggestions = await this.generateBaseSuggestions(context, currentMode, input);
      
      // Score and rank suggestions
      const scoredSuggestions = this.scoreSuggestions(baseSuggestions, context, currentMode, input);
      
      // Apply personalization
      const personalizedSuggestions = this.applyPersonalization(scoredSuggestions);
      
      // Apply A/B testing variants if enabled
      const finalSuggestions = this.config.abTestingEnabled 
        ? this.applyABTestingVariants(personalizedSuggestions)
        : personalizedSuggestions;

      // Sort and limit
      const topSuggestions = finalSuggestions
        .sort((a, b) => b.score!.total - a.score!.total)
        .filter(s => s.score!.total >= this.config.minRelevanceScore)
        .slice(0, this.config.maxSuggestions);

      // Cache results
      await this.cacheSuggestions(cacheKey, topSuggestions);

      // Track impressions
      this.trackImpressions(topSuggestions);

      performanceMonitor.endTimer('suggestion.generate', { 
        cache: 'miss',
        count: topSuggestions.length 
      });

      return topSuggestions;
    } catch (error) {
      performanceMonitor.endTimer('suggestion.generate', { error: 'true' });
      throw error;
    }
  }

  /**
   * Track suggestion usage and learn from it
   */
  trackSuggestionUsage(
    suggestionId: string,
    action: 'click' | 'complete' | 'dismiss',
    context: ContextSnapshot
  ): void {
    let metrics = this.suggestionMetrics.get(suggestionId);
    if (!metrics) {
      metrics = {
        impressions: 0,
        clicks: 0,
        completions: 0,
        ratings: new Map(),
        lastUsed: Date.now(),
        contexts: new Map(),
      };
      this.suggestionMetrics.set(suggestionId, metrics);
    }

    // Update metrics
    switch (action) {
      case 'click':
        metrics.clicks++;
        metrics.lastUsed = Date.now();
        break;
      case 'complete':
        metrics.completions++;
        metrics.lastUsed = Date.now();
        break;
      case 'dismiss':
        this.userPreferences.dismissedSuggestions.add(suggestionId);
        break;
    }

    // Track context
    const contextSig = this.getContextSignature(context);
    metrics.contexts.set(contextSig, (metrics.contexts.get(contextSig) || 0) + 1);

    // Update user preferences
    if (action !== 'dismiss') {
      const suggestion = this.findSuggestionById(suggestionId);
      if (suggestion) {
        const categoryScore = this.userPreferences.preferredCategories.get(suggestion.category) || 0;
        this.userPreferences.preferredCategories.set(
          suggestion.category,
          categoryScore + (action === 'complete' ? 0.2 : 0.1)
        );
      }
    }

    // Emit event for analytics
    eventBus.emit({
      type: 'suggestion.used',
      payload: { suggestionId, action, context },
      source: 'SmartSuggestionEngine',
    });

    this.saveUserPreferences();
  }

  /**
   * Rate a suggestion for quality
   */
  rateSuggestion(suggestionId: string, rating: number): void {
    if (rating < 1 || rating > 5) return;

    let metrics = this.suggestionMetrics.get(suggestionId);
    if (!metrics) {
      metrics = {
        impressions: 0,
        clicks: 0,
        completions: 0,
        ratings: new Map(),
        lastUsed: Date.now(),
        contexts: new Map(),
      };
      this.suggestionMetrics.set(suggestionId, metrics);
    }

    // Update rating count
    const currentCount = metrics.ratings.get(rating) || 0;
    metrics.ratings.set(rating, currentCount + 1);

    // Track in user preferences
    this.userPreferences.ratingHistory.push({
      suggestionId,
      rating,
      timestamp: Date.now(),
    });

    // Limit history size
    if (this.userPreferences.ratingHistory.length > 1000) {
      this.userPreferences.ratingHistory = this.userPreferences.ratingHistory.slice(-1000);
    }

    this.saveUserPreferences();
  }

  /**
   * Get suggestions for a specific category
   */
  getSuggestionsByCategory(category: string, context: ContextSnapshot): SmartSuggestion[] {
    const allTemplates = this.getAllTemplatesForMode(context);
    
    return allTemplates
      .filter(template => template.category === category)
      .map(template => this.templateToSuggestion(template, context))
      .filter(suggestion => !this.userPreferences.dismissedSuggestions.has(suggestion.id));
  }

  /**
   * Add custom templates for a mode
   */
  addTemplates(modeId: ModeId, templates: PromptTemplate[]): void {
    const existing = this.templates.get(modeId) || [];
    this.templates.set(modeId, [...existing, ...templates]);
  }

  /**
   * Add dynamic template generator
   */
  addDynamicTemplateGenerator(
    id: string, 
    generator: (context: ContextSnapshot) => PromptTemplate[]
  ): void {
    this.dynamicTemplates.set(id, generator);
  }

  /**
   * Generate base suggestions from templates and context
   */
  private async generateBaseSuggestions(
    context: ContextSnapshot,
    currentMode: Mode,
    input?: string
  ): Promise<SmartSuggestion[]> {
    const suggestions: SmartSuggestion[] = [];

    // Get mode-specific templates
    const modeTemplates = this.templates.get(currentMode.id) || [];
    suggestions.push(...modeTemplates.map(t => this.templateToSuggestion(t, context)));

    // Get dynamic templates
    this.dynamicTemplates.forEach(generator => {
      try {
        const dynamicTemplates = generator(context);
        suggestions.push(...dynamicTemplates.map(t => this.templateToSuggestion(t, context)));
      } catch (error) {
        console.warn('Dynamic template generator failed:', error);
      }
    });

    // Generate contextual suggestions
    suggestions.push(...this.generateContextualSuggestions(context, currentMode));

    // Generate completion suggestions if input provided
    if (input && input.length > 2) {
      suggestions.push(...this.generateCompletionSuggestions(input, context, currentMode));
    }

    // Filter out dismissed suggestions
    return suggestions.filter(s => !this.userPreferences.dismissedSuggestions.has(s.id));
  }

  /**
   * Score suggestions based on multiple factors
   */
  private scoreSuggestions(
    suggestions: SmartSuggestion[],
    context: ContextSnapshot,
    currentMode: Mode,
    input?: string
  ): SmartSuggestion[] {
    return suggestions.map(suggestion => {
      const score: SuggestionScore = {
        relevance: this.calculateRelevanceScore(suggestion, context, currentMode, input),
        popularity: this.calculatePopularityScore(suggestion),
        recency: this.calculateRecencyScore(suggestion),
        personalization: this.calculatePersonalizationScore(suggestion),
        contextMatch: this.calculateContextMatchScore(suggestion, context),
        total: 0,
      };

      // Calculate weighted total
      score.total = 
        score.relevance * 0.3 +
        score.popularity * this.config.popularityWeight +
        score.recency * this.config.recencyWeight +
        score.personalization * this.config.personalizationWeight +
        score.contextMatch * this.config.contextWeight;

      return { ...suggestion, score };
    });
  }

  private calculateRelevanceScore(
    suggestion: SmartSuggestion,
    context: ContextSnapshot,
    currentMode: Mode,
    input?: string
  ): number {
    let score = 0.5; // Base relevance

    // Mode match
    if (suggestion.tags.includes(currentMode.id)) {
      score += 0.3;
    }

    // Input match
    if (input) {
      const lowerInput = input.toLowerCase();
      const lowerText = suggestion.text.toLowerCase();
      
      if (lowerText.startsWith(lowerInput)) {
        score += 0.3;
      } else if (lowerText.includes(lowerInput)) {
        score += 0.15;
      }
      
      // Check description and tags
      if (suggestion.description?.toLowerCase().includes(lowerInput)) {
        score += 0.1;
      }
      if (suggestion.tags.some(tag => tag.toLowerCase().includes(lowerInput))) {
        score += 0.1;
      }
    }

    // Context relevance
    if (context.fileContext.hasErrors && suggestion.tags.includes('error')) {
      score += 0.2;
    }
    if (context.fileContext.type === 'test' && suggestion.tags.includes('test')) {
      score += 0.2;
    }

    return Math.min(score, 1);
  }

  private calculatePopularityScore(suggestion: SmartSuggestion): number {
    const metrics = this.suggestionMetrics.get(suggestion.id);
    if (!metrics || metrics.impressions === 0) return 0.1;

    // Click-through rate
    const ctr = metrics.clicks / metrics.impressions;
    
    // Completion rate
    const completionRate = metrics.clicks > 0 ? metrics.completions / metrics.clicks : 0;
    
    // Average rating
    let avgRating = 0;
    let totalRatings = 0;
    metrics.ratings.forEach((count, rating) => {
      avgRating += rating * count;
      totalRatings += count;
    });
    avgRating = totalRatings > 0 ? avgRating / totalRatings / 5 : 0.6;

    return (ctr * 0.4 + completionRate * 0.4 + avgRating * 0.2);
  }

  private calculateRecencyScore(suggestion: SmartSuggestion): number {
    const metrics = this.suggestionMetrics.get(suggestion.id);
    if (!metrics || !metrics.lastUsed) return 0.5;

    const daysSinceUsed = (Date.now() - metrics.lastUsed) / (1000 * 60 * 60 * 24);
    
    // Exponential decay over 7 days
    return Math.exp(-daysSinceUsed / 7);
  }

  private calculatePersonalizationScore(suggestion: SmartSuggestion): number {
    // Category preference
    const categoryPreference = this.userPreferences.preferredCategories.get(suggestion.category) || 0;
    
    // Usage pattern match
    let patternScore = 0;
    this.userPreferences.usagePatterns.forEach((frequency, pattern) => {
      if (suggestion.text.includes(pattern) || suggestion.tags.some(tag => tag.includes(pattern))) {
        patternScore += frequency * 0.1;
      }
    });

    // Rating history
    const userRatings = this.userPreferences.ratingHistory.filter(
      r => r.suggestionId === suggestion.id
    );
    const avgUserRating = userRatings.length > 0
      ? userRatings.reduce((sum, r) => sum + r.rating, 0) / userRatings.length / 5
      : 0.6;

    return Math.min(categoryPreference * 0.4 + patternScore * 0.3 + avgUserRating * 0.3, 1);
  }

  private calculateContextMatchScore(suggestion: SmartSuggestion, context: ContextSnapshot): number {
    const metrics = this.suggestionMetrics.get(suggestion.id);
    if (!metrics) return 0.3;

    const contextSig = this.getContextSignature(context);
    const contextUsage = metrics.contexts.get(contextSig) || 0;
    
    // Also check similar contexts
    let similarContextScore = 0;
    metrics.contexts.forEach((usage, sig) => {
      const similarity = this.calculateContextSimilarity(contextSig, sig);
      similarContextScore += similarity * usage * 0.1;
    });

    return Math.min(contextUsage * 0.2 + similarContextScore, 1);
  }

  /**
   * Apply personalization adjustments
   */
  private applyPersonalization(suggestions: SmartSuggestion[]): SmartSuggestion[] {
    return suggestions.map(suggestion => {
      // Boost suggestions from preferred categories
      const categoryBoost = this.userPreferences.preferredCategories.get(suggestion.category) || 0;
      if (categoryBoost > 0 && suggestion.score) {
        suggestion.score.total *= (1 + categoryBoost * 0.2);
      }

      // Apply collaborative filtering if we have enough data
      const collaborativeBoost = this.getCollaborativeFilteringBoost(suggestion);
      if (collaborativeBoost > 0 && suggestion.score) {
        suggestion.score.total *= (1 + collaborativeBoost * 0.15);
      }

      return suggestion;
    });
  }

  /**
   * Apply A/B testing variants
   */
  private applyABTestingVariants(suggestions: SmartSuggestion[]): SmartSuggestion[] {
    if (Math.random() > this.config.variantProbability) {
      return suggestions; // Control group
    }

    // Variant: Try different ranking algorithm
    return suggestions.map(suggestion => {
      if (suggestion.score) {
        // Experiment with different weight distribution
        const experimentalScore = 
          suggestion.score.relevance * 0.4 +
          suggestion.score.popularity * 0.15 +
          suggestion.score.recency * 0.1 +
          suggestion.score.personalization * 0.2 +
          suggestion.score.contextMatch * 0.15;

        suggestion.score.total = experimentalScore;
        suggestion.metadata = { ...suggestion.metadata, abVariant: 'experimental-weights' };
      }
      return suggestion;
    });
  }

  /**
   * Generate contextual suggestions based on current state
   */
  private generateContextualSuggestions(
    context: ContextSnapshot,
    currentMode: Mode
  ): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];

    // Error context
    if (context.fileContext.hasErrors) {
      suggestions.push({
        id: 'ctx-error-fix',
        text: 'Fix the current error',
        category: 'action',
        priority: 9,
        tags: ['error', 'fix', currentMode.id],
        description: 'Analyze and fix the error in the current file',
        icon: '🔧',
      });
    }

    // Git context
    if (context.environmentContext.gitStatus) {
      const git = context.environmentContext.gitStatus;
      
      if (git.hasConflicts) {
        suggestions.push({
          id: 'ctx-resolve-conflicts',
          text: 'Resolve merge conflicts',
          category: 'action',
          priority: 10,
          tags: ['git', 'conflict', 'merge'],
          description: 'Help resolve the current merge conflicts',
          icon: '🔀',
        });
      }

      if (git.staged.length > 0) {
        suggestions.push({
          id: 'ctx-commit-changes',
          text: 'Commit staged changes',
          category: 'action',
          priority: 8,
          tags: ['git', 'commit'],
          description: 'Create a commit for the staged changes',
          icon: '📝',
        });
      }
    }

    // Test context
    if (context.fileContext.path.includes('test') || context.fileContext.path.includes('spec')) {
      suggestions.push({
        id: 'ctx-run-tests',
        text: 'Run tests in this file',
        category: 'action',
        priority: 7,
        tags: ['test', 'run'],
        description: 'Execute the tests in the current file',
        icon: '🧪',
      });
    }

    return suggestions;
  }

  /**
   * Generate completion suggestions
   */
  private generateCompletionSuggestions(
    input: string,
    context: ContextSnapshot,
    currentMode: Mode
  ): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];
    const lowerInput = input.toLowerCase();

    // Common patterns
    const patterns = [
      { 
        match: /^(explain|what|how)/i, 
        completions: [
          ' this code works',
          ' to implement this feature',
          ' does this function do',
          ' can I improve this',
        ]
      },
      {
        match: /^(fix|debug|solve)/i,
        completions: [
          ' this error',
          ' the failing test',
          ' performance issues',
          ' type errors',
        ]
      },
      {
        match: /^(refactor|improve|optimize)/i,
        completions: [
          ' for better readability',
          ' for performance',
          ' using best practices',
          ' with modern patterns',
        ]
      },
      {
        match: /^(implement|add|create)/i,
        completions: [
          ' unit tests',
          ' error handling',
          ' logging',
          ' documentation',
        ]
      },
    ];

    patterns.forEach(pattern => {
      if (pattern.match.test(input)) {
        pattern.completions.forEach((completion, index) => {
          suggestions.push({
            id: `complete-${input.slice(0, 10)}-${index}`,
            text: input + completion,
            category: 'completion',
            priority: 6 - index * 0.5,
            tags: ['completion', currentMode.id],
            description: 'Auto-completion suggestion',
          });
        });
      }
    });

    return suggestions;
  }

  /**
   * Helper methods
   */
  private templateToSuggestion(template: PromptTemplate, context: ContextSnapshot): SmartSuggestion {
    // Process template variables
    let text = template.template;
    if (template.variables) {
      template.variables.forEach(variable => {
        const value = this.resolveVariable(variable.name, context);
        text = text.replace(`{{${variable.name}}}`, value);
      });
    }

    return {
      id: template.id,
      text,
      category: template.category,
      priority: template.priority || 5,
      tags: template.tags,
      description: template.description,
      icon: template.icon,
      metadata: template.metadata,
    };
  }

  private resolveVariable(name: string, context: ContextSnapshot): string {
    switch (name) {
      case 'fileName':
        return context.fileContext.path.split('/').pop() || 'current file';
      case 'fileType':
        return context.fileContext.type;
      case 'projectType':
        return context.projectContext.type;
      case 'branch':
        return context.environmentContext.gitStatus?.branch || 'current branch';
      default:
        return `{{${name}}}`;
    }
  }

  private getContextSignature(context: ContextSnapshot): string {
    return [
      context.fileContext.type,
      context.fileContext.hasErrors ? 'errors' : 'clean',
      context.projectContext.type,
      context.environmentContext.gitStatus?.hasConflicts ? 'conflicts' : 'no-conflicts',
    ].join(':');
  }

  private calculateContextSimilarity(sig1: string, sig2: string): number {
    const parts1 = sig1.split(':');
    const parts2 = sig2.split(':');
    
    let matches = 0;
    const maxParts = Math.max(parts1.length, parts2.length);
    
    for (let i = 0; i < maxParts; i++) {
      if (parts1[i] === parts2[i]) {
        matches++;
      }
    }
    
    return matches / maxParts;
  }

  private getCollaborativeFilteringBoost(suggestion: SmartSuggestion): number {
    // This would typically use more sophisticated collaborative filtering
    // For now, return a simple boost based on overall popularity
    const metrics = this.suggestionMetrics.get(suggestion.id);
    if (!metrics) return 0;

    const totalUsage = metrics.clicks + metrics.completions * 2;
    return Math.min(totalUsage / 100, 0.5);
  }

  private findSuggestionById(id: string): SmartSuggestion | null {
    // This would typically search through all available suggestions
    // For now, return null as we don't maintain a global registry
    return null;
  }

  private getCacheKey(context: ContextSnapshot, mode: Mode, input?: string): string {
    return [
      mode.id,
      context.fileContext.path,
      context.fileContext.hasErrors ? '1' : '0',
      input || '',
    ].join(':');
  }

  private async getCachedSuggestions(key: string): Promise<SmartSuggestion[] | null> {
    return memoryCache.get<SmartSuggestion[]>(`suggestions:${key}`);
  }

  private async cacheSuggestions(key: string, suggestions: SmartSuggestion[]): Promise<void> {
    await memoryCache.set(`suggestions:${key}`, suggestions, this.config.cacheTimeout);
  }

  private trackImpressions(suggestions: SmartSuggestion[]): void {
    suggestions.forEach(suggestion => {
      let metrics = this.suggestionMetrics.get(suggestion.id);
      if (!metrics) {
        metrics = {
          impressions: 0,
          clicks: 0,
          completions: 0,
          ratings: new Map(),
          lastUsed: Date.now(),
          contexts: new Map(),
        };
        this.suggestionMetrics.set(suggestion.id, metrics);
      }
      metrics.impressions++;
    });
  }

  private getAllTemplatesForMode(context: ContextSnapshot): PromptTemplate[] {
    const templates: PromptTemplate[] = [];
    
    // Get all mode templates
    this.templates.forEach((modeTemplates) => {
      templates.push(...modeTemplates);
    });

    // Get dynamic templates
    this.dynamicTemplates.forEach(generator => {
      try {
        templates.push(...generator(context));
      } catch (error) {
        console.warn('Dynamic template generator failed:', error);
      }
    });

    return templates;
  }

  /**
   * Initialize default templates
   */
  private initializeTemplates(): void {
    // Architect mode templates
    this.templates.set('architect', [
      {
        id: 'arch-review-structure',
        template: 'Review the project structure and suggest architectural improvements',
        category: 'analysis',
        tags: ['architecture', 'review', 'structure'],
        priority: 8,
        description: 'Comprehensive architectural analysis',
        icon: '🏗️',
      },
      {
        id: 'arch-design-pattern',
        template: 'Suggest design patterns for {{fileName}}',
        category: 'suggestion',
        tags: ['architecture', 'patterns', 'design'],
        priority: 7,
        variables: [{ name: 'fileName', type: 'string', source: 'file' }],
        description: 'Design pattern recommendations',
        icon: '📐',
      },
    ]);

    // Debug mode templates
    this.templates.set('debug', [
      {
        id: 'debug-analyze-error',
        template: 'Analyze this error and provide a fix',
        category: 'action',
        tags: ['debug', 'error', 'fix'],
        priority: 10,
        description: 'Error analysis and resolution',
        icon: '🐛',
      },
      {
        id: 'debug-trace-execution',
        template: 'Trace the execution flow and identify the issue',
        category: 'analysis',
        tags: ['debug', 'trace', 'flow'],
        priority: 8,
        description: 'Execution flow analysis',
        icon: '🔍',
      },
    ]);

    // Add more mode templates...
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    // Listen for context changes
    eventBus.on('context.analyzed', (event) => {
      // Clear suggestion cache on significant context changes
      memoryCache.clear('suggestions:*');
    });

    // Listen for mode transitions
    eventBus.on('mode.transitioned', (event) => {
      const { from, to, context } = event.payload;
      // Learn from mode transitions
      if (from && to) {
        this.learnFromModeTransition(from, to, context);
      }
    });
  }

  private learnFromModeTransition(from: ModeId, to: ModeId, context: ContextSnapshot): void {
    // Update usage patterns based on transition
    const pattern = `${from}->${to}`;
    const currentCount = this.userPreferences.usagePatterns.get(pattern) || 0;
    this.userPreferences.usagePatterns.set(pattern, currentCount + 1);
    
    this.saveUserPreferences();
  }

  /**
   * Persistence methods
   */
  private async loadUserPreferences(): Promise<void> {
    try {
      const stored = await memoryCache.get<UserPreferences>('suggestion.preferences');
      if (stored) {
        this.userPreferences = {
          preferredCategories: new Map(stored.preferredCategories),
          dismissedSuggestions: new Set(stored.dismissedSuggestions),
          ratingHistory: stored.ratingHistory,
          usagePatterns: new Map(stored.usagePatterns),
        };
      }
    } catch (error) {
      console.warn('Failed to load user preferences:', error);
    }
  }

  private async saveUserPreferences(): Promise<void> {
    try {
      const toStore = {
        preferredCategories: Array.from(this.userPreferences.preferredCategories),
        dismissedSuggestions: Array.from(this.userPreferences.dismissedSuggestions),
        ratingHistory: this.userPreferences.ratingHistory,
        usagePatterns: Array.from(this.userPreferences.usagePatterns),
      };
      await memoryCache.set('suggestion.preferences', toStore, 86400000); // 24 hours
    } catch (error) {
      console.warn('Failed to save user preferences:', error);
    }
  }
}

// Export singleton instance
export const smartSuggestionEngine = SmartSuggestionEngine.getInstance();
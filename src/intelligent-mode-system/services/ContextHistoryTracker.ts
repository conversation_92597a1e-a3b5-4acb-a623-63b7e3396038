import { 
  ContextSnapshot,
  ModeId,
  User<PERSON>ction,
  FileContext,
  EnvironmentContext
} from '../types';
import { eventBus } from '../core/EventBus';
import { memoryCache } from '../core/CacheSystem';
import { performanceMonitor } from '../utils/performance';

/**
 * Context History Tracking and Learning Service
 * 
 * Features:
 * - Tracks context changes over time
 * - Identifies patterns in user behavior
 * - Learns from context transitions
 * - Provides historical insights
 * - Supports time-series analysis
 */

interface ContextHistoryEntry {
  snapshot: ContextSnapshot;
  timestamp: number;
  modeId: ModeId;
  significantChanges: string[];
  metadata?: Record<string, any>;
}

interface ContextPattern {
  id: string;
  description: string;
  frequency: number;
  lastSeen: number;
  contexts: ContextSnapshot[];
  transitions: Array<{ from: string; to: string; count: number }>;
}

interface UserBehaviorInsight {
  type: 'productivity' | 'workflow' | 'preference' | 'issue';
  description: string;
  confidence: number;
  evidence: string[];
  recommendations: string[];
}

interface TimeSeriesData {
  timestamp: number;
  value: number;
  label: string;
}

export class ContextHistoryTracker {
  private static instance: ContextHistoryTracker;
  
  // History storage
  private history: ContextHistoryEntry[] = [];
  private patterns: Map<string, ContextPattern> = new Map();
  private insights: UserBehaviorInsight[] = [];
  
  // Configuration
  private readonly config = {
    maxHistorySize: 10000,
    maxPatternAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    analysisInterval: 5 * 60 * 1000, // 5 minutes
    minPatternFrequency: 3,
    significanceThreshold: 0.3,
  };

  // Analysis state
  private analysisTimer: NodeJS.Timeout | null = null;
  private lastAnalysis = 0;

  private constructor() {
    this.loadHistory();
    this.setupEventListeners();
    this.startPeriodicAnalysis();
  }

  static getInstance(): ContextHistoryTracker {
    if (!ContextHistoryTracker.instance) {
      ContextHistoryTracker.instance = new ContextHistoryTracker();
    }
    return ContextHistoryTracker.instance;
  }

  /**
   * Track a context snapshot
   */
  trackContext(snapshot: ContextSnapshot, modeId: ModeId): void {
    performanceMonitor.startTimer('history.track');

    try {
      // Detect significant changes
      const significantChanges = this.detectSignificantChanges(snapshot);

      // Create history entry
      const entry: ContextHistoryEntry = {
        snapshot,
        timestamp: Date.now(),
        modeId,
        significantChanges,
      };

      // Add to history
      this.history.push(entry);

      // Maintain history size
      if (this.history.length > this.config.maxHistorySize) {
        this.history = this.history.slice(-this.config.maxHistorySize);
      }

      // Update patterns in real-time
      this.updatePatternsIncremental(entry);

      // Emit event
      eventBus.emit({
        type: 'context.tracked',
        payload: { entry, totalEntries: this.history.length },
        source: 'ContextHistoryTracker',
      });

      performanceMonitor.endTimer('history.track');
    } catch (error) {
      performanceMonitor.endTimer('history.track', { error: 'true' });
      console.error('Failed to track context:', error);
    }
  }

  /**
   * Get historical context data
   */
  getHistory(options?: {
    startTime?: number;
    endTime?: number;
    modeId?: ModeId;
    limit?: number;
  }): ContextHistoryEntry[] {
    let filtered = this.history;

    if (options?.startTime) {
      filtered = filtered.filter(entry => entry.timestamp >= options.startTime!);
    }

    if (options?.endTime) {
      filtered = filtered.filter(entry => entry.timestamp <= options.endTime!);
    }

    if (options?.modeId) {
      filtered = filtered.filter(entry => entry.modeId === options.modeId);
    }

    if (options?.limit) {
      filtered = filtered.slice(-options.limit);
    }

    return filtered;
  }

  /**
   * Get insights about user behavior
   */
  getInsights(): UserBehaviorInsight[] {
    return this.insights;
  }

  /**
   * Get identified patterns
   */
  getPatterns(): ContextPattern[] {
    return Array.from(this.patterns.values())
      .filter(pattern => pattern.frequency >= this.config.minPatternFrequency)
      .sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * Get time series data for analysis
   */
  getTimeSeries(metric: 'actions' | 'errors' | 'mode_switches' | 'productivity', options?: {
    startTime?: number;
    endTime?: number;
    interval?: number; // milliseconds
  }): TimeSeriesData[] {
    const interval = options?.interval || 3600000; // 1 hour default
    const startTime = options?.startTime || Date.now() - 24 * 3600000; // 24 hours ago
    const endTime = options?.endTime || Date.now();

    const buckets = new Map<number, number>();

    // Initialize buckets
    for (let time = startTime; time <= endTime; time += interval) {
      buckets.set(time, 0);
    }

    // Fill buckets based on metric
    this.history.forEach(entry => {
      if (entry.timestamp < startTime || entry.timestamp > endTime) return;

      const bucketTime = Math.floor(entry.timestamp / interval) * interval;
      const currentValue = buckets.get(bucketTime) || 0;

      switch (metric) {
        case 'actions':
          buckets.set(bucketTime, currentValue + entry.snapshot.userContext.recentActions.length);
          break;
        case 'errors':
          buckets.set(bucketTime, currentValue + (entry.snapshot.fileContext.hasErrors ? 1 : 0));
          break;
        case 'mode_switches':
          // Count mode transitions
          const prevEntry = this.history[this.history.indexOf(entry) - 1];
          if (prevEntry && prevEntry.modeId !== entry.modeId) {
            buckets.set(bucketTime, currentValue + 1);
          }
          break;
        case 'productivity':
          // Simple productivity score based on actions and progress
          const productivityScore = this.calculateProductivityScore(entry);
          buckets.set(bucketTime, currentValue + productivityScore);
          break;
      }
    });

    // Convert to time series data
    return Array.from(buckets.entries()).map(([timestamp, value]) => ({
      timestamp,
      value,
      label: new Date(timestamp).toISOString(),
    }));
  }

  /**
   * Predict next likely context or action
   */
  predictNext(currentContext: ContextSnapshot): {
    likelyMode: ModeId | null;
    likelyActions: string[];
    confidence: number;
  } {
    const predictions = {
      likelyMode: null as ModeId | null,
      likelyActions: [] as string[],
      confidence: 0,
    };

    // Find similar historical contexts
    const similarContexts = this.findSimilarContexts(currentContext, 10);
    if (similarContexts.length === 0) return predictions;

    // Analyze what happened next in similar contexts
    const modeTransitions = new Map<ModeId, number>();
    const actionPatterns = new Map<string, number>();

    similarContexts.forEach(({ entry, similarity }) => {
      const index = this.history.indexOf(entry);
      if (index < this.history.length - 1) {
        const nextEntry = this.history[index + 1];
        
        // Track mode transitions
        if (nextEntry.modeId !== entry.modeId) {
          const count = modeTransitions.get(nextEntry.modeId) || 0;
          modeTransitions.set(nextEntry.modeId, count + similarity);
        }

        // Track action patterns
        nextEntry.snapshot.userContext.recentActions.slice(0, 3).forEach(action => {
          const key = `${action.type}:${action.target || ''}`;
          const count = actionPatterns.get(key) || 0;
          actionPatterns.set(key, count + similarity);
        });
      }
    });

    // Determine most likely mode
    let maxModeScore = 0;
    modeTransitions.forEach((score, modeId) => {
      if (score > maxModeScore) {
        maxModeScore = score;
        predictions.likelyMode = modeId;
      }
    });

    // Determine likely actions
    const sortedActions = Array.from(actionPatterns.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);
    predictions.likelyActions = sortedActions.map(([action]) => action);

    // Calculate confidence
    predictions.confidence = Math.min(maxModeScore / similarContexts.length, 1);

    return predictions;
  }

  /**
   * Analyze patterns and generate insights
   */
  private async analyzePatterns(): Promise<void> {
    performanceMonitor.startTimer('history.analyze');

    try {
      // Clear old insights
      this.insights = [];

      // Analyze productivity patterns
      this.analyzeProductivityPatterns();

      // Analyze workflow patterns
      this.analyzeWorkflowPatterns();

      // Analyze error patterns
      this.analyzeErrorPatterns();

      // Analyze mode usage patterns
      this.analyzeModeUsagePatterns();

      // Clean old patterns
      this.cleanOldPatterns();

      // Save insights
      await this.saveInsights();

      this.lastAnalysis = Date.now();
      performanceMonitor.endTimer('history.analyze');
    } catch (error) {
      performanceMonitor.endTimer('history.analyze', { error: 'true' });
      console.error('Pattern analysis failed:', error);
    }
  }

  private analyzeProductivityPatterns(): void {
    const recentHistory = this.getHistory({ 
      startTime: Date.now() - 24 * 3600000 // Last 24 hours
    });

    if (recentHistory.length < 10) return;

    // Calculate productivity metrics
    const hourlyProductivity = new Map<number, number[]>();
    
    recentHistory.forEach(entry => {
      const hour = new Date(entry.timestamp).getHours();
      const productivity = this.calculateProductivityScore(entry);
      
      if (!hourlyProductivity.has(hour)) {
        hourlyProductivity.set(hour, []);
      }
      hourlyProductivity.get(hour)!.push(productivity);
    });

    // Find peak productivity hours
    let peakHour = -1;
    let peakScore = 0;

    hourlyProductivity.forEach((scores, hour) => {
      const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
      if (avgScore > peakScore) {
        peakScore = avgScore;
        peakHour = hour;
      }
    });

    if (peakHour >= 0) {
      this.insights.push({
        type: 'productivity',
        description: `Your peak productivity is typically around ${peakHour}:00`,
        confidence: Math.min(peakScore / 100, 1),
        evidence: [`Based on ${recentHistory.length} context snapshots`],
        recommendations: [
          `Schedule complex tasks around ${peakHour}:00`,
          'Take breaks during low productivity periods',
        ],
      });
    }
  }

  private analyzeWorkflowPatterns(): void {
    // Analyze common action sequences
    const sequences = new Map<string, number>();
    
    for (let i = 0; i < this.history.length - 1; i++) {
      const current = this.history[i];
      const next = this.history[i + 1];
      
      if (next.timestamp - current.timestamp < 300000) { // Within 5 minutes
        const currentActions = current.snapshot.userContext.recentActions.slice(-3);
        const nextActions = next.snapshot.userContext.recentActions.slice(-3);
        
        if (currentActions.length > 0 && nextActions.length > 0) {
          const sequence = `${currentActions.map(a => a.type).join('->')} => ${nextActions.map(a => a.type).join('->')}`;
          sequences.set(sequence, (sequences.get(sequence) || 0) + 1);
        }
      }
    }

    // Find most common sequences
    const commonSequences = Array.from(sequences.entries())
      .filter(([_, count]) => count >= 5)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);

    commonSequences.forEach(([sequence, count]) => {
      this.insights.push({
        type: 'workflow',
        description: `Common workflow pattern detected: ${sequence}`,
        confidence: Math.min(count / 20, 1),
        evidence: [`Occurred ${count} times`],
        recommendations: [
          'Consider creating shortcuts for this workflow',
          'Automate repetitive sequences',
        ],
      });
    });
  }

  private analyzeErrorPatterns(): void {
    const errorContexts = this.history.filter(entry => entry.snapshot.fileContext.hasErrors);
    
    if (errorContexts.length < 5) return;

    // Group errors by file type
    const errorsByType = new Map<string, number>();
    errorContexts.forEach(entry => {
      const fileType = entry.snapshot.fileContext.type;
      errorsByType.set(fileType, (errorsByType.get(fileType) || 0) + 1);
    });

    // Find most error-prone file types
    const sortedTypes = Array.from(errorsByType.entries())
      .sort((a, b) => b[1] - a[1]);

    if (sortedTypes.length > 0 && sortedTypes[0][1] >= 5) {
      const [fileType, count] = sortedTypes[0];
      this.insights.push({
        type: 'issue',
        description: `Frequent errors in ${fileType} files`,
        confidence: Math.min(count / errorContexts.length, 1),
        evidence: [`${count} errors in ${fileType} files`],
        recommendations: [
          `Review ${fileType} file patterns`,
          'Consider additional linting rules',
          'Add more comprehensive tests',
        ],
      });
    }
  }

  private analyzeModeUsagePatterns(): void {
    const modeUsage = new Map<ModeId, number>();
    const modeTransitions = new Map<string, number>();

    // Count mode usage and transitions
    for (let i = 0; i < this.history.length; i++) {
      const entry = this.history[i];
      modeUsage.set(entry.modeId, (modeUsage.get(entry.modeId) || 0) + 1);

      if (i > 0) {
        const prevEntry = this.history[i - 1];
        if (prevEntry.modeId !== entry.modeId) {
          const transition = `${prevEntry.modeId} -> ${entry.modeId}`;
          modeTransitions.set(transition, (modeTransitions.get(transition) || 0) + 1);
        }
      }
    }

    // Find underutilized modes
    const totalUsage = this.history.length;
    modeUsage.forEach((count, modeId) => {
      const usage = count / totalUsage;
      if (usage < 0.05 && count > 0) {
        this.insights.push({
          type: 'preference',
          description: `${modeId} mode is rarely used (${(usage * 100).toFixed(1)}%)`,
          confidence: 0.8,
          evidence: [`Used ${count} times out of ${totalUsage}`],
          recommendations: [
            `Explore ${modeId} mode features`,
            'Consider if this mode fits your workflow',
          ],
        });
      }
    });

    // Find common transitions
    const commonTransitions = Array.from(modeTransitions.entries())
      .filter(([_, count]) => count >= 5)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);

    commonTransitions.forEach(([transition, count]) => {
      this.insights.push({
        type: 'workflow',
        description: `Common mode transition: ${transition}`,
        confidence: Math.min(count / 20, 1),
        evidence: [`Occurred ${count} times`],
        recommendations: [
          'Consider keyboard shortcuts for this transition',
          'Review if this workflow can be optimized',
        ],
      });
    });
  }

  /**
   * Helper methods
   */
  private detectSignificantChanges(snapshot: ContextSnapshot): string[] {
    const changes: string[] = [];
    
    if (this.history.length === 0) return ['initial'];

    const lastEntry = this.history[this.history.length - 1];
    const lastSnapshot = lastEntry.snapshot;

    // File changes
    if (snapshot.fileContext.path !== lastSnapshot.fileContext.path) {
      changes.push('file_changed');
    }
    if (snapshot.fileContext.hasErrors !== lastSnapshot.fileContext.hasErrors) {
      changes.push(snapshot.fileContext.hasErrors ? 'errors_appeared' : 'errors_resolved');
    }

    // Git changes
    if (snapshot.environmentContext.gitStatus && lastSnapshot.environmentContext.gitStatus) {
      const git = snapshot.environmentContext.gitStatus;
      const lastGit = lastSnapshot.environmentContext.gitStatus;
      
      if (git.branch !== lastGit.branch) {
        changes.push('branch_changed');
      }
      if (git.hasConflicts !== lastGit.hasConflicts) {
        changes.push(git.hasConflicts ? 'conflicts_appeared' : 'conflicts_resolved');
      }
      if (git.staged.length !== lastGit.staged.length) {
        changes.push('staging_changed');
      }
    }

    // Process changes
    const currentProcessTypes = new Set(
      snapshot.environmentContext.runningProcesses.map(p => p.type)
    );
    const lastProcessTypes = new Set(
      lastSnapshot.environmentContext.runningProcesses.map(p => p.type)
    );

    currentProcessTypes.forEach(type => {
      if (!lastProcessTypes.has(type)) {
        changes.push(`process_started:${type}`);
      }
    });

    lastProcessTypes.forEach(type => {
      if (!currentProcessTypes.has(type)) {
        changes.push(`process_stopped:${type}`);
      }
    });

    return changes;
  }

  private updatePatternsIncremental(entry: ContextHistoryEntry): void {
    // Look for patterns in recent history
    const recentHistory = this.history.slice(-50);
    
    entry.significantChanges.forEach(change => {
      const patternId = `change:${change}`;
      let pattern = this.patterns.get(patternId);
      
      if (!pattern) {
        pattern = {
          id: patternId,
          description: `Pattern: ${change}`,
          frequency: 0,
          lastSeen: Date.now(),
          contexts: [],
          transitions: [],
        };
        this.patterns.set(patternId, pattern);
      }

      pattern.frequency++;
      pattern.lastSeen = Date.now();
      pattern.contexts.push(entry.snapshot);
      
      // Keep only recent contexts
      if (pattern.contexts.length > 10) {
        pattern.contexts = pattern.contexts.slice(-10);
      }
    });
  }

  private findSimilarContexts(
    target: ContextSnapshot, 
    limit: number
  ): Array<{ entry: ContextHistoryEntry; similarity: number }> {
    const similarities = this.history.map(entry => ({
      entry,
      similarity: this.calculateContextSimilarity(target, entry.snapshot),
    }));

    return similarities
      .filter(s => s.similarity > this.config.significanceThreshold)
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit);
  }

  private calculateContextSimilarity(ctx1: ContextSnapshot, ctx2: ContextSnapshot): number {
    let similarity = 0;
    let factors = 0;

    // File similarity
    if (ctx1.fileContext.path === ctx2.fileContext.path) {
      similarity += 0.3;
    } else if (ctx1.fileContext.type === ctx2.fileContext.type) {
      similarity += 0.1;
    }
    factors += 0.3;

    // Error state similarity
    if (ctx1.fileContext.hasErrors === ctx2.fileContext.hasErrors) {
      similarity += 0.2;
    }
    factors += 0.2;

    // Project similarity
    if (ctx1.projectContext.type === ctx2.projectContext.type) {
      similarity += 0.2;
    }
    factors += 0.2;

    // Action similarity
    const actions1 = new Set(ctx1.userContext.recentActions.map(a => a.type));
    const actions2 = new Set(ctx2.userContext.recentActions.map(a => a.type));
    const commonActions = Array.from(actions1).filter(a => actions2.has(a)).length;
    const totalActions = Math.max(actions1.size, actions2.size);
    if (totalActions > 0) {
      similarity += 0.3 * (commonActions / totalActions);
    }
    factors += 0.3;

    return similarity / factors;
  }

  private calculateProductivityScore(entry: ContextHistoryEntry): number {
    let score = 0;

    // Actions performed
    const actionCount = entry.snapshot.userContext.recentActions.length;
    score += Math.min(actionCount * 10, 50);

    // No errors is productive
    if (!entry.snapshot.fileContext.hasErrors) {
      score += 20;
    }

    // Git activity
    if (entry.snapshot.environmentContext.gitStatus) {
      const git = entry.snapshot.environmentContext.gitStatus;
      if (git.staged.length > 0) score += 10;
      if (git.ahead > 0) score += 10;
    }

    // Mode-specific productivity
    switch (entry.modeId) {
      case 'architect':
        if (entry.significantChanges.includes('file_changed')) score += 15;
        break;
      case 'debug':
        if (entry.significantChanges.includes('errors_resolved')) score += 30;
        break;
      case 'deploy':
        if (entry.significantChanges.includes('process_started:build')) score += 20;
        break;
    }

    return score;
  }

  private cleanOldPatterns(): void {
    const cutoff = Date.now() - this.config.maxPatternAge;
    
    this.patterns.forEach((pattern, id) => {
      if (pattern.lastSeen < cutoff && pattern.frequency < 10) {
        this.patterns.delete(id);
      }
    });
  }

  private startPeriodicAnalysis(): void {
    this.analysisTimer = setInterval(() => {
      if (Date.now() - this.lastAnalysis > this.config.analysisInterval) {
        this.analyzePatterns();
      }
    }, this.config.analysisInterval);
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    // Track context changes
    eventBus.on('context.analyzed', (event) => {
      const currentMode = event.payload.currentMode || 'architect';
      this.trackContext(event.payload, currentMode);
    });

    // Track mode transitions
    eventBus.on('mode.transitioned', (event) => {
      // Mode transitions are tracked as part of context tracking
    });
  }

  /**
   * Persistence methods
   */
  private async loadHistory(): Promise<void> {
    try {
      const stored = await memoryCache.get<ContextHistoryEntry[]>('history.entries');
      if (stored) {
        this.history = stored.slice(-this.config.maxHistorySize);
      }

      const storedPatterns = await memoryCache.get<Array<[string, ContextPattern]>>('history.patterns');
      if (storedPatterns) {
        this.patterns = new Map(storedPatterns);
      }
    } catch (error) {
      console.warn('Failed to load context history:', error);
    }
  }

  private async saveHistory(): Promise<void> {
    try {
      await memoryCache.set('history.entries', this.history, 86400000); // 24 hours
      
      const patternsArray = Array.from(this.patterns.entries());
      await memoryCache.set('history.patterns', patternsArray, 86400000);
    } catch (error) {
      console.warn('Failed to save context history:', error);
    }
  }

  private async saveInsights(): Promise<void> {
    try {
      await memoryCache.set('history.insights', this.insights, 3600000); // 1 hour
    } catch (error) {
      console.warn('Failed to save insights:', error);
    }
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.analysisTimer) {
      clearInterval(this.analysisTimer);
      this.analysisTimer = null;
    }
    this.saveHistory();
  }
}

// Export singleton instance
export const contextHistoryTracker = ContextHistoryTracker.getInstance();
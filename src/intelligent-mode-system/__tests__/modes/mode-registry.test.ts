import { describe, it, expect, beforeEach, vi } from 'vitest';
import { modeRegistry } from '../../core/ModeRegistry';
import { allModes } from '../../modes';
import { ContextSnapshot } from '../../types';

describe('Mode Registry with Real Modes', () => {
  beforeEach(() => {
    // Reset registry state
    modeRegistry['activeMode'] = null;
    modeRegistry['transitionHistory'] = [];
  });

  describe('Mode Registration', () => {
    it('should have all 6 modes registered', () => {
      const modes = modeRegistry.getAllModes();
      expect(modes).toHaveLength(6);
      
      const modeIds = modes.map(m => m.id);
      expect(modeIds).toContain('architect');
      expect(modeIds).toContain('debug');
      expect(modeIds).toContain('review');
      expect(modeIds).toContain('deploy');
      expect(modeIds).toContain('experiment');
      expect(modeIds).toContain('learn');
    });

    it('should have correct mode properties', () => {
      const architectMode = modeRegistry.getMode('architect');
      expect(architectMode).toBeDefined();
      expect(architectMode?.name).toBe('Architect Mode');
      expect(architectMode?.icon).toBe('🏗️');
      expect(architectMode?.color).toBe('#3B82F6');
      expect(architectMode?.features).toHaveLength(4);
    });
  });

  describe('Mode Features', () => {
    it('architect mode should have correct features', () => {
      const mode = modeRegistry.getMode('architect');
      const featureIds = mode?.features.map(f => f.id);
      
      expect(featureIds).toContain('system-designer');
      expect(featureIds).toContain('dependency-analyzer');
      expect(featureIds).toContain('component-mapper');
      expect(featureIds).toContain('architecture-patterns');
    });

    it('debug mode should have correct features', () => {
      const mode = modeRegistry.getMode('debug');
      const featureIds = mode?.features.map(f => f.id);
      
      expect(featureIds).toContain('breakpoint-manager');
      expect(featureIds).toContain('error-detector');
      expect(featureIds).toContain('performance-profiler');
      expect(featureIds).toContain('variable-inspector');
    });

    it('review mode should have quality checking features', () => {
      const mode = modeRegistry.getMode('review');
      const featureIds = mode?.features.map(f => f.id);
      
      expect(featureIds).toContain('code-analyzer');
      expect(featureIds).toContain('quality-checker');
      expect(featureIds).toContain('documentation-verifier');
      expect(featureIds).toContain('diff-viewer');
    });
  });

  describe('Mode Transitions', () => {
    it('should transition between modes', async () => {
      const mockContext: ContextSnapshot = {
        fileContext: {
          path: 'test.ts',
          name: 'test.ts',
          extension: '.ts',
          size: 100,
          lastModified: Date.now(),
          hasErrors: false,
          isTest: false,
          isConfig: false,
        },
        projectContext: {
          name: 'test-project',
          type: 'web-application',
          path: '/test',
          dependencies: {},
          hasTypeScript: true,
          hasTests: true,
        },
        environmentContext: {
          gitStatus: {
            branch: 'main',
            isClean: true,
          },
        },
        userContext: {
          recentActions: [],
          preferences: {},
          activityPatterns: {},
        },
        timestamp: Date.now(),
      };

      await modeRegistry.setActiveMode('architect', mockContext);
      expect(modeRegistry.getActiveMode()?.id).toBe('architect');

      await modeRegistry.setActiveMode('debug', mockContext);
      expect(modeRegistry.getActiveMode()?.id).toBe('debug');

      const history = modeRegistry.getTransitionHistory();
      expect(history).toHaveLength(2);
      expect(history[1].from).toBe('architect');
      expect(history[1].to).toBe('debug');
    });

    it('should track mode activation callbacks', async () => {
      const activateSpy = vi.fn();
      const deactivateSpy = vi.fn();

      // Get the architect mode definition and spy on its callbacks
      const architectModeDef = allModes.find(m => m.id === 'architect');
      if (architectModeDef) {
        architectModeDef.activate = activateSpy;
        architectModeDef.deactivate = deactivateSpy;
        
        // Re-register with spied version
        modeRegistry.register(architectModeDef);
      }

      const mockContext: ContextSnapshot = {
        fileContext: {
          path: 'test.ts',
          name: 'test.ts',
          extension: '.ts',
          size: 100,
          lastModified: Date.now(),
          hasErrors: false,
          isTest: false,
          isConfig: false,
        },
        projectContext: {
          name: 'test-project',
          type: 'web-application',
          path: '/test',
          dependencies: {},
          hasTypeScript: true,
          hasTests: true,
        },
        environmentContext: {
          gitStatus: {
            branch: 'main',
            isClean: true,
          },
        },
        userContext: {
          recentActions: [],
          preferences: {},
          activityPatterns: {},
        },
        timestamp: Date.now(),
      };

      await modeRegistry.setActiveMode('architect', mockContext);
      expect(activateSpy).toHaveBeenCalledWith(mockContext);

      await modeRegistry.setActiveMode('debug', mockContext);
      expect(deactivateSpy).toHaveBeenCalled();
    });
  });

  describe('Mode Shortcuts', () => {
    it('each mode should have unique shortcuts', () => {
      const modes = modeRegistry.getAllModes();
      const allShortcuts = new Set<string>();

      modes.forEach(mode => {
        mode.shortcuts.forEach(shortcut => {
          const key = `${shortcut.modifiers?.join('+')}+${shortcut.key}`;
          expect(allShortcuts.has(key)).toBe(false);
          allShortcuts.add(key);
        });
      });
    });

    it('architect mode should have design-related shortcuts', () => {
      const mode = modeRegistry.getMode('architect');
      const shortcuts = mode?.shortcuts.map(s => s.action);
      
      expect(shortcuts).toContain('openSystemDesigner');
      expect(shortcuts).toContain('analyzeDependencies');
      expect(shortcuts).toContain('mapComponents');
    });
  });

  describe('Mode Colors and Icons', () => {
    it('each mode should have unique colors and icons', () => {
      const modes = modeRegistry.getAllModes();
      const colors = new Set(modes.map(m => m.color));
      const icons = new Set(modes.map(m => m.icon));

      expect(colors.size).toBe(6);
      expect(icons.size).toBe(6);
    });
  });
});
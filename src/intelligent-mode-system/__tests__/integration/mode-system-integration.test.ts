import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { modeRegistry } from '../../core/ModeRegistry';
import { contextManager } from '../../core/ContextManager';
import { eventBus } from '../../core/EventBus';
import { ModeTransitionManager } from '../../core/ModeTransitionManager';
import { ContextSnapshot } from '../../types';
import { fileContextProvider } from '../../providers/FileContextProvider';
import { userContextProvider } from '../../providers/UserContextProvider';

describe('Intelligent Mode System Integration', () => {
  let transitionManager: ModeTransitionManager;
  let mockContext: ContextSnapshot;

  beforeEach(() => {
    transitionManager = new ModeTransitionManager();
    
    // Reset all systems
    modeRegistry['activeMode'] = null;
    modeRegistry['transitionHistory'] = [];
    eventBus['listeners'].clear();
    
    mockContext = {
      fileContext: {
        path: 'src/App.tsx',
        name: 'App.tsx',
        extension: '.tsx',
        size: 1000,
        lastModified: Date.now(),
        hasErrors: false,
        isTest: false,
        isConfig: false,
      },
      projectContext: {
        name: 'test-project',
        type: 'web-application',
        path: '/test',
        dependencies: {
          'react': '^18.0.0',
          'typescript': '^5.0.0',
        },
        hasTypeScript: true,
        hasTests: true,
      },
      environmentContext: {
        gitStatus: {
          branch: 'feature/new-component',
          isClean: false,
        },
      },
      userContext: {
        recentActions: [],
        preferences: {},
        activityPatterns: {},
      },
      timestamp: Date.now(),
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('End-to-End Mode Transitions', () => {
    it('should perform complete mode lifecycle', async () => {
      const modeChangeHandler = vi.fn();
      eventBus.on('mode:changed', modeChangeHandler);

      // Start with architect mode
      await modeRegistry.setActiveMode('architect', mockContext);
      expect(modeRegistry.getActiveMode()?.id).toBe('architect');
      expect(modeChangeHandler).toHaveBeenCalledWith({
        from: null,
        to: 'architect',
        context: mockContext,
      });

      // Simulate error occurring
      mockContext.fileContext.hasErrors = true;
      const shouldTransition = await transitionManager.shouldTransition('architect', mockContext);
      expect(shouldTransition?.to).toBe('debug');

      // Execute transition
      await transitionManager.executeTransition('architect', 'debug', mockContext);
      await modeRegistry.setActiveMode('debug', mockContext);
      expect(modeRegistry.getActiveMode()?.id).toBe('debug');

      // Fix error and transition to review
      mockContext.fileContext.hasErrors = false;
      mockContext.environmentContext.gitStatus.branch = 'review/feature';
      await modeRegistry.setActiveMode('review', mockContext);
      expect(modeRegistry.getActiveMode()?.id).toBe('review');

      // Check transition history
      const history = modeRegistry.getTransitionHistory();
      expect(history).toHaveLength(3);
      expect(history[0].to).toBe('architect');
      expect(history[1].to).toBe('debug');
      expect(history[2].to).toBe('review');
    });
  });

  describe('Context-Driven Mode Selection', () => {
    it('should select appropriate mode based on file context', async () => {
      // Architecture file
      fileContextProvider.setCurrentFile('docs/architecture.md');
      const archContext = await contextManager.getContext();
      
      // Get mode recommendations
      const modes = modeRegistry.getAllModes();
      const architectMode = modes.find(m => m.id === 'architect');
      
      // Check if architect mode rules match
      const archMatch = architectMode?.contextRules.some(rule => 
        rule.condition(archContext)
      );
      expect(archMatch).toBe(true);

      // Error file
      fileContextProvider.setCurrentFile('src/error.ts');
      mockContext.fileContext.hasErrors = true;
      const debugContext = { ...archContext, fileContext: { ...archContext.fileContext, hasErrors: true } };
      
      const debugMode = modes.find(m => m.id === 'debug');
      const debugMatch = debugMode?.contextRules.some(rule =>
        rule.condition(debugContext)
      );
      expect(debugMatch).toBe(true);
    });
  });

  describe('Mode Feature Activation', () => {
    it('should activate and deactivate mode features correctly', async () => {
      const architectActivate = vi.fn();
      const architectDeactivate = vi.fn();
      
      // Get architect mode and spy on its lifecycle
      const architectMode = modeRegistry.getMode('architect');
      if (architectMode) {
        architectMode.activate = architectActivate;
        architectMode.deactivate = architectDeactivate;
        modeRegistry.register(architectMode);
      }

      // Activate architect mode
      await modeRegistry.setActiveMode('architect', mockContext);
      expect(architectActivate).toHaveBeenCalledWith(mockContext);

      // Switch to debug mode
      await modeRegistry.setActiveMode('debug', mockContext);
      expect(architectDeactivate).toHaveBeenCalled();
    });
  });

  describe('User Action Integration', () => {
    it('should track user actions and influence mode selection', async () => {
      // Simulate debugging actions
      userContextProvider.recordAction({
        type: 'debug',
        target: 'breakpoint',
      });
      userContextProvider.recordAction({
        type: 'edit',
        target: 'console.log',
      });

      // Get updated context
      const context = await contextManager.getContext();
      
      // Check if debug mode would be recommended
      const debugMode = modeRegistry.getMode('debug');
      const debugMatch = debugMode?.contextRules.some(rule =>
        rule.condition(context)
      );
      expect(debugMatch).toBe(true);
    });
  });

  describe('Performance and Caching', () => {
    it('should utilize caching for mode recommendations', async () => {
      const startTime = performance.now();
      
      // First context fetch
      const context1 = await contextManager.getContext();
      const firstFetchTime = performance.now() - startTime;

      // Second context fetch (should be cached)
      const cacheStart = performance.now();
      const context2 = await contextManager.getContext();
      const cachedFetchTime = performance.now() - cacheStart;

      // Cached fetch should be much faster
      expect(cachedFetchTime).toBeLessThan(firstFetchTime);
      expect(context1.timestamp).toBe(context2.timestamp);
    });
  });

  describe('Event System Integration', () => {
    it('should emit and handle mode-related events', async () => {
      const events: any[] = [];
      
      eventBus.on('mode:changed', (data) => events.push({ type: 'changed', data }));
      eventBus.on('mode:activated', (data) => events.push({ type: 'activated', data }));
      eventBus.on('mode:deactivated', (data) => events.push({ type: 'deactivated', data }));

      // Perform mode changes
      await modeRegistry.setActiveMode('architect', mockContext);
      await modeRegistry.setActiveMode('debug', mockContext);

      // Verify events
      expect(events).toHaveLength(5); // changed, activated, changed, deactivated, activated
      expect(events[0].type).toBe('changed');
      expect(events[1].type).toBe('activated');
      expect(events[2].type).toBe('changed');
      expect(events[3].type).toBe('deactivated');
      expect(events[4].type).toBe('activated');
    });
  });

  describe('Mode Shortcuts Integration', () => {
    it('should handle mode shortcuts correctly', () => {
      const modes = modeRegistry.getAllModes();
      const shortcutMap = new Map<string, string>();

      // Build shortcut map
      modes.forEach(mode => {
        mode.shortcuts.forEach(shortcut => {
          const key = `${shortcut.modifiers?.join('+')}+${shortcut.key}`;
          expect(shortcutMap.has(key)).toBe(false); // No duplicates
          shortcutMap.set(key, mode.id);
        });
      });

      // Verify each mode has unique shortcuts
      expect(shortcutMap.size).toBeGreaterThan(0);
    });
  });

  describe('Complex Scenarios', () => {
    it('should handle rapid mode switches gracefully', async () => {
      const modes = ['architect', 'debug', 'review', 'deploy', 'experiment', 'learn'];
      
      // Rapid mode switches
      for (const modeId of modes) {
        await modeRegistry.setActiveMode(modeId as any, mockContext);
      }

      // Verify final state
      expect(modeRegistry.getActiveMode()?.id).toBe('learn');
      
      // Verify history
      const history = modeRegistry.getTransitionHistory();
      expect(history).toHaveLength(modes.length);
    });

    it('should handle mode transitions with changing context', async () => {
      // Start in architect mode
      await modeRegistry.setActiveMode('architect', mockContext);

      // Simulate working on architecture
      userContextProvider.recordAction({ type: 'edit', target: 'architecture.md' });
      userContextProvider.recordAction({ type: 'navigate', target: 'docs/' });

      // Error occurs
      mockContext.fileContext.hasErrors = true;
      fileContextProvider.setCurrentFile('src/error.ts');

      // Check if transition is recommended
      const shouldTransition = await transitionManager.shouldTransition('architect', mockContext);
      expect(shouldTransition?.to).toBe('debug');

      // Transition to debug
      await modeRegistry.setActiveMode('debug', mockContext);

      // Fix error and prepare for review
      mockContext.fileContext.hasErrors = false;
      mockContext.environmentContext.gitStatus.branch = 'feature/fixed';
      userContextProvider.recordAction({ type: 'command', target: 'git add' });
      userContextProvider.recordAction({ type: 'command', target: 'git commit' });

      // Transition to review
      await modeRegistry.setActiveMode('review', mockContext);
      expect(modeRegistry.getActiveMode()?.id).toBe('review');
    });
  });
});
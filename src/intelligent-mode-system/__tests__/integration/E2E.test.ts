/**
 * End-to-End Integration Tests
 * 
 * Comprehensive integration tests that verify the entire intelligent mode system
 * working together from context analysis to mode selection and suggestions.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { IntelligentModeSystem } from '../../core/IntelligentModeSystem';
import { ModeProviderRegistry } from '../../core/ModeProviderRegistry';
import { ContextAnalyzer } from '../../core/ContextAnalyzer';
import { pluginRegistry } from '../../core/PluginRegistry';
import { pluginManager } from '../../core/PluginManager';
import { MockDataFactory, TestEnvironment } from '../utils/TestUtils';
import { ContextSnapshot, ModeType } from '../../types';

// Setup test environment
beforeEach(() => {
  TestEnvironment.setupMocks();
});

afterEach(() => {
  TestEnvironment.resetMocks();
});

describe('End-to-End Integration Tests', () => {
  let intelligentModeSystem: IntelligentModeSystem;
  let modeRegistry: ModeProviderRegistry;
  let contextAnalyzer: ContextAnalyzer;

  beforeEach(async () => {
    // Initialize core components
    modeRegistry = new ModeProviderRegistry();
    contextAnalyzer = new ContextAnalyzer();
    intelligentModeSystem = new IntelligentModeSystem();

    // Initialize systems
    await contextAnalyzer.initialize();
    await intelligentModeSystem.initialize();
    await pluginManager.initialize();
  });

  describe('Complete Mode Selection Workflow', () => {
    it('should complete full workflow for React component development', async () => {
      const context = MockDataFactory.createContextSnapshot({
        fileContext: {
          path: '/src/components/LoginForm.tsx',
          content: `
            import React, { useState } from 'react';
            
            interface LoginFormProps {
              onSubmit: (email: string, password: string) => void;
            }
            
            export const LoginForm: React.FC<LoginFormProps> = ({ onSubmit }) => {
              const [email, setEmail] = useState('');
              const [password, setPassword] = useState('');
              
              const handleSubmit = (e: React.FormEvent) => {
                e.preventDefault();
                onSubmit(email, password);
              };
              
              return (
                <form onSubmit={handleSubmit}>
                  <input 
                    type="email" 
                    value={email} 
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Email"
                  />
                  <input 
                    type="password" 
                    value={password} 
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Password"
                  />
                  <button type="submit">Login</button>
                </form>
              );
            };
          `,
          language: 'typescript',
          hasErrors: false,
          openFiles: ['/src/components/LoginForm.tsx'],
        },
        projectContext: {
          name: 'auth-app',
          type: 'typescript',
          structure: {
            hasTests: true,
            hasPackageJson: true,
            directories: ['src', 'components', '__tests__', 'styles'],
            mainFiles: ['index.ts', 'App.tsx'],
          },
          dependencies: ['react', '@types/react', 'typescript', 'styled-components'],
        },
      });

      // Step 1: Analyze context
      const analysisResult = await contextAnalyzer.analyzeContext(context);
      
      expect(analysisResult.score).toBeGreaterThan(0.5);
      expect(analysisResult.confidence).toBeGreaterThan(0.7);
      expect(analysisResult.features.is_react_component).toBe(1);
      expect(analysisResult.features.file_type).toBe('typescript');

      // Step 2: Get intelligent mode suggestions
      const modeResult = await intelligentModeSystem.analyzeAndSuggest(context);
      
      expect(modeResult.recommendedMode).toBeDefined();
      expect(modeResult.confidence).toBeGreaterThan(0.6);
      expect(modeResult.suggestions).toHaveLength.greaterThan(0);
      expect(modeResult.actions).toHaveLength.greaterThan(0);

      // Step 3: Verify mode is appropriate for React development
      expect(['coding', 'creative', 'analytical']).toContain(modeResult.recommendedMode);

      // Step 4: Test suggestions are relevant
      const suggestionTexts = modeResult.suggestions.join(' ').toLowerCase();
      const expectedKeywords = ['test', 'component', 'validation', 'accessibility'];
      const hasRelevantSuggestions = expectedKeywords.some(keyword => 
        suggestionTexts.includes(keyword)
      );
      expect(hasRelevantSuggestions).toBe(true);
    });

    it('should complete full workflow for API development', async () => {
      const context = MockDataFactory.createContextSnapshot({
        fileContext: {
          path: '/src/api/auth.ts',
          content: `
            import express from 'express';
            import bcrypt from 'bcrypt';
            import jwt from 'jsonwebtoken';
            import { User } from '../models/User';
            
            export const authRouter = express.Router();
            
            authRouter.post('/login', async (req, res) => {
              try {
                const { email, password } = req.body;
                
                const user = await User.findOne({ where: { email } });
                if (!user) {
                  return res.status(401).json({ error: 'Invalid credentials' });
                }
                
                const isValidPassword = await bcrypt.compare(password, user.password);
                if (!isValidPassword) {
                  return res.status(401).json({ error: 'Invalid credentials' });
                }
                
                const token = jwt.sign(
                  { userId: user.id, email: user.email },
                  process.env.JWT_SECRET!,
                  { expiresIn: '24h' }
                );
                
                res.json({ token, user: { id: user.id, email: user.email } });
              } catch (error) {
                res.status(500).json({ error: 'Internal server error' });
              }
            });
          `,
          language: 'typescript',
          hasErrors: false,
          openFiles: ['/src/api/auth.ts'],
        },
        projectContext: {
          name: 'auth-api',
          type: 'typescript',
          structure: {
            hasTests: true,
            hasPackageJson: true,
            directories: ['src', 'api', 'models', '__tests__', 'middleware'],
            mainFiles: ['index.ts', 'server.ts'],
          },
          dependencies: ['express', 'bcrypt', 'jsonwebtoken', 'sequelize'],
        },
      });

      // Analyze context and get mode suggestions
      const analysisResult = await contextAnalyzer.analyzeContext(context);
      const modeResult = await intelligentModeSystem.analyzeAndSuggest(context);

      // Verify API-specific analysis
      expect(analysisResult.features.is_api_endpoint).toBe(1);
      expect(analysisResult.features.has_error_handling).toBe(1);
      expect(analysisResult.features.has_security_patterns).toBe(1);

      // Verify appropriate mode selection for API development
      expect(['coding', 'analytical', 'security']).toContain(modeResult.recommendedMode);

      // Verify security and API-relevant suggestions
      const suggestionTexts = modeResult.suggestions.join(' ').toLowerCase();
      const expectedKeywords = ['security', 'validation', 'test', 'rate limit', 'authentication'];
      const hasRelevantSuggestions = expectedKeywords.some(keyword => 
        suggestionTexts.includes(keyword)
      );
      expect(hasRelevantSuggestions).toBe(true);
    });

    it('should complete full workflow for data analysis script', async () => {
      const context = MockDataFactory.createContextSnapshot({
        fileContext: {
          path: '/src/analysis/sales_report.py',
          content: `
            import pandas as pd
            import numpy as np
            import matplotlib.pyplot as plt
            import seaborn as sns
            from datetime import datetime, timedelta
            
            def analyze_sales_data(data_path: str) -> dict:
                """
                Analyze sales data and generate insights.
                """
                # Load data
                df = pd.read_csv(data_path)
                
                # Data cleaning
                df['date'] = pd.to_datetime(df['date'])
                df = df.dropna()
                
                # Calculate metrics
                total_revenue = df['amount'].sum()
                avg_order_value = df['amount'].mean()
                monthly_growth = calculate_monthly_growth(df)
                
                # Generate visualizations
                create_sales_charts(df)
                
                return {
                    'total_revenue': total_revenue,
                    'avg_order_value': avg_order_value,
                    'monthly_growth': monthly_growth,
                    'top_products': get_top_products(df)
                }
            
            def calculate_monthly_growth(df: pd.DataFrame) -> float:
                monthly_sales = df.groupby(df['date'].dt.to_period('M'))['amount'].sum()
                return monthly_sales.pct_change().mean()
          `,
          language: 'python',
          hasErrors: false,
          openFiles: ['/src/analysis/sales_report.py'],
        },
        projectContext: {
          name: 'sales-analytics',
          type: 'python',
          structure: {
            hasTests: true,
            hasPackageJson: false,
            directories: ['src', 'analysis', 'data', 'notebooks', 'tests'],
            mainFiles: ['main.py', 'requirements.txt'],
          },
          dependencies: ['pandas', 'numpy', 'matplotlib', 'seaborn', 'jupyter'],
        },
      });

      // Analyze context and get mode suggestions
      const analysisResult = await contextAnalyzer.analyzeContext(context);
      const modeResult = await intelligentModeSystem.analyzeAndSuggest(context);

      // Verify data analysis-specific features
      expect(analysisResult.features.is_data_analysis).toBe(1);
      expect(analysisResult.features.has_visualization).toBe(1);
      expect(analysisResult.features.file_type).toBe('python');

      // Verify appropriate mode selection for data analysis
      expect(['analytical', 'research', 'creative']).toContain(modeResult.recommendedMode);

      // Verify data analysis-relevant suggestions
      const suggestionTexts = modeResult.suggestions.join(' ').toLowerCase();
      const expectedKeywords = ['visualization', 'data', 'analysis', 'statistics', 'documentation'];
      const hasRelevantSuggestions = expectedKeywords.some(keyword => 
        suggestionTexts.includes(keyword)
      );
      expect(hasRelevantSuggestions).toBe(true);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle rapid context changes efficiently', async () => {
      const contexts = [
        MockDataFactory.createContextSnapshot({ // React component
          fileContext: { path: '/src/Button.tsx', language: 'typescript' }
        }),
        MockDataFactory.createContextSnapshot({ // Python script
          fileContext: { path: '/src/analysis.py', language: 'python' }
        }),
        MockDataFactory.createContextSnapshot({ // API endpoint
          fileContext: { path: '/src/api/users.ts', language: 'typescript' }
        }),
        MockDataFactory.createContextSnapshot({ // CSS styles
          fileContext: { path: '/src/styles.css', language: 'css' }
        }),
        MockDataFactory.createContextSnapshot({ // Documentation
          fileContext: { path: '/docs/README.md', language: 'markdown' }
        }),
      ];

      // Measure time for rapid context switches
      const startTime = performance.now();
      
      const results = await Promise.all(
        contexts.map(context => intelligentModeSystem.analyzeAndSuggest(context))
      );
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // Should handle 5 context switches in under 1 second
      expect(totalTime).toBeLessThan(1000);
      expect(results).toHaveLength(5);
      
      // All results should be valid
      results.forEach(result => {
        expect(result.recommendedMode).toBeDefined();
        expect(result.confidence).toBeGreaterThanOrEqual(0);
        expect(result.confidence).toBeLessThanOrEqual(1);
        expect(Array.isArray(result.suggestions)).toBe(true);
        expect(Array.isArray(result.actions)).toBe(true);
      });
    });

    it('should maintain performance with large file content', async () => {
      const largeContent = `
        import React from 'react';
        ${'// '.repeat(10000)}Large comment section
        ${Array.from({ length: 1000 }, (_, i) => `
          const Component${i} = () => <div>Component ${i}</div>;
        `).join('\n')}
      `;

      const context = MockDataFactory.createContextSnapshot({
        fileContext: {
          path: '/src/LargeFile.tsx',
          content: largeContent,
          language: 'typescript',
          hasErrors: false,
          openFiles: ['/src/LargeFile.tsx'],
        },
      });

      const startTime = performance.now();
      const result = await intelligentModeSystem.analyzeAndSuggest(context);
      const endTime = performance.now();

      // Should handle large files within reasonable time
      expect(endTime - startTime).toBeLessThan(500);
      expect(result.recommendedMode).toBeDefined();
      expect(result.confidence).toBeGreaterThan(0);
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle malformed context gracefully', async () => {
      const malformedContext = {
        fileContext: null,
        projectContext: undefined,
        userContext: {},
        environmentContext: {},
        timestamp: 'invalid',
      } as any;

      const result = await intelligentModeSystem.analyzeAndSuggest(malformedContext);

      expect(result.recommendedMode).toBeDefined();
      expect(result.confidence).toBe(0);
      expect(result.suggestions).toHaveLength(0);
      expect(result.actions).toHaveLength(0);
      expect(result.error).toBeDefined();
    });

    it('should handle context analyzer failures gracefully', async () => {
      // Mock analyzer to throw error
      const originalAnalyze = contextAnalyzer.analyzeContext;
      contextAnalyzer.analyzeContext = vi.fn().mockRejectedValue(new Error('Analysis failed'));

      const context = MockDataFactory.createContextSnapshot();
      const result = await intelligentModeSystem.analyzeAndSuggest(context);

      expect(result.recommendedMode).toBeDefined();
      expect(result.confidence).toBe(0);
      expect(result.error).toContain('Analysis failed');

      // Restore original method
      contextAnalyzer.analyzeContext = originalAnalyze;
    });

    it('should handle mode provider failures gracefully', async () => {
      const context = MockDataFactory.createContextSnapshot();
      
      // Register a failing mode provider
      const failingProvider = MockDataFactory.createModeProvider('coding');
      failingProvider.getScore = vi.fn(() => {
        throw new Error('Scoring failed');
      });
      
      modeRegistry.registerProvider(failingProvider);

      const result = await intelligentModeSystem.analyzeAndSuggest(context);

      // Should still provide a result even with failing provider
      expect(result.recommendedMode).toBeDefined();
      expect(result.confidence).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Plugin System Integration', () => {
    it('should integrate custom plugins into workflow', async () => {
      // This test would require a more complete plugin implementation
      // For now, we verify the plugin system is initialized
      expect(pluginManager.getStats()).toBeDefined();
      
      const stats = pluginManager.getStats();
      expect(stats.totalPlugins).toBeGreaterThanOrEqual(0);
      expect(stats.activePlugins).toBeGreaterThanOrEqual(0);
    });

    it('should handle plugin lifecycle during analysis', async () => {
      const context = MockDataFactory.createContextSnapshot();
      
      // Verify plugins can be activated/deactivated during analysis
      const initialStats = pluginManager.getStats();
      const result = await intelligentModeSystem.analyzeAndSuggest(context);
      const finalStats = pluginManager.getStats();

      expect(result.recommendedMode).toBeDefined();
      expect(finalStats.totalPlugins).toEqual(initialStats.totalPlugins);
    });
  });

  describe('Memory Management and Cleanup', () => {
    it('should not leak memory during repeated analysis', async () => {
      const context = MockDataFactory.createContextSnapshot();
      
      // Perform many analyses to test for memory leaks
      for (let i = 0; i < 100; i++) {
        await intelligentModeSystem.analyzeAndSuggest(context);
      }

      // In a real environment, we would check actual memory usage
      // For now, verify system still responds correctly
      const finalResult = await intelligentModeSystem.analyzeAndSuggest(context);
      expect(finalResult.recommendedMode).toBeDefined();
    });

    it('should cleanup resources on shutdown', async () => {
      // Verify cleanup methods exist and can be called
      expect(typeof intelligentModeSystem.shutdown).toBe('function');
      
      await intelligentModeSystem.shutdown();
      
      // System should still be in a valid state
      expect(intelligentModeSystem).toBeDefined();
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle typical development workflow', async () => {
      // Simulate a typical development session
      const scenarios = [
        // 1. Opening a new React component
        MockDataFactory.createContextSnapshot({
          fileContext: { path: '/src/NewComponent.tsx', content: '', language: 'typescript' }
        }),
        
        // 2. Adding some basic structure
        MockDataFactory.createContextSnapshot({
          fileContext: { 
            path: '/src/NewComponent.tsx',
            content: 'import React from "react";',
            language: 'typescript'
          }
        }),
        
        // 3. Adding component logic
        MockDataFactory.createContextSnapshot({
          fileContext: { 
            path: '/src/NewComponent.tsx',
            content: `
              import React from 'react';
              
              export const NewComponent = () => {
                return <div>Hello World</div>;
              };
            `,
            language: 'typescript'
          }
        }),
        
        // 4. Adding tests
        MockDataFactory.createContextSnapshot({
          fileContext: { 
            path: '/src/__tests__/NewComponent.test.tsx',
            content: `
              import { render } from '@testing-library/react';
              import { NewComponent } from '../NewComponent';
              
              test('renders hello world', () => {
                render(<NewComponent />);
              });
            `,
            language: 'typescript'
          }
        }),
      ];

      // Each scenario should provide appropriate suggestions
      for (const scenario of scenarios) {
        const result = await intelligentModeSystem.analyzeAndSuggest(scenario);
        
        expect(result.recommendedMode).toBeDefined();
        expect(result.confidence).toBeGreaterThanOrEqual(0);
        
        // Suggestions should evolve based on context
        if (scenario.fileContext.content.includes('test')) {
          expect(result.recommendedMode).toBe('analytical');
        } else if (scenario.fileContext.content.includes('React')) {
          expect(['coding', 'creative']).toContain(result.recommendedMode);
        }
      }
    });

    it('should provide consistent results for similar contexts', async () => {
      const baseContext = MockDataFactory.createContextSnapshot({
        fileContext: {
          path: '/src/Button.tsx',
          content: `
            import React from 'react';
            export const Button = () => <button>Click me</button>;
          `,
          language: 'typescript',
        },
      });

      // Create slight variations of the same context
      const variations = [
        { ...baseContext, timestamp: Date.now() },
        { 
          ...baseContext, 
          fileContext: {
            ...baseContext.fileContext,
            path: '/src/components/Button.tsx'
          }
        },
        {
          ...baseContext,
          userContext: {
            ...baseContext.userContext,
            preferences: { theme: 'light' }
          }
        },
      ];

      const results = await Promise.all(
        variations.map(context => intelligentModeSystem.analyzeAndSuggest(context))
      );

      // Results should be consistent (same mode, similar confidence)
      const modes = results.map(r => r.recommendedMode);
      const confidences = results.map(r => r.confidence);

      expect(new Set(modes).size).toBeLessThanOrEqual(2); // At most 2 different modes
      
      const maxConfidenceDiff = Math.max(...confidences) - Math.min(...confidences);
      expect(maxConfidenceDiff).toBeLessThan(0.3); // Confidence should be similar
    });
  });
});
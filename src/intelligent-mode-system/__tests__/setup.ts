/**
 * Test Setup Configuration
 * 
 * Global test setup and configuration for the intelligent mode system test suite.
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { TestEnvironment } from './utils/TestUtils';

// Global test setup
beforeAll(async () => {
  // Initialize test environment
  TestEnvironment.setupMocks();

  // Set up global test configuration
  process.env.NODE_ENV = 'test';
  process.env.INTELLIGENT_MODE_DEBUG = 'false';
  
  // Mock console methods to reduce noise during tests
  const originalLog = console.log;
  const originalWarn = console.warn;
  const originalError = console.error;

  console.log = (...args: any[]) => {
    if (process.env.VITEST_VERBOSE === 'true') {
      originalLog(...args);
    }
  };

  console.warn = (...args: any[]) => {
    if (process.env.VITEST_VERBOSE === 'true') {
      originalWarn(...args);
    }
  };

  console.error = (...args: any[]) => {
    if (process.env.VITEST_VERBOSE === 'true') {
      originalError(...args);
    }
  };

  // Store original methods for cleanup
  (global as any).__originalConsole = { log: originalLog, warn: originalWarn, error: originalError };
});

// Global test cleanup
afterAll(async () => {
  // Restore console methods
  const original = (global as any).__originalConsole;
  if (original) {
    console.log = original.log;
    console.warn = original.warn;
    console.error = original.error;
  }

  // Clean up test environment
  TestEnvironment.resetMocks();
});

// Per-test setup
beforeEach(() => {
  // Reset any test-specific state
  TestEnvironment.resetMocks();
});

// Per-test cleanup
afterEach(() => {
  // Ensure clean state between tests
  TestEnvironment.resetMocks();
});
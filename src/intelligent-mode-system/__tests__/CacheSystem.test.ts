import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CacheSystem } from '../core/CacheSystem';
import { CacheStrategy } from '../types';

describe('CacheSystem', () => {
  let cache: CacheSystem;
  let strategy: CacheStrategy;

  beforeEach(() => {
    strategy = {
      type: 'memory',
      ttl: 1000, // 1 second
      maxSize: 1024 * 10, // 10KB
      evictionPolicy: 'lru',
    };
    cache = new CacheSystem(strategy);
  });

  describe('get/set', () => {
    it('should store and retrieve values', async () => {
      const testData = { key: 'value', number: 42 };
      await cache.set('test-key', testData);
      
      const retrieved = await cache.get('test-key');
      expect(retrieved).toEqual(testData);
    });

    it('should return null for non-existent keys', async () => {
      const result = await cache.get('non-existent');
      expect(result).toBeNull();
    });

    it('should respect TTL', async () => {
      await cache.set('expiring-key', 'value');
      
      // Should exist immediately
      expect(await cache.get('expiring-key')).toBe('value');
      
      // Wait for TTL to expire
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      // Should be expired
      expect(await cache.get('expiring-key')).toBeNull();
    });

    it('should update access info on get', async () => {
      await cache.set('test-key', 'value');
      
      // Get multiple times
      await cache.get('test-key');
      await cache.get('test-key');
      
      const stats = cache.getStats();
      expect(stats.hits).toBe(2);
    });
  });

  describe('invalidate', () => {
    it('should remove entries matching pattern', async () => {
      await cache.set('user.1', { id: 1 });
      await cache.set('user.2', { id: 2 });
      await cache.set('post.1', { id: 1 });
      
      await cache.invalidate('user\\..*');
      
      expect(await cache.get('user.1')).toBeNull();
      expect(await cache.get('user.2')).toBeNull();
      expect(await cache.get('post.1')).toEqual({ id: 1 });
    });
  });

  describe('clear', () => {
    it('should remove all entries and reset stats', async () => {
      await cache.set('key1', 'value1');
      await cache.set('key2', 'value2');
      await cache.get('key1');
      
      await cache.clear();
      
      expect(await cache.get('key1')).toBeNull();
      expect(await cache.get('key2')).toBeNull();
      
      const stats = cache.getStats();
      expect(stats.hits).toBe(0);
      expect(stats.misses).toBe(2); // Two misses from checking cleared items
      expect(stats.itemCount).toBe(0);
    });
  });

  describe('eviction', () => {
    it('should evict items when max size is reached', async () => {
      const smallCache = new CacheSystem({
        ...strategy,
        maxSize: 100, // Very small cache
      });
      
      // Add items that will exceed max size
      await smallCache.set('item1', 'a'.repeat(30));
      await smallCache.set('item2', 'b'.repeat(30));
      await smallCache.set('item3', 'c'.repeat(30));
      await smallCache.set('item4', 'd'.repeat(30)); // This should trigger eviction
      
      const stats = smallCache.getStats();
      expect(stats.evictions).toBeGreaterThan(0);
      expect(stats.size).toBeLessThanOrEqual(100);
    });

    it('should evict based on LRU policy', async () => {
      const lruCache = new CacheSystem({
        ...strategy,
        maxSize: 250, // Increased to fit 3 items but force eviction on 4th
        evictionPolicy: 'lru',
      });
      
      // Set items with distinct timing
      await lruCache.set('old', 'a'.repeat(30));
      await new Promise(resolve => setTimeout(resolve, 10));
      await lruCache.set('middle', 'b'.repeat(30));
      await new Promise(resolve => setTimeout(resolve, 10));
      await lruCache.set('recent', 'c'.repeat(30));
      
      // Access 'old' and 'recent' to update their last accessed time
      await new Promise(resolve => setTimeout(resolve, 10));
      await lruCache.get('old');
      await new Promise(resolve => setTimeout(resolve, 10));
      await lruCache.get('recent');
      
      // Add new item that requires eviction (30 chars * 2 bytes = 60 bytes per item)
      // Total would be 240 bytes which exceeds 250, so one item must be evicted
      await new Promise(resolve => setTimeout(resolve, 10));
      await lruCache.set('new', 'd'.repeat(30));
      
      // 'middle' should be evicted (least recently used)
      expect(await lruCache.get('middle')).toBeNull();
      expect(await lruCache.get('old')).not.toBeNull();
      expect(await lruCache.get('recent')).not.toBeNull();
      expect(await lruCache.get('new')).not.toBeNull();
    });
  });

  describe('getStats', () => {
    it('should track cache statistics', async () => {
      // Initial state
      let stats = cache.getStats();
      expect(stats.hits).toBe(0);
      expect(stats.misses).toBe(0);
      expect(stats.itemCount).toBe(0);
      expect(stats.size).toBe(0);
      
      // Add items and access them
      await cache.set('key1', 'value1');
      await cache.set('key2', 'value2');
      await cache.get('key1'); // Hit
      await cache.get('key3'); // Miss
      
      stats = cache.getStats();
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
      expect(stats.itemCount).toBe(2);
      expect(stats.size).toBeGreaterThan(0);
    });
  });

  describe('different storage types', () => {
    it('should support session storage', async () => {
      const sessionCache = new CacheSystem({
        ...strategy,
        type: 'session',
      });
      
      // Mock sessionStorage
      const mockStorage: { [key: string]: string } = {};
      global.sessionStorage = {
        getItem: (key: string) => mockStorage[key] || null,
        setItem: (key: string, value: string) => { mockStorage[key] = value; },
        removeItem: (key: string) => { delete mockStorage[key]; },
        clear: () => { Object.keys(mockStorage).forEach(key => delete mockStorage[key]); },
        length: 0,
        key: () => null,
      };
      
      await sessionCache.set('test', { data: 'value' });
      const result = await sessionCache.get('test');
      expect(result).toEqual({ data: 'value' });
    });
  });
});
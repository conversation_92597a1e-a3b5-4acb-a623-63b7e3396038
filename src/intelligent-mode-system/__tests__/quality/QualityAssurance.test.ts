/**
 * Quality Assurance Tests
 * 
 * Comprehensive test suite for ensuring code quality, type safety,
 * error handling, and overall system reliability.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { IntelligentModeSystem } from '../../core/IntelligentModeSystem';
import { ContextAnalyzer } from '../../core/ContextAnalyzer';
import { ModeProviderRegistry } from '../../core/ModeProviderRegistry';
import { pluginRegistry } from '../../core/PluginRegistry';
import { MockDataFactory, TestEnvironment, ErrorTestUtils } from '../utils/TestUtils';
import { ContextSnapshot, ModeType } from '../../types';

describe('Quality Assurance Tests', () => {
  let intelligentModeSystem: IntelligentModeSystem;
  let contextAnalyzer: ContextAnalyzer;
  let modeRegistry: ModeProviderRegistry;

  beforeEach(async () => {
    TestEnvironment.setupMocks();
    
    intelligentModeSystem = new IntelligentModeSystem();
    contextAnalyzer = new ContextAnalyzer();
    modeRegistry = new ModeProviderRegistry();

    await contextAnalyzer.initialize();
    await intelligentModeSystem.initialize();
  });

  afterEach(() => {
    TestEnvironment.resetMocks();
  });

  describe('Type Safety and Validation', () => {
    it('should validate context snapshot structure', () => {
      const validContext = MockDataFactory.createContextSnapshot();
      
      // Check required properties exist and have correct types
      expect(validContext).toHaveProperty('fileContext');
      expect(validContext).toHaveProperty('projectContext');
      expect(validContext).toHaveProperty('userContext');
      expect(validContext).toHaveProperty('environmentContext');
      expect(validContext).toHaveProperty('timestamp');
      
      expect(typeof validContext.timestamp).toBe('number');
      expect(typeof validContext.fileContext.path).toBe('string');
      expect(typeof validContext.fileContext.language).toBe('string');
      expect(typeof validContext.projectContext.name).toBe('string');
      expect(Array.isArray(validContext.projectContext.dependencies)).toBe(true);
    });

    it('should validate analysis result structure', async () => {
      const context = MockDataFactory.createContextSnapshot();
      const result = await contextAnalyzer.analyzeContext(context);
      
      expect(result).toHaveProperty('score');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('features');
      expect(result).toHaveProperty('metadata');
      
      expect(typeof result.score).toBe('number');
      expect(typeof result.confidence).toBe('number');
      expect(typeof result.features).toBe('object');
      expect(typeof result.metadata).toBe('object');
      
      // Validate score and confidence ranges
      expect(result.score).toBeGreaterThanOrEqual(0);
      expect(result.score).toBeLessThanOrEqual(1);
      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeLessThanOrEqual(1);
    });

    it('should validate mode provider interface compliance', () => {
      const provider = MockDataFactory.createModeProvider('creative');
      
      expect(typeof provider.id).toBe('string');
      expect(typeof provider.displayName).toBe('string');
      expect(typeof provider.description).toBe('string');
      expect(typeof provider.isApplicable).toBe('function');
      expect(typeof provider.getScore).toBe('function');
      expect(typeof provider.getSuggestions).toBe('function');
      expect(typeof provider.getActions).toBe('function');
    });

    it('should enforce plugin manifest schema', () => {
      const manifest = MockDataFactory.createPluginManifest();
      
      // Required fields
      expect(manifest.id).toBeDefined();
      expect(manifest.name).toBeDefined();
      expect(manifest.version).toBeDefined();
      expect(manifest.type).toBeDefined();
      expect(manifest.apiVersion).toBeDefined();
      
      // Version format validation
      expect(manifest.version).toMatch(/^\d+\.\d+\.\d+/);
      
      // Array fields
      expect(Array.isArray(manifest.dependencies)).toBe(true);
      expect(Array.isArray(manifest.permissions)).toBe(true);
      
      // Permission structure
      manifest.permissions.forEach(permission => {
        expect(permission).toHaveProperty('type');
        expect(permission).toHaveProperty('description');
        expect(typeof permission.type).toBe('string');
        expect(typeof permission.description).toBe('string');
      });
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle null/undefined inputs gracefully', async () => {
      const nullResult = await intelligentModeSystem.analyzeAndSuggest(null as any);
      expect(nullResult.recommendedMode).toBeDefined();
      expect(nullResult.confidence).toBe(0);
      expect(nullResult.error).toBeDefined();

      const undefinedResult = await intelligentModeSystem.analyzeAndSuggest(undefined as any);
      expect(undefinedResult.recommendedMode).toBeDefined();
      expect(undefinedResult.confidence).toBe(0);
      expect(undefinedResult.error).toBeDefined();
    });

    it('should handle malformed context data', async () => {
      const malformedContexts = [
        { fileContext: null, projectContext: {}, userContext: {}, environmentContext: {}, timestamp: 0 },
        { fileContext: {}, projectContext: null, userContext: {}, environmentContext: {}, timestamp: 0 },
        { fileContext: {}, projectContext: {}, userContext: null, environmentContext: {}, timestamp: 0 },
        { fileContext: {}, projectContext: {}, userContext: {}, environmentContext: null, timestamp: 0 },
        { fileContext: {}, projectContext: {}, userContext: {}, environmentContext: {}, timestamp: 'invalid' },
      ];

      for (const malformedContext of malformedContexts) {
        const result = await intelligentModeSystem.analyzeAndSuggest(malformedContext as any);
        
        expect(result.recommendedMode).toBeDefined();
        expect(result.confidence).toBeGreaterThanOrEqual(0);
        expect(result.suggestions).toBeDefined();
        expect(result.actions).toBeDefined();
      }
    });

    it('should recover from analysis failures', async () => {
      // Mock analyzer to throw error
      const originalAnalyze = contextAnalyzer.analyzeContext;
      contextAnalyzer.analyzeContext = vi.fn().mockRejectedValue(new Error('Analysis failed'));

      const context = MockDataFactory.createContextSnapshot();
      const result = await intelligentModeSystem.analyzeAndSuggest(context);

      expect(result.recommendedMode).toBeDefined();
      expect(result.confidence).toBe(0);
      expect(result.error).toContain('Analysis failed');

      // Restore original method
      contextAnalyzer.analyzeContext = originalAnalyze;
    });

    it('should handle mode provider failures gracefully', async () => {
      const failingProvider = MockDataFactory.createModeProvider('creative');
      failingProvider.getScore = vi.fn(() => {
        throw new Error('Scoring failed');
      });
      failingProvider.getSuggestions = vi.fn().mockRejectedValue(new Error('Suggestions failed'));

      modeRegistry.registerProvider(failingProvider);

      const context = MockDataFactory.createContextSnapshot();
      const result = await intelligentModeSystem.analyzeAndSuggest(context);

      expect(result.recommendedMode).toBeDefined();
      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(result.suggestions)).toBe(true);
      expect(Array.isArray(result.actions)).toBe(true);
    });

    it('should handle network failures in plugin operations', async () => {
      // Mock fetch to fail
      global.fetch = vi.fn().mockRejectedValue(ErrorTestUtils.createNetworkError());

      const context = MockDataFactory.createContextSnapshot();
      const result = await intelligentModeSystem.analyzeAndSuggest(context);

      // System should still work without network-dependent features
      expect(result.recommendedMode).toBeDefined();
      expect(result.confidence).toBeGreaterThanOrEqual(0);
    });

    it('should validate input ranges and constraints', async () => {
      const outOfRangeContext = MockDataFactory.createContextSnapshot({
        timestamp: -1, // Invalid timestamp
        fileContext: {
          path: '', // Empty path
          content: null as any, // Invalid content
          language: 'invalid-language' as any,
          hasErrors: 'not-boolean' as any,
          openFiles: null as any,
        },
      });

      const result = await intelligentModeSystem.analyzeAndSuggest(outOfRangeContext);
      
      expect(result.recommendedMode).toBeDefined();
      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeLessThanOrEqual(1);
    });
  });

  describe('Data Integrity and Consistency', () => {
    it('should maintain consistency between analysis results', async () => {
      const context = MockDataFactory.createContextSnapshot();
      
      // Run analysis multiple times
      const results = await Promise.all([
        contextAnalyzer.analyzeContext(context),
        contextAnalyzer.analyzeContext(context),
        contextAnalyzer.analyzeContext(context),
      ]);

      // Results should be identical for same input
      const firstResult = results[0];
      results.forEach(result => {
        expect(result.score).toBeCloseTo(firstResult.score, 2);
        expect(result.confidence).toBeCloseTo(firstResult.confidence, 2);
        expect(Object.keys(result.features)).toEqual(Object.keys(firstResult.features));
      });
    });

    it('should ensure feature values are within valid ranges', async () => {
      const context = MockDataFactory.createContextSnapshot();
      const features = await contextAnalyzer.extractFeatures(context);

      Object.entries(features).forEach(([key, value]) => {
        expect(typeof value).toBe('number');
        expect(isFinite(value)).toBe(true);
        expect(isNaN(value)).toBe(false);
        
        // Most normalized features should be between 0 and 1
        if (!key.includes('count') && !key.includes('size') && !key.includes('time')) {
          expect(value).toBeGreaterThanOrEqual(0);
          expect(value).toBeLessThanOrEqual(1);
        }
      });
    });

    it('should maintain mode provider ranking consistency', async () => {
      const providers = [
        MockDataFactory.createModeProvider('creative', { getScore: vi.fn(() => 0.9) }),
        MockDataFactory.createModeProvider('analytical', { getScore: vi.fn(() => 0.7) }),
        MockDataFactory.createModeProvider('coding', { getScore: vi.fn(() => 0.5) }),
      ];

      providers.forEach(provider => modeRegistry.registerProvider(provider));

      const context = MockDataFactory.createContextSnapshot();
      
      // Get rankings multiple times
      const rankings = await Promise.all([
        modeRegistry.getRankedProviders(context),
        modeRegistry.getRankedProviders(context),
        modeRegistry.getRankedProviders(context),
      ]);

      // Rankings should be consistent
      const firstRanking = rankings[0];
      rankings.forEach(ranking => {
        expect(ranking.map(r => r.provider.id)).toEqual(firstRanking.map(r => r.provider.id));
        ranking.forEach((item, index) => {
          expect(item.score).toBeCloseTo(firstRanking[index].score, 2);
        });
      });
    });

    it('should preserve context immutability', async () => {
      const originalContext = MockDataFactory.createContextSnapshot();
      const contextCopy = JSON.parse(JSON.stringify(originalContext));

      await intelligentModeSystem.analyzeAndSuggest(originalContext);

      // Original context should remain unchanged
      expect(originalContext).toEqual(contextCopy);
    });
  });

  describe('Security and Privacy', () => {
    it('should not expose sensitive information in error messages', async () => {
      const sensitiveContext = MockDataFactory.createContextSnapshot({
        fileContext: {
          path: '/src/config.ts',
          content: `
            const API_KEY = 'sk-1234567890abcdef';
            const DB_PASSWORD = 'super-secret-password';
            const JWT_SECRET = 'my-jwt-secret-key';
          `,
          language: 'typescript',
          hasErrors: false,
          openFiles: ['/src/config.ts'],
        },
      });

      // Force an error during analysis
      const originalAnalyze = contextAnalyzer.analyzeContext;
      contextAnalyzer.analyzeContext = vi.fn().mockRejectedValue(
        new Error(`Analysis failed for content: ${sensitiveContext.fileContext.content}`)
      );

      const result = await intelligentModeSystem.analyzeAndSuggest(sensitiveContext);

      // Error message should not contain sensitive content
      expect(result.error).toBeDefined();
      expect(result.error).not.toContain('sk-1234567890abcdef');
      expect(result.error).not.toContain('super-secret-password');
      expect(result.error).not.toContain('my-jwt-secret-key');

      // Restore original method
      contextAnalyzer.analyzeContext = originalAnalyze;
    });

    it('should validate plugin permissions properly', async () => {
      const restrictedManifest = MockDataFactory.createPluginManifest({
        permissions: [
          { type: 'file.write', description: 'Write to files' },
          { type: 'network.access', description: 'Access network' },
          { type: 'system.exec', description: 'Execute system commands' },
        ],
      });

      // Verify permission validation exists
      expect(Array.isArray(restrictedManifest.permissions)).toBe(true);
      restrictedManifest.permissions.forEach(permission => {
        expect(permission.type).toBeDefined();
        expect(permission.description).toBeDefined();
      });
    });

    it('should sanitize user input in suggestions', async () => {
      const maliciousContext = MockDataFactory.createContextSnapshot({
        fileContext: {
          path: '/src/malicious.ts',
          content: `
            // <script>alert('xss')</script>
            // javascript:void(0)
            // file:///etc/passwd
          `,
          language: 'typescript',
          hasErrors: false,
          openFiles: ['/src/malicious.ts'],
        },
      });

      const result = await intelligentModeSystem.analyzeAndSuggest(maliciousContext);

      // Suggestions should not contain potentially malicious content
      const allSuggestions = result.suggestions.join(' ');
      expect(allSuggestions).not.toContain('<script>');
      expect(allSuggestions).not.toContain('javascript:');
      expect(allSuggestions).not.toContain('file://');
    });
  });

  describe('Performance and Resource Management', () => {
    it('should not cause memory leaks during repeated operations', async () => {
      const context = MockDataFactory.createContextSnapshot();
      
      // Simulate memory usage tracking
      let simulatedMemoryUsage = 50; // MB
      const maxExpectedMemory = 100; // MB

      for (let i = 0; i < 100; i++) {
        await intelligentModeSystem.analyzeAndSuggest(context);
        
        // Simulate minor memory increase
        simulatedMemoryUsage += Math.random() * 0.1;
        
        // Simulate garbage collection
        if (i % 10 === 0) {
          simulatedMemoryUsage = Math.max(50, simulatedMemoryUsage - 2);
        }
      }

      expect(simulatedMemoryUsage).toBeLessThan(maxExpectedMemory);
    });

    it('should handle resource cleanup properly', async () => {
      const context = MockDataFactory.createContextSnapshot();
      
      // Create many analysis results
      const results = [];
      for (let i = 0; i < 20; i++) {
        const result = await intelligentModeSystem.analyzeAndSuggest(context);
        results.push(result);
      }

      // Verify all results are valid
      results.forEach(result => {
        expect(result.recommendedMode).toBeDefined();
        expect(result.confidence).toBeGreaterThanOrEqual(0);
        expect(result.confidence).toBeLessThanOrEqual(1);
      });

      // Clear results (simulate cleanup)
      results.length = 0;

      // System should still work after cleanup
      const finalResult = await intelligentModeSystem.analyzeAndSuggest(context);
      expect(finalResult.recommendedMode).toBeDefined();
    });

    it('should respect timeout limits', async () => {
      const slowProvider = MockDataFactory.createModeProvider('slow-mode');
      slowProvider.getScore = vi.fn(async () => {
        // Simulate slow operation
        await new Promise(resolve => setTimeout(resolve, 200));
        return 0.5;
      });

      modeRegistry.registerProvider(slowProvider);

      const context = MockDataFactory.createContextSnapshot();
      
      const startTime = performance.now();
      const result = await intelligentModeSystem.analyzeAndSuggest(context);
      const endTime = performance.now();

      // Should complete within reasonable time even with slow providers
      expect(endTime - startTime).toBeLessThan(1000);
      expect(result.recommendedMode).toBeDefined();
    });
  });

  describe('API Contract Compliance', () => {
    it('should maintain backward compatibility in return types', async () => {
      const context = MockDataFactory.createContextSnapshot();
      const result = await intelligentModeSystem.analyzeAndSuggest(context);

      // Required fields for backward compatibility
      expect(result).toHaveProperty('recommendedMode');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('suggestions');
      expect(result).toHaveProperty('actions');

      // Types should match expected API
      expect(typeof result.recommendedMode).toBe('string');
      expect(typeof result.confidence).toBe('number');
      expect(Array.isArray(result.suggestions)).toBe(true);
      expect(Array.isArray(result.actions)).toBe(true);
    });

    it('should provide stable plugin interfaces', () => {
      const manifest = MockDataFactory.createPluginManifest();
      
      // Core plugin interface should be stable
      expect(manifest).toHaveProperty('id');
      expect(manifest).toHaveProperty('name');
      expect(manifest).toHaveProperty('version');
      expect(manifest).toHaveProperty('type');
      expect(manifest).toHaveProperty('apiVersion');
      expect(manifest).toHaveProperty('dependencies');
      expect(manifest).toHaveProperty('permissions');
    });

    it('should handle version compatibility correctly', () => {
      const manifests = [
        MockDataFactory.createPluginManifest({ apiVersion: '1.0.0' }),
        MockDataFactory.createPluginManifest({ apiVersion: '1.1.0' }),
        MockDataFactory.createPluginManifest({ apiVersion: '1.2.0' }),
      ];

      manifests.forEach(manifest => {
        expect(manifest.apiVersion).toMatch(/^\d+\.\d+\.\d+$/);
        
        // Version should be parseable
        const [major, minor, patch] = manifest.apiVersion.split('.').map(Number);
        expect(major).toBeGreaterThanOrEqual(1);
        expect(minor).toBeGreaterThanOrEqual(0);
        expect(patch).toBeGreaterThanOrEqual(0);
      });
    });
  });

  describe('Code Quality Metrics', () => {
    it('should maintain high code coverage', () => {
      // This test verifies that core functionality is being tested
      // In a real scenario, this would integrate with coverage tools
      
      const coreComponents = [
        'IntelligentModeSystem',
        'ContextAnalyzer', 
        'ModeProviderRegistry',
        'PluginRegistry',
      ];

      // Verify all core components are being tested
      coreComponents.forEach(component => {
        expect(component).toBeDefined();
      });
    });

    it('should follow consistent coding patterns', async () => {
      const context = MockDataFactory.createContextSnapshot();
      
      // All async operations should return promises
      const analysisPromise = contextAnalyzer.analyzeContext(context);
      expect(analysisPromise).toBeInstanceOf(Promise);

      const modePromise = intelligentModeSystem.analyzeAndSuggest(context);
      expect(modePromise).toBeInstanceOf(Promise);

      // Promises should resolve properly
      const analysisResult = await analysisPromise;
      const modeResult = await modePromise;

      expect(analysisResult).toBeDefined();
      expect(modeResult).toBeDefined();
    });

    it('should handle edge cases consistently', async () => {
      const edgeCases = [
        MockDataFactory.createContextSnapshot({ // Empty file
          fileContext: { path: '/empty.ts', content: '', language: 'typescript', hasErrors: false, openFiles: [] }
        }),
        MockDataFactory.createContextSnapshot({ // Binary file
          fileContext: { path: '/image.png', content: '', language: 'binary', hasErrors: false, openFiles: [] }
        }),
        MockDataFactory.createContextSnapshot({ // Very long path
          fileContext: { path: '/very/long/nested/path/'.repeat(10) + 'file.ts', content: 'const x = 1;', language: 'typescript', hasErrors: false, openFiles: [] }
        }),
      ];

      for (const edgeCase of edgeCases) {
        const result = await intelligentModeSystem.analyzeAndSuggest(edgeCase);
        
        expect(result.recommendedMode).toBeDefined();
        expect(result.confidence).toBeGreaterThanOrEqual(0);
        expect(result.confidence).toBeLessThanOrEqual(1);
        expect(Array.isArray(result.suggestions)).toBe(true);
        expect(Array.isArray(result.actions)).toBe(true);
      }
    });
  });
});
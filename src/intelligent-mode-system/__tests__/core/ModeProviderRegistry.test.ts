/**
 * ModeProviderRegistry Unit Tests
 * 
 * Comprehensive test suite for the mode provider registry,
 * covering registration, retrieval, and lifecycle management.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ModeProviderRegistry } from '../../core/ModeProviderRegistry';
import { ModeType, ModeProvider } from '../../types';
import { ContextSnapshot } from '../../types';

// Mock dependencies
vi.mock('../../core/EventBus', () => ({
  eventBus: {
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
  },
}));

// Test utilities
const createMockContext = (): ContextSnapshot => ({
  fileContext: {
    path: '/test/file.ts',
    content: 'test content',
    language: 'typescript',
    hasErrors: false,
    openFiles: ['/test/file.ts'],
  },
  projectContext: {
    name: 'test-project',
    type: 'typescript',
    structure: {
      hasTests: true,
      hasPackageJson: true,
      directories: ['src', 'tests'],
      mainFiles: ['index.ts'],
    },
    dependencies: ['react', 'typescript'],
  },
  userContext: {
    recentActions: [],
    preferences: {},
    workingHours: { start: 9, end: 17 },
  },
  environmentContext: {
    os: 'darwin',
    editor: 'vscode',
    gitStatus: {
      branch: 'main',
      staged: [],
      unstaged: [],
      ahead: 0,
      behind: 0,
    },
  },
  timestamp: Date.now(),
});

const createMockModeProvider = (id: ModeType, score: number = 0.5): ModeProvider => ({
  id,
  displayName: `${id} Mode`,
  description: `Test ${id} mode`,
  icon: '🧪',
  color: '#000000',
  isApplicable: vi.fn(() => true),
  getScore: vi.fn(() => score),
  getSuggestions: vi.fn(async () => ['suggestion 1', 'suggestion 2']),
  getActions: vi.fn(() => []),
});

describe('ModeProviderRegistry', () => {
  let registry: ModeProviderRegistry;
  let mockContext: ContextSnapshot;

  beforeEach(() => {
    registry = new ModeProviderRegistry();
    mockContext = createMockContext();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Provider Registration', () => {
    it('should register a mode provider successfully', () => {
      const provider = createMockModeProvider('creative');
      
      registry.registerProvider(provider);
      
      expect(registry.getProvider('creative')).toBe(provider);
    });

    it('should throw error when registering duplicate provider', () => {
      const provider1 = createMockModeProvider('creative');
      const provider2 = createMockModeProvider('creative');
      
      registry.registerProvider(provider1);
      
      expect(() => registry.registerProvider(provider2)).toThrow(
        'Mode provider for creative already registered'
      );
    });

    it('should unregister a mode provider successfully', () => {
      const provider = createMockModeProvider('creative');
      
      registry.registerProvider(provider);
      registry.unregisterProvider('creative');
      
      expect(registry.getProvider('creative')).toBeNull();
    });

    it('should handle unregistering non-existent provider gracefully', () => {
      expect(() => registry.unregisterProvider('nonexistent' as ModeType)).not.toThrow();
    });
  });

  describe('Provider Retrieval', () => {
    beforeEach(() => {
      const providers = [
        createMockModeProvider('creative', 0.8),
        createMockModeProvider('analytical', 0.6),
        createMockModeProvider('coding', 0.9),
      ];
      
      providers.forEach(provider => registry.registerProvider(provider));
    });

    it('should get all registered providers', () => {
      const providers = registry.getAllProviders();
      
      expect(providers).toHaveLength(3);
      expect(providers.map(p => p.id)).toContain('creative');
      expect(providers.map(p => p.id)).toContain('analytical');
      expect(providers.map(p => p.id)).toContain('coding');
    });

    it('should get applicable providers for context', async () => {
      const applicableProviders = await registry.getApplicableProviders(mockContext);
      
      expect(applicableProviders).toHaveLength(3);
      applicableProviders.forEach(provider => {
        expect(provider.isApplicable).toHaveBeenCalledWith(mockContext);
      });
    });

    it('should filter out non-applicable providers', async () => {
      const nonApplicableProvider = createMockModeProvider('research');
      nonApplicableProvider.isApplicable = vi.fn(() => false);
      registry.registerProvider(nonApplicableProvider);
      
      const applicableProviders = await registry.getApplicableProviders(mockContext);
      
      expect(applicableProviders).toHaveLength(3);
      expect(applicableProviders.map(p => p.id)).not.toContain('research');
    });
  });

  describe('Best Provider Selection', () => {
    beforeEach(() => {
      const providers = [
        createMockModeProvider('creative', 0.3),
        createMockModeProvider('analytical', 0.7),
        createMockModeProvider('coding', 0.9),
        createMockModeProvider('research', 0.5),
      ];
      
      providers.forEach(provider => registry.registerProvider(provider));
    });

    it('should return the highest scoring provider', async () => {
      const bestProvider = await registry.getBestProvider(mockContext);
      
      expect(bestProvider?.id).toBe('coding');
      expect(bestProvider?.getScore).toHaveBeenCalledWith(mockContext);
    });

    it('should return null when no providers are applicable', async () => {
      // Make all providers non-applicable
      registry.getAllProviders().forEach(provider => {
        provider.isApplicable = vi.fn(() => false);
      });
      
      const bestProvider = await registry.getBestProvider(mockContext);
      
      expect(bestProvider).toBeNull();
    });

    it('should handle providers with equal scores', async () => {
      // Set equal scores
      registry.getAllProviders().forEach(provider => {
        provider.getScore = vi.fn(() => 0.5);
      });
      
      const bestProvider = await registry.getBestProvider(mockContext);
      
      expect(bestProvider).not.toBeNull();
      expect(['creative', 'analytical', 'coding', 'research']).toContain(bestProvider!.id);
    });
  });

  describe('Provider Scoring', () => {
    it('should get ranked providers in descending score order', async () => {
      const providers = [
        createMockModeProvider('creative', 0.3),
        createMockModeProvider('analytical', 0.9),
        createMockModeProvider('coding', 0.6),
      ];
      
      providers.forEach(provider => registry.registerProvider(provider));
      
      const rankedProviders = await registry.getRankedProviders(mockContext);
      
      expect(rankedProviders).toHaveLength(3);
      expect(rankedProviders[0].id).toBe('analytical');
      expect(rankedProviders[1].id).toBe('coding');
      expect(rankedProviders[2].id).toBe('creative');
    });

    it('should include scores in ranked results', async () => {
      const provider = createMockModeProvider('creative', 0.7);
      registry.registerProvider(provider);
      
      const rankedProviders = await registry.getRankedProviders(mockContext);
      
      expect(rankedProviders[0]).toEqual({
        provider,
        score: 0.7,
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle provider scoring errors gracefully', async () => {
      const errorProvider = createMockModeProvider('creative');
      errorProvider.getScore = vi.fn(() => {
        throw new Error('Scoring failed');
      });
      
      registry.registerProvider(errorProvider);
      
      const bestProvider = await registry.getBestProvider(mockContext);
      
      expect(bestProvider).toBeNull();
    });

    it('should handle provider applicability check errors gracefully', async () => {
      const errorProvider = createMockModeProvider('creative');
      errorProvider.isApplicable = vi.fn(() => {
        throw new Error('Applicability check failed');
      });
      
      registry.registerProvider(errorProvider);
      
      const applicableProviders = await registry.getApplicableProviders(mockContext);
      
      expect(applicableProviders).toHaveLength(0);
    });
  });

  describe('Event Emission', () => {
    it('should emit events when providers are registered', () => {
      const { eventBus } = require('../../core/EventBus');
      const provider = createMockModeProvider('creative');
      
      registry.registerProvider(provider);
      
      expect(eventBus.emit).toHaveBeenCalledWith({
        type: 'mode.provider_registered',
        payload: { providerId: 'creative', provider },
        source: 'ModeProviderRegistry',
      });
    });

    it('should emit events when providers are unregistered', () => {
      const { eventBus } = require('../../core/EventBus');
      const provider = createMockModeProvider('creative');
      
      registry.registerProvider(provider);
      registry.unregisterProvider('creative');
      
      expect(eventBus.emit).toHaveBeenCalledWith({
        type: 'mode.provider_unregistered',
        payload: { providerId: 'creative' },
        source: 'ModeProviderRegistry',
      });
    });
  });

  describe('Performance', () => {
    it('should handle large numbers of providers efficiently', async () => {
      // Register 100 providers
      for (let i = 0; i < 100; i++) {
        const provider = createMockModeProvider(`test-${i}` as ModeType, Math.random());
        registry.registerProvider(provider);
      }
      
      const startTime = performance.now();
      await registry.getBestProvider(mockContext);
      const endTime = performance.now();
      
      // Should complete within reasonable time (less than 100ms)
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('should cache provider results when appropriate', async () => {
      const provider = createMockModeProvider('creative', 0.5);
      registry.registerProvider(provider);
      
      // Call multiple times
      await registry.getBestProvider(mockContext);
      await registry.getBestProvider(mockContext);
      
      // Score should be called for each request (no caching by default)
      expect(provider.getScore).toHaveBeenCalledTimes(2);
    });
  });
});
/**
 * Plugin System Integration Tests
 * 
 * Comprehensive test suite for the plugin architecture,
 * covering loading, registration, lifecycle, and integration.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { pluginRegistry } from '../../core/PluginRegistry';
import { pluginManager } from '../../core/PluginManager';
import { pluginLoader } from '../../plugins/PluginLoader';
import { BasePlugin, BaseModePlugin, BaseAnalyzerPlugin } from '../../plugins/BasePlugin';
import { PluginManifest, PluginContext, ModeProvider, ContextAnalyzer } from '../../core/PluginRegistry';
import { ContextSnapshot } from '../../types';

// Mock dependencies
vi.mock('../../core/EventBus', () => ({
  eventBus: {
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
  },
}));

vi.mock('../../utils/performance', () => ({
  performanceMonitor: {
    startTimer: vi.fn(),
    endTimer: vi.fn(),
  },
}));

// Test plugin implementations
class TestModePlugin extends BaseModePlugin {
  protected async onInitialize(): Promise<void> {
    // Test initialization
  }
  
  protected async onDestroy(): Promise<void> {
    // Test cleanup
  }
  
  protected createModeProvider(): ModeProvider {
    return {
      id: 'test-mode' as any,
      displayName: 'Test Mode',
      description: 'A test mode for unit testing',
      icon: '🧪',
      color: '#FF0000',
      isApplicable: vi.fn(() => true),
      getScore: vi.fn(() => 0.8),
      getSuggestions: vi.fn(async () => ['test suggestion']),
      getActions: vi.fn(() => []),
    };
  }
}

class TestAnalyzerPlugin extends BaseAnalyzerPlugin {
  protected async onInitialize(): Promise<void> {
    // Test initialization
  }
  
  protected async onDestroy(): Promise<void> {
    // Test cleanup
  }
  
  protected createAnalyzer(): ContextAnalyzer {
    return {
      id: 'test-analyzer',
      name: 'Test Analyzer',
      priority: 5,
      analyze: vi.fn(async () => ({
        score: 0.7,
        confidence: 0.9,
        features: { test_feature: 1 },
        metadata: { test: true },
      })),
      getFeatures: vi.fn(() => ({ test_feature: 1 })),
    };
  }
}

// Test utilities
const createMockManifest = (overrides: Partial<PluginManifest> = {}): PluginManifest => ({
  id: 'test-plugin',
  name: 'Test Plugin',
  version: '1.0.0',
  description: 'A test plugin',
  author: 'Test Author',
  type: 'mode',
  dependencies: [],
  optionalDependencies: [],
  permissions: [],
  apiVersion: '1.0.0',
  config: {},
  ...overrides,
});

const createMockContext = (): PluginContext => ({
  eventBus: require('../../core/EventBus').eventBus,
  requestPermission: vi.fn(async () => true),
  registerModeProvider: vi.fn(),
  registerContextAnalyzer: vi.fn(),
  registerUIComponent: vi.fn(),
});

const createMockContextSnapshot = (): ContextSnapshot => ({
  fileContext: {
    path: '/test/file.ts',
    content: 'test content',
    language: 'typescript',
    hasErrors: false,
    openFiles: ['/test/file.ts'],
  },
  projectContext: {
    name: 'test-project',
    type: 'typescript',
    structure: {
      hasTests: true,
      hasPackageJson: true,
      directories: ['src'],
      mainFiles: ['index.ts'],
    },
    dependencies: ['react'],
  },
  userContext: {
    recentActions: [],
    preferences: {},
    workingHours: { start: 9, end: 17 },
  },
  environmentContext: {
    os: 'darwin',
    editor: 'vscode',
    gitStatus: {
      branch: 'main',
      staged: [],
      unstaged: [],
      ahead: 0,
      behind: 0,
    },
  },
  timestamp: Date.now(),
});

describe('Plugin System Integration', () => {
  let mockContext: PluginContext;
  let mockContextSnapshot: ContextSnapshot;

  beforeEach(() => {
    mockContext = createMockContext();
    mockContextSnapshot = createMockContextSnapshot();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Plugin Registry', () => {
    it('should register plugins successfully', async () => {
      const manifest = createMockManifest();
      const plugin = new TestModePlugin(manifest);
      
      await pluginRegistry.registerPlugin(plugin);
      
      expect(pluginRegistry.getPlugin('test-plugin')).toBe(plugin);
    });

    it('should handle plugin lifecycle correctly', async () => {
      const manifest = createMockManifest();
      const plugin = new TestModePlugin(manifest);
      
      await pluginRegistry.registerPlugin(plugin);
      await pluginRegistry.activatePlugin('test-plugin');
      
      expect(pluginRegistry.getActivePlugins()).toContain(plugin);
      
      await pluginRegistry.deactivatePlugin('test-plugin');
      
      expect(pluginRegistry.getActivePlugins()).not.toContain(plugin);
    });

    it('should unregister plugins and clean up', async () => {
      const manifest = createMockManifest();
      const plugin = new TestModePlugin(manifest);
      
      await pluginRegistry.registerPlugin(plugin);
      await pluginRegistry.activatePlugin('test-plugin');
      await pluginRegistry.unregisterPlugin('test-plugin');
      
      expect(pluginRegistry.getPlugin('test-plugin')).toBeNull();
      expect(pluginRegistry.getActivePlugins()).not.toContain(plugin);
    });

    it('should handle dependency resolution', async () => {
      const dependencyManifest = createMockManifest({
        id: 'dependency-plugin',
        name: 'Dependency Plugin',
      });
      const dependentManifest = createMockManifest({
        id: 'dependent-plugin',
        name: 'Dependent Plugin',
        dependencies: ['dependency-plugin'],
      });
      
      const dependencyPlugin = new TestModePlugin(dependencyManifest);
      const dependentPlugin = new TestModePlugin(dependentManifest);
      
      await pluginRegistry.registerPlugin(dependencyPlugin);
      await pluginRegistry.registerPlugin(dependentPlugin);
      
      const resolved = await pluginRegistry.resolveDependencies('dependent-plugin');
      
      expect(resolved).toContain('dependency-plugin');
    });

    it('should detect circular dependencies', async () => {
      const plugin1Manifest = createMockManifest({
        id: 'plugin-1',
        dependencies: ['plugin-2'],
      });
      const plugin2Manifest = createMockManifest({
        id: 'plugin-2',
        dependencies: ['plugin-1'],
      });
      
      const plugin1 = new TestModePlugin(plugin1Manifest);
      const plugin2 = new TestModePlugin(plugin2Manifest);
      
      await pluginRegistry.registerPlugin(plugin1);
      await pluginRegistry.registerPlugin(plugin2);
      
      await expect(
        pluginRegistry.resolveDependencies('plugin-1')
      ).rejects.toThrow('Circular dependency detected');
    });
  });

  describe('Plugin Manager', () => {
    it('should initialize successfully', async () => {
      await expect(pluginManager.initialize()).resolves.not.toThrow();
    });

    it('should provide plugin statistics', () => {
      const stats = pluginManager.getStats();
      
      expect(stats).toHaveProperty('totalPlugins');
      expect(stats).toHaveProperty('activePlugins');
      expect(stats).toHaveProperty('errorPlugins');
      expect(stats).toHaveProperty('availableUpdates');
      expect(stats).toHaveProperty('memoryUsage');
      expect(stats).toHaveProperty('avgLoadTime');
    });

    it('should manage plugin states correctly', async () => {
      const manifest = createMockManifest();
      const plugin = new TestModePlugin(manifest);
      
      await pluginRegistry.registerPlugin(plugin);
      
      expect(pluginManager.isPluginInstalled('test-plugin')).toBe(true);
      expect(pluginManager.isPluginActive('test-plugin')).toBe(false);
      
      await pluginManager.activatePlugin('test-plugin');
      
      expect(pluginManager.isPluginActive('test-plugin')).toBe(true);
    });

    it('should handle batch operations', async () => {
      const manifests = [
        createMockManifest({ id: 'plugin-1' }),
        createMockManifest({ id: 'plugin-2' }),
        createMockManifest({ id: 'plugin-3' }),
      ];
      
      const plugins = manifests.map(manifest => new TestModePlugin(manifest));
      
      for (const plugin of plugins) {
        await pluginRegistry.registerPlugin(plugin);
      }
      
      const result = await pluginManager.activatePlugins(['plugin-1', 'plugin-2', 'plugin-3']);
      
      expect(result.success).toHaveLength(3);
      expect(result.failed).toHaveLength(0);
    });

    it('should provide context-aware recommendations', async () => {
      const manifest = createMockManifest({
        id: 'research-plugin',
        type: 'mode',
      });
      const plugin = new TestModePlugin(manifest);
      
      await pluginRegistry.registerPlugin(plugin);
      
      const recommendations = await pluginManager.getRecommendedPlugins(mockContextSnapshot);
      
      expect(Array.isArray(recommendations)).toBe(true);
    });
  });

  describe('Plugin Loader', () => {
    it('should validate plugin sources', () => {
      expect(() => {
        pluginLoader['validateSource']({ type: 'file', location: '/path/to/plugin' });
      }).not.toThrow();
    });

    it('should handle plugin discovery from different sources', async () => {
      const fileSource = { type: 'file' as const, location: '/test/plugins' };
      const urlSource = { type: 'url' as const, location: 'https://example.com/plugin.js' };
      const marketplaceSource = { type: 'marketplace' as const, location: 'test-plugin' };
      
      // These would normally make actual file/network calls
      // For testing, we verify the methods exist and handle different source types
      expect(() => pluginLoader['discoverPlugins'](fileSource)).not.toThrow();
      expect(() => pluginLoader['discoverPlugins'](urlSource)).not.toThrow();
      expect(() => pluginLoader['discoverPlugins'](marketplaceSource)).not.toThrow();
    });

    it('should validate plugin packages', async () => {
      const validManifest = createMockManifest();
      const validPackage = {
        manifest: validManifest,
        source: { type: 'file' as const, location: '/test' },
        code: 'export default class TestPlugin {}',
      };
      
      await expect(
        pluginLoader['validatePackage'](validPackage)
      ).resolves.not.toThrow();
    });

    it('should reject invalid manifests', async () => {
      const invalidManifest = { ...createMockManifest(), version: 'invalid-version' };
      const invalidPackage = {
        manifest: invalidManifest,
        source: { type: 'file' as const, location: '/test' },
        code: 'export default class TestPlugin {}',
      };
      
      await expect(
        pluginLoader['validatePackage'](invalidPackage)
      ).rejects.toThrow('Invalid version format');
    });

    it('should handle plugin size limits', async () => {
      const manifest = createMockManifest();
      const largePackage = {
        manifest,
        source: { type: 'file' as const, location: '/test' },
        code: 'x'.repeat(20 * 1024 * 1024), // 20MB - exceeds default 10MB limit
      };
      
      await expect(
        pluginLoader['validatePackage'](largePackage)
      ).rejects.toThrow('Plugin size exceeds limit');
    });
  });

  describe('Plugin Base Classes', () => {
    it('should handle mode plugin lifecycle', async () => {
      const manifest = createMockManifest();
      const plugin = new TestModePlugin(manifest);
      
      await plugin.initialize(mockContext);
      expect(plugin['isInitialized']).toBe(true);
      
      await plugin.activate();
      expect(plugin['isActive']).toBe(true);
      
      await plugin.deactivate();
      expect(plugin['isActive']).toBe(false);
      
      await plugin.destroy();
      expect(plugin['isInitialized']).toBe(false);
    });

    it('should handle analyzer plugin lifecycle', async () => {
      const manifest = createMockManifest({ type: 'analyzer' });
      const plugin = new TestAnalyzerPlugin(manifest);
      
      await plugin.initialize(mockContext);
      await plugin.activate();
      
      const capabilities = plugin.getCapabilities();
      expect(capabilities).toHaveLength(1);
      expect(capabilities[0].type).toBe('analyzer');
      
      await plugin.deactivate();
      await plugin.destroy();
    });

    it('should handle permission requests', async () => {
      const manifest = createMockManifest({
        permissions: [
          { type: 'context.read', description: 'Read context' },
          { type: 'file.write', description: 'Write files' },
        ],
      });
      const plugin = new TestModePlugin(manifest);
      
      await plugin.initialize(mockContext);
      
      expect(mockContext.requestPermission).toHaveBeenCalledTimes(2);
    });

    it('should handle permission denial', async () => {
      const manifest = createMockManifest({
        permissions: [
          { type: 'restricted.access', description: 'Restricted access' },
        ],
      });
      const plugin = new TestModePlugin(manifest);
      
      mockContext.requestPermission = vi.fn(async () => false);
      
      await expect(plugin.initialize(mockContext)).rejects.toThrow(
        'Permission restricted.access denied'
      );
    });

    it('should handle configuration updates', () => {
      const manifest = createMockManifest({ config: { option1: 'value1' } });
      const plugin = new TestModePlugin(manifest);
      
      expect(plugin.getConfig()).toEqual({ option1: 'value1' });
      
      plugin.updateConfig({ option2: 'value2' });
      
      expect(plugin.getConfig()).toEqual({ option1: 'value1', option2: 'value2' });
    });
  });

  describe('Plugin Integration', () => {
    it('should integrate mode providers with registry', async () => {
      const manifest = createMockManifest();
      const plugin = new TestModePlugin(manifest);
      
      await pluginRegistry.registerPlugin(plugin);
      await pluginRegistry.activatePlugin('test-plugin');
      
      const modeProvider = pluginRegistry.getModeProvider('test-mode' as any);
      expect(modeProvider).toBeDefined();
      expect(modeProvider?.displayName).toBe('Test Mode');
    });

    it('should integrate analyzers with registry', async () => {
      const manifest = createMockManifest({ type: 'analyzer' });
      const plugin = new TestAnalyzerPlugin(manifest);
      
      await pluginRegistry.registerPlugin(plugin);
      await pluginRegistry.activatePlugin('test-plugin');
      
      const analyzer = pluginRegistry.getContextAnalyzer('test-analyzer');
      expect(analyzer).toBeDefined();
      expect(analyzer?.name).toBe('Test Analyzer');
    });

    it('should handle plugin hot reloading', async () => {
      const manifest = createMockManifest();
      const originalPlugin = new TestModePlugin(manifest);
      const updatedPlugin = new TestModePlugin({ ...manifest, version: '1.1.0' });
      
      await pluginRegistry.registerPlugin(originalPlugin);
      await pluginRegistry.activatePlugin('test-plugin');
      
      await pluginRegistry.reloadPlugin('test-plugin', updatedPlugin);
      
      const reloadedPlugin = pluginRegistry.getPlugin('test-plugin');
      expect(reloadedPlugin?.manifest.version).toBe('1.1.0');
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle plugin initialization failures gracefully', async () => {
      class FailingPlugin extends BasePlugin {
        protected async onInitialize(): Promise<void> {
          throw new Error('Initialization failed');
        }
        
        protected async onActivate(): Promise<void> {}
        protected async onDeactivate(): Promise<void> {}
        protected async onDestroy(): Promise<void> {}
      }
      
      const manifest = createMockManifest();
      const plugin = new FailingPlugin(manifest);
      
      await expect(plugin.initialize(mockContext)).rejects.toThrow('Initialization failed');
    });

    it('should handle missing dependencies gracefully', async () => {
      const manifest = createMockManifest({
        dependencies: ['non-existent-plugin'],
      });
      const plugin = new TestModePlugin(manifest);
      
      await pluginRegistry.registerPlugin(plugin);
      
      await expect(
        pluginRegistry.resolveDependencies('test-plugin')
      ).rejects.toThrow('Missing dependency: non-existent-plugin');
    });

    it('should isolate plugin failures', async () => {
      const workingPlugin = new TestModePlugin(createMockManifest({ id: 'working-plugin' }));
      
      class FailingPlugin extends TestModePlugin {
        protected createModeProvider(): ModeProvider {
          const provider = super.createModeProvider();
          provider.getScore = vi.fn(() => {
            throw new Error('Scoring failed');
          });
          return provider;
        }
      }
      
      const failingPlugin = new FailingPlugin(createMockManifest({ id: 'failing-plugin' }));
      
      await pluginRegistry.registerPlugin(workingPlugin);
      await pluginRegistry.registerPlugin(failingPlugin);
      await pluginRegistry.activatePlugin('working-plugin');
      await pluginRegistry.activatePlugin('failing-plugin');
      
      // Working plugin should still function despite failing plugin
      const workingProvider = pluginRegistry.getModeProvider('test-mode' as any);
      expect(workingProvider).toBeDefined();
      expect(() => workingProvider?.getScore(mockContextSnapshot)).not.toThrow();
    });
  });

  describe('Performance', () => {
    it('should handle large numbers of plugins efficiently', async () => {
      const startTime = performance.now();
      
      // Register 50 plugins
      for (let i = 0; i < 50; i++) {
        const manifest = createMockManifest({ id: `plugin-${i}` });
        const plugin = new TestModePlugin(manifest);
        await pluginRegistry.registerPlugin(plugin);
      }
      
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
      expect(pluginRegistry.getAllPlugins()).toHaveLength(50);
    });

    it('should activate plugins concurrently', async () => {
      const pluginIds = [];
      
      for (let i = 0; i < 10; i++) {
        const manifest = createMockManifest({ id: `plugin-${i}` });
        const plugin = new TestModePlugin(manifest);
        await pluginRegistry.registerPlugin(plugin);
        pluginIds.push(`plugin-${i}`);
      }
      
      const startTime = performance.now();
      const result = await pluginManager.activatePlugins(pluginIds);
      const endTime = performance.now();
      
      expect(result.success).toHaveLength(10);
      expect(endTime - startTime).toBeLessThan(500); // Should complete within 500ms
    });
  });
});
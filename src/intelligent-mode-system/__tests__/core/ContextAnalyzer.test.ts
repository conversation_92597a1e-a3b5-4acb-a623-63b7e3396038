/**
 * ContextAnalyzer Integration Tests
 * 
 * Comprehensive test suite for context analysis functionality,
 * covering feature extraction, scoring, and machine learning.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ContextAnalyzer } from '../../core/ContextAnalyzer';
import { ContextSnapshot, AnalysisResult } from '../../types';

// Mock dependencies
vi.mock('../../utils/OptimizedAlgorithms', () => ({
  FastFeatureExtractor: {
    extractOptimized: vi.fn(() => new Float32Array([0.1, 0.2, 0.3, 0.4, 0.5])),
  },
  FastSimilarityCalculator: {
    calculateSimilarity: vi.fn(() => 0.85),
  },
  FastScoringEngine: {
    calculateScore: vi.fn(() => 0.75),
  },
}));

vi.mock('../../core/EventBus', () => ({
  eventBus: {
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
  },
}));

// Test utilities
const createMockContext = (overrides: Partial<ContextSnapshot> = {}): ContextSnapshot => ({
  fileContext: {
    path: '/test/components/Button.tsx',
    content: `
      import React from 'react';
      
      interface ButtonProps {
        children: React.ReactNode;
        onClick: () => void;
        variant?: 'primary' | 'secondary';
      }
      
      export const Button: React.FC<ButtonProps> = ({ children, onClick, variant = 'primary' }) => {
        return (
          <button 
            className={\`btn btn-\${variant}\`}
            onClick={onClick}
          >
            {children}
          </button>
        );
      };
    `,
    language: 'typescript',
    hasErrors: false,
    openFiles: ['/test/components/Button.tsx'],
  },
  projectContext: {
    name: 'react-ui-library',
    type: 'typescript',
    structure: {
      hasTests: true,
      hasPackageJson: true,
      directories: ['src', 'components', 'tests', 'docs'],
      mainFiles: ['index.ts', 'App.tsx'],
    },
    dependencies: ['react', '@types/react', 'typescript', 'vite'],
  },
  userContext: {
    recentActions: [
      { type: 'file.open', timestamp: Date.now() - 1000 },
      { type: 'edit', timestamp: Date.now() - 500 },
    ],
    preferences: {
      theme: 'dark',
      fontSize: 14,
      autoSave: true,
    },
    workingHours: { start: 9, end: 17 },
  },
  environmentContext: {
    os: 'darwin',
    editor: 'vscode',
    gitStatus: {
      branch: 'feature/new-component',
      staged: ['src/components/Button.tsx'],
      unstaged: [],
      ahead: 2,
      behind: 0,
    },
  },
  timestamp: Date.now(),
  ...overrides,
});

describe('ContextAnalyzer', () => {
  let analyzer: ContextAnalyzer;
  let mockContext: ContextSnapshot;

  beforeEach(() => {
    analyzer = new ContextAnalyzer();
    mockContext = createMockContext();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      await expect(analyzer.initialize()).resolves.not.toThrow();
    });

    it('should handle multiple initialization calls gracefully', async () => {
      await analyzer.initialize();
      await expect(analyzer.initialize()).resolves.not.toThrow();
    });
  });

  describe('Context Analysis', () => {
    beforeEach(async () => {
      await analyzer.initialize();
    });

    it('should analyze context successfully', async () => {
      const result = await analyzer.analyzeContext(mockContext);
      
      expect(result).toBeDefined();
      expect(result.score).toBeGreaterThanOrEqual(0);
      expect(result.score).toBeLessThanOrEqual(1);
      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeLessThanOrEqual(1);
      expect(result.features).toBeDefined();
      expect(result.metadata).toBeDefined();
    });

    it('should extract file type features correctly', async () => {
      const result = await analyzer.analyzeContext(mockContext);
      
      expect(result.features).toHaveProperty('file_type');
      expect(result.features.file_type).toBe('typescript');
    });

    it('should detect React component context', async () => {
      const result = await analyzer.analyzeContext(mockContext);
      
      expect(result.features).toHaveProperty('is_react_component');
      expect(result.features.is_react_component).toBe(1);
    });

    it('should analyze project structure correctly', async () => {
      const result = await analyzer.analyzeContext(mockContext);
      
      expect(result.features).toHaveProperty('has_tests');
      expect(result.features.has_tests).toBe(1);
      expect(result.features).toHaveProperty('project_complexity');
      expect(result.features.project_complexity).toBeGreaterThan(0);
    });

    it('should handle different file types', async () => {
      const pythonContext = createMockContext({
        fileContext: {
          path: '/test/main.py',
          content: 'def hello_world():\n    print("Hello, World!")',
          language: 'python',
          hasErrors: false,
          openFiles: ['/test/main.py'],
        },
      });
      
      const result = await analyzer.analyzeContext(pythonContext);
      
      expect(result.features.file_type).toBe('python');
      expect(result.features.is_react_component).toBe(0);
    });
  });

  describe('Feature Extraction', () => {
    beforeEach(async () => {
      await analyzer.initialize();
    });

    it('should extract comprehensive feature set', async () => {
      const features = await analyzer.extractFeatures(mockContext);
      
      // File features
      expect(features).toHaveProperty('file_size');
      expect(features).toHaveProperty('line_count');
      expect(features).toHaveProperty('file_type');
      
      // Code features
      expect(features).toHaveProperty('function_count');
      expect(features).toHaveProperty('import_count');
      expect(features).toHaveProperty('complexity_score');
      
      // Project features
      expect(features).toHaveProperty('project_type');
      expect(features).toHaveProperty('has_tests');
      expect(features).toHaveProperty('dependency_count');
      
      // User features
      expect(features).toHaveProperty('recent_actions_count');
      expect(features).toHaveProperty('time_of_day');
      
      // Environment features
      expect(features).toHaveProperty('git_status');
      expect(features).toHaveProperty('editor_type');
    });

    it('should normalize feature values appropriately', async () => {
      const features = await analyzer.extractFeatures(mockContext);
      
      // Most features should be normalized between 0 and 1
      Object.entries(features).forEach(([key, value]) => {
        if (typeof value === 'number' && !key.includes('count') && !key.includes('size')) {
          expect(value).toBeGreaterThanOrEqual(0);
          expect(value).toBeLessThanOrEqual(1);
        }
      });
    });

    it('should handle missing or invalid data gracefully', async () => {
      const incompleteContext = createMockContext({
        fileContext: {
          path: '/test/empty.ts',
          content: '',
          language: 'typescript',
          hasErrors: false,
          openFiles: [],
        },
        projectContext: {
          name: '',
          type: 'unknown',
          structure: {
            hasTests: false,
            hasPackageJson: false,
            directories: [],
            mainFiles: [],
          },
          dependencies: [],
        },
      });
      
      const features = await analyzer.extractFeatures(incompleteContext);
      
      expect(features).toBeDefined();
      expect(Object.keys(features).length).toBeGreaterThan(0);
    });
  });

  describe('Pattern Recognition', () => {
    beforeEach(async () => {
      await analyzer.initialize();
    });

    it('should identify React component patterns', async () => {
      const patterns = await analyzer.identifyPatterns(mockContext);
      
      expect(patterns).toContain('react_component');
      expect(patterns).toContain('typescript_interface');
      expect(patterns).toContain('functional_component');
    });

    it('should identify TypeScript patterns', async () => {
      const patterns = await analyzer.identifyPatterns(mockContext);
      
      expect(patterns).toContain('typescript');
      expect(patterns).toContain('typescript_interface');
      expect(patterns).toContain('typed_props');
    });

    it('should identify UI/Component patterns', async () => {
      const patterns = await analyzer.identifyPatterns(mockContext);
      
      expect(patterns).toContain('ui_component');
      expect(patterns).toContain('button_component');
      expect(patterns).toContain('prop_variants');
    });

    it('should handle different code patterns', async () => {
      const apiContext = createMockContext({
        fileContext: {
          path: '/test/api/users.ts',
          content: `
            import express from 'express';
            
            export const usersRouter = express.Router();
            
            usersRouter.get('/', async (req, res) => {
              try {
                const users = await db.users.findMany();
                res.json(users);
              } catch (error) {
                res.status(500).json({ error: 'Internal server error' });
              }
            });
          `,
          language: 'typescript',
          hasErrors: false,
          openFiles: ['/test/api/users.ts'],
        },
      });
      
      const patterns = await analyzer.identifyPatterns(apiContext);
      
      expect(patterns).toContain('api_endpoint');
      expect(patterns).toContain('express_router');
      expect(patterns).toContain('async_handler');
      expect(patterns).toContain('error_handling');
    });
  });

  describe('Scoring and Confidence', () => {
    beforeEach(async () => {
      await analyzer.initialize();
    });

    it('should calculate appropriate confidence scores', async () => {
      const result = await analyzer.analyzeContext(mockContext);
      
      expect(result.confidence).toBeGreaterThan(0.5); // Should be confident about clear React component
      expect(result.confidence).toBeLessThanOrEqual(1);
    });

    it('should have lower confidence for ambiguous contexts', async () => {
      const ambiguousContext = createMockContext({
        fileContext: {
          path: '/test/unknown.txt',
          content: 'some random text content',
          language: 'plaintext',
          hasErrors: false,
          openFiles: ['/test/unknown.txt'],
        },
      });
      
      const result = await analyzer.analyzeContext(ambiguousContext);
      
      expect(result.confidence).toBeLessThan(0.5);
    });

    it('should provide consistent scoring for similar contexts', async () => {
      const context1 = createMockContext();
      const context2 = createMockContext();
      
      const result1 = await analyzer.analyzeContext(context1);
      const result2 = await analyzer.analyzeContext(context2);
      
      expect(Math.abs(result1.score - result2.score)).toBeLessThan(0.1);
      expect(Math.abs(result1.confidence - result2.confidence)).toBeLessThan(0.1);
    });
  });

  describe('Performance', () => {
    beforeEach(async () => {
      await analyzer.initialize();
    });

    it('should analyze context within reasonable time', async () => {
      const startTime = performance.now();
      await analyzer.analyzeContext(mockContext);
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Should complete within 100ms
    });

    it('should handle multiple concurrent analyses', async () => {
      const contexts = Array.from({ length: 10 }, () => createMockContext());
      
      const startTime = performance.now();
      const results = await Promise.all(
        contexts.map(context => analyzer.analyzeContext(context))
      );
      const endTime = performance.now();
      
      expect(results).toHaveLength(10);
      expect(endTime - startTime).toBeLessThan(500); // Should handle 10 analyses within 500ms
    });

    it('should use optimized algorithms for feature extraction', async () => {
      const { FastFeatureExtractor } = require('../../utils/OptimizedAlgorithms');
      
      await analyzer.analyzeContext(mockContext);
      
      expect(FastFeatureExtractor.extractOptimized).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      await analyzer.initialize();
    });

    it('should handle malformed context gracefully', async () => {
      const malformedContext = {} as ContextSnapshot;
      
      const result = await analyzer.analyzeContext(malformedContext);
      
      expect(result).toBeDefined();
      expect(result.score).toBe(0);
      expect(result.confidence).toBe(0);
      expect(result.metadata).toHaveProperty('error');
    });

    it('should handle feature extraction errors', async () => {
      const { FastFeatureExtractor } = require('../../utils/OptimizedAlgorithms');
      FastFeatureExtractor.extractOptimized.mockImplementationOnce(() => {
        throw new Error('Feature extraction failed');
      });
      
      const result = await analyzer.analyzeContext(mockContext);
      
      expect(result).toBeDefined();
      expect(result.score).toBe(0);
      expect(result.confidence).toBe(0);
    });

    it('should handle large file content gracefully', async () => {
      const largeContent = 'x'.repeat(1000000); // 1MB of content
      const largeContext = createMockContext({
        fileContext: {
          path: '/test/large.ts',
          content: largeContent,
          language: 'typescript',
          hasErrors: false,
          openFiles: ['/test/large.ts'],
        },
      });
      
      const result = await analyzer.analyzeContext(largeContext);
      
      expect(result).toBeDefined();
      expect(result.score).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Event Emission', () => {
    beforeEach(async () => {
      await analyzer.initialize();
    });

    it('should emit analysis events', async () => {
      const { eventBus } = require('../../core/EventBus');
      
      await analyzer.analyzeContext(mockContext);
      
      expect(eventBus.emit).toHaveBeenCalledWith(expect.objectContaining({
        type: 'context.analyzed',
        source: 'ContextAnalyzer',
      }));
    });

    it('should emit pattern detection events', async () => {
      const { eventBus } = require('../../core/EventBus');
      
      await analyzer.identifyPatterns(mockContext);
      
      expect(eventBus.emit).toHaveBeenCalledWith(expect.objectContaining({
        type: 'context.patterns_identified',
        source: 'ContextAnalyzer',
      }));
    });
  });
});
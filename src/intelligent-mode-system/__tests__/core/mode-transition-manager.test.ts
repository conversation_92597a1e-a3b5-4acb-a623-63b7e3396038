import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ModeTransitionManager } from '../../core/ModeTransitionManager';
import { ContextSnapshot, ModeTransitionRule } from '../../types';
import { modeRegistry } from '../../core/ModeRegistry';

describe('ModeTransitionManager', () => {
  let manager: ModeTransitionManager;
  let mockContext: ContextSnapshot;

  beforeEach(() => {
    manager = new ModeTransitionManager();
    mockContext = {
      fileContext: {
        path: 'test.ts',
        name: 'test.ts',
        extension: '.ts',
        size: 100,
        lastModified: Date.now(),
        hasErrors: false,
        isTest: false,
        isConfig: false,
      },
      projectContext: {
        name: 'test-project',
        type: 'web-application',
        path: '/test',
        dependencies: {},
        hasTypeScript: true,
        hasTests: true,
      },
      environmentContext: {
        gitStatus: {
          branch: 'main',
          isClean: true,
        },
      },
      userContext: {
        recentActions: [],
        preferences: {},
        activityPatterns: {},
      },
      timestamp: Date.now(),
    };
  });

  describe('Default Transition Rules', () => {
    it('should have default transition rules loaded', () => {
      const rules = manager.getTransitionRules();
      expect(rules.length).toBeGreaterThan(0);
      
      // Check for specific default rules
      const architectToDebugRule = rules.find(r => 
        r.from === 'architect' && r.to === 'debug'
      );
      expect(architectToDebugRule).toBeDefined();
    });

    it('should transition from architect to debug when errors detected', async () => {
      // Set active mode to architect
      await modeRegistry.setActiveMode('architect', mockContext);
      
      // Update context to have errors
      mockContext.fileContext.hasErrors = true;
      
      // Check transition
      const shouldTransition = await manager.shouldTransition('architect', mockContext);
      expect(shouldTransition).toBeTruthy();
      expect(shouldTransition?.to).toBe('debug');
      expect(shouldTransition?.confidence).toBeGreaterThan(0.7);
    });

    it('should transition from review to deploy on release branch', async () => {
      await modeRegistry.setActiveMode('review', mockContext);
      
      // Update to release branch
      mockContext.environmentContext.gitStatus = {
        branch: 'release/v1.0.0',
        isClean: true,
      };
      
      const shouldTransition = await manager.shouldTransition('review', mockContext);
      expect(shouldTransition).toBeTruthy();
      expect(shouldTransition?.to).toBe('deploy');
    });
  });

  describe('Custom Transition Rules', () => {
    it('should add custom transition rule', () => {
      const customRule: ModeTransitionRule = {
        from: 'experiment',
        to: 'review',
        condition: (context) => context.fileContext.name.includes('prototype'),
        priority: 100,
        description: 'Transition to review when prototype is ready',
      };

      manager.addTransitionRule(customRule);
      const rules = manager.getTransitionRules();
      
      expect(rules).toContainEqual(customRule);
    });

    it('should remove transition rule', () => {
      const customRule: ModeTransitionRule = {
        from: 'learn',
        to: 'experiment',
        condition: () => true,
        priority: 50,
      };

      manager.addTransitionRule(customRule);
      const rulesWithCustom = manager.getTransitionRules();
      expect(rulesWithCustom).toContainEqual(customRule);

      manager.removeTransitionRule(customRule);
      const rulesWithoutCustom = manager.getTransitionRules();
      expect(rulesWithoutCustom).not.toContainEqual(customRule);
    });
  });

  describe('Transition Execution', () => {
    it('should execute transition with callbacks', async () => {
      const onTransition = vi.fn();
      manager.onTransition(onTransition);

      await modeRegistry.setActiveMode('architect', mockContext);
      mockContext.fileContext.hasErrors = true;

      await manager.executeTransition('architect', 'debug', mockContext);

      expect(onTransition).toHaveBeenCalledWith({
        from: 'architect',
        to: 'debug',
        context: mockContext,
        reason: expect.any(String),
      });
    });

    it('should respect priority when multiple rules match', async () => {
      // Add high priority rule
      manager.addTransitionRule({
        from: 'architect',
        to: 'learn',
        condition: () => true,
        priority: 100,
      });

      // Add low priority rule
      manager.addTransitionRule({
        from: 'architect',
        to: 'experiment',
        condition: () => true,
        priority: 10,
      });

      const shouldTransition = await manager.shouldTransition('architect', mockContext);
      expect(shouldTransition?.to).toBe('learn'); // Higher priority wins
    });
  });

  describe('Transition Conditions', () => {
    it('should evaluate complex conditions correctly', async () => {
      const complexRule: ModeTransitionRule = {
        from: 'debug',
        to: 'architect',
        condition: (context) => {
          const hasNoErrors = !context.fileContext.hasErrors;
          const recentDebugActions = context.userContext.recentActions
            .filter(a => a.type === 'debug').length;
          return hasNoErrors && recentDebugActions > 5;
        },
        priority: 80,
        description: 'Return to architect after debugging session',
      };

      manager.addTransitionRule(complexRule);

      // Test when condition is not met
      mockContext.fileContext.hasErrors = true;
      let shouldTransition = await manager.shouldTransition('debug', mockContext);
      expect(shouldTransition).toBeFalsy();

      // Test when condition is met
      mockContext.fileContext.hasErrors = false;
      mockContext.userContext.recentActions = Array(6).fill({
        type: 'debug',
        target: 'breakpoint',
        timestamp: Date.now(),
      });

      shouldTransition = await manager.shouldTransition('debug', mockContext);
      expect(shouldTransition).toBeTruthy();
      expect(shouldTransition?.to).toBe('architect');
    });
  });

  describe('Transition History', () => {
    it('should track transition history', async () => {
      const onTransition = vi.fn();
      manager.onTransition(onTransition);

      // Execute multiple transitions
      await manager.executeTransition('architect', 'debug', mockContext);
      await manager.executeTransition('debug', 'review', mockContext);
      await manager.executeTransition('review', 'deploy', mockContext);

      expect(onTransition).toHaveBeenCalledTimes(3);
      
      // Verify transition sequence
      const calls = onTransition.mock.calls;
      expect(calls[0][0].to).toBe('debug');
      expect(calls[1][0].to).toBe('review');
      expect(calls[2][0].to).toBe('deploy');
    });
  });

  describe('Error Handling', () => {
    it('should handle errors in transition conditions gracefully', async () => {
      const faultyRule: ModeTransitionRule = {
        from: 'architect',
        to: 'debug',
        condition: () => {
          throw new Error('Condition evaluation failed');
        },
        priority: 100,
      };

      manager.addTransitionRule(faultyRule);

      // Should not throw, but return null
      const shouldTransition = await manager.shouldTransition('architect', mockContext);
      expect(shouldTransition).toBeNull();
    });

    it('should handle invalid mode transitions', async () => {
      const result = await manager.executeTransition('invalid-mode', 'debug', mockContext);
      expect(result).toBe(false);
    });
  });
});
/**
 * Test Utilities
 * 
 * Common utilities and helpers for testing the intelligent mode system.
 */

import { vi } from 'vitest';
import { ContextSnapshot, ModeType, ModeProvider, AnalysisResult } from '../../types';
import { PluginManifest, PluginContext } from '../../core/PluginRegistry';

/**
 * Mock Data Factories
 */
export class MockDataFactory {
  static createContextSnapshot(overrides: Partial<ContextSnapshot> = {}): ContextSnapshot {
    return {
      fileContext: {
        path: '/test/file.ts',
        content: `
          import React from 'react';
          
          interface Props {
            title: string;
            onClick: () => void;
          }
          
          export const Component: React.FC<Props> = ({ title, onClick }) => {
            return <button onClick={onClick}>{title}</button>;
          };
        `,
        language: 'typescript',
        hasErrors: false,
        openFiles: ['/test/file.ts'],
      },
      projectContext: {
        name: 'test-project',
        type: 'typescript',
        structure: {
          hasTests: true,
          hasPackageJson: true,
          directories: ['src', 'components', 'tests'],
          mainFiles: ['index.ts', 'App.tsx'],
        },
        dependencies: ['react', '@types/react', 'typescript'],
      },
      userContext: {
        recentActions: [
          { type: 'file.open', timestamp: Date.now() - 1000 },
          { type: 'edit', timestamp: Date.now() - 500 },
        ],
        preferences: {
          theme: 'dark',
          fontSize: 14,
        },
        workingHours: { start: 9, end: 17 },
      },
      environmentContext: {
        os: 'darwin',
        editor: 'vscode',
        gitStatus: {
          branch: 'main',
          staged: [],
          unstaged: [],
          ahead: 0,
          behind: 0,
        },
      },
      timestamp: Date.now(),
      ...overrides,
    };
  }

  static createModeProvider(id: ModeType, overrides: Partial<ModeProvider> = {}): ModeProvider {
    return {
      id,
      displayName: `${id} Mode`,
      description: `Test ${id} mode provider`,
      icon: '🧪',
      color: '#FF0000',
      isApplicable: vi.fn(() => true),
      getScore: vi.fn(() => 0.5),
      getSuggestions: vi.fn(async () => ['test suggestion 1', 'test suggestion 2']),
      getActions: vi.fn(() => [
        {
          id: 'test-action',
          label: 'Test Action',
          description: 'A test action',
          execute: vi.fn(),
        },
      ]),
      ...overrides,
    };
  }

  static createPluginManifest(overrides: Partial<PluginManifest> = {}): PluginManifest {
    return {
      id: 'test-plugin',
      name: 'Test Plugin',
      version: '1.0.0',
      description: 'A test plugin for unit testing',
      author: 'Test Author',
      type: 'mode',
      dependencies: [],
      optionalDependencies: [],
      permissions: [
        {
          type: 'context.read',
          description: 'Read context information',
        },
      ],
      apiVersion: '1.0.0',
      config: {
        enabled: true,
        priority: 5,
      },
      ...overrides,
    };
  }

  static createPluginContext(): PluginContext {
    return {
      eventBus: {
        emit: vi.fn(),
        on: vi.fn(),
        off: vi.fn(),
      },
      requestPermission: vi.fn(async () => true),
      registerModeProvider: vi.fn(),
      registerContextAnalyzer: vi.fn(),
      registerUIComponent: vi.fn(),
    };
  }

  static createAnalysisResult(overrides: Partial<AnalysisResult> = {}): AnalysisResult {
    return {
      score: 0.75,
      confidence: 0.85,
      features: {
        file_type: 'typescript',
        is_react_component: 1,
        has_tests: 1,
        complexity_score: 0.4,
        line_count: 25,
        function_count: 2,
      },
      metadata: {
        analysisTime: 45,
        patterns: ['react_component', 'typescript'],
        suggestions: ['Add unit tests', 'Consider memoization'],
      },
      ...overrides,
    };
  }
}

/**
 * Test Environment Setup
 */
export class TestEnvironment {
  static setupMocks() {
    // Mock performance API
    global.performance = {
      now: vi.fn(() => Date.now()),
      mark: vi.fn(),
      measure: vi.fn(),
      getEntriesByName: vi.fn(() => []),
      getEntriesByType: vi.fn(() => []),
      clearMarks: vi.fn(),
      clearMeasures: vi.fn(),
    } as any;

    // Mock fetch API
    global.fetch = vi.fn();

    // Mock File API
    global.Blob = vi.fn(() => ({
      size: 1024,
      type: 'application/javascript',
    })) as any;

    // Mock FileReader
    global.FileReader = vi.fn(() => ({
      readAsText: vi.fn(),
      onload: null,
      onerror: null,
      result: '',
    })) as any;
  }

  static resetMocks() {
    vi.clearAllMocks();
    vi.resetAllMocks();
  }

  static mockEventBus() {
    return {
      emit: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
      once: vi.fn(),
      removeAllListeners: vi.fn(),
    };
  }

  static mockPerformanceMonitor() {
    return {
      startTimer: vi.fn(),
      endTimer: vi.fn(),
      getMetrics: vi.fn(() => ({
        averageResponseTime: 100,
        throughput: 10,
        errorRate: 0.01,
        memoryUsage: 50,
        cacheHitRate: 0.8,
      })),
    };
  }
}

/**
 * Assertion Helpers
 */
export class TestAssertions {
  static expectValidContextSnapshot(context: ContextSnapshot) {
    expect(context).toBeDefined();
    expect(context.fileContext).toBeDefined();
    expect(context.projectContext).toBeDefined();
    expect(context.userContext).toBeDefined();
    expect(context.environmentContext).toBeDefined();
    expect(context.timestamp).toBeTypeOf('number');
  }

  static expectValidAnalysisResult(result: AnalysisResult) {
    expect(result).toBeDefined();
    expect(result.score).toBeGreaterThanOrEqual(0);
    expect(result.score).toBeLessThanOrEqual(1);
    expect(result.confidence).toBeGreaterThanOrEqual(0);
    expect(result.confidence).toBeLessThanOrEqual(1);
    expect(result.features).toBeDefined();
    expect(typeof result.features).toBe('object');
    expect(result.metadata).toBeDefined();
  }

  static expectValidModeProvider(provider: ModeProvider) {
    expect(provider).toBeDefined();
    expect(provider.id).toBeDefined();
    expect(provider.displayName).toBeDefined();
    expect(provider.description).toBeDefined();
    expect(typeof provider.isApplicable).toBe('function');
    expect(typeof provider.getScore).toBe('function');
    expect(typeof provider.getSuggestions).toBe('function');
    expect(typeof provider.getActions).toBe('function');
  }

  static expectValidPluginManifest(manifest: PluginManifest) {
    expect(manifest).toBeDefined();
    expect(manifest.id).toBeDefined();
    expect(manifest.name).toBeDefined();
    expect(manifest.version).toMatch(/^\d+\.\d+\.\d+/);
    expect(manifest.type).toBeDefined();
    expect(manifest.apiVersion).toBeDefined();
    expect(Array.isArray(manifest.dependencies)).toBe(true);
    expect(Array.isArray(manifest.permissions)).toBe(true);
  }
}

/**
 * Performance Testing Utilities
 */
export class PerformanceTestUtils {
  static async measureAsyncOperation<T>(
    operation: () => Promise<T>,
    maxDuration: number = 1000
  ): Promise<{ result: T; duration: number }> {
    const startTime = performance.now();
    const result = await operation();
    const endTime = performance.now();
    const duration = endTime - startTime;

    if (duration > maxDuration) {
      throw new Error(`Operation took ${duration}ms, expected < ${maxDuration}ms`);
    }

    return { result, duration };
  }

  static async measureSyncOperation<T>(
    operation: () => T,
    maxDuration: number = 100
  ): Promise<{ result: T; duration: number }> {
    const startTime = performance.now();
    const result = operation();
    const endTime = performance.now();
    const duration = endTime - startTime;

    if (duration > maxDuration) {
      throw new Error(`Operation took ${duration}ms, expected < ${maxDuration}ms`);
    }

    return { result, duration };
  }

  static async testConcurrency<T>(
    operation: () => Promise<T>,
    concurrency: number,
    maxTotalDuration: number = 5000
  ): Promise<T[]> {
    const startTime = performance.now();
    
    const promises = Array.from({ length: concurrency }, () => operation());
    const results = await Promise.all(promises);
    
    const endTime = performance.now();
    const duration = endTime - startTime;

    if (duration > maxTotalDuration) {
      throw new Error(`Concurrent operations took ${duration}ms, expected < ${maxTotalDuration}ms`);
    }

    return results;
  }
}

/**
 * Error Testing Utilities
 */
export class ErrorTestUtils {
  static createNetworkError(message: string = 'Network error') {
    const error = new Error(message);
    error.name = 'NetworkError';
    return error;
  }

  static createValidationError(field: string, message: string = 'Validation failed') {
    const error = new Error(`${field}: ${message}`);
    error.name = 'ValidationError';
    return error;
  }

  static createPermissionError(permission: string) {
    const error = new Error(`Permission denied: ${permission}`);
    error.name = 'PermissionError';
    return error;
  }

  static async expectAsyncError<T>(
    operation: () => Promise<T>,
    expectedErrorType?: string,
    expectedMessage?: string
  ): Promise<Error> {
    try {
      await operation();
      throw new Error('Expected operation to throw an error');
    } catch (error) {
      if (expectedErrorType && error.name !== expectedErrorType) {
        throw new Error(`Expected error type ${expectedErrorType}, got ${error.name}`);
      }
      if (expectedMessage && !error.message.includes(expectedMessage)) {
        throw new Error(`Expected error message to contain "${expectedMessage}", got "${error.message}"`);
      }
      return error;
    }
  }
}

/**
 * Fixture Data
 */
export const TestFixtures = {
  contexts: {
    reactComponent: MockDataFactory.createContextSnapshot({
      fileContext: {
        path: '/src/components/Button.tsx',
        content: `
          import React from 'react';
          
          interface ButtonProps {
            children: React.ReactNode;
            variant?: 'primary' | 'secondary';
            onClick: () => void;
          }
          
          export const Button: React.FC<ButtonProps> = ({ children, variant = 'primary', onClick }) => {
            return (
              <button className={\`btn btn-\${variant}\`} onClick={onClick}>
                {children}
              </button>
            );
          };
        `,
        language: 'typescript',
        hasErrors: false,
        openFiles: ['/src/components/Button.tsx'],
      },
      projectContext: {
        name: 'ui-components',
        type: 'typescript',
        structure: {
          hasTests: true,
          hasPackageJson: true,
          directories: ['src', 'components', '__tests__'],
          mainFiles: ['index.ts', 'App.tsx'],
        },
        dependencies: ['react', '@types/react', 'typescript'],
      },
    }),

    pythonScript: MockDataFactory.createContextSnapshot({
      fileContext: {
        path: '/src/data_processor.py',
        content: `
          import pandas as pd
          import numpy as np
          from typing import List, Dict
          
          def process_data(data: pd.DataFrame) -> Dict[str, float]:
              """Process data and return statistics."""
              return {
                  'mean': data.mean().mean(),
                  'std': data.std().mean(),
                  'count': len(data)
              }
        `,
        language: 'python',
        hasErrors: false,
        openFiles: ['/src/data_processor.py'],
      },
      projectContext: {
        name: 'data-analysis',
        type: 'python',
        structure: {
          hasTests: true,
          hasPackageJson: false,
          directories: ['src', 'tests', 'data'],
          mainFiles: ['main.py', 'requirements.txt'],
        },
        dependencies: ['pandas', 'numpy', 'matplotlib'],
      },
    }),

    apiEndpoint: MockDataFactory.createContextSnapshot({
      fileContext: {
        path: '/src/api/users.ts',
        content: `
          import express from 'express';
          import { User } from '../types';
          
          export const usersRouter = express.Router();
          
          usersRouter.get('/', async (req, res) => {
            try {
              const users = await User.findAll();
              res.json(users);
            } catch (error) {
              res.status(500).json({ error: 'Internal server error' });
            }
          });
        `,
        language: 'typescript',
        hasErrors: false,
        openFiles: ['/src/api/users.ts'],
      },
      projectContext: {
        name: 'rest-api',
        type: 'typescript',
        structure: {
          hasTests: true,
          hasPackageJson: true,
          directories: ['src', 'api', 'tests', 'models'],
          mainFiles: ['index.ts', 'server.ts'],
        },
        dependencies: ['express', '@types/express', 'sequelize'],
      },
    }),
  },

  manifests: {
    modePlugin: MockDataFactory.createPluginManifest({
      id: 'creative-mode',
      name: 'Creative Mode',
      type: 'mode',
      description: 'Enhances creative thinking and brainstorming',
    }),

    analyzerPlugin: MockDataFactory.createPluginManifest({
      id: 'complexity-analyzer',
      name: 'Complexity Analyzer',
      type: 'analyzer',
      description: 'Analyzes code complexity and suggests improvements',
    }),

    uiPlugin: MockDataFactory.createPluginManifest({
      id: 'dashboard-ui',
      name: 'Dashboard UI',
      type: 'ui',
      description: 'Provides dashboard UI components',
    }),
  },
};
import { describe, it, expect } from 'vitest';
import { ArchitectPanel } from '../../components/ModeFeatures/ArchitectPanel';
import { DebugPanel } from '../../components/ModeFeatures/DebugPanel';
import { ReviewPanel } from '../../components/ModeFeatures/ReviewPanel';
import { ModePanel } from '../../components/ModeFeatures';
import { Mode } from '../../types';

describe('Mode-Specific UI Panels (Component Tests)', () => {
  const mockMode: Mode = {
    id: 'architect',
    name: 'Architect Mode',
    description: 'System design and architecture',
    icon: '🏗️',
    color: '#3B82F6',
    features: [],
    shortcuts: [],
    contextRules: [],
    activate: () => {},
    deactivate: () => {},
  };

  const mockContext = {
    fileContext: {
      path: 'test.ts',
      name: 'test.ts',
      extension: '.ts',
      size: 100,
      lastModified: Date.now(),
      hasErrors: false,
      isTest: false,
      isConfig: false,
    },
  };

  describe('Panel Components', () => {
    it('should have ArchitectPanel component', () => {
      expect(ArchitectPanel).toBeDefined();
      expect(typeof ArchitectPanel).toBe('function');
    });

    it('should have DebugPanel component', () => {
      expect(DebugPanel).toBeDefined();
      expect(typeof DebugPanel).toBe('function');
    });

    it('should have ReviewPanel component', () => {
      expect(ReviewPanel).toBeDefined();
      expect(typeof ReviewPanel).toBe('function');
    });

    it('should have ModePanel component', () => {
      expect(ModePanel).toBeDefined();
      expect(typeof ModePanel).toBe('function');
    });
  });

  describe('Panel Props', () => {
    it('should accept mode and context props', () => {
      // Test that components can be called with proper props
      const architectProps = { mode: mockMode, context: mockContext };
      const debugProps = { mode: { ...mockMode, id: 'debug' }, context: mockContext };
      const reviewProps = { mode: { ...mockMode, id: 'review' }, context: mockContext };

      // Verify props structure matches expected interface
      expect(architectProps.mode.id).toBe('architect');
      expect(debugProps.mode.id).toBe('debug');
      expect(reviewProps.mode.id).toBe('review');
      expect(architectProps.context).toBeDefined();
    });
  });

  describe('Mode Feature Configurations', () => {
    it('should have architect feature config', () => {
      const { architectFeatureConfig } = require('../../modes/architect.mode');
      expect(architectFeatureConfig).toBeDefined();
      expect(architectFeatureConfig.systemDesigner).toBeDefined();
      expect(architectFeatureConfig.systemDesigner.enabled).toBe(true);
    });

    it('should have debug feature config', () => {
      const { debugFeatureConfig } = require('../../modes/debug.mode');
      expect(debugFeatureConfig).toBeDefined();
      expect(debugFeatureConfig.breakpointManager).toBeDefined();
      expect(debugFeatureConfig.breakpointManager.enabled).toBe(true);
    });

    it('should have review feature config', () => {
      const { reviewFeatureConfig } = require('../../modes/review.mode');
      expect(reviewFeatureConfig).toBeDefined();
      expect(reviewFeatureConfig.codeAnalyzer).toBeDefined();
      expect(reviewFeatureConfig.codeAnalyzer.enabled).toBe(true);
    });
  });
});
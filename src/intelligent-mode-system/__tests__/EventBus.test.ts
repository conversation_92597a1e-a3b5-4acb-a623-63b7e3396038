import { describe, it, expect, beforeEach, vi } from 'vitest';
import { EventBusImpl } from '../core/EventBus';
import { SystemEvent } from '../types';

describe('EventBus', () => {
  let eventBus: EventBusImpl;

  beforeEach(() => {
    eventBus = new EventBusImpl();
  });

  describe('emit', () => {
    it('should emit events to registered handlers', () => {
      const handler = vi.fn();
      eventBus.on('test.event', handler);

      eventBus.emit({
        type: 'test.event',
        payload: { data: 'test' },
        source: 'test',
      });

      expect(handler).toHaveBeenCalledTimes(1);
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'test.event',
          payload: { data: 'test' },
          source: 'test',
          id: expect.any(String),
          timestamp: expect.any(Number),
        })
      );
    });

    it('should handle multiple handlers for same event', () => {
      const handler1 = vi.fn();
      const handler2 = vi.fn();

      eventBus.on('test.event', handler1);
      eventBus.on('test.event', handler2);

      eventBus.emit({
        type: 'test.event',
        payload: {},
        source: 'test',
      });

      expect(handler1).toHaveBeenCalledTimes(1);
      expect(handler2).toHaveBeenCalledTimes(1);
    });

    it('should not call handlers for different event types', () => {
      const handler = vi.fn();
      eventBus.on('test.event1', handler);

      eventBus.emit({
        type: 'test.event2',
        payload: {},
        source: 'test',
      });

      expect(handler).not.toHaveBeenCalled();
    });
  });

  describe('on', () => {
    it('should return unsubscribe function', () => {
      const handler = vi.fn();
      const unsubscribe = eventBus.on('test.event', handler);

      eventBus.emit({
        type: 'test.event',
        payload: {},
        source: 'test',
      });

      expect(handler).toHaveBeenCalledTimes(1);

      unsubscribe();

      eventBus.emit({
        type: 'test.event',
        payload: {},
        source: 'test',
      });

      expect(handler).toHaveBeenCalledTimes(1); // Still 1, not called again
    });

    it('should support subscribing to multiple event types', () => {
      const handler = vi.fn();
      eventBus.on(['test.event1', 'test.event2'], handler);

      eventBus.emit({
        type: 'test.event1',
        payload: {},
        source: 'test',
      });

      eventBus.emit({
        type: 'test.event2',
        payload: {},
        source: 'test',
      });

      expect(handler).toHaveBeenCalledTimes(2);
    });
  });

  describe('once', () => {
    it('should only call handler once', () => {
      const handler = vi.fn();
      eventBus.once('test.event', handler);

      eventBus.emit({
        type: 'test.event',
        payload: {},
        source: 'test',
      });

      eventBus.emit({
        type: 'test.event',
        payload: {},
        source: 'test',
      });

      expect(handler).toHaveBeenCalledTimes(1);
    });
  });

  describe('off', () => {
    it('should remove specific handler', () => {
      const handler1 = vi.fn();
      const handler2 = vi.fn();

      eventBus.on('test.event', handler1);
      eventBus.on('test.event', handler2);

      eventBus.off('test.event', handler1);

      eventBus.emit({
        type: 'test.event',
        payload: {},
        source: 'test',
      });

      expect(handler1).not.toHaveBeenCalled();
      expect(handler2).toHaveBeenCalledTimes(1);
    });

    it('should remove all handlers when no handler specified', () => {
      const handler1 = vi.fn();
      const handler2 = vi.fn();

      eventBus.on('test.event', handler1);
      eventBus.on('test.event', handler2);

      eventBus.off('test.event');

      eventBus.emit({
        type: 'test.event',
        payload: {},
        source: 'test',
      });

      expect(handler1).not.toHaveBeenCalled();
      expect(handler2).not.toHaveBeenCalled();
    });
  });

  describe('clear', () => {
    it('should remove all handlers and clear history', () => {
      const handler = vi.fn();
      eventBus.on('test.event', handler);

      eventBus.emit({
        type: 'test.event',
        payload: {},
        source: 'test',
      });

      expect(handler).toHaveBeenCalledTimes(1);
      expect(eventBus.getHistory()).toHaveLength(1);

      eventBus.clear();

      eventBus.emit({
        type: 'test.event',
        payload: {},
        source: 'test',
      });

      expect(handler).toHaveBeenCalledTimes(1); // Still 1 because handler was cleared
      expect(eventBus.getHistory()).toHaveLength(1); // New event after clear should be in history
    });
  });

  describe('getHistory', () => {
    it('should return event history', () => {
      eventBus.emit({
        type: 'test.event1',
        payload: { data: 1 },
        source: 'test',
      });

      eventBus.emit({
        type: 'test.event2',
        payload: { data: 2 },
        source: 'test',
      });

      const history = eventBus.getHistory();
      expect(history).toHaveLength(2);
      expect(history[0].type).toBe('test.event1');
      expect(history[1].type).toBe('test.event2');
    });

    it('should filter history by type', () => {
      eventBus.emit({
        type: 'test.event1',
        payload: {},
        source: 'test',
      });

      eventBus.emit({
        type: 'test.event2',
        payload: {},
        source: 'test',
      });

      eventBus.emit({
        type: 'test.event1',
        payload: {},
        source: 'test',
      });

      const filtered = eventBus.getHistory({ type: 'test.event1' });
      expect(filtered).toHaveLength(2);
      expect(filtered.every(e => e.type === 'test.event1')).toBe(true);
    });

    it('should limit history results', () => {
      for (let i = 0; i < 5; i++) {
        eventBus.emit({
          type: 'test.event',
          payload: { index: i },
          source: 'test',
        });
      }

      const limited = eventBus.getHistory({ limit: 2 });
      expect(limited).toHaveLength(2);
      expect(limited[0].payload.index).toBe(3);
      expect(limited[1].payload.index).toBe(4);
    });
  });
});
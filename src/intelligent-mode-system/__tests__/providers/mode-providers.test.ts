import { describe, it, expect, beforeEach } from 'vitest';
import { ArchitectContextProvider } from '../../providers/mode-providers/ArchitectContextProvider';
import { DebugContextProvider } from '../../providers/mode-providers/DebugContextProvider';
import { ReviewContextProvider } from '../../providers/mode-providers/ReviewContextProvider';
import { DeployContextProvider } from '../../providers/mode-providers/DeployContextProvider';
import { ContextSnapshot } from '../../types';

describe('Mode Context Providers', () => {
  let mockContext: ContextSnapshot;

  beforeEach(() => {
    mockContext = {
      fileContext: {
        path: 'test.ts',
        name: 'test.ts',
        extension: '.ts',
        size: 100,
        lastModified: Date.now(),
        hasErrors: false,
        isTest: false,
        isConfig: false,
      },
      projectContext: {
        name: 'test-project',
        type: 'web-application',
        path: '/test',
        dependencies: {},
        hasTypeScript: true,
        hasTests: true,
      },
      environmentContext: {
        gitStatus: {
          branch: 'main',
          isClean: true,
        },
      },
      userContext: {
        recentActions: [],
        preferences: {},
        activityPatterns: {},
      },
      timestamp: Date.now(),
    };
  });

  describe('ArchitectContextProvider', () => {
    let provider: ArchitectContextProvider;

    beforeEach(() => {
      provider = new ArchitectContextProvider();
    });

    it('should have correct metadata', () => {
      expect(provider.id).toBe('architect-mode-provider');
      expect(provider.name).toBe('Architect Mode Context Provider');
      expect(provider.priority).toBe(80);
    });

    it('should recommend architect mode for architecture files', async () => {
      mockContext.fileContext.path = 'architecture.md';
      mockContext.fileContext.name = 'architecture.md';
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.7);
      expect(analysis.recommendations).toContain('architect');
      expect(analysis.metadata.patterns).toContain('architecture_file');
    });

    it('should detect system design files', async () => {
      mockContext.fileContext.path = 'docs/system-design.md';
      mockContext.fileContext.name = 'system-design.md';
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.7);
      expect(analysis.metadata.patterns).toContain('system_design_file');
    });

    it('should detect component planning activity', async () => {
      mockContext.userContext.recentActions = [
        { type: 'edit', target: 'Component.tsx', timestamp: Date.now() - 60000 },
        { type: 'edit', target: 'Component.test.tsx', timestamp: Date.now() - 30000 },
        { type: 'navigate', target: 'components/', timestamp: Date.now() },
      ];
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.5);
      expect(analysis.metadata.patterns).toContain('component_planning');
    });
  });

  describe('DebugContextProvider', () => {
    let provider: DebugContextProvider;

    beforeEach(() => {
      provider = new DebugContextProvider();
    });

    it('should have high priority', () => {
      expect(provider.priority).toBe(90);
    });

    it('should strongly recommend debug mode when errors present', async () => {
      mockContext.fileContext.hasErrors = true;
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.8);
      expect(analysis.recommendations).toContain('debug');
      expect(analysis.metadata.patterns).toContain('has_errors');
    });

    it('should detect debugging activity', async () => {
      mockContext.userContext.recentActions = [
        { type: 'debug', target: 'breakpoint', timestamp: Date.now() - 60000 },
        { type: 'search', target: 'error', timestamp: Date.now() - 30000 },
      ];
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.6);
      expect(analysis.metadata.patterns).toContain('debugging_activity');
    });

    it('should detect console logs pattern', async () => {
      mockContext.userContext.recentActions = [
        { type: 'edit', target: 'console.log', timestamp: Date.now() - 30000 },
        { type: 'edit', target: 'console.error', timestamp: Date.now() - 15000 },
      ];
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.5);
      expect(analysis.metadata.patterns).toContain('console_logs');
    });
  });

  describe('ReviewContextProvider', () => {
    let provider: ReviewContextProvider;

    beforeEach(() => {
      provider = new ReviewContextProvider();
    });

    it('should detect review branch', async () => {
      mockContext.environmentContext.gitStatus = {
        branch: 'review/feature-123',
        isClean: true,
      };
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.7);
      expect(analysis.recommendations).toContain('review');
      expect(analysis.metadata.patterns).toContain('review_branch');
    });

    it('should detect PR branch pattern', async () => {
      mockContext.environmentContext.gitStatus = {
        branch: 'feature/add-new-component',
        isClean: false,
      };
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.5);
      expect(analysis.metadata.patterns).toContain('pr_branch');
    });

    it('should detect code review activity', async () => {
      mockContext.userContext.recentActions = [
        { type: 'command', target: 'git diff', timestamp: Date.now() - 60000 },
        { type: 'navigate', target: 'changes.md', timestamp: Date.now() - 30000 },
      ];
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.5);
      expect(analysis.metadata.patterns).toContain('review_activity');
    });
  });

  describe('DeployContextProvider', () => {
    let provider: DeployContextProvider;

    beforeEach(() => {
      provider = new DeployContextProvider();
    });

    it('should detect CI/CD files', async () => {
      mockContext.fileContext.path = '.github/workflows/deploy.yml';
      mockContext.fileContext.name = 'deploy.yml';
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.7);
      expect(analysis.recommendations).toContain('deploy');
      expect(analysis.metadata.patterns).toContain('cicd_file');
    });

    it('should detect Dockerfile', async () => {
      mockContext.fileContext.path = 'Dockerfile';
      mockContext.fileContext.name = 'Dockerfile';
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.7);
      expect(analysis.metadata.patterns).toContain('dockerfile');
    });

    it('should detect deploy activity', async () => {
      mockContext.userContext.recentActions = [
        { type: 'command', target: 'npm build', timestamp: Date.now() - 60000 },
        { type: 'command', target: 'docker build', timestamp: Date.now() - 30000 },
      ];
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.5);
      expect(analysis.metadata.patterns).toContain('deploy_activity');
    });

    it('should detect release branch', async () => {
      mockContext.environmentContext.gitStatus = {
        branch: 'release/v1.2.0',
        isClean: true,
      };
      
      const analysis = await provider.analyze(mockContext);
      
      expect(analysis.confidence).toBeGreaterThan(0.6);
      expect(analysis.metadata.patterns).toContain('release_branch');
    });
  });

  describe('Provider Integration', () => {
    it('should work with different contexts producing different recommendations', async () => {
      const architectProvider = new ArchitectContextProvider();
      const debugProvider = new DebugContextProvider();
      
      // Test with architecture context
      mockContext.fileContext.path = 'architecture.md';
      const archAnalysis = await architectProvider.analyze(mockContext);
      expect(archAnalysis.recommendations).toContain('architect');
      
      // Test with error context
      mockContext.fileContext.path = 'error.ts';
      mockContext.fileContext.hasErrors = true;
      const debugAnalysis = await debugProvider.analyze(mockContext);
      expect(debugAnalysis.recommendations).toContain('debug');
      
      // Debug should have higher confidence for errors
      expect(debugAnalysis.confidence).toBeGreaterThan(archAnalysis.confidence);
    });
  });
});
/**
 * Performance Benchmark Tests
 * 
 * Comprehensive performance testing suite to ensure the intelligent mode system
 * meets performance requirements and scales appropriately.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { IntelligentModeSystem } from '../../core/IntelligentModeSystem';
import { ContextAnalyzer } from '../../core/ContextAnalyzer';
import { ModeProviderRegistry } from '../../core/ModeProviderRegistry';
import { MockDataFactory, PerformanceTestUtils, TestEnvironment } from '../utils/TestUtils';
import { ContextSnapshot } from '../../types';

// Performance targets
const PERFORMANCE_TARGETS = {
  CONTEXT_ANALYSIS: 100, // ms
  MODE_SELECTION: 50, // ms
  FULL_WORKFLOW: 200, // ms
  BATCH_PROCESSING: 1000, // ms for 10 items
  MEMORY_USAGE: 100, // MB
  STARTUP_TIME: 500, // ms
};

describe('Performance Benchmark Tests', () => {
  let intelligentModeSystem: IntelligentModeSystem;
  let contextAnalyzer: ContextAnalyzer;
  let modeRegistry: ModeProviderRegistry;

  beforeEach(async () => {
    TestEnvironment.setupMocks();
    
    intelligentModeSystem = new IntelligentModeSystem();
    contextAnalyzer = new ContextAnalyzer();
    modeRegistry = new ModeProviderRegistry();

    await contextAnalyzer.initialize();
    await intelligentModeSystem.initialize();
  });

  afterEach(() => {
    TestEnvironment.resetMocks();
  });

  describe('Context Analysis Performance', () => {
    it('should analyze simple context within performance target', async () => {
      const context = MockDataFactory.createContextSnapshot();

      const { duration } = await PerformanceTestUtils.measureAsyncOperation(
        () => contextAnalyzer.analyzeContext(context),
        PERFORMANCE_TARGETS.CONTEXT_ANALYSIS
      );

      expect(duration).toBeLessThan(PERFORMANCE_TARGETS.CONTEXT_ANALYSIS);
    });

    it('should analyze complex context efficiently', async () => {
      const complexContent = `
        import React, { useState, useEffect, useCallback, useMemo } from 'react';
        import { connect } from 'react-redux';
        import { ThunkDispatch } from 'redux-thunk';
        import styled from 'styled-components';
        
        interface Props {
          users: User[];
          loading: boolean;
          error: string | null;
          fetchUsers: () => void;
          updateUser: (id: string, data: Partial<User>) => void;
        }
        
        const Container = styled.div\`
          display: flex;
          flex-direction: column;
          padding: 20px;
          background: \${props => props.theme.background};
        \`;
        
        const UserList: React.FC<Props> = ({ users, loading, error, fetchUsers, updateUser }) => {
          const [filter, setFilter] = useState('');
          const [sortBy, setSortBy] = useState<'name' | 'email' | 'created'>('name');
          
          useEffect(() => {
            fetchUsers();
          }, [fetchUsers]);
          
          const filteredUsers = useMemo(() => {
            return users
              .filter(user => 
                user.name.toLowerCase().includes(filter.toLowerCase()) ||
                user.email.toLowerCase().includes(filter.toLowerCase())
              )
              .sort((a, b) => {
                switch (sortBy) {
                  case 'name':
                    return a.name.localeCompare(b.name);
                  case 'email':
                    return a.email.localeCompare(b.email);
                  case 'created':
                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                  default:
                    return 0;
                }
              });
          }, [users, filter, sortBy]);
          
          const handleUserUpdate = useCallback((id: string, data: Partial<User>) => {
            updateUser(id, data);
          }, [updateUser]);
          
          if (loading) {
            return <div>Loading...</div>;
          }
          
          if (error) {
            return <div>Error: {error}</div>;
          }
          
          return (
            <Container>
              <input
                type="text"
                placeholder="Filter users..."
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
              />
              <select value={sortBy} onChange={(e) => setSortBy(e.target.value as any)}>
                <option value="name">Sort by Name</option>
                <option value="email">Sort by Email</option>
                <option value="created">Sort by Created Date</option>
              </select>
              <div>
                {filteredUsers.map(user => (
                  <UserCard
                    key={user.id}
                    user={user}
                    onUpdate={(data) => handleUserUpdate(user.id, data)}
                  />
                ))}
              </div>
            </Container>
          );
        };
      `;

      const complexContext = MockDataFactory.createContextSnapshot({
        fileContext: {
          path: '/src/components/UserList.tsx',
          content: complexContent,
          language: 'typescript',
          hasErrors: false,
          openFiles: ['/src/components/UserList.tsx'],
        },
      });

      const { duration } = await PerformanceTestUtils.measureAsyncOperation(
        () => contextAnalyzer.analyzeContext(complexContext),
        PERFORMANCE_TARGETS.CONTEXT_ANALYSIS * 2 // Allow more time for complex context
      );

      expect(duration).toBeLessThan(PERFORMANCE_TARGETS.CONTEXT_ANALYSIS * 2);
    });

    it('should handle large file content efficiently', async () => {
      const largeContent = Array.from({ length: 10000 }, (_, i) => 
        `const function${i} = () => { return ${i}; };`
      ).join('\n');

      const largeContext = MockDataFactory.createContextSnapshot({
        fileContext: {
          path: '/src/large-file.ts',
          content: largeContent,
          language: 'typescript',
          hasErrors: false,
          openFiles: ['/src/large-file.ts'],
        },
      });

      const { duration } = await PerformanceTestUtils.measureAsyncOperation(
        () => contextAnalyzer.analyzeContext(largeContext),
        PERFORMANCE_TARGETS.CONTEXT_ANALYSIS * 3 // Allow more time for large files
      );

      expect(duration).toBeLessThan(PERFORMANCE_TARGETS.CONTEXT_ANALYSIS * 3);
    });
  });

  describe('Mode Selection Performance', () => {
    it('should select mode within performance target', async () => {
      const context = MockDataFactory.createContextSnapshot();

      const { duration } = await PerformanceTestUtils.measureAsyncOperation(
        () => intelligentModeSystem.selectBestMode(context),
        PERFORMANCE_TARGETS.MODE_SELECTION
      );

      expect(duration).toBeLessThan(PERFORMANCE_TARGETS.MODE_SELECTION);
    });

    it('should handle multiple mode providers efficiently', async () => {
      // Register multiple mode providers
      for (let i = 0; i < 20; i++) {
        const provider = MockDataFactory.createModeProvider(`test-mode-${i}` as any);
        modeRegistry.registerProvider(provider);
      }

      const context = MockDataFactory.createContextSnapshot();

      const { duration } = await PerformanceTestUtils.measureAsyncOperation(
        () => modeRegistry.getBestProvider(context),
        PERFORMANCE_TARGETS.MODE_SELECTION * 2 // Allow more time with many providers
      );

      expect(duration).toBeLessThan(PERFORMANCE_TARGETS.MODE_SELECTION * 2);
    });
  });

  describe('Full Workflow Performance', () => {
    it('should complete full analysis workflow within target', async () => {
      const context = MockDataFactory.createContextSnapshot();

      const { duration } = await PerformanceTestUtils.measureAsyncOperation(
        () => intelligentModeSystem.analyzeAndSuggest(context),
        PERFORMANCE_TARGETS.FULL_WORKFLOW
      );

      expect(duration).toBeLessThan(PERFORMANCE_TARGETS.FULL_WORKFLOW);
    });

    it('should maintain performance with feature extraction', async () => {
      const context = MockDataFactory.createContextSnapshot();

      const { duration } = await PerformanceTestUtils.measureAsyncOperation(
        async () => {
          const analysis = await contextAnalyzer.analyzeContext(context);
          const features = await contextAnalyzer.extractFeatures(context);
          const patterns = await contextAnalyzer.identifyPatterns(context);
          return { analysis, features, patterns };
        },
        PERFORMANCE_TARGETS.FULL_WORKFLOW
      );

      expect(duration).toBeLessThan(PERFORMANCE_TARGETS.FULL_WORKFLOW);
    });
  });

  describe('Batch Processing Performance', () => {
    it('should process multiple contexts efficiently', async () => {
      const contexts = Array.from({ length: 10 }, () => 
        MockDataFactory.createContextSnapshot()
      );

      const { duration } = await PerformanceTestUtils.measureAsyncOperation(
        () => Promise.all(contexts.map(context => 
          intelligentModeSystem.analyzeAndSuggest(context)
        )),
        PERFORMANCE_TARGETS.BATCH_PROCESSING
      );

      expect(duration).toBeLessThan(PERFORMANCE_TARGETS.BATCH_PROCESSING);
    });

    it('should handle concurrent analysis efficiently', async () => {
      const context = MockDataFactory.createContextSnapshot();

      const results = await PerformanceTestUtils.testConcurrency(
        () => intelligentModeSystem.analyzeAndSuggest(context),
        10,
        PERFORMANCE_TARGETS.BATCH_PROCESSING
      );

      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result.recommendedMode).toBeDefined();
        expect(result.confidence).toBeGreaterThanOrEqual(0);
      });
    });
  });

  describe('Memory Performance', () => {
    it('should not accumulate memory during repeated operations', async () => {
      const context = MockDataFactory.createContextSnapshot();
      
      // Get baseline memory (simulated)
      const getMemoryUsage = () => {
        // In a real environment, this would check actual memory usage
        return Math.random() * 50 + 30; // Simulate 30-80MB usage
      };

      const baselineMemory = getMemoryUsage();

      // Perform many operations
      for (let i = 0; i < 100; i++) {
        await intelligentModeSystem.analyzeAndSuggest(context);
      }

      const finalMemory = getMemoryUsage();
      const memoryIncrease = finalMemory - baselineMemory;

      // Memory increase should be minimal (less than 20MB)
      expect(memoryIncrease).toBeLessThan(20);
    });

    it('should cleanup resources efficiently', async () => {
      const context = MockDataFactory.createContextSnapshot();

      // Create many analysis results
      const results = [];
      for (let i = 0; i < 50; i++) {
        const result = await intelligentModeSystem.analyzeAndSuggest(context);
        results.push(result);
      }

      // Simulate cleanup
      results.length = 0;

      // Verify system still performs well after cleanup
      const { duration } = await PerformanceTestUtils.measureAsyncOperation(
        () => intelligentModeSystem.analyzeAndSuggest(context),
        PERFORMANCE_TARGETS.FULL_WORKFLOW
      );

      expect(duration).toBeLessThan(PERFORMANCE_TARGETS.FULL_WORKFLOW);
    });
  });

  describe('Startup Performance', () => {
    it('should initialize system within performance target', async () => {
      const freshSystem = new IntelligentModeSystem();
      const freshAnalyzer = new ContextAnalyzer();

      const { duration } = await PerformanceTestUtils.measureAsyncOperation(
        async () => {
          await freshAnalyzer.initialize();
          await freshSystem.initialize();
        },
        PERFORMANCE_TARGETS.STARTUP_TIME
      );

      expect(duration).toBeLessThan(PERFORMANCE_TARGETS.STARTUP_TIME);
    });

    it('should handle cold start efficiently', async () => {
      const freshSystem = new IntelligentModeSystem();
      await freshSystem.initialize();

      const context = MockDataFactory.createContextSnapshot();

      // First analysis (cold start)
      const { duration } = await PerformanceTestUtils.measureAsyncOperation(
        () => freshSystem.analyzeAndSuggest(context),
        PERFORMANCE_TARGETS.FULL_WORKFLOW * 2 // Allow more time for cold start
      );

      expect(duration).toBeLessThan(PERFORMANCE_TARGETS.FULL_WORKFLOW * 2);
    });
  });

  describe('Scalability Tests', () => {
    it('should scale linearly with context complexity', async () => {
      const simpleContext = MockDataFactory.createContextSnapshot({
        fileContext: {
          path: '/src/simple.ts',
          content: 'const x = 1;',
          language: 'typescript',
        },
      });

      const complexContext = MockDataFactory.createContextSnapshot({
        fileContext: {
          path: '/src/complex.ts',
          content: 'x'.repeat(10000), // 10x larger
          language: 'typescript',
        },
      });

      const { duration: simpleDuration } = await PerformanceTestUtils.measureAsyncOperation(
        () => intelligentModeSystem.analyzeAndSuggest(simpleContext)
      );

      const { duration: complexDuration } = await PerformanceTestUtils.measureAsyncOperation(
        () => intelligentModeSystem.analyzeAndSuggest(complexContext)
      );

      // Complex analysis should not be more than 10x slower
      expect(complexDuration / simpleDuration).toBeLessThan(10);
    });

    it('should handle increasing number of mode providers efficiently', async () => {
      const context = MockDataFactory.createContextSnapshot();
      const registry = new ModeProviderRegistry();

      // Test with different numbers of providers
      const providerCounts = [1, 5, 10, 20, 50];
      const durations = [];

      for (const count of providerCounts) {
        // Clear registry
        const freshRegistry = new ModeProviderRegistry();
        
        // Add providers
        for (let i = 0; i < count; i++) {
          const provider = MockDataFactory.createModeProvider(`test-${i}` as any);
          freshRegistry.registerProvider(provider);
        }

        const { duration } = await PerformanceTestUtils.measureAsyncOperation(
          () => freshRegistry.getBestProvider(context)
        );

        durations.push(duration);
      }

      // Performance should scale sub-linearly
      const first = durations[0];
      const last = durations[durations.length - 1];
      
      // 50x more providers should not take 50x longer
      expect(last / first).toBeLessThan(25);
    });
  });

  describe('Optimization Verification', () => {
    it('should use optimized algorithms for feature extraction', async () => {
      const context = MockDataFactory.createContextSnapshot();

      // Mock the optimized algorithms to verify they're called
      const mockExtractor = {
        extractOptimized: vi.fn(() => new Float32Array([0.1, 0.2, 0.3, 0.4, 0.5])),
      };

      // Replace with mock temporarily
      const originalModule = await import('../../utils/OptimizedAlgorithms');
      originalModule.FastFeatureExtractor = mockExtractor as any;

      await contextAnalyzer.analyzeContext(context);

      expect(mockExtractor.extractOptimized).toHaveBeenCalled();
    });

    it('should benefit from caching on repeated analysis', async () => {
      const context = MockDataFactory.createContextSnapshot();

      // First analysis (cold)
      const { duration: firstDuration } = await PerformanceTestUtils.measureAsyncOperation(
        () => intelligentModeSystem.analyzeAndSuggest(context)
      );

      // Second analysis (potentially cached)
      const { duration: secondDuration } = await PerformanceTestUtils.measureAsyncOperation(
        () => intelligentModeSystem.analyzeAndSuggest(context)
      );

      // Note: Caching benefits depend on implementation
      // For now, just verify both complete successfully
      expect(firstDuration).toBeGreaterThan(0);
      expect(secondDuration).toBeGreaterThan(0);
    });

    it('should demonstrate performance improvement over naive implementation', async () => {
      const context = MockDataFactory.createContextSnapshot();

      // Optimized version
      const { duration: optimizedDuration } = await PerformanceTestUtils.measureAsyncOperation(
        () => intelligentModeSystem.analyzeAndSuggest(context)
      );

      // Simulate naive implementation (synchronous, no optimizations)
      const naiveImplementation = () => {
        // Simulate slow operations
        const start = performance.now();
        while (performance.now() - start < 50) {
          // Busy wait to simulate slow processing
        }
        return MockDataFactory.createAnalysisResult();
      };

      const { duration: naiveDuration } = await PerformanceTestUtils.measureSyncOperation(
        naiveImplementation,
        200 // Allow more time for naive implementation
      );

      // Optimized version should be significantly faster
      expect(optimizedDuration).toBeLessThan(naiveDuration);
    });
  });

  describe('Resource Utilization', () => {
    it('should efficiently utilize system resources', async () => {
      const contexts = Array.from({ length: 20 }, (_, i) => 
        MockDataFactory.createContextSnapshot({
          fileContext: { path: `/src/file${i}.ts` }
        })
      );

      // Process all contexts
      const startTime = performance.now();
      const results = await Promise.all(
        contexts.map(context => intelligentModeSystem.analyzeAndSuggest(context))
      );
      const endTime = performance.now();

      const totalDuration = endTime - startTime;
      const averageDuration = totalDuration / contexts.length;

      // Average per-context time should be reasonable
      expect(averageDuration).toBeLessThan(PERFORMANCE_TARGETS.FULL_WORKFLOW);
      expect(results).toHaveLength(20);
    });

    it('should handle resource constraints gracefully', async () => {
      // Simulate resource-constrained environment
      const limitedMemoryContext = MockDataFactory.createContextSnapshot({
        fileContext: {
          content: 'x'.repeat(1000000), // 1MB content
        },
      });

      // Should still complete, even if slower
      const result = await intelligentModeSystem.analyzeAndSuggest(limitedMemoryContext);
      
      expect(result.recommendedMode).toBeDefined();
      expect(result.confidence).toBeGreaterThanOrEqual(0);
    });
  });
});
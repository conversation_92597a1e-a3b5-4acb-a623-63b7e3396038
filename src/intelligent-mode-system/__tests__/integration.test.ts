import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  fileContextProvider, 
  projectContextProvider, 
  gitContextProvider, 
  userContextProvider,
  registerAllProviders
} from '../providers';
import { modeRegistry } from '../core/ModeRegistry';
import { eventBus } from '../core/EventBus';
import { contextInferenceEngine } from '../core/ContextInferenceEngine';
import { contextManager } from '../core/ContextManager';
import { memoryCache } from '../core/CacheSystem';
import { ContextSnapshot } from '../types';

describe('Intelligent Mode System Integration', () => {
  beforeEach(async () => {
    // Register all providers
    registerAllProviders();
    
    // Clear all state
    eventBus.clear();
    
    // Clear cache
    await memoryCache.clear();
    
    // Reset file context to default
    if (fileContextProvider) {
      fileContextProvider.setCurrentFile('');
    }
    
    // Clear user actions
    if (userContextProvider) {
      userContextProvider.clearActions();
    }
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Mode transitions based on context', () => {
    it('should recommend debug mode when errors are detected', async () => {
      // Simulate file with errors
      fileContextProvider.setCurrentFile('src/buggy.ts');

      // Get fresh context
      const context = await contextManager.refreshContext();
      
      // Update inference engine with new context
      await contextInferenceEngine.analyze();

      // Should recommend debug mode
      const recommended = contextInferenceEngine.getRecommendedMode();
      expect(recommended).toBe('debug');
    });

    it('should recommend architect mode for system design files', async () => {
      // Simulate architecture file
      fileContextProvider.setCurrentFile('src/architecture/system-design.md');

      const context = await contextManager.refreshContext();
      await contextInferenceEngine.analyze();

      const recommended = contextInferenceEngine.getRecommendedMode();
      expect(recommended).toBe('architect');
    });

    it('should recommend deploy mode for CI/CD files', async () => {
      // Simulate deployment file
      fileContextProvider.setCurrentFile('.github/workflows/deploy.yml');
      
      // Wait for the file context provider to complete its analysis
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Force a fresh analysis from the file provider
      const fileAnalysis = await fileContextProvider.analyze();
      console.log('Direct file analysis path:', fileAnalysis.path);

      const context = await contextManager.refreshContext();
      await contextInferenceEngine.analyze();

      const recommended = contextInferenceEngine.getRecommendedMode();
      const probabilities = contextInferenceEngine.getProbabilities();
      
      // Log for debugging
      console.log('File path from context:', context.fileContext.path);
      console.log('Mode probabilities:', probabilities);
      console.log('Recommended mode:', recommended);
      
      expect(recommended).toBe('deploy');
    });
  });

  describe('Event propagation and handling', () => {
    it('should emit events on mode transitions', async () => {
      const eventHandler = vi.fn();
      eventBus.on('mode.transition.complete', eventHandler);

      const mockContext: ContextSnapshot = {
        fileContext: {
          path: 'test.ts',
          type: 'typescript',
          language: 'typescript',
          hasErrors: false,
          hasWarnings: false,
          symbols: [],
          imports: [],
          lastModified: Date.now(),
        },
        projectContext: {
          type: 'node',
          rootPath: '/test',
          dependencies: {},
          structure: {
            directories: [],
            fileCount: 0,
            totalSize: 0,
            depth: 0,
            hasTests: true,
            hasDocs: false,
          },
          configuration: {},
        },
        environmentContext: {
          gitStatus: {
            branch: 'main',
            ahead: 0,
            behind: 0,
            modified: [],
            staged: [],
            untracked: [],
            hasConflicts: false,
          },
          runningProcesses: [],
          systemResources: {
            cpuUsage: 0,
            memoryUsage: 0,
            diskUsage: 0,
          },
          openFiles: [],
          activeTerminals: 0,
        },
        userContext: {
          recentActions: [],
          preferences: {
            theme: 'dark',
            shortcuts: {},
            autoTransition: true,
            suggestionLevel: 'standard',
          },
          patterns: [],
          sessionDuration: 0,
          lastActivity: Date.now(),
        },
        timestamp: Date.now(),
      };

      await modeRegistry.setActiveMode('debug', mockContext);

      expect(eventHandler).toHaveBeenCalled();
      const eventCall = eventHandler.mock.calls[0][0];
      expect(eventCall.type).toBe('mode.transition.complete');
      expect(eventCall.payload).toMatchObject({
        from: 'none',
        to: 'debug',
      });
    });

    it('should track user actions and update context', async () => {
      userContextProvider.recordAction({
        type: 'edit',
        target: 'src/App.tsx',
      });

      userContextProvider.recordAction({
        type: 'debug',
        target: 'breakpoint',
      });
      
      // Add a third debug action to ensure pattern detection (threshold is 2)
      userContextProvider.recordAction({
        type: 'debug',
        target: 'console.log',
      });

      // Force pattern detection by calling analyze directly
      const userAnalysis = await userContextProvider.analyze();
      console.log('User analysis patterns:', userAnalysis.patterns);

      const context = await contextManager.refreshContext();
      console.log('Context patterns:', context.userContext.patterns);

      expect(context.userContext.recentActions).toHaveLength(3);
      expect(context.userContext.patterns.length).toBeGreaterThan(0);
    });
  });

  describe('Performance monitoring', () => {
    it('should complete context analysis within 2 seconds', async () => {
      const startTime = Date.now();
      
      const context = await contextManager.refreshContext();
      await contextInferenceEngine.analyze();

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(2000);
    });

    it('should complete mode transitions within 1 second', async () => {
      const startTime = Date.now();
      
      const context = await contextManager.getContext();
      await modeRegistry.setActiveMode('debug', context);

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(1000);
    });
  });

  describe('Cache effectiveness', () => {
    it('should cache and reuse context data', async () => {
      // First context refresh
      const context1 = await contextManager.getContext();

      // Second context should use cache
      const context2 = await contextManager.getContext();

      // Contexts should be the same due to caching
      expect(context1.timestamp).toBe(context2.timestamp);

      // Add a small delay to ensure time passes
      await new Promise(resolve => setTimeout(resolve, 10));
      
      // Force refresh should get new context
      const context3 = await contextManager.refreshContext();
      expect(context3.timestamp).toBeGreaterThan(context1.timestamp);
    });
  });

  describe('Mode usage statistics', () => {
    it('should track time spent in each mode', async () => {
      const context = await contextManager.getContext();

      await modeRegistry.setActiveMode('architect', context);

      // Simulate some time passing
      await new Promise(resolve => setTimeout(resolve, 100));

      await modeRegistry.setActiveMode('debug', context);

      const stats = modeRegistry.getModeUsageStats();
      expect(stats.architect).toBeGreaterThan(0);
      expect(stats.debug).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Complex workflow scenarios', () => {
    it('should handle rapid mode switching gracefully', async () => {
      const context = await contextManager.getContext();
      const modes = ['architect', 'debug', 'review', 'deploy', 'experiment', 'learn'];
      
      for (const mode of modes) {
        await modeRegistry.setActiveMode(mode as any, context);
      }

      const history = modeRegistry.getTransitionHistory();
      // Initial transition from 'none' + 6 mode transitions = 7 total
      // But if deactivations are counted separately, it could be more
      expect(history.length).toBeGreaterThanOrEqual(6);
      expect(modeRegistry.getActiveMode()?.id).toBe('learn');
    });

    it('should maintain context integrity during concurrent operations', async () => {
      // Simulate concurrent operations
      const operations = [
        () => fileContextProvider.setCurrentFile('test1.ts'),
        () => userContextProvider.recordAction({ type: 'edit', target: 'test1.ts' }),
        () => fileContextProvider.setCurrentFile('test2.ts'),
        () => userContextProvider.recordAction({ type: 'debug', target: 'breakpoint' }),
      ];

      await Promise.all(operations.map(op => op()));

      const context = await contextManager.refreshContext();

      // Context should remain consistent
      expect(context).toBeDefined();
      expect(context.userContext.recentActions).toHaveLength(2);
    });
  });
});
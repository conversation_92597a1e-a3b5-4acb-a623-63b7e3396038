import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ModeRegistry } from '../core/ModeRegistry';
import { ModeDefinition, ModeId } from '../types';

describe('ModeRegistry', () => {
  let registry: ModeRegistry;

  beforeEach(() => {
    registry = new ModeRegistry();
  });

  describe('register', () => {
    it('should register a new mode', () => {
      const testMode: ModeDefinition = {
        id: 'test' as ModeId,
        name: 'Test Mode',
        description: 'Test description',
        icon: '🧪',
        color: '#000000',
        contextRules: [],
        features: [],
        shortcuts: [],
      };

      registry.register(testMode);
      const mode = registry.getMode('test' as ModeId);

      expect(mode).toBeTruthy();
      expect(mode?.name).toBe('Test Mode');
    });

    it('should overwrite existing mode with warning', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      const mode1: ModeDefinition = {
        id: 'architect',
        name: 'Original',
        description: 'Original description',
        icon: '🏗️',
        color: '#000000',
        contextRules: [],
        features: [],
        shortcuts: [],
      };

      const mode2: ModeDefinition = {
        ...mode1,
        name: 'Updated',
      };

      registry.register(mode1);
      registry.register(mode2);

      const mode = registry.getMode('architect');
      expect(mode?.name).toBe('Updated');
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('unregister', () => {
    it('should remove a registered mode', () => {
      const mode = registry.getMode('architect');
      expect(mode).toBeTruthy();

      registry.unregister('architect');
      const removedMode = registry.getMode('architect');
      expect(removedMode).toBeNull();
    });

    it('should throw error when trying to unregister active mode', async () => {
      await registry.setActiveMode('architect');
      
      expect(() => {
        registry.unregister('architect');
      }).toThrow('Cannot unregister active mode');
    });
  });

  describe('getAllModes', () => {
    it('should return all registered modes', () => {
      const modes = registry.getAllModes();
      
      expect(modes).toHaveLength(6); // Default modes
      expect(modes.map(m => m.id)).toContain('architect');
      expect(modes.map(m => m.id)).toContain('debug');
      expect(modes.map(m => m.id)).toContain('review');
      expect(modes.map(m => m.id)).toContain('deploy');
      expect(modes.map(m => m.id)).toContain('experiment');
      expect(modes.map(m => m.id)).toContain('learn');
    });
  });

  describe('setActiveMode', () => {
    it('should activate a mode', async () => {
      await registry.setActiveMode('debug');
      
      const activeMode = registry.getActiveMode();
      expect(activeMode?.id).toBe('debug');
      expect(activeMode?.isActive).toBe(true);
    });

    it('should throw error for non-existent mode', async () => {
      await expect(
        registry.setActiveMode('nonexistent' as ModeId)
      ).rejects.toThrow('Mode nonexistent not found');
    });

    it('should not transition if already in target mode', async () => {
      await registry.setActiveMode('architect');
      const history1 = registry.getTransitionHistory();
      
      await registry.setActiveMode('architect');
      const history2 = registry.getTransitionHistory();
      
      expect(history1.length).toBe(history2.length);
    });

    it('should prevent concurrent transitions', async () => {
      const promise1 = registry.setActiveMode('debug');
      const promise2 = registry.setActiveMode('review');
      
      await expect(promise2).rejects.toThrow('Mode transition already in progress');
      await promise1; // Let first transition complete
    });
  });

  describe('getTransitionHistory', () => {
    it('should track mode transitions', async () => {
      await registry.setActiveMode('architect');
      await registry.setActiveMode('debug');
      await registry.setActiveMode('review');
      
      const history = registry.getTransitionHistory();
      
      expect(history).toHaveLength(3);
      expect(history[0].to).toBe('architect');
      expect(history[1].from).toBe('architect');
      expect(history[1].to).toBe('debug');
      expect(history[2].from).toBe('debug');
      expect(history[2].to).toBe('review');
    });

    it('should limit history to 100 entries', async () => {
      // Create more than 100 transitions
      for (let i = 0; i < 105; i++) {
        const modes: ModeId[] = ['architect', 'debug', 'review', 'deploy', 'experiment', 'learn'];
        await registry.setActiveMode(modes[i % modes.length]);
      }
      
      const history = registry.getTransitionHistory();
      expect(history).toHaveLength(100);
    });
  });

  describe('getModeUsageStats', () => {
    it('should calculate time spent in each mode', async () => {
      const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
      
      await registry.setActiveMode('architect');
      await delay(50);
      await registry.setActiveMode('debug');
      await delay(100);
      await registry.setActiveMode('review');
      
      const stats = registry.getModeUsageStats();
      
      expect(stats.architect).toBeGreaterThan(0);
      expect(stats.debug).toBeGreaterThan(0);
      expect(stats.debug).toBeGreaterThan(stats.architect);
      expect(stats.review).toBeGreaterThanOrEqual(0);
    });
  });

  describe('mode lifecycle', () => {
    it('should call activate and deactivate hooks', async () => {
      const activateMock = vi.fn();
      const deactivateMock = vi.fn();
      
      const customMode: ModeDefinition = {
        id: 'custom' as ModeId,
        name: 'Custom Mode',
        description: 'Custom mode with lifecycle hooks',
        icon: '🔧',
        color: '#FF0000',
        contextRules: [],
        features: [],
        shortcuts: [],
        activate: activateMock,
        deactivate: deactivateMock,
      };
      
      registry.register(customMode);
      
      await registry.setActiveMode('custom' as ModeId, {
        fileContext: {} as any,
        projectContext: {} as any,
        userContext: {} as any,
        environmentContext: {} as any,
        timestamp: Date.now(),
      });
      
      expect(activateMock).toHaveBeenCalledTimes(1);
      
      await registry.setActiveMode('architect');
      
      expect(deactivateMock).toHaveBeenCalledTimes(1);
    });
  });
});
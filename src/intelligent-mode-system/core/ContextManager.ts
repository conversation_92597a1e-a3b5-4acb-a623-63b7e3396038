import { ContextSnapshot } from '../types';
import { 
  fileContextProvider, 
  projectContextProvider, 
  gitContextProvider, 
  userContextProvider 
} from '../providers';
import { memoryCache } from './CacheSystem';
import { eventBus } from './EventBus';

export class ContextManager {
  private static instance: ContextManager;
  private lastContext: ContextSnapshot | null = null;
  private contextCacheKey = 'current-context';
  private contextTTL = 5000; // 5 seconds

  private constructor() {}

  static getInstance(): ContextManager {
    if (!ContextManager.instance) {
      ContextManager.instance = new ContextManager();
    }
    return ContextManager.instance;
  }

  async getContext(forceRefresh = false): Promise<ContextSnapshot> {
    // Check cache first
    if (!forceRefresh) {
      const cached = await memoryCache.get<ContextSnapshot>(this.contextCacheKey);
      if (cached) {
        return cached;
      }
    }

    // Gather context from all providers
    const [fileContext, projectContext, gitContext, userContext] = await Promise.all([
      fileContextProvider.analyze(),
      projectContextProvider.analyze(),
      gitContextProvider.analyze(),
      userContextProvider.analyze(),
    ]);

    const context: ContextSnapshot = {
      fileContext,
      projectContext,
      environmentContext: gitContext, // Git context is part of environment context
      userContext,
      timestamp: Date.now(),
    };

    // Cache the context
    await memoryCache.set(this.contextCacheKey, context, this.contextTTL);
    
    // Update last context
    this.lastContext = context;

    // Emit context update event
    eventBus.emit({ type: 'context:updated', payload: context, source: 'ContextManager' });

    return context;
  }

  async refreshContext(): Promise<ContextSnapshot> {
    return this.getContext(true);
  }

  getLastContext(): ContextSnapshot | null {
    return this.lastContext;
  }

  async invalidateCache(): Promise<void> {
    await memoryCache.invalidate(this.contextCacheKey);
    await this.refreshContext();
  }

  onContextUpdate(callback: (context: ContextSnapshot) => void): () => void {
    return eventBus.on('context:updated', callback);
  }
}

export const contextManager = ContextManager.getInstance();
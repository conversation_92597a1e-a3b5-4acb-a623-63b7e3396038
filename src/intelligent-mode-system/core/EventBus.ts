import { EventBus, EventHandler, EventType, SystemEvent, Unsubscribe } from '../types';

export class EventBusImpl implements EventBus {
  private handlers: Map<EventType, Set<EventHandler>> = new Map();
  private eventHistory: SystemEvent[] = [];
  private maxHistorySize = 1000;
  private eventCounter = 0;

  emit(event: Omit<SystemEvent, 'id' | 'timestamp'>): void {
    const fullEvent: SystemEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: Date.now(),
    };

    // Add to history
    this.addToHistory(fullEvent);

    // Get handlers for this event type
    const handlers = this.handlers.get(event.type);
    if (!handlers || handlers.size === 0) return;

    // Execute handlers
    handlers.forEach(handler => {
      try {
        Promise.resolve(handler(fullEvent)).catch(error => {
          console.error(`Error in event handler for ${event.type}:`, error);
        });
      } catch (error) {
        console.error(`Sync error in event handler for ${event.type}:`, error);
      }
    });
  }

  on(eventType: EventType | EventType[], handler: EventHandler): Unsubscribe {
    const types = Array.isArray(eventType) ? eventType : [eventType];

    types.forEach(type => {
      if (!this.handlers.has(type)) {
        this.handlers.set(type, new Set());
      }
      this.handlers.get(type)!.add(handler);
    });

    // Return unsubscribe function
    return () => {
      types.forEach(type => {
        const handlers = this.handlers.get(type);
        if (handlers) {
          handlers.delete(handler);
          if (handlers.size === 0) {
            this.handlers.delete(type);
          }
        }
      });
    };
  }

  once(eventType: EventType, handler: EventHandler): Unsubscribe {
    const wrappedHandler: EventHandler = (event) => {
      handler(event);
      unsubscribe();
    };

    const unsubscribe = this.on(eventType, wrappedHandler);
    return unsubscribe;
  }

  off(eventType: EventType, handler?: EventHandler): void {
    if (!handler) {
      // Remove all handlers for this event type
      this.handlers.delete(eventType);
    } else {
      // Remove specific handler
      const handlers = this.handlers.get(eventType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.handlers.delete(eventType);
        }
      }
    }
  }

  clear(): void {
    this.handlers.clear();
    this.eventHistory = [];
    this.eventCounter = 0;
  }

  // Additional utility methods
  getHistory(filter?: { type?: EventType; limit?: number }): SystemEvent[] {
    let history = [...this.eventHistory];

    if (filter?.type) {
      history = history.filter(event => event.type === filter.type);
    }

    if (filter?.limit) {
      history = history.slice(-filter.limit);
    }

    return history;
  }

  private generateEventId(): string {
    return `evt_${Date.now()}_${++this.eventCounter}`;
  }

  private addToHistory(event: SystemEvent): void {
    this.eventHistory.push(event);
    
    // Trim history if it exceeds max size
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }
  }

}

// Singleton instance
export const eventBus = new EventBusImpl();
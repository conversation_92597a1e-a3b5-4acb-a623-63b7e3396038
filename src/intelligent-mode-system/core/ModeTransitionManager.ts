import { ModeId, ContextSnapshot } from '../types';
import { ModeTransitionRule, TransitionCondition } from '../types/mode-features.types';
import { modeRegistry } from './ModeRegistry';
import { eventBus } from './EventBus';
import { performanceMonitor } from './PerformanceMonitor';

export class ModeTransitionManager {
  private transitionRules: ModeTransitionRule[] = [];
  private transitionInProgress = false;
  private automaticTransitionsEnabled = true;

  constructor() {
    this.initializeDefaultRules();
    this.setupEventListeners();
  }

  // Register a new transition rule
  registerRule(rule: ModeTransitionRule): void {
    this.transitionRules.push(rule);
    this.sortRulesByPriority();
  }

  // Remove a transition rule
  removeRule(fromMode: string, toMode: string): void {
    this.transitionRules = this.transitionRules.filter(
      rule => !(rule.fromMode === fromMode && rule.toMode === toMode)
    );
  }

  // Evaluate if a transition should occur
  async evaluateTransition(context: ContextSnapshot): Promise<ModeId | null> {
    if (!this.automaticTransitionsEnabled || this.transitionInProgress) {
      return null;
    }

    const currentMode = modeRegistry.getActiveMode();
    if (!currentMode) return null;

    performanceMonitor.startTimer('transition.evaluation');

    try {
      // Find applicable rules for current mode
      const applicableRules = this.transitionRules.filter(
        rule => rule.fromMode === currentMode.id && rule.automatic
      );

      // Evaluate each rule
      for (const rule of applicableRules) {
        const shouldTransition = await this.evaluateRule(rule, context);
        
        if (shouldTransition) {
          performanceMonitor.endTimer('transition.evaluation', {
            result: 'transition',
            toMode: rule.toMode,
          });
          
          return rule.toMode as ModeId;
        }
      }

      performanceMonitor.endTimer('transition.evaluation', {
        result: 'no-transition',
      });

      return null;
    } catch (error) {
      performanceMonitor.endTimer('transition.evaluation', {
        error: 'true',
      });
      console.error('Error evaluating transition:', error);
      return null;
    }
  }

  // Execute a mode transition
  async executeTransition(
    toMode: ModeId,
    context: ContextSnapshot,
    reason: string = 'automatic'
  ): Promise<boolean> {
    if (this.transitionInProgress) {
      console.warn('Transition already in progress');
      return false;
    }

    this.transitionInProgress = true;
    const startTime = Date.now();

    try {
      const currentMode = modeRegistry.getActiveMode();
      
      // Emit pre-transition event
      eventBus.emit({
        type: 'mode.transition.start',
        payload: {
          from: currentMode?.id,
          to: toMode,
          reason,
        },
        source: 'ModeTransitionManager',
      });

      // Execute the transition
      await modeRegistry.setActiveMode(toMode, context);

      // Record transition metrics
      const duration = Date.now() - startTime;
      performanceMonitor.recordMetric({
        name: 'mode.transition.duration',
        value: duration,
        unit: 'ms',
        timestamp: Date.now(),
        tags: {
          from: currentMode?.id || 'none',
          to: toMode,
          reason,
        },
      });

      // Emit post-transition event
      eventBus.emit({
        type: 'mode.transition.completed',
        payload: {
          from: currentMode?.id,
          to: toMode,
          duration,
          reason,
        },
        source: 'ModeTransitionManager',
      });

      return true;
    } catch (error) {
      console.error('Failed to execute transition:', error);
      
      eventBus.emit({
        type: 'mode.transition.failed',
        payload: {
          from: modeRegistry.getActiveMode()?.id,
          to: toMode,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        source: 'ModeTransitionManager',
      });

      return false;
    } finally {
      this.transitionInProgress = false;
    }
  }

  // Enable/disable automatic transitions
  setAutomaticTransitions(enabled: boolean): void {
    this.automaticTransitionsEnabled = enabled;
    
    eventBus.emit({
      type: 'mode.change',
      payload: {
        action: 'automatic-transitions',
        enabled,
      },
      source: 'ModeTransitionManager',
    });
  }

  // Get all transition rules
  getTransitionRules(): ModeTransitionRule[] {
    return [...this.transitionRules];
  }

  // Private methods
  private async evaluateRule(
    rule: ModeTransitionRule,
    context: ContextSnapshot
  ): Promise<boolean> {
    // All conditions must be met for the rule to apply
    for (const condition of rule.conditions) {
      const result = await this.evaluateCondition(condition, context);
      if (!result) {
        return false;
      }
    }
    
    return true;
  }

  private async evaluateCondition(
    condition: TransitionCondition,
    context: ContextSnapshot
  ): Promise<boolean> {
    try {
      return condition.evaluate(context);
    } catch (error) {
      console.error('Error evaluating condition:', error);
      return false;
    }
  }

  private sortRulesByPriority(): void {
    this.transitionRules.sort((a, b) => b.priority - a.priority);
  }

  private setupEventListeners(): void {
    // Listen for context changes to evaluate transitions
    eventBus.on('context.analyzed', async (event) => {
      if (event.payload.context) {
        const targetMode = await this.evaluateTransition(event.payload.context);
        
        if (targetMode) {
          await this.executeTransition(
            targetMode,
            event.payload.context,
            'context-change'
          );
        }
      }
    });

    // Listen for user actions that might trigger transitions
    eventBus.on('user.action', async (event) => {
      if (event.payload.action?.type === 'mode-hint') {
        // User is hinting at a mode change
        const hintedMode = event.payload.action.metadata?.mode;
        if (hintedMode && this.isValidMode(hintedMode)) {
          const context = event.payload.context;
          await this.executeTransition(hintedMode, context, 'user-hint');
        }
      }
    });
  }

  private isValidMode(modeId: string): modeId is ModeId {
    const validModes: ModeId[] = ['architect', 'debug', 'review', 'deploy', 'experiment', 'learn'];
    return validModes.includes(modeId as ModeId);
  }

  private initializeDefaultRules(): void {
    // Architect → Debug: When errors are detected
    this.registerRule({
      fromMode: 'architect',
      toMode: 'debug',
      conditions: [
        {
          type: 'context',
          evaluate: (context) => context.fileContext.hasErrors === true,
          description: 'Errors detected in file',
        },
      ],
      priority: 90,
      automatic: true,
    });

    // Debug → Review: When errors are fixed and changes are ready
    this.registerRule({
      fromMode: 'debug',
      toMode: 'review',
      conditions: [
        {
          type: 'context',
          evaluate: (context) => !context.fileContext.hasErrors,
          description: 'No errors in file',
        },
        {
          type: 'context',
          evaluate: (context) => (context.environmentContext.gitStatus?.modifiedFiles || 0) > 0,
          description: 'Uncommitted changes exist',
        },
      ],
      priority: 80,
      automatic: true,
    });

    // Review → Deploy: When on deployment branch with approved changes
    this.registerRule({
      fromMode: 'review',
      toMode: 'deploy',
      conditions: [
        {
          type: 'context',
          evaluate: (context) => {
            const branch = context.environmentContext.gitStatus?.branch || '';
            return ['main', 'master', 'release'].includes(branch);
          },
          description: 'On deployment branch',
        },
        {
          type: 'user',
          evaluate: (context) => {
            const recentActions = context.userContext.recentActions;
            return recentActions.some(a => 
              a.type === 'command' && 
              (a.target?.includes('merge') || a.target?.includes('approve'))
            );
          },
          description: 'Recent merge or approval action',
        },
      ],
      priority: 85,
      automatic: true,
    });

    // Any → Experiment: When working with scratch files
    ['architect', 'debug', 'review', 'deploy', 'learn'].forEach(fromMode => {
      this.registerRule({
        fromMode,
        toMode: 'experiment',
        conditions: [
          {
            type: 'context',
            evaluate: (context) => {
              const path = context.fileContext.path?.toLowerCase() || '';
              return path.includes('scratch') || 
                     path.includes('temp') || 
                     path.includes('playground');
            },
            description: 'Working with experimental files',
          },
        ],
        priority: 70,
        automatic: true,
      });
    });

    // Any → Learn: When accessing documentation
    ['architect', 'debug', 'review', 'deploy', 'experiment'].forEach(fromMode => {
      this.registerRule({
        fromMode,
        toMode: 'learn',
        conditions: [
          {
            type: 'context',
            evaluate: (context) => {
              const path = context.fileContext.path?.toLowerCase() || '';
              return path.includes('readme') || 
                     path.includes('docs') || 
                     path.endsWith('.md');
            },
            description: 'Viewing documentation',
          },
          {
            type: 'user',
            evaluate: (context) => {
              const searchActions = context.userContext.recentActions.filter(
                a => a.type === 'search'
              );
              return searchActions.length > 3;
            },
            description: 'Multiple documentation searches',
          },
        ],
        priority: 60,
        automatic: true,
      });
    });

    // Deploy → Architect: After successful deployment
    this.registerRule({
      fromMode: 'deploy',
      toMode: 'architect',
      conditions: [
        {
          type: 'event',
          evaluate: (context, data) => {
            // This would be triggered by deployment success event
            return data?.deploymentStatus === 'success';
          },
          description: 'Deployment completed successfully',
        },
      ],
      priority: 75,
      automatic: true,
    });

    // Experiment → Previous Mode: When leaving experimental context
    this.registerRule({
      fromMode: 'experiment',
      toMode: 'architect', // Default fallback
      conditions: [
        {
          type: 'time',
          evaluate: (context) => {
            const currentFile = context.fileContext.path?.toLowerCase() || '';
            return !currentFile.includes('scratch') && 
                   !currentFile.includes('temp') && 
                   !currentFile.includes('playground');
          },
          description: 'Left experimental context',
        },
      ],
      priority: 50,
      automatic: true,
    });
  }
}

// Singleton instance
export const modeTransitionManager = new ModeTransitionManager();
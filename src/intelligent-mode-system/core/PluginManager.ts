/**
 * Plugin Manager - High-level plugin management interface
 * 
 * Provides a unified interface for managing plugins, handling
 * installation, updates, and coordination between registry and loader.
 */

import { pluginRegistry, Plugin, PluginManifest } from './PluginRegistry';
import { pluginLoader, PluginSource, PluginUpdateInfo } from '../plugins/PluginLoader';
import { eventBus } from './EventBus';
import { performanceMonitor } from '../utils/performance';
import { ContextSnapshot } from '../types';

export interface PluginInfo {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  type: string;
  state: 'active' | 'inactive' | 'error' | 'updating';
  hasUpdates: boolean;
  dependencies: string[];
  capabilities: string[];
  lastUsed?: number;
  installDate: number;
}

export interface InstallOptions {
  force?: boolean;
  autoActivate?: boolean;
  skipDependencyCheck?: boolean;
}

export interface PluginStats {
  totalPlugins: number;
  activePlugins: number;
  errorPlugins: number;
  availableUpdates: number;
  memoryUsage: number;
  avgLoadTime: number;
}

class PluginManager {
  private static instance: PluginManager;
  
  // Plugin state tracking
  private pluginInfo = new Map<string, PluginInfo>();
  private loadTimes = new Map<string, number>();
  private updateCheckInterval: NodeJS.Timeout | null = null;
  
  // Configuration
  private config = {
    autoUpdateCheck: true,
    updateCheckInterval: 24 * 60 * 60 * 1000, // 24 hours
    autoLoadBuiltins: true,
    maxConcurrentOperations: 3,
  };
  
  private constructor() {
    this.setupEventListeners();
    this.startAutoUpdateCheck();
  }
  
  static getInstance(): PluginManager {
    if (!PluginManager.instance) {
      PluginManager.instance = new PluginManager();
    }
    return PluginManager.instance;
  }
  
  /**
   * Initialize plugin system
   */
  async initialize(): Promise<void> {
    performanceMonitor.startTimer('plugin_manager.initialize');
    
    try {
      // Load built-in plugins if enabled
      if (this.config.autoLoadBuiltins) {
        await this.loadBuiltinPlugins();
      }
      
      // Discover and load available plugins from known sources
      await this.discoverAndLoadPlugins();
      
      // Perform initial update check
      if (this.config.autoUpdateCheck) {
        setTimeout(() => this.checkForUpdates(), 5000); // Delay to avoid startup overhead
      }
      
      eventBus.emit({
        type: 'plugin_manager.initialized',
        payload: { stats: this.getStats() },
        source: 'PluginManager',
      });
      
      performanceMonitor.endTimer('plugin_manager.initialize');
    } catch (error) {
      performanceMonitor.endTimer('plugin_manager.initialize', { error: 'true' });
      throw error;
    }
  }
  
  /**\n   * Install a plugin from various sources\n   */\n  async installPlugin(source: PluginSource, options: InstallOptions = {}): Promise<PluginInfo> {\n    performanceMonitor.startTimer('plugin_manager.install');\n    \n    try {\n      // Load plugin from source\n      const plugins = await pluginLoader.loadFromSource(source, {\n        force: options.force,\n        skipDependencyCheck: options.skipDependencyCheck,\n      });\n      \n      if (plugins.length === 0) {\n        throw new Error('No plugins found at source');\n      }\n      \n      const plugin = plugins[0]; // Install first plugin found\n      const startTime = Date.now();\n      \n      // Create plugin info\n      const info: PluginInfo = {\n        id: plugin.manifest.id,\n        name: plugin.manifest.name,\n        version: plugin.manifest.version,\n        description: plugin.manifest.description,\n        author: plugin.manifest.author,\n        type: plugin.manifest.type,\n        state: 'inactive',\n        hasUpdates: false,\n        dependencies: plugin.manifest.dependencies,\n        capabilities: plugin.getCapabilities().map(c => c.name),\n        installDate: Date.now(),\n      };\n      \n      this.pluginInfo.set(plugin.manifest.id, info);\n      this.loadTimes.set(plugin.manifest.id, Date.now() - startTime);\n      \n      // Auto-activate if requested\n      if (options.autoActivate) {\n        await this.activatePlugin(plugin.manifest.id);\n      }\n      \n      eventBus.emit({\n        type: 'plugin_manager.installed',\n        payload: { pluginInfo: info },\n        source: 'PluginManager',\n      });\n      \n      performanceMonitor.endTimer('plugin_manager.install', { pluginId: plugin.manifest.id });\n      return info;\n      \n    } catch (error) {\n      performanceMonitor.endTimer('plugin_manager.install', { error: 'true' });\n      throw error;\n    }\n  }\n  \n  /**\n   * Uninstall a plugin\n   */\n  async uninstallPlugin(pluginId: string): Promise<void> {\n    try {\n      // Deactivate if active\n      if (this.isPluginActive(pluginId)) {\n        await this.deactivatePlugin(pluginId);\n      }\n      \n      // Uninstall via loader\n      await pluginLoader.uninstallPlugin(pluginId);\n      \n      // Clean up local state\n      this.pluginInfo.delete(pluginId);\n      this.loadTimes.delete(pluginId);\n      \n      eventBus.emit({\n        type: 'plugin_manager.uninstalled',\n        payload: { pluginId },\n        source: 'PluginManager',\n      });\n    } catch (error) {\n      console.error(`Failed to uninstall plugin ${pluginId}:`, error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Activate a plugin\n   */\n  async activatePlugin(pluginId: string): Promise<void> {\n    try {\n      await pluginRegistry.activatePlugin(pluginId);\n      \n      const info = this.pluginInfo.get(pluginId);\n      if (info) {\n        info.state = 'active';\n        info.lastUsed = Date.now();\n      }\n      \n      eventBus.emit({\n        type: 'plugin_manager.activated',\n        payload: { pluginId },\n        source: 'PluginManager',\n      });\n    } catch (error) {\n      const info = this.pluginInfo.get(pluginId);\n      if (info) {\n        info.state = 'error';\n      }\n      throw error;\n    }\n  }\n  \n  /**\n   * Deactivate a plugin\n   */\n  async deactivatePlugin(pluginId: string): Promise<void> {\n    try {\n      await pluginRegistry.deactivatePlugin(pluginId);\n      \n      const info = this.pluginInfo.get(pluginId);\n      if (info) {\n        info.state = 'inactive';\n      }\n      \n      eventBus.emit({\n        type: 'plugin_manager.deactivated',\n        payload: { pluginId },\n        source: 'PluginManager',\n      });\n    } catch (error) {\n      const info = this.pluginInfo.get(pluginId);\n      if (info) {\n        info.state = 'error';\n      }\n      throw error;\n    }\n  }\n  \n  /**\n   * Update a plugin to the latest version\n   */\n  async updatePlugin(pluginId: string): Promise<PluginInfo> {\n    const info = this.pluginInfo.get(pluginId);\n    if (!info) {\n      throw new Error(`Plugin ${pluginId} not found`);\n    }\n    \n    try {\n      info.state = 'updating';\n      \n      const updatedPlugin = await pluginLoader.updatePlugin(pluginId);\n      \n      // Update plugin info\n      info.version = updatedPlugin.manifest.version;\n      info.hasUpdates = false;\n      info.state = this.isPluginActive(pluginId) ? 'active' : 'inactive';\n      \n      eventBus.emit({\n        type: 'plugin_manager.updated',\n        payload: { pluginId, newVersion: info.version },\n        source: 'PluginManager',\n      });\n      \n      return info;\n    } catch (error) {\n      info.state = 'error';\n      throw error;\n    }\n  }\n  \n  /**\n   * Check for plugin updates\n   */\n  async checkForUpdates(): Promise<PluginUpdateInfo[]> {\n    try {\n      const updates = await pluginLoader.checkForUpdates();\n      \n      // Update plugin info with update availability\n      updates.forEach(update => {\n        const info = this.pluginInfo.get(update.pluginId);\n        if (info) {\n          info.hasUpdates = true;\n        }\n      });\n      \n      if (updates.length > 0) {\n        eventBus.emit({\n          type: 'plugin_manager.updates_available',\n          payload: { updates },\n          source: 'PluginManager',\n        });\n      }\n      \n      return updates;\n    } catch (error) {\n      console.error('Failed to check for updates:', error);\n      return [];\n    }\n  }\n  \n  /**\n   * Get plugin information\n   */\n  getPluginInfo(pluginId: string): PluginInfo | undefined {\n    return this.pluginInfo.get(pluginId);\n  }\n  \n  getAllPluginInfo(): PluginInfo[] {\n    return Array.from(this.pluginInfo.values());\n  }\n  \n  getActivePluginInfo(): PluginInfo[] {\n    return this.getAllPluginInfo().filter(info => info.state === 'active');\n  }\n  \n  getPluginsByType(type: string): PluginInfo[] {\n    return this.getAllPluginInfo().filter(info => info.type === type);\n  }\n  \n  /**\n   * Plugin state queries\n   */\n  isPluginInstalled(pluginId: string): boolean {\n    return this.pluginInfo.has(pluginId);\n  }\n  \n  isPluginActive(pluginId: string): boolean {\n    return pluginRegistry.getActivePlugins().some(p => p.manifest.id === pluginId);\n  }\n  \n  hasPluginUpdates(pluginId: string): boolean {\n    const info = this.pluginInfo.get(pluginId);\n    return info?.hasUpdates || false;\n  }\n  \n  /**\n   * Batch operations\n   */\n  async activatePlugins(pluginIds: string[]): Promise<{ success: string[]; failed: string[] }> {\n    const success: string[] = [];\n    const failed: string[] = [];\n    \n    // Process in batches to avoid overwhelming the system\n    const batchSize = this.config.maxConcurrentOperations;\n    \n    for (let i = 0; i < pluginIds.length; i += batchSize) {\n      const batch = pluginIds.slice(i, i + batchSize);\n      \n      const promises = batch.map(async (pluginId) => {\n        try {\n          await this.activatePlugin(pluginId);\n          success.push(pluginId);\n        } catch (error) {\n          console.error(`Failed to activate plugin ${pluginId}:`, error);\n          failed.push(pluginId);\n        }\n      });\n      \n      await Promise.allSettled(promises);\n    }\n    \n    return { success, failed };\n  }\n  \n  async updateAllPlugins(): Promise<{ success: string[]; failed: string[] }> {\n    const pluginsWithUpdates = this.getAllPluginInfo()\n      .filter(info => info.hasUpdates)\n      .map(info => info.id);\n    \n    const success: string[] = [];\n    const failed: string[] = [];\n    \n    for (const pluginId of pluginsWithUpdates) {\n      try {\n        await this.updatePlugin(pluginId);\n        success.push(pluginId);\n      } catch (error) {\n        console.error(`Failed to update plugin ${pluginId}:`, error);\n        failed.push(pluginId);\n      }\n    }\n    \n    return { success, failed };\n  }\n  \n  /**\n   * Context-aware plugin recommendations\n   */\n  async getRecommendedPlugins(context: ContextSnapshot): Promise<PluginInfo[]> {\n    const recommendations: PluginInfo[] = [];\n    const activePlugins = this.getActivePluginInfo();\n    \n    // Analyze context to recommend relevant inactive plugins\n    const inactivePlugins = this.getAllPluginInfo()\n      .filter(info => info.state === 'inactive');\n    \n    for (const info of inactivePlugins) {\n      const plugin = pluginRegistry.getPlugin(info.id);\n      if (!plugin) continue;\n      \n      // Check if plugin would be applicable to current context\n      const capabilities = plugin.getCapabilities();\n      const modeCapability = capabilities.find(c => c.type === 'mode');\n      \n      if (modeCapability) {\n        // For mode plugins, check if they would be applicable\n        const provider = pluginRegistry.getModeProvider(modeCapability.name as any);\n        if (provider && provider.isApplicable(context)) {\n          recommendations.push(info);\n        }\n      }\n    }\n    \n    // Sort by relevance (simplified scoring)\n    recommendations.sort((a, b) => {\n      const scoreA = this.calculateRelevanceScore(a, context);\n      const scoreB = this.calculateRelevanceScore(b, context);\n      return scoreB - scoreA;\n    });\n    \n    return recommendations.slice(0, 5); // Top 5 recommendations\n  }\n  \n  /**\n   * Statistics and metrics\n   */\n  getStats(): PluginStats {\n    const allPlugins = this.getAllPluginInfo();\n    const activePlugins = allPlugins.filter(p => p.state === 'active');\n    const errorPlugins = allPlugins.filter(p => p.state === 'error');\n    const pluginsWithUpdates = allPlugins.filter(p => p.hasUpdates);\n    \n    const loadTimes = Array.from(this.loadTimes.values());\n    const avgLoadTime = loadTimes.length > 0 \n      ? loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length \n      : 0;\n    \n    return {\n      totalPlugins: allPlugins.length,\n      activePlugins: activePlugins.length,\n      errorPlugins: errorPlugins.length,\n      availableUpdates: pluginsWithUpdates.length,\n      memoryUsage: this.estimateMemoryUsage(),\n      avgLoadTime,\n    };\n  }\n  \n  /**\n   * Private methods\n   */\n  \n  private async loadBuiltinPlugins(): Promise<void> {\n    try {\n      const builtinSource: PluginSource = {\n        type: 'builtin',\n        location: 'builtin',\n      };\n      \n      await pluginLoader.loadFromSource(builtinSource, { autoActivate: true } as any);\n    } catch (error) {\n      console.warn('Failed to load builtin plugins:', error);\n    }\n  }\n  \n  private async discoverAndLoadPlugins(): Promise<void> {\n    // In a real implementation, this would scan known plugin directories\n    // For now, it's a placeholder for automatic plugin discovery\n    try {\n      // Example: scan user plugins directory\n      // await this.scanUserPlugins();\n    } catch (error) {\n      console.warn('Plugin discovery failed:', error);\n    }\n  }\n  \n  private calculateRelevanceScore(info: PluginInfo, context: ContextSnapshot): number {\n    let score = 0;\n    \n    // File type relevance\n    if (info.type === 'analyzer' && context.fileContext.hasErrors) {\n      score += 0.5;\n    }\n    \n    // Last used relevance\n    if (info.lastUsed) {\n      const daysSinceUsed = (Date.now() - info.lastUsed) / (24 * 60 * 60 * 1000);\n      score += Math.max(0, 0.3 - (daysSinceUsed * 0.05));\n    }\n    \n    // Capability relevance (simplified)\n    if (info.capabilities.includes('research') && \n        context.fileContext.path.endsWith('.md')) {\n      score += 0.4;\n    }\n    \n    return score;\n  }\n  \n  private estimateMemoryUsage(): number {\n    // Simple estimation based on active plugins\n    const activeCount = this.getActivePluginInfo().length;\n    return activeCount * 2; // Assume 2MB per active plugin\n  }\n  \n  private startAutoUpdateCheck(): void {\n    if (!this.config.autoUpdateCheck) return;\n    \n    this.updateCheckInterval = setInterval(() => {\n      this.checkForUpdates().catch(error => {\n        console.warn('Automatic update check failed:', error);\n      });\n    }, this.config.updateCheckInterval);\n  }\n  \n  private setupEventListeners(): void {\n    // Update plugin states when registry events occur\n    eventBus.on('plugin.activated', (event) => {\n      const { pluginId } = event.payload;\n      const info = this.pluginInfo.get(pluginId);\n      if (info) {\n        info.state = 'active';\n        info.lastUsed = Date.now();\n      }\n    });\n    \n    eventBus.on('plugin.deactivated', (event) => {\n      const { pluginId } = event.payload;\n      const info = this.pluginInfo.get(pluginId);\n      if (info) {\n        info.state = 'inactive';\n      }\n    });\n    \n    eventBus.on('plugin.registered', (event) => {\n      const { pluginId, manifest } = event.payload;\n      \n      if (!this.pluginInfo.has(pluginId)) {\n        const info: PluginInfo = {\n          id: manifest.id,\n          name: manifest.name,\n          version: manifest.version,\n          description: manifest.description,\n          author: manifest.author,\n          type: manifest.type,\n          state: 'inactive',\n          hasUpdates: false,\n          dependencies: manifest.dependencies,\n          capabilities: [],\n          installDate: Date.now(),\n        };\n        \n        this.pluginInfo.set(pluginId, info);\n      }\n    });\n    \n    // Clean up on shutdown\n    eventBus.on('system.shutdown', () => {\n      if (this.updateCheckInterval) {\n        clearInterval(this.updateCheckInterval);\n      }\n    });\n  }\n  \n  /**\n   * Configuration\n   */\n  updateConfig(updates: Partial<typeof this.config>): void {\n    this.config = { ...this.config, ...updates };\n    \n    // Restart auto-update if interval changed\n    if (updates.autoUpdateCheck !== undefined || updates.updateCheckInterval !== undefined) {\n      if (this.updateCheckInterval) {\n        clearInterval(this.updateCheckInterval);\n      }\n      this.startAutoUpdateCheck();\n    }\n  }\n  \n  getConfig(): typeof this.config {\n    return { ...this.config };\n  }\n}\n\n// Export singleton instance\nexport const pluginManager = PluginManager.getInstance();
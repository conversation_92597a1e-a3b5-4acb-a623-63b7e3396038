import { CacheStrategy, CacheStats } from '../types';
import { eventBus } from './EventBus';

interface CacheEntry<T> {
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  size: number;
}

export class CacheSystem {
  private memoryCache: Map<string, CacheEntry<any>> = new Map();
  private strategy: CacheStrategy;
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    size: 0,
    itemCount: 0,
    evictions: 0,
  };

  constructor(strategy: CacheStrategy) {
    this.strategy = strategy;
    this.initializeStorage();
  }

  async get<T>(key: string): Promise<T | null> {
    const entry = await this.getEntry(key);

    if (!entry) {
      this.stats.misses++;
      eventBus.emit({ type: 'cache.miss', payload: { key }, source: 'CacheSystem' });
      return null;
    }

    // Check TTL
    if (this.isExpired(entry)) {
      await this.delete(key);
      this.stats.misses++;
      eventBus.emit({ type: 'cache.miss', payload: { key, reason: 'expired' }, source: 'CacheSystem' });
      return null;
    }

    // Update access info
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    this.stats.hits++;
    eventBus.emit({ type: 'cache.hit', payload: { key }, source: 'CacheSystem' });

    return entry.value;
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const size = this.estimateSize(value);
    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      accessCount: 0,
      lastAccessed: Date.now(),
      size,
    };

    // Check if we need to evict items
    await this.ensureSpace(size);

    // Store based on strategy
    await this.setEntry(key, entry);

    // Update stats
    this.stats.size += size;
    this.stats.itemCount++;
  }

  async invalidate(pattern: string): Promise<void> {
    const regex = new RegExp(pattern);
    const keysToDelete: string[] = [];

    // Find matching keys
    if (this.strategy.type === 'memory') {
      for (const key of this.memoryCache.keys()) {
        if (regex.test(key)) {
          keysToDelete.push(key);
        }
      }
    } else if (this.strategy.type === 'indexed-db') {
      // Implementation for IndexedDB pattern matching
      const keys = await this.getAllKeysFromIndexedDB();
      keysToDelete.push(...keys.filter(key => regex.test(key)));
    }

    // Delete matching entries
    for (const key of keysToDelete) {
      await this.delete(key);
    }
  }

  async clear(): Promise<void> {
    if (this.strategy.type === 'memory') {
      this.memoryCache.clear();
    } else if (this.strategy.type === 'indexed-db') {
      await this.clearIndexedDB();
    } else if (this.strategy.type === 'session') {
      sessionStorage.clear();
    }

    this.stats = {
      hits: 0,
      misses: 0,
      size: 0,
      itemCount: 0,
      evictions: 0,
    };
  }

  getStats(): CacheStats {
    return { ...this.stats };
  }

  private async getEntry(key: string): Promise<CacheEntry<any> | null> {
    switch (this.strategy.type) {
      case 'memory':
        return this.memoryCache.get(key) || null;
      
      case 'indexed-db':
        return await this.getFromIndexedDB(key);
      
      case 'session':
        const data = sessionStorage.getItem(key);
        return data ? JSON.parse(data) : null;
      
      default:
        return null;
    }
  }

  private async setEntry(key: string, entry: CacheEntry<any>): Promise<void> {
    switch (this.strategy.type) {
      case 'memory':
        this.memoryCache.set(key, entry);
        break;
      
      case 'indexed-db':
        await this.setToIndexedDB(key, entry);
        break;
      
      case 'session':
        sessionStorage.setItem(key, JSON.stringify(entry));
        break;
    }
  }

  private async delete(key: string): Promise<void> {
    const entry = await this.getEntry(key);
    if (!entry) return;

    switch (this.strategy.type) {
      case 'memory':
        this.memoryCache.delete(key);
        break;
      
      case 'indexed-db':
        await this.deleteFromIndexedDB(key);
        break;
      
      case 'session':
        sessionStorage.removeItem(key);
        break;
    }

    this.stats.size -= entry.size;
    this.stats.itemCount--;
  }

  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > this.strategy.ttl;
  }

  private estimateSize(value: any): number {
    // Simple size estimation based on JSON stringification
    try {
      return JSON.stringify(value).length * 2; // 2 bytes per character
    } catch {
      return 1000; // Default size for non-serializable objects
    }
  }

  private async ensureSpace(requiredSize: number): Promise<void> {
    if (this.stats.size + requiredSize <= this.strategy.maxSize) {
      return;
    }

    // Need to evict items based on policy
    const itemsToEvict = await this.selectItemsToEvict(requiredSize);
    
    for (const key of itemsToEvict) {
      await this.delete(key);
      this.stats.evictions++;
    }
  }

  private async selectItemsToEvict(requiredSize: number): Promise<string[]> {
    const entries: Array<[string, CacheEntry<any>]> = [];
    
    if (this.strategy.type === 'memory') {
      entries.push(...Array.from(this.memoryCache.entries()));
    } else {
      // Load all entries for other storage types
      const keys = await this.getAllKeys();
      for (const key of keys) {
        const entry = await this.getEntry(key);
        if (entry) entries.push([key, entry]);
      }
    }

    // Sort based on eviction policy
    switch (this.strategy.evictionPolicy) {
      case 'lru':
        entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
        break;
      
      case 'lfu':
        entries.sort((a, b) => a[1].accessCount - b[1].accessCount);
        break;
      
      case 'fifo':
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        break;
    }

    // Select items to evict
    const keysToEvict: string[] = [];
    let freedSpace = 0;

    for (const [key, entry] of entries) {
      keysToEvict.push(key);
      freedSpace += entry.size;
      
      if (this.stats.size - freedSpace + requiredSize <= this.strategy.maxSize) {
        break;
      }
    }

    return keysToEvict;
  }

  private async getAllKeys(): Promise<string[]> {
    switch (this.strategy.type) {
      case 'memory':
        return Array.from(this.memoryCache.keys());
      
      case 'indexed-db':
        return await this.getAllKeysFromIndexedDB();
      
      case 'session':
        return Object.keys(sessionStorage);
      
      default:
        return [];
    }
  }

  // IndexedDB implementation stubs
  private async initializeStorage(): Promise<void> {
    if (this.strategy.type === 'indexed-db') {
      // Initialize IndexedDB
      // Implementation would go here
    }
  }

  private async getFromIndexedDB(key: string): Promise<CacheEntry<any> | null> {
    // Implementation for IndexedDB get
    return null;
  }

  private async setToIndexedDB(key: string, entry: CacheEntry<any>): Promise<void> {
    // Implementation for IndexedDB set
  }

  private async deleteFromIndexedDB(key: string): Promise<void> {
    // Implementation for IndexedDB delete
  }

  private async getAllKeysFromIndexedDB(): Promise<string[]> {
    // Implementation for IndexedDB keys
    return [];
  }

  private async clearIndexedDB(): Promise<void> {
    // Implementation for IndexedDB clear
  }
}

// Create default cache instances
export const memoryCache = new CacheSystem({
  type: 'memory',
  ttl: 3600000, // 1 hour
  maxSize: 50 * 1024 * 1024, // 50MB
  evictionPolicy: 'lru',
});

export const persistentCache = new CacheSystem({
  type: 'indexed-db',
  ttl: 86400000, // 24 hours
  maxSize: 100 * 1024 * 1024, // 100MB
  evictionPolicy: 'lru',
});

// Export as cacheSystem for backward compatibility
export const cacheSystem = persistentCache;
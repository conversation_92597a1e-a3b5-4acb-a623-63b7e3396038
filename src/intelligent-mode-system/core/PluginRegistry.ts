/**
 * Plugin Registry for Extensible Mode System
 * 
 * Features:
 * - Dynamic plugin registration and loading
 * - Type-safe plugin interfaces
 * - Dependency management and resolution
 * - Plugin lifecycle management
 * - Hot reloading capabilities
 * - Conflict resolution and validation
 */

import { eventBus } from './EventBus';
import { performanceMonitor } from '../utils/performance';
import { ContextSnapshot, ModeId } from '../types';

export interface PluginManifest {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  type: 'mode' | 'provider' | 'analyzer' | 'ui' | 'integration';
  dependencies: string[];
  optionalDependencies: string[];
  permissions: PluginPermission[];
  config?: Record<string, any>;
  apiVersion: string;
}

export interface PluginPermission {
  type: 'context.read' | 'context.write' | 'ui.render' | 'system.notifications' | 'file.access' | 'network.request';
  scope?: string;
  description: string;
}

export interface Plugin {
  manifest: PluginManifest;
  initialize(context: PluginContext): Promise<void>;
  activate(): Promise<void>;
  deactivate(): Promise<void>;
  destroy(): Promise<void>;
  getCapabilities(): PluginCapability[];
}

export interface PluginContext {
  eventBus: typeof eventBus;
  registerModeProvider?: (provider: ModeProvider) => void;
  registerContextAnalyzer?: (analyzer: ContextAnalyzer) => void;
  registerUIComponent?: (component: UIComponent) => void;
  getSystemInfo(): SystemInfo;
  requestPermission(permission: PluginPermission): Promise<boolean>;
}

export interface PluginCapability {
  type: 'mode' | 'analyzer' | 'ui' | 'integration';
  name: string;
  config?: Record<string, any>;
}

export interface ModeProvider {
  id: ModeId;
  displayName: string;
  description: string;
  icon: string;
  color: string;
  isApplicable(context: ContextSnapshot): boolean;
  getScore(context: ContextSnapshot): number;
  getSuggestions(context: ContextSnapshot, input?: string): Promise<string[]>;
  getActions(context: ContextSnapshot): PluginAction[];
}

export interface ContextAnalyzer {
  id: string;
  name: string;
  analyze(context: ContextSnapshot): Promise<AnalysisResult>;
  getFeatures(context: ContextSnapshot): Record<string, number>;
  priority: number;
}

export interface UIComponent {
  id: string;
  name: string;
  type: 'panel' | 'overlay' | 'sidebar' | 'statusbar' | 'menu';
  render(props: any): React.ComponentType;
  isVisible(context: ContextSnapshot): boolean;
}

export interface PluginAction {
  id: string;
  label: string;
  description: string;
  icon?: string;
  shortcut?: string;
  execute(context: ContextSnapshot): Promise<void>;
}

export interface AnalysisResult {
  score: number;
  confidence: number;
  features: Record<string, number>;
  metadata?: Record<string, any>;
}

export interface SystemInfo {
  version: string;
  platform: string;
  capabilities: string[];
  limits: {
    maxPlugins: number;
    maxMemoryMB: number;
    allowedPermissions: PluginPermission['type'][];
  };
}

export interface PluginLoadOptions {
  force?: boolean;
  skipDependencyCheck?: boolean;
  dryRun?: boolean;
}

class PluginRegistry {
  private static instance: PluginRegistry;
  
  // Plugin storage
  private plugins = new Map<string, Plugin>();
  private activePlugins = new Set<string>();
  private pluginStates = new Map<string, 'loading' | 'active' | 'inactive' | 'error'>();
  private pluginErrors = new Map<string, Error>();
  
  // Plugin capabilities
  private modeProviders = new Map<ModeId, ModeProvider>();
  private contextAnalyzers = new Map<string, ContextAnalyzer>();
  private uiComponents = new Map<string, UIComponent>();
  
  // Dependency management
  private dependencyGraph = new Map<string, Set<string>>();
  private loadOrder: string[] = [];
  
  // Configuration
  private readonly config = {
    maxPlugins: 50,
    maxMemoryMB: 100,
    allowedPermissions: [
      'context.read',
      'context.write', 
      'ui.render',
      'system.notifications'
    ] as PluginPermission['type'][],
    hotReloadEnabled: true,
    sandboxed: true,
  };
  
  private constructor() {
    this.setupEventListeners();
  }
  
  static getInstance(): PluginRegistry {
    if (!PluginRegistry.instance) {
      PluginRegistry.instance = new PluginRegistry();
    }
    return PluginRegistry.instance;
  }
  
  /**
   * Register a plugin with the system
   */
  async registerPlugin(plugin: Plugin, options: PluginLoadOptions = {}): Promise<void> {
    performanceMonitor.startTimer('plugin.register');
    
    try {
      const { manifest } = plugin;
      
      // Validate plugin
      this.validatePlugin(plugin);
      
      // Check dependencies unless skipped
      if (!options.skipDependencyCheck) {
        await this.validateDependencies(manifest);
      }
      
      // Check for conflicts
      if (this.plugins.has(manifest.id) && !options.force) {
        throw new Error(`Plugin ${manifest.id} is already registered`);
      }
      
      // Dry run check
      if (options.dryRun) {
        return;
      }
      
      // Store plugin
      this.plugins.set(manifest.id, plugin);
      this.pluginStates.set(manifest.id, 'loading');
      
      // Update dependency graph
      this.updateDependencyGraph(manifest);
      
      // Initialize plugin
      const context = this.createPluginContext(manifest);
      await plugin.initialize(context);
      
      this.pluginStates.set(manifest.id, 'inactive');
      
      // Emit registration event
      eventBus.emit({
        type: 'plugin.registered',
        payload: { pluginId: manifest.id, manifest },
        source: 'PluginRegistry',
      });
      
      performanceMonitor.endTimer('plugin.register', { pluginId: manifest.id });
    } catch (error) {
      this.pluginErrors.set(plugin.manifest.id, error as Error);
      this.pluginStates.set(plugin.manifest.id, 'error');
      performanceMonitor.endTimer('plugin.register', { error: 'true', pluginId: plugin.manifest.id });
      throw error;
    }
  }
  
  /**
   * Activate a plugin
   */
  async activatePlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      throw new Error(`Plugin ${pluginId} not found`);
    }
    
    if (this.activePlugins.has(pluginId)) {
      return; // Already active
    }
    
    try {
      // Activate dependencies first
      await this.activateDependencies(plugin.manifest);
      
      // Activate plugin
      this.pluginStates.set(pluginId, 'loading');
      await plugin.activate();
      
      // Register capabilities
      this.registerPluginCapabilities(plugin);
      
      this.activePlugins.add(pluginId);
      this.pluginStates.set(pluginId, 'active');
      
      eventBus.emit({
        type: 'plugin.activated',
        payload: { pluginId, manifest: plugin.manifest },
        source: 'PluginRegistry',
      });
    } catch (error) {
      this.pluginErrors.set(pluginId, error as Error);
      this.pluginStates.set(pluginId, 'error');
      throw error;
    }
  }
  
  /**
   * Deactivate a plugin
   */
  async deactivatePlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      throw new Error(`Plugin ${pluginId} not found`);
    }
    
    if (!this.activePlugins.has(pluginId)) {
      return; // Already inactive
    }
    
    try {
      // Deactivate dependents first
      await this.deactivateDependents(pluginId);
      
      // Deactivate plugin
      await plugin.deactivate();
      
      // Unregister capabilities
      this.unregisterPluginCapabilities(plugin);
      
      this.activePlugins.delete(pluginId);
      this.pluginStates.set(pluginId, 'inactive');
      
      eventBus.emit({
        type: 'plugin.deactivated',
        payload: { pluginId, manifest: plugin.manifest },
        source: 'PluginRegistry',
      });
    } catch (error) {
      this.pluginErrors.set(pluginId, error as Error);
      throw error;
    }
  }
  
  /**
   * Unregister and destroy a plugin
   */
  async unregisterPlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      return;
    }
    
    try {
      // Deactivate if active
      if (this.activePlugins.has(pluginId)) {
        await this.deactivatePlugin(pluginId);
      }
      
      // Destroy plugin
      await plugin.destroy();
      
      // Clean up
      this.plugins.delete(pluginId);
      this.pluginStates.delete(pluginId);
      this.pluginErrors.delete(pluginId);
      this.removeDependencyNode(pluginId);
      
      eventBus.emit({
        type: 'plugin.unregistered',
        payload: { pluginId },
        source: 'PluginRegistry',
      });
    } catch (error) {
      console.error(`Failed to unregister plugin ${pluginId}:`, error);
    }
  }
  
  /**
   * Get plugin information
   */
  getPlugin(pluginId: string): Plugin | undefined {
    return this.plugins.get(pluginId);
  }
  
  getPluginState(pluginId: string): string {
    return this.pluginStates.get(pluginId) || 'unknown';
  }
  
  getPluginError(pluginId: string): Error | undefined {
    return this.pluginErrors.get(pluginId);
  }
  
  getActivePlugins(): Plugin[] {
    return Array.from(this.activePlugins)
      .map(id => this.plugins.get(id))
      .filter((plugin): plugin is Plugin => plugin !== undefined);
  }
  
  getAllPlugins(): Plugin[] {
    return Array.from(this.plugins.values());
  }
  
  /**
   * Get registered capabilities
   */
  getModeProviders(): ModeProvider[] {
    return Array.from(this.modeProviders.values());
  }
  
  getModeProvider(modeId: ModeId): ModeProvider | undefined {
    return this.modeProviders.get(modeId);
  }
  
  getContextAnalyzers(): ContextAnalyzer[] {
    return Array.from(this.contextAnalyzers.values())
      .sort((a, b) => b.priority - a.priority);
  }
  
  getUIComponents(type?: UIComponent['type']): UIComponent[] {
    const components = Array.from(this.uiComponents.values());
    return type ? components.filter(c => c.type === type) : components;
  }
  
  /**
   * Plugin discovery and management
   */
  async discoverModeForContext(context: ContextSnapshot): Promise<{
    mode: ModeId | null;
    provider: ModeProvider | null;
    score: number;
  }> {
    let bestScore = 0;
    let bestMode: ModeId | null = null;
    let bestProvider: ModeProvider | null = null;
    
    // Check all active mode providers
    for (const provider of this.modeProviders.values()) {
      if (provider.isApplicable(context)) {
        const score = provider.getScore(context);
        if (score > bestScore) {
          bestScore = score;
          bestMode = provider.id;
          bestProvider = provider;
        }
      }
    }
    
    return { mode: bestMode, provider: bestProvider, score: bestScore };
  }
  
  async analyzeContext(context: ContextSnapshot): Promise<{
    features: Record<string, number>;
    analysis: Record<string, AnalysisResult>;
  }> {
    const features: Record<string, number> = {};
    const analysis: Record<string, AnalysisResult> = {};
    
    // Run all analyzers
    const analyzePromises = this.getContextAnalyzers().map(async analyzer => {
      try {
        const result = await analyzer.analyze(context);
        const analyzerFeatures = analyzer.getFeatures(context);
        
        // Merge features with analyzer prefix
        Object.entries(analyzerFeatures).forEach(([key, value]) => {
          features[`${analyzer.id}.${key}`] = value;
        });
        
        analysis[analyzer.id] = result;
      } catch (error) {
        console.warn(`Analyzer ${analyzer.id} failed:`, error);
      }
    });
    
    await Promise.allSettled(analyzePromises);
    
    return { features, analysis };
  }
  
  /**
   * Hot reload capabilities
   */
  async reloadPlugin(pluginId: string, newPlugin: Plugin): Promise<void> {
    if (!this.config.hotReloadEnabled) {
      throw new Error('Hot reloading is disabled');
    }
    
    const wasActive = this.activePlugins.has(pluginId);
    
    // Unregister old plugin
    await this.unregisterPlugin(pluginId);
    
    // Register new plugin
    await this.registerPlugin(newPlugin);
    
    // Reactivate if it was active before
    if (wasActive) {
      await this.activatePlugin(pluginId);
    }
    
    eventBus.emit({
      type: 'plugin.reloaded',
      payload: { pluginId, manifest: newPlugin.manifest },
      source: 'PluginRegistry',
    });
  }
  
  /**
   * Private methods
   */
  
  private validatePlugin(plugin: Plugin): void {
    const { manifest } = plugin;
    
    if (!manifest.id || !manifest.name || !manifest.version) {
      throw new Error('Plugin manifest missing required fields');
    }
    
    if (!manifest.apiVersion || manifest.apiVersion !== '1.0.0') {
      throw new Error(`Unsupported API version: ${manifest.apiVersion}`);
    }
    
    // Validate permissions
    manifest.permissions.forEach(permission => {
      if (!this.config.allowedPermissions.includes(permission.type)) {
        throw new Error(`Permission ${permission.type} is not allowed`);
      }
    });
  }
  
  private async validateDependencies(manifest: PluginManifest): Promise<void> {
    for (const depId of manifest.dependencies) {
      if (!this.plugins.has(depId)) {
        throw new Error(`Required dependency ${depId} not found`);
      }
    }
    
    // Check for circular dependencies
    if (this.hasCircularDependency(manifest.id, manifest.dependencies)) {
      throw new Error('Circular dependency detected');
    }
  }
  
  private hasCircularDependency(pluginId: string, dependencies: string[]): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    
    const checkCycle = (id: string): boolean => {
      if (recursionStack.has(id)) return true;
      if (visited.has(id)) return false;
      
      visited.add(id);
      recursionStack.add(id);
      
      const deps = this.dependencyGraph.get(id) || new Set();
      for (const dep of deps) {
        if (checkCycle(dep)) return true;
      }
      
      recursionStack.delete(id);
      return false;
    };
    
    return checkCycle(pluginId);
  }
  
  private updateDependencyGraph(manifest: PluginManifest): void {
    const deps = new Set(manifest.dependencies);
    this.dependencyGraph.set(manifest.id, deps);
    this.updateLoadOrder();
  }
  
  private removeDependencyNode(pluginId: string): void {
    this.dependencyGraph.delete(pluginId);
    // Remove from other dependencies
    this.dependencyGraph.forEach(deps => deps.delete(pluginId));
    this.updateLoadOrder();
  }
  
  private updateLoadOrder(): void {
    // Topological sort
    const visited = new Set<string>();
    const temp = new Set<string>();
    const order: string[] = [];
    
    const visit = (id: string): void => {
      if (temp.has(id)) throw new Error('Circular dependency detected');
      if (visited.has(id)) return;
      
      temp.add(id);
      const deps = this.dependencyGraph.get(id) || new Set();
      deps.forEach(visit);
      temp.delete(id);
      visited.add(id);
      order.unshift(id);
    };
    
    this.dependencyGraph.forEach((_, id) => {
      if (!visited.has(id)) visit(id);
    });
    
    this.loadOrder = order;
  }
  
  private async activateDependencies(manifest: PluginManifest): Promise<void> {
    for (const depId of manifest.dependencies) {
      if (!this.activePlugins.has(depId)) {
        await this.activatePlugin(depId);
      }
    }
  }
  
  private async deactivateDependents(pluginId: string): Promise<void> {
    // Find plugins that depend on this one
    const dependents: string[] = [];
    this.dependencyGraph.forEach((deps, id) => {
      if (deps.has(pluginId)) {
        dependents.push(id);
      }
    });
    
    // Deactivate dependents first
    for (const dependent of dependents) {
      if (this.activePlugins.has(dependent)) {
        await this.deactivatePlugin(dependent);
      }
    }
  }
  
  private createPluginContext(manifest: PluginManifest): PluginContext {
    return {
      eventBus,
      registerModeProvider: (provider: ModeProvider) => {
        this.modeProviders.set(provider.id, provider);
      },
      registerContextAnalyzer: (analyzer: ContextAnalyzer) => {
        this.contextAnalyzers.set(analyzer.id, analyzer);
      },
      registerUIComponent: (component: UIComponent) => {
        this.uiComponents.set(component.id, component);
      },
      getSystemInfo: () => ({
        version: '1.0.0',
        platform: 'web',
        capabilities: ['ui', 'context', 'ml'],
        limits: {
          maxPlugins: this.config.maxPlugins,
          maxMemoryMB: this.config.maxMemoryMB,
          allowedPermissions: this.config.allowedPermissions,
        },
      }),
      requestPermission: async (permission: PluginPermission) => {
        // Simple permission check - in real implementation, might prompt user
        return this.config.allowedPermissions.includes(permission.type);
      },
    };
  }
  
  private registerPluginCapabilities(plugin: Plugin): void {
    const capabilities = plugin.getCapabilities();
    
    capabilities.forEach(capability => {
      switch (capability.type) {
        case 'mode':
          // Mode providers are registered through context
          break;
        case 'analyzer':
          // Analyzers are registered through context
          break;
        case 'ui':
          // UI components are registered through context
          break;
      }
    });
  }
  
  private unregisterPluginCapabilities(plugin: Plugin): void {
    const capabilities = plugin.getCapabilities();
    
    capabilities.forEach(capability => {
      switch (capability.type) {
        case 'mode':
          this.modeProviders.delete(capability.name as ModeId);
          break;
        case 'analyzer':
          this.contextAnalyzers.delete(capability.name);
          break;
        case 'ui':
          this.uiComponents.delete(capability.name);
          break;
      }
    });
  }
  
  private setupEventListeners(): void {
    // Listen for system events that might affect plugins
    eventBus.on('system.shutdown', async () => {
      // Gracefully shutdown all plugins
      const activePlugins = Array.from(this.activePlugins);
      for (const pluginId of activePlugins) {
        try {
          await this.deactivatePlugin(pluginId);
        } catch (error) {
          console.error(`Failed to deactivate plugin ${pluginId} on shutdown:`, error);
        }
      }
    });
  }
}

// Export singleton instance
export const pluginRegistry = PluginRegistry.getInstance();
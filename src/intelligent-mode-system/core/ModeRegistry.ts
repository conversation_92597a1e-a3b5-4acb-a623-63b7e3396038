import { Mode, ModeDefinition, ModeId, ModeTransition, ContextSnapshot } from '../types';
import { eventBus } from './EventBus';
import { memoryCache } from './CacheSystem';
import { allModes } from '../modes';

export class ModeRegistry {
  private modes: Map<ModeId, ModeDefinition> = new Map();
  private activeMode: Mode | null = null;
  private transitionHistory: ModeTransition[] = [];
  private defaultModeId: ModeId = 'architect';
  private isTransitioning = false;

  constructor() {
    this.initializeDefaultModes();
  }

  register(definition: ModeDefinition): void {
    if (this.modes.has(definition.id)) {
      console.warn(`Mode ${definition.id} is already registered. Overwriting.`);
    }

    this.modes.set(definition.id, definition);
    
    eventBus.emit({
      type: 'mode.change',
      payload: { action: 'registered', modeId: definition.id },
      source: 'ModeRegistry',
    });
  }

  unregister(modeId: ModeId): void {
    if (this.activeMode?.id === modeId) {
      throw new Error('Cannot unregister active mode');
    }

    this.modes.delete(modeId);
    
    eventBus.emit({
      type: 'mode.change',
      payload: { action: 'unregistered', modeId },
      source: 'ModeRegistry',
    });
  }

  getMode(modeId: ModeId): Mode | null {
    const definition = this.modes.get(modeId);
    if (!definition) return null;

    return this.definitionToMode(definition);
  }

  getAllModes(): Mode[] {
    return Array.from(this.modes.values()).map(def => this.definitionToMode(def));
  }

  getActiveMode(): Mode | null {
    return this.activeMode;
  }

  async setActiveMode(modeId: ModeId, context?: ContextSnapshot): Promise<void> {
    if (this.isTransitioning) {
      throw new Error('Mode transition already in progress');
    }

    const targetDefinition = this.modes.get(modeId);
    if (!targetDefinition) {
      throw new Error(`Mode ${modeId} not found`);
    }

    if (this.activeMode?.id === modeId) {
      return; // Already in this mode
    }

    this.isTransitioning = true;

    try {
      // Emit transition start event
      eventBus.emit({
        type: 'mode.transition.start',
        payload: {
          from: this.activeMode?.id,
          to: modeId,
        },
        source: 'ModeRegistry',
      });

      // Deactivate current mode
      if (this.activeMode) {
        const currentDefinition = this.modes.get(this.activeMode.id);
        if (currentDefinition?.deactivate) {
          await currentDefinition.deactivate();
        }

        // Deactivate features
        for (const feature of this.activeMode.features) {
          eventBus.emit({
            type: 'mode.feature.deactivate',
            payload: { feature, modeId: this.activeMode.id },
            source: 'ModeRegistry',
          });
        }
      }

      // Record transition
      const transition: ModeTransition = {
        from: this.activeMode?.id || 'none' as ModeId,
        to: modeId,
        reason: context ? 'automatic' : 'manual',
        timestamp: Date.now(),
        automatic: !!context,
      };
      this.transitionHistory.push(transition);

      // Keep history limited
      if (this.transitionHistory.length > 100) {
        this.transitionHistory = this.transitionHistory.slice(-100);
      }

      // Activate new mode
      const newMode = this.definitionToMode(targetDefinition);
      newMode.isActive = true;
      newMode.lastActivated = Date.now();

      if (targetDefinition.activate && context) {
        await targetDefinition.activate(context);
      }

      // Activate features
      for (const feature of newMode.features) {
        eventBus.emit({
          type: 'mode.feature.activate',
          payload: { feature, modeId: newMode.id },
          source: 'ModeRegistry',
        });
      }

      this.activeMode = newMode;

      // Cache active mode
      await memoryCache.set('activeMode', modeId);

      // Emit transition complete event
      eventBus.emit({
        type: 'mode.transition.complete',
        payload: {
          from: transition.from,
          to: modeId,
          duration: Date.now() - transition.timestamp,
        },
        source: 'ModeRegistry',
      });

    } catch (error) {
      // Rollback on error
      console.error('Mode transition failed:', error);
      
      eventBus.emit({
        type: 'error.occurred',
        payload: {
          error: error instanceof Error ? error.message : 'Unknown error',
          context: 'mode-transition',
          from: this.activeMode?.id,
          to: modeId,
        },
        source: 'ModeRegistry',
      });

      throw error;
    } finally {
      this.isTransitioning = false;
    }
  }

  getTransitionHistory(): ModeTransition[] {
    return [...this.transitionHistory];
  }

  getModeUsageStats(): Record<ModeId, number> {
    const stats: Record<ModeId, number> = {} as Record<ModeId, number>;
    
    // Initialize all modes with 0
    for (const modeId of this.modes.keys()) {
      stats[modeId] = 0;
    }

    // Calculate time spent in each mode
    for (let i = 0; i < this.transitionHistory.length; i++) {
      const transition = this.transitionHistory[i];
      const nextTransition = this.transitionHistory[i + 1];
      
      const duration = nextTransition 
        ? nextTransition.timestamp - transition.timestamp
        : Date.now() - transition.timestamp;
      
      stats[transition.to] = (stats[transition.to] || 0) + duration;
    }

    return stats;
  }

  private definitionToMode(definition: ModeDefinition): Mode {
    return {
      id: definition.id,
      name: definition.name,
      description: definition.description,
      icon: definition.icon,
      color: definition.color,
      isActive: this.activeMode?.id === definition.id,
      features: definition.features,
      shortcuts: definition.shortcuts,
      lastActivated: this.activeMode?.id === definition.id ? this.activeMode.lastActivated : undefined,
    };
  }

  private initializeDefaultModes(): void {
    // Register all mode definitions from the modes directory
    allModes.forEach(mode => this.register(mode));
  }

  async restoreLastMode(): Promise<void> {
    try {
      const lastModeId = await memoryCache.get<ModeId>('activeMode');
      if (lastModeId && this.modes.has(lastModeId)) {
        await this.setActiveMode(lastModeId);
      } else {
        await this.setActiveMode(this.defaultModeId);
      }
    } catch (error) {
      console.error('Failed to restore last mode:', error);
      await this.setActiveMode(this.defaultModeId);
    }
  }
}

// Singleton instance
export const modeRegistry = new ModeRegistry();
import { memoryCache } from './CacheSystem';
import { performanceMonitor } from '../utils/performance';
import { eventBus } from './EventBus';

/**
 * Intelligent Caching System for ML-based Context Analysis
 * 
 * Features:
 * - Multi-level caching strategy
 * - Predictive pre-computation
 * - Cache invalidation based on context changes
 * - Memory management and compression
 * - Background cache warming
 */

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  computationCost: number;
  dependencies: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface CacheMetrics {
  hitRate: number;
  missRate: number;
  totalRequests: number;
  averageAccessTime: number;
  memoryUsage: number;
  compressionRatio: number;
}

export interface CacheConfig {
  maxMemoryMB: number;
  defaultTTL: number;
  compressionThreshold: number;
  backgroundCleanupInterval: number;
  predictivePreloadEnabled: boolean;
  adaptiveTTLEnabled: boolean;
}

class IntelligentCacheSystem {
  private static instance: IntelligentCacheSystem;
  
  // Cache storage
  private cache = new Map<string, CacheEntry<any>>();
  private computationQueue: Array<{ key: string; computation: () => Promise<any> }> = [];
  private dependencyGraph = new Map<string, Set<string>>();
  
  // Configuration
  private config: CacheConfig = {
    maxMemoryMB: 100,
    defaultTTL: 300000, // 5 minutes
    compressionThreshold: 1024, // 1KB
    backgroundCleanupInterval: 60000, // 1 minute
    predictivePreloadEnabled: true,
    adaptiveTTLEnabled: true,
  };
  
  // Metrics
  private metrics = {
    totalRequests: 0,
    hits: 0,
    misses: 0,
    accessTimes: [] as number[],
    memoryUsage: 0,
    compressionRatio: 1,
  };
  
  // Background processing
  private cleanupTimer: NodeJS.Timeout | null = null;
  private isProcessingQueue = false;
  
  private constructor() {
    this.startBackgroundProcessing();
    this.setupEventListeners();
  }
  
  static getInstance(): IntelligentCacheSystem {
    if (!IntelligentCacheSystem.instance) {
      IntelligentCacheSystem.instance = new IntelligentCacheSystem();
    }
    return IntelligentCacheSystem.instance;
  }
  
  /**
   * Get cached data with intelligent loading
   */
  async get<T>(
    key: string,
    computation?: () => Promise<T>,
    options?: {
      ttl?: number;
      priority?: CacheEntry<T>['priority'];
      dependencies?: string[];
      computationCost?: number;
    }
  ): Promise<T | null> {
    performanceMonitor.startTimer('cache.get');
    const startTime = performance.now();
    
    try {
      this.metrics.totalRequests++;
      
      // Check cache first
      const cached = this.cache.get(key);
      if (cached && this.isValidEntry(cached, options?.ttl)) {
        // Update access metrics
        cached.accessCount++;
        cached.lastAccessed = Date.now();
        
        this.metrics.hits++;
        this.recordAccessTime(performance.now() - startTime);
        
        performanceMonitor.endTimer('cache.get', { result: 'hit' });
        return this.decompressData(cached.data);
      }
      
      // Cache miss - compute if function provided
      if (computation) {
        this.metrics.misses++;
        
        const computedData = await this.computeWithCache(key, computation, options);
        
        this.recordAccessTime(performance.now() - startTime);
        performanceMonitor.endTimer('cache.get', { result: 'computed' });
        
        return computedData;
      }
      
      this.metrics.misses++;
      this.recordAccessTime(performance.now() - startTime);
      performanceMonitor.endTimer('cache.get', { result: 'miss' });
      
      return null;
    } catch (error) {
      performanceMonitor.endTimer('cache.get', { error: 'true' });
      throw error;
    }
  }
  
  /**
   * Set cached data with intelligent storage
   */
  async set<T>(
    key: string,
    data: T,
    options?: {
      ttl?: number;
      priority?: CacheEntry<T>['priority'];
      dependencies?: string[];
      computationCost?: number;
    }
  ): Promise<void> {
    performanceMonitor.startTimer('cache.set');
    
    try {
      const compressedData = this.compressData(data);
      const entry: CacheEntry<T> = {
        data: compressedData,
        timestamp: Date.now(),
        accessCount: 1,
        lastAccessed: Date.now(),
        computationCost: options?.computationCost || 1,
        dependencies: options?.dependencies || [],
        priority: options?.priority || 'medium',
      };
      
      this.cache.set(key, entry);
      
      // Update dependency graph
      if (options?.dependencies) {
        options.dependencies.forEach(dep => {
          if (!this.dependencyGraph.has(dep)) {
            this.dependencyGraph.set(dep, new Set());
          }
          this.dependencyGraph.get(dep)!.add(key);
        });
      }
      
      // Manage memory usage
      await this.manageMemory();
      
      // Schedule predictive preloading
      if (this.config.predictivePreloadEnabled) {
        this.schedulePredictiveLoad(key, entry);
      }
      
      performanceMonitor.endTimer('cache.set');
    } catch (error) {
      performanceMonitor.endTimer('cache.set', { error: 'true' });
      throw error;
    }
  }
  
  /**
   * Invalidate cache entries based on dependencies
   */
  invalidate(pattern: string | string[]): void {
    const patterns = Array.isArray(pattern) ? pattern : [pattern];
    
    patterns.forEach(p => {
      // Direct key invalidation
      if (this.cache.has(p)) {
        this.cache.delete(p);
      }
      
      // Pattern-based invalidation
      if (p.includes('*')) {
        const regex = new RegExp(p.replace(/\*/g, '.*'));
        Array.from(this.cache.keys()).forEach(key => {
          if (regex.test(key)) {
            this.cache.delete(key);
          }
        });
      }
      
      // Dependency-based invalidation
      const dependents = this.dependencyGraph.get(p);
      if (dependents) {
        dependents.forEach(dependent => {
          this.cache.delete(dependent);
        });
        this.dependencyGraph.delete(p);
      }
    });
    
    // Emit invalidation event
    eventBus.emit({
      type: 'cache.invalidated',
      payload: { patterns },
      source: 'IntelligentCacheSystem',
    });
  }
  
  /**
   * Preload data in background
   */
  preload<T>(
    key: string,
    computation: () => Promise<T>,
    options?: {
      priority?: CacheEntry<T>['priority'];
      dependencies?: string[];
      computationCost?: number;
    }
  ): void {
    // Check if already cached
    if (this.cache.has(key)) return;
    
    // Add to computation queue
    this.computationQueue.push({
      key,
      computation: async () => {
        const data = await computation();
        await this.set(key, data, options);
        return data;
      },
    });
    
    // Process queue if not already processing
    if (!this.isProcessingQueue) {
      this.processComputationQueue();
    }
  }
  
  /**
   * Get cache metrics
   */
  getMetrics(): CacheMetrics {
    const avgAccessTime = this.metrics.accessTimes.length > 0
      ? this.metrics.accessTimes.reduce((a, b) => a + b, 0) / this.metrics.accessTimes.length
      : 0;
    
    return {
      hitRate: this.metrics.totalRequests > 0 ? this.metrics.hits / this.metrics.totalRequests : 0,
      missRate: this.metrics.totalRequests > 0 ? this.metrics.misses / this.metrics.totalRequests : 0,
      totalRequests: this.metrics.totalRequests,
      averageAccessTime: avgAccessTime,
      memoryUsage: this.calculateMemoryUsage(),
      compressionRatio: this.metrics.compressionRatio,
    };
  }
  
  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.dependencyGraph.clear();
    this.computationQueue = [];
    this.resetMetrics();
    
    eventBus.emit({
      type: 'cache.cleared',
      payload: {},
      source: 'IntelligentCacheSystem',
    });
  }
  
  /**
   * Update cache configuration
   */
  updateConfig(updates: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...updates };
    
    // Restart background processing with new config
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.startBackgroundProcessing();
    }
  }
  
  /**
   * Private methods
   */
  
  private async computeWithCache<T>(
    key: string,
    computation: () => Promise<T>,
    options?: {
      ttl?: number;
      priority?: CacheEntry<T>['priority'];
      dependencies?: string[];
      computationCost?: number;
    }
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const data = await computation();
      const computationTime = performance.now() - startTime;
      
      // Store in cache
      await this.set(key, data, {
        ...options,
        computationCost: computationTime,
      });
      
      return data;
    } catch (error) {
      // Cache error for short time to prevent repeated failures
      await this.set(key, { error: error.message }, {
        ttl: 30000, // 30 seconds
        priority: 'low',
      });
      throw error;
    }
  }
  
  private isValidEntry(entry: CacheEntry<any>, customTTL?: number): boolean {
    const ttl = customTTL || this.calculateAdaptiveTTL(entry);
    const age = Date.now() - entry.timestamp;
    return age < ttl;
  }
  
  private calculateAdaptiveTTL(entry: CacheEntry<any>): number {
    if (!this.config.adaptiveTTLEnabled) {
      return this.config.defaultTTL;
    }
    
    // Adjust TTL based on access patterns and computation cost
    let adaptiveTTL = this.config.defaultTTL;
    
    // High-access items get longer TTL
    if (entry.accessCount > 10) {
      adaptiveTTL *= 2;
    }
    
    // High-computation-cost items get longer TTL
    if (entry.computationCost > 1000) {
      adaptiveTTL *= 1.5;
    }
    
    // Critical priority items get longer TTL
    switch (entry.priority) {
      case 'critical':
        adaptiveTTL *= 3;
        break;
      case 'high':
        adaptiveTTL *= 2;
        break;
      case 'low':
        adaptiveTTL *= 0.5;
        break;
    }
    
    return Math.min(adaptiveTTL, this.config.defaultTTL * 5);
  }
  
  private compressData<T>(data: T): T | string {
    const serialized = JSON.stringify(data);
    
    if (serialized.length > this.config.compressionThreshold) {
      try {
        // Simple compression simulation (in real implementation, use actual compression)
        const compressed = this.simpleCompress(serialized);
        this.metrics.compressionRatio = serialized.length / compressed.length;
        return compressed;
      } catch (error) {
        console.warn('Compression failed:', error);
      }
    }
    
    return data;
  }
  
  private decompressData<T>(data: T | string): T {
    if (typeof data === 'string' && data.startsWith('COMPRESSED:')) {
      try {
        const decompressed = this.simpleDecompress(data);
        return JSON.parse(decompressed);
      } catch (error) {
        console.warn('Decompression failed:', error);
      }
    }
    
    return data as T;
  }
  
  private simpleCompress(data: string): string {
    // Simple compression simulation - in real implementation use gzip or similar
    return `COMPRESSED:${Buffer.from(data).toString('base64')}`;
  }
  
  private simpleDecompress(compressed: string): string {
    // Simple decompression simulation
    const encoded = compressed.replace('COMPRESSED:', '');
    return Buffer.from(encoded, 'base64').toString();
  }
  
  private async manageMemory(): Promise<void> {
    const currentMemory = this.calculateMemoryUsage();
    const maxMemoryBytes = this.config.maxMemoryMB * 1024 * 1024;
    
    if (currentMemory > maxMemoryBytes) {
      await this.evictLeastValuable();
    }
  }
  
  private calculateMemoryUsage(): number {
    let totalSize = 0;
    
    this.cache.forEach(entry => {
      const serialized = JSON.stringify(entry);
      totalSize += Buffer.byteLength(serialized, 'utf8');
    });
    
    return totalSize;
  }
  
  private async evictLeastValuable(): Promise<void> {
    // Calculate value score for each entry
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      entry,
      value: this.calculateEntryValue(entry),
    }));
    
    // Sort by value (ascending) and remove least valuable
    entries.sort((a, b) => a.value - b.value);
    
    // Remove bottom 25%
    const toRemove = Math.ceil(entries.length * 0.25);
    for (let i = 0; i < toRemove; i++) {
      this.cache.delete(entries[i].key);
    }
    
    eventBus.emit({
      type: 'cache.evicted',
      payload: { count: toRemove },
      source: 'IntelligentCacheSystem',
    });
  }
  
  private calculateEntryValue(entry: CacheEntry<any>): number {
    const age = Date.now() - entry.lastAccessed;
    const ageScore = Math.exp(-age / 300000); // Decay over 5 minutes
    
    const accessScore = Math.min(entry.accessCount / 10, 1);
    const costScore = Math.min(entry.computationCost / 1000, 1);
    
    const priorityScore = {
      low: 0.25,
      medium: 0.5,
      high: 0.75,
      critical: 1.0,
    }[entry.priority];
    
    return ageScore * 0.3 + accessScore * 0.3 + costScore * 0.2 + priorityScore * 0.2;
  }
  
  private schedulePredictiveLoad(key: string, entry: CacheEntry<any>): void {
    // Predict related keys that might be needed
    const relatedKeys = this.predictRelatedKeys(key, entry);
    
    relatedKeys.forEach(relatedKey => {
      if (!this.cache.has(relatedKey)) {
        // Schedule for background loading
        setTimeout(() => {
          eventBus.emit({
            type: 'cache.predictive_load_requested',
            payload: { key: relatedKey, originalKey: key },
            source: 'IntelligentCacheSystem',
          });
        }, 100); // Small delay to avoid blocking
      }
    });
  }
  
  private predictRelatedKeys(key: string, entry: CacheEntry<any>): string[] {
    const relatedKeys: string[] = [];
    
    // Pattern-based prediction
    if (key.includes('context')) {
      relatedKeys.push(key.replace('context', 'suggestions'));
      relatedKeys.push(key.replace('context', 'probabilities'));
    }
    
    if (key.includes('suggestions')) {
      relatedKeys.push(key.replace('suggestions', 'context'));
    }
    
    // Dependency-based prediction
    entry.dependencies.forEach(dep => {
      relatedKeys.push(`${dep}:derived`);
      relatedKeys.push(`${dep}:analysis`);
    });
    
    return relatedKeys;
  }
  
  private recordAccessTime(time: number): void {
    this.metrics.accessTimes.push(time);
    
    // Keep only last 1000 access times
    if (this.metrics.accessTimes.length > 1000) {
      this.metrics.accessTimes = this.metrics.accessTimes.slice(-1000);
    }
  }
  
  private async processComputationQueue(): Promise<void> {
    if (this.isProcessingQueue || this.computationQueue.length === 0) {
      return;
    }
    
    this.isProcessingQueue = true;
    
    try {
      while (this.computationQueue.length > 0) {
        const { key, computation } = this.computationQueue.shift()!;
        
        try {
          await computation();
          
          // Small delay to prevent overwhelming the system
          await new Promise(resolve => setTimeout(resolve, 10));
        } catch (error) {
          console.warn(`Background computation failed for ${key}:`, error);
        }
      }
    } finally {
      this.isProcessingQueue = false;
    }
  }
  
  private startBackgroundProcessing(): void {
    this.cleanupTimer = setInterval(() => {
      this.performBackgroundCleanup();
    }, this.config.backgroundCleanupInterval);
  }
  
  private performBackgroundCleanup(): void {
    // Remove expired entries
    const now = Date.now();
    let removedCount = 0;
    
    this.cache.forEach((entry, key) => {
      if (!this.isValidEntry(entry)) {
        this.cache.delete(key);
        removedCount++;
      }
    });
    
    // Clean dependency graph
    this.dependencyGraph.forEach((dependents, dependency) => {
      const validDependents = new Set(
        Array.from(dependents).filter(dep => this.cache.has(dep))
      );
      
      if (validDependents.size === 0) {
        this.dependencyGraph.delete(dependency);
      } else {
        this.dependencyGraph.set(dependency, validDependents);
      }
    });
    
    if (removedCount > 0) {
      eventBus.emit({
        type: 'cache.cleanup_completed',
        payload: { removedCount },
        source: 'IntelligentCacheSystem',
      });
    }
  }
  
  private setupEventListeners(): void {
    // Listen for context changes to invalidate related caches
    eventBus.on('context.analyzed', () => {
      this.invalidate(['context:*', 'suggestions:*']);
    });
    
    // Listen for mode transitions to invalidate mode-specific caches
    eventBus.on('mode.transitioned', (event) => {
      const { from, to } = event.payload;
      this.invalidate([`mode:${from}:*`, `mode:${to}:*`]);
    });
  }
  
  private resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      hits: 0,
      misses: 0,
      accessTimes: [],
      memoryUsage: 0,
      compressionRatio: 1,
    };
  }
  
  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    
    this.clear();
  }
}

// Export singleton instance
export const intelligentCache = IntelligentCacheSystem.getInstance();
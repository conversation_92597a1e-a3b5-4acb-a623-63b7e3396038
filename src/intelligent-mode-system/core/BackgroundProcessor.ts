import { eventBus } from './EventBus';
import { intelligentCache } from './IntelligentCacheSystem';
import { performanceMonitor } from '../utils/performance';
import { ContextSnapshot, ModeId } from '../types';

/**
 * Background Processing System for ML Computations
 * 
 * Features:
 * - Worker-like background processing without actual web workers
 * - Priority queue for task scheduling
 * - Resource-aware throttling
 * - Batch processing optimization
 * - Error handling and retry logic
 */

export interface BackgroundTask {
  id: string;
  type: 'analysis' | 'prediction' | 'learning' | 'optimization';
  priority: 'low' | 'medium' | 'high' | 'critical';
  data: any;
  computation: (data: any) => Promise<any>;
  retries: number;
  maxRetries: number;
  createdAt: number;
  estimatedDuration: number;
}

export interface ProcessorMetrics {
  tasksProcessed: number;
  tasksQueued: number;
  averageProcessingTime: number;
  errorRate: number;
  throughput: number; // tasks per second
  resourceUtilization: number;
}

export interface ProcessorConfig {
  maxConcurrentTasks: number;
  maxQueueSize: number;
  processingInterval: number;
  resourceThrottleThreshold: number;
  enableBatching: boolean;
  batchSize: number;
  retryDelay: number;
}

class BackgroundProcessor {
  private static instance: BackgroundProcessor;
  
  // Task management
  private taskQueue: BackgroundTask[] = [];
  private activeTasks = new Map<string, BackgroundTask>();
  private completedTasks = new Map<string, { result: any; completedAt: number }>();
  
  // Configuration
  private config: ProcessorConfig = {
    maxConcurrentTasks: 3,
    maxQueueSize: 100,
    processingInterval: 100, // ms
    resourceThrottleThreshold: 0.8,
    enableBatching: true,
    batchSize: 5,
    retryDelay: 1000,
  };
  
  // State management
  private isProcessing = false;
  private processingTimer: NodeJS.Timeout | null = null;
  private resourceMonitor: NodeJS.Timeout | null = null;
  
  // Metrics
  private metrics = {
    tasksProcessed: 0,
    tasksQueued: 0,
    processingTimes: [] as number[],
    errors: 0,
    startTime: Date.now(),
    resourceUsage: 0,
  };
  
  private constructor() {
    this.startProcessing();
    this.startResourceMonitoring();
    this.setupEventListeners();
  }
  
  static getInstance(): BackgroundProcessor {
    if (!BackgroundProcessor.instance) {
      BackgroundProcessor.instance = new BackgroundProcessor();
    }
    return BackgroundProcessor.instance;
  }
  
  /**
   * Schedule a task for background processing
   */
  async scheduleTask(task: Omit<BackgroundTask, 'id' | 'createdAt' | 'retries'>): Promise<string> {
    if (this.taskQueue.length >= this.config.maxQueueSize) {
      // Remove lowest priority tasks to make room
      this.evictLowPriorityTasks();
    }
    
    const taskId = this.generateTaskId();
    const fullTask: BackgroundTask = {
      ...task,
      id: taskId,
      createdAt: Date.now(),
      retries: 0,
    };
    
    // Insert task in priority order
    this.insertTaskByPriority(fullTask);
    this.metrics.tasksQueued++;
    
    // Emit task scheduled event
    eventBus.emit({
      type: 'background.task_scheduled',
      payload: { taskId, type: task.type, priority: task.priority },
      source: 'BackgroundProcessor',
    });
    
    return taskId;
  }
  
  /**
   * Get task result (blocking until completion)
   */
  async getTaskResult(taskId: string, timeout = 30000): Promise<any> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      // Check if task is completed
      const completed = this.completedTasks.get(taskId);
      if (completed) {
        this.completedTasks.delete(taskId); // Clean up
        return completed.result;
      }
      
      // Check if task failed
      const activeTask = this.activeTasks.get(taskId);
      if (activeTask && activeTask.retries >= activeTask.maxRetries) {
        throw new Error(`Task ${taskId} failed after ${activeTask.maxRetries} retries`);
      }
      
      // Wait before checking again
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    throw new Error(`Task ${taskId} timed out after ${timeout}ms`);
  }
  
  /**
   * Check if task is completed
   */
  isTaskCompleted(taskId: string): boolean {
    return this.completedTasks.has(taskId);
  }
  
  /**
   * Cancel a pending task
   */
  cancelTask(taskId: string): boolean {
    // Remove from queue
    const queueIndex = this.taskQueue.findIndex(task => task.id === taskId);
    if (queueIndex >= 0) {
      this.taskQueue.splice(queueIndex, 1);
      return true;
    }
    
    // Cannot cancel active tasks (they're already running)
    return false;
  }
  
  /**
   * Get processor metrics
   */
  getMetrics(): ProcessorMetrics {
    const uptime = (Date.now() - this.metrics.startTime) / 1000;
    const avgProcessingTime = this.metrics.processingTimes.length > 0
      ? this.metrics.processingTimes.reduce((a, b) => a + b, 0) / this.metrics.processingTimes.length
      : 0;
    
    return {
      tasksProcessed: this.metrics.tasksProcessed,
      tasksQueued: this.taskQueue.length,
      averageProcessingTime: avgProcessingTime,
      errorRate: this.metrics.tasksProcessed > 0 ? this.metrics.errors / this.metrics.tasksProcessed : 0,
      throughput: uptime > 0 ? this.metrics.tasksProcessed / uptime : 0,
      resourceUtilization: this.metrics.resourceUsage,
    };
  }
  
  /**
   * Update processor configuration
   */
  updateConfig(updates: Partial<ProcessorConfig>): void {
    this.config = { ...this.config, ...updates };
    
    // Restart processing with new config
    this.stopProcessing();
    this.startProcessing();
  }
  
  /**
   * High-level task scheduling methods
   */
  
  async scheduleContextAnalysis(context: ContextSnapshot): Promise<string> {
    return this.scheduleTask({
      type: 'analysis',
      priority: 'medium',
      data: { context },
      computation: async ({ context }) => {
        const { enhancedContextInference } = await import('../services/EnhancedContextInference');
        return enhancedContextInference.calculateModeProbabilities(context);
      },
      maxRetries: 3,
      estimatedDuration: 500,
    });
  }
  
  async schedulePatternLearning(transition: {
    from: ModeId;
    to: ModeId;
    context: ContextSnapshot;
  }): Promise<string> {
    return this.scheduleTask({
      type: 'learning',
      priority: 'low',
      data: { transition },
      computation: async ({ transition }) => {
        const { enhancedContextInference } = await import('../services/EnhancedContextInference');
        enhancedContextInference.recordModeTransition(
          transition.from,
          transition.to,
          transition.context
        );
        return { learned: true };
      },
      maxRetries: 2,
      estimatedDuration: 200,
    });
  }
  
  async scheduleSuggestionGeneration(context: ContextSnapshot, input?: string): Promise<string> {
    return this.scheduleTask({
      type: 'prediction',
      priority: 'high',
      data: { context, input },
      computation: async ({ context, input }) => {
        const { smartSuggestionEngine } = await import('../services/SmartSuggestionEngine');
        const { modeRegistry } = await import('./ModeRegistry');
        
        const currentMode = modeRegistry.getMode('architect'); // Default mode
        return smartSuggestionEngine.generateSuggestions(context, currentMode, input);
      },
      maxRetries: 3,
      estimatedDuration: 800,
    });
  }
  
  async scheduleHistoryAnalysis(): Promise<string> {
    return this.scheduleTask({
      type: 'optimization',
      priority: 'low',
      data: {},
      computation: async () => {
        const { contextHistoryTracker } = await import('../services/ContextHistoryTracker');
        // Trigger pattern analysis
        await (contextHistoryTracker as any).analyzePatterns();
        return { analyzed: true };
      },
      maxRetries: 1,
      estimatedDuration: 2000,
    });
  }
  
  /**
   * Private methods
   */
  
  private startProcessing(): void {
    if (this.processingTimer) return;
    
    this.processingTimer = setInterval(() => {
      this.processNextBatch();
    }, this.config.processingInterval);
  }
  
  private stopProcessing(): void {
    if (this.processingTimer) {
      clearInterval(this.processingTimer);
      this.processingTimer = null;
    }
  }
  
  private async processNextBatch(): Promise<void> {
    if (this.isProcessing || this.isResourceConstrained()) {
      return;
    }
    
    this.isProcessing = true;
    
    try {
      const availableSlots = this.config.maxConcurrentTasks - this.activeTasks.size;
      if (availableSlots <= 0) {
        return;
      }
      
      const batchSize = this.config.enableBatching 
        ? Math.min(this.config.batchSize, availableSlots)
        : 1;
      
      const tasksToProcess = this.taskQueue.splice(0, batchSize);
      
      // Process tasks concurrently
      const processingPromises = tasksToProcess.map(task => this.processTask(task));
      await Promise.allSettled(processingPromises);
      
    } finally {
      this.isProcessing = false;
    }
  }
  
  private async processTask(task: BackgroundTask): Promise<void> {
    performanceMonitor.startTimer(`background.${task.type}`);
    const startTime = performance.now();
    
    this.activeTasks.set(task.id, task);
    
    try {
      // Execute the computation
      const result = await task.computation(task.data);
      
      // Store result
      this.completedTasks.set(task.id, {
        result,
        completedAt: Date.now(),
      });
      
      // Update metrics
      const processingTime = performance.now() - startTime;
      this.metrics.processingTimes.push(processingTime);
      this.metrics.tasksProcessed++;
      
      // Keep only last 1000 processing times
      if (this.metrics.processingTimes.length > 1000) {
        this.metrics.processingTimes = this.metrics.processingTimes.slice(-1000);
      }
      
      // Clean up completed tasks periodically
      this.cleanupCompletedTasks();
      
      performanceMonitor.endTimer(`background.${task.type}`, { 
        success: 'true',
        duration: processingTime 
      });
      
      // Emit completion event
      eventBus.emit({
        type: 'background.task_completed',
        payload: { 
          taskId: task.id, 
          type: task.type, 
          duration: processingTime 
        },
        source: 'BackgroundProcessor',
      });
      
    } catch (error) {
      this.metrics.errors++;
      
      // Retry logic
      if (task.retries < task.maxRetries) {
        task.retries++;
        
        // Exponential backoff
        const delay = this.config.retryDelay * Math.pow(2, task.retries - 1);
        
        setTimeout(() => {
          this.insertTaskByPriority(task);
        }, delay);
        
        performanceMonitor.endTimer(`background.${task.type}`, { 
          error: 'retry',
          attempt: task.retries 
        });
      } else {
        // Task failed permanently
        this.completedTasks.set(task.id, {
          result: { error: error.message },
          completedAt: Date.now(),
        });
        
        performanceMonitor.endTimer(`background.${task.type}`, { 
          error: 'failed',
          attempts: task.retries 
        });
        
        eventBus.emit({
          type: 'background.task_failed',
          payload: { 
            taskId: task.id, 
            type: task.type, 
            error: error.message,
            attempts: task.retries 
          },
          source: 'BackgroundProcessor',
        });
      }
    } finally {
      this.activeTasks.delete(task.id);
    }
  }
  
  private insertTaskByPriority(task: BackgroundTask): void {
    const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    const taskPriority = priorityOrder[task.priority];
    
    let insertIndex = 0;
    for (let i = 0; i < this.taskQueue.length; i++) {
      const queuedTaskPriority = priorityOrder[this.taskQueue[i].priority];
      if (taskPriority > queuedTaskPriority) {
        insertIndex = i;
        break;
      }
      insertIndex = i + 1;
    }
    
    this.taskQueue.splice(insertIndex, 0, task);
  }
  
  private evictLowPriorityTasks(): void {
    // Remove low priority tasks to make room
    const lowPriorityIndex = this.taskQueue.findIndex(task => task.priority === 'low');
    if (lowPriorityIndex >= 0) {
      const evicted = this.taskQueue.splice(lowPriorityIndex, 1)[0];
      
      eventBus.emit({
        type: 'background.task_evicted',
        payload: { taskId: evicted.id, type: evicted.type },
        source: 'BackgroundProcessor',
      });
    }
  }
  
  private isResourceConstrained(): boolean {
    return this.metrics.resourceUsage > this.config.resourceThrottleThreshold;
  }
  
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private cleanupCompletedTasks(): void {
    const cutoff = Date.now() - 300000; // 5 minutes ago
    
    this.completedTasks.forEach((result, taskId) => {
      if (result.completedAt < cutoff) {
        this.completedTasks.delete(taskId);
      }
    });
  }
  
  private startResourceMonitoring(): void {
    this.resourceMonitor = setInterval(() => {
      this.updateResourceUsage();
    }, 5000); // Check every 5 seconds
  }
  
  private updateResourceUsage(): void {
    // Simple resource usage simulation
    // In a real implementation, this would check actual CPU/memory usage
    const queueLoadFactor = this.taskQueue.length / this.config.maxQueueSize;
    const activeLoadFactor = this.activeTasks.size / this.config.maxConcurrentTasks;
    
    this.metrics.resourceUsage = Math.max(queueLoadFactor, activeLoadFactor);
  }
  
  private setupEventListeners(): void {
    // Listen for cache preload requests
    eventBus.on('cache.predictive_load_requested', (event) => {
      const { key, originalKey } = event.payload;
      
      // Schedule predictive loading as low priority
      this.scheduleTask({
        type: 'prediction',
        priority: 'low',
        data: { key, originalKey },
        computation: async ({ key }) => {
          // This would trigger the appropriate computation based on key pattern
          return intelligentCache.get(key);
        },
        maxRetries: 1,
        estimatedDuration: 300,
      });
    });
  }
  
  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopProcessing();
    
    if (this.resourceMonitor) {
      clearInterval(this.resourceMonitor);
      this.resourceMonitor = null;
    }
    
    this.taskQueue = [];
    this.activeTasks.clear();
    this.completedTasks.clear();
  }
}

// Export singleton instance
export const backgroundProcessor = BackgroundProcessor.getInstance();
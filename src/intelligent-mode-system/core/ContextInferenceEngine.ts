import { 
  ContextSnapshot, 
  ModeId, 
  ModeP<PERSON>babilities,
  ContextRule,
  FileContext,
  ProjectContext,
  UserContext,
  EnvironmentContext 
} from '../types';
import { eventBus } from './EventBus';
import { memoryCache } from './CacheSystem';
import { performanceMonitor } from '../utils/performance';
import { modeRegistry } from './ModeRegistry';

export interface ContextProvider<T> {
  id: string;
  priority: number;
  analyze(): Promise<T>;
  subscribe(listener: (data: T) => void): () => void;
  cache?: boolean;
}

export interface ContextListener {
  (snapshot: ContextSnapshot): void;
}

export class ContextInferenceEngine {
  private providers: Map<string, ContextProvider<any>> = new Map();
  private listeners: Set<ContextListener> = new Set();
  private currentSnapshot: ContextSnapshot | null = null;
  private isAnalyzing = false;
  private analysisInterval: NodeJS.Timeout | null = null;
  private analysisIntervalMs = 5000; // 5 seconds

  constructor() {
    this.setupEventListeners();
  }

  registerProvider<T>(provider: ContextProvider<T>): void {
    this.providers.set(provider.id, provider);
    
    // Subscribe to provider updates if the provider supports it
    if ('subscribe' in provider && typeof provider.subscribe === 'function') {
      provider.subscribe((data) => {
        this.handleProviderUpdate(provider.id, data);
      });
    }
  }

  unregisterProvider(providerId: string): void {
    this.providers.delete(providerId);
  }

  async analyze(): Promise<ContextSnapshot> {
    if (this.isAnalyzing) {
      // Return cached snapshot if analysis is in progress
      if (this.currentSnapshot) {
        return this.currentSnapshot;
      }
      // Wait for current analysis to complete
      return new Promise((resolve) => {
        const checkComplete = setInterval(() => {
          if (!this.isAnalyzing && this.currentSnapshot) {
            clearInterval(checkComplete);
            resolve(this.currentSnapshot);
          }
        }, 100);
      });
    }

    this.isAnalyzing = true;
    performanceMonitor.startTimer('context.analysis');

    try {
      // Check cache first
      const cachedSnapshot = await memoryCache.get<ContextSnapshot>('context.snapshot');
      if (cachedSnapshot && Date.now() - cachedSnapshot.timestamp < 2000) {
        this.currentSnapshot = cachedSnapshot;
        performanceMonitor.endTimer('context.analysis', { cache: 'hit' });
        return cachedSnapshot;
      }

      // Gather context from all providers in parallel
      const providerPromises = Array.from(this.providers.entries()).map(
        async ([id, provider]) => {
          try {
            const startTime = performance.now();
            const data = await provider.analyze();
            const duration = performance.now() - startTime;

            performanceMonitor.recordMetric({
              name: `context.provider.${id}`,
              value: duration,
              unit: 'ms',
              timestamp: Date.now(),
            });

            return { id, data };
          } catch (error) {
            console.error(`Provider ${id} failed:`, error);
            return { id, data: null };
          }
        }
      );

      const results = await Promise.all(providerPromises);

      // Build context snapshot
      const snapshot: ContextSnapshot = {
        fileContext: this.findProviderData(results, 'file') || this.getDefaultFileContext(),
        projectContext: this.findProviderData(results, 'project') || this.getDefaultProjectContext(),
        userContext: this.findProviderData(results, 'user') || this.getDefaultUserContext(),
        environmentContext: this.findProviderData(results, 'environment') || this.getDefaultEnvironmentContext(),
        timestamp: Date.now(),
      };

      this.currentSnapshot = snapshot;

      // Cache the snapshot
      await memoryCache.set('context.snapshot', snapshot, 5000);

      // Notify listeners
      this.notifyListeners(snapshot);

      // Emit event
      eventBus.emit({
        type: 'context.analyzed',
        payload: snapshot,
        source: 'ContextInferenceEngine',
      });

      performanceMonitor.endTimer('context.analysis', { cache: 'miss' });

      return snapshot;
    } catch (error) {
      performanceMonitor.endTimer('context.analysis', { error: 'true' });
      throw error;
    } finally {
      this.isAnalyzing = false;
    }
  }

  subscribe(listener: ContextListener): () => void {
    this.listeners.add(listener);
    
    // Send current snapshot if available
    if (this.currentSnapshot) {
      listener(this.currentSnapshot);
    }

    return () => {
      this.listeners.delete(listener);
    };
  }

  getRecommendedMode(snapshot?: ContextSnapshot): ModeId {
    const context = snapshot || this.currentSnapshot;
    if (!context) return 'architect'; // Default mode

    const probabilities = this.calculateModeProbabilities(context);
    
    // Find mode with highest probability
    let recommendedMode: ModeId = 'architect';
    let highestProbability = 0;

    for (const [modeId, probability] of Object.entries(probabilities)) {
      if (probability > highestProbability) {
        highestProbability = probability;
        recommendedMode = modeId as ModeId;
      }
    }

    // Only recommend if confidence is high enough
    return highestProbability > 0.6 ? recommendedMode : 'architect';
  }

  getProbabilities(snapshot?: ContextSnapshot): ModeProbabilities {
    const context = snapshot || this.currentSnapshot;
    if (!context) {
      return this.getDefaultProbabilities();
    }

    return this.calculateModeProbabilities(context);
  }

  startContinuousAnalysis(intervalMs?: number): void {
    if (this.analysisInterval) {
      this.stopContinuousAnalysis();
    }

    this.analysisIntervalMs = intervalMs || this.analysisIntervalMs;
    
    this.analysisInterval = setInterval(async () => {
      try {
        await this.analyze();
      } catch (error) {
        console.error('Continuous analysis error:', error);
      }
    }, this.analysisIntervalMs);

    // Run initial analysis
    this.analyze().catch(console.error);
  }

  stopContinuousAnalysis(): void {
    if (this.analysisInterval) {
      clearInterval(this.analysisInterval);
      this.analysisInterval = null;
    }
  }

  private calculateModeProbabilities(context: ContextSnapshot): ModeProbabilities {
    const probabilities: ModeProbabilities = {};
    const modes = modeRegistry.getAllModes();

    for (const mode of modes) {
      probabilities[mode.id] = this.calculateModeProbability(mode.id, context);
    }

    // Normalize probabilities
    const total = Object.values(probabilities).reduce((sum, p) => sum + p, 0);
    if (total > 0) {
      for (const modeId in probabilities) {
        probabilities[modeId] = probabilities[modeId] / total;
      }
    }

    return probabilities;
  }

  private calculateModeProbability(modeId: ModeId, context: ContextSnapshot): number {
    let score = 0;
    let totalWeight = 0;

    // Mode-specific scoring based on context
    switch (modeId) {
      case 'architect':
        score += this.scoreArchitectMode(context);
        break;
      case 'debug':
        score += this.scoreDebugMode(context);
        break;
      case 'review':
        score += this.scoreReviewMode(context);
        break;
      case 'deploy':
        score += this.scoreDeployMode(context);
        break;
      case 'experiment':
        score += this.scoreExperimentMode(context);
        break;
      case 'learn':
        score += this.scoreLearnMode(context);
        break;
    }

    return Math.max(0, Math.min(1, score));
  }

  private scoreArchitectMode(context: ContextSnapshot): number {
    let score = 0.1; // Base score

    // Check for architecture-related activities
    if (context.fileContext.path.includes('package.json') || 
        context.fileContext.path.includes('tsconfig') ||
        context.fileContext.path.includes('README')) {
      score += 0.3;
    }
    
    // Architecture-specific files
    if (context.fileContext.path.includes('architecture') || 
        context.fileContext.path.includes('design') ||
        context.fileContext.path.includes('system')) {
      score += 0.5;
    }

    // Check for structural changes
    const recentActions = context.userContext.recentActions.slice(-5);
    const hasStructuralChanges = recentActions.some(action => 
      action.type === 'edit' && (
        action.target?.includes('import') ||
        action.target?.includes('export') ||
        action.target?.includes('module')
      )
    );
    if (hasStructuralChanges) score += 0.2;

    // New file creation
    if (recentActions.some(action => action.type === 'navigate' && action.metadata?.isNew)) {
      score += 0.2;
    }

    return score;
  }

  private scoreDebugMode(context: ContextSnapshot): number {
    let score = 0;

    // High score if errors present
    if (context.fileContext.hasErrors) score += 0.5;
    if (context.fileContext.hasWarnings) score += 0.2;

    // Check for debugging patterns in recent actions
    const recentActions = context.userContext.recentActions.slice(-10);
    const debugPatterns = ['console.log', 'debugger', 'breakpoint', 'error', 'exception'];
    
    const hasDebugActivity = recentActions.some(action => 
      action.type === 'edit' && 
      debugPatterns.some(pattern => action.target?.toLowerCase().includes(pattern))
    );
    if (hasDebugActivity) score += 0.3;

    // Running processes that might be debugging
    const hasDebugProcess = context.environmentContext.runningProcesses.some(
      proc => proc.type === 'test' || proc.name.includes('debug')
    );
    if (hasDebugProcess) score += 0.2;

    return score;
  }

  private scoreReviewMode(context: ContextSnapshot): number {
    let score = 0;

    // Git status indicates review scenarios
    if (context.environmentContext.gitStatus) {
      const git = context.environmentContext.gitStatus;
      if (git.ahead > 0 || git.behind > 0) score += 0.2;
      if (git.staged.length > 0) score += 0.2;
      if (git.hasConflicts) score += 0.3;
    }

    // Check for diff/compare activities
    const recentActions = context.userContext.recentActions.slice(-5);
    const hasReviewActivity = recentActions.some(action => 
      action.type === 'command' && (
        action.target?.includes('diff') ||
        action.target?.includes('compare') ||
        action.target?.includes('review')
      )
    );
    if (hasReviewActivity) score += 0.3;

    return score;
  }

  private scoreDeployMode(context: ContextSnapshot): number {
    let score = 0;

    // Check for deployment-related files
    const deployFiles = ['Dockerfile', '.github/workflows', 'deploy', '.env', 'ci/cd'];
    if (deployFiles.some(file => context.fileContext.path.includes(file))) {
      score += 0.7; // Increased score for stronger signal
    }

    // Check for build/deploy processes
    const hasDeployProcess = context.environmentContext.runningProcesses.some(
      proc => proc.type === 'build' || proc.name.includes('deploy')
    );
    if (hasDeployProcess) score += 0.3;

    // Check for deployment commands
    const recentActions = context.userContext.recentActions.slice(-5);
    const deployCommands = ['build', 'deploy', 'release', 'publish'];
    const hasDeployActivity = recentActions.some(action => 
      action.type === 'command' && 
      deployCommands.some(cmd => action.target?.includes(cmd))
    );
    if (hasDeployActivity) score += 0.3;

    return score;
  }

  private scoreExperimentMode(context: ContextSnapshot): number {
    let score = 0.1; // Base score for experimentation

    // Scratch files or playground
    if (context.fileContext.path.includes('scratch') || 
        context.fileContext.path.includes('playground') ||
        context.fileContext.path.includes('test')) {
      score += 0.4;
    }

    // Rapid file switching or creation
    const recentActions = context.userContext.recentActions.slice(-10);
    const uniqueFiles = new Set(
      recentActions
        .filter(action => action.type === 'navigate')
        .map(action => action.target)
    );
    if (uniqueFiles.size > 5) score += 0.2;

    // High edit frequency
    const editCount = recentActions.filter(action => action.type === 'edit').length;
    if (editCount > 7) score += 0.2;

    return score;
  }

  private scoreLearnMode(context: ContextSnapshot): number {
    let score = 0;

    // Documentation files
    if (context.fileContext.path.endsWith('.md') || 
        context.fileContext.path.includes('docs') ||
        context.fileContext.path.includes('README')) {
      score += 0.5;
    }

    // Search and help activities
    const recentActions = context.userContext.recentActions.slice(-5);
    const hasLearnActivity = recentActions.some(action => 
      action.type === 'search' || 
      (action.type === 'command' && action.target?.includes('help'))
    );
    if (hasLearnActivity) score += 0.3;

    return score;
  }

  private findProviderData(results: Array<{ id: string; data: any }>, type: string): any {
    const result = results.find(r => r.id.includes(type));
    return result?.data || null;
  }

  private handleProviderUpdate(providerId: string, data: any): void {
    // Trigger incremental analysis if significant change
    if (this.isSignificantChange(providerId, data)) {
      this.analyze().catch(console.error);
    }
  }

  private isSignificantChange(providerId: string, data: any): boolean {
    // Define what constitutes a significant change per provider
    if (providerId.includes('file') && data.hasErrors !== this.currentSnapshot?.fileContext.hasErrors) {
      return true;
    }
    if (providerId.includes('git') && data.hasConflicts) {
      return true;
    }
    // Add more significant change detection logic
    return false;
  }

  private notifyListeners(snapshot: ContextSnapshot): void {
    this.listeners.forEach(listener => {
      try {
        listener(snapshot);
      } catch (error) {
        console.error('Context listener error:', error);
      }
    });
  }

  private setupEventListeners(): void {
    // Listen for context update requests
    eventBus.on('context.update', async () => {
      await this.analyze();
    });
  }

  // Default context generators
  private getDefaultFileContext(): FileContext {
    return {
      path: '',
      type: 'unknown',
      hasErrors: false,
      hasWarnings: false,
      lastModified: Date.now(),
    };
  }

  private getDefaultProjectContext(): ProjectContext {
    return {
      type: 'unknown',
      rootPath: '',
      dependencies: {},
      structure: {
        directories: [],
        fileCount: 0,
        totalSize: 0,
        depth: 0,
        hasTests: false,
        hasDocs: false,
      },
      configuration: {},
    };
  }

  private getDefaultUserContext(): UserContext {
    return {
      recentActions: [],
      preferences: {
        theme: 'dark',
        shortcuts: {},
        autoTransition: true,
        suggestionLevel: 'standard',
      },
      patterns: [],
      sessionDuration: 0,
      lastActivity: Date.now(),
    };
  }

  private getDefaultEnvironmentContext(): EnvironmentContext {
    return {
      runningProcesses: [],
      systemResources: {
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
      },
      openFiles: [],
      activeTerminals: 0,
    };
  }

  private getDefaultProbabilities(): ModeProbabilities {
    return {
      architect: 0.2,
      debug: 0.2,
      review: 0.15,
      deploy: 0.15,
      experiment: 0.15,
      learn: 0.15,
    };
  }
}

// Singleton instance
export const contextInferenceEngine = new ContextInferenceEngine();
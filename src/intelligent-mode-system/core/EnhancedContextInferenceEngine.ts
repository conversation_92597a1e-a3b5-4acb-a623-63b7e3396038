import { 
  ContextSnapshot, 
  ModeId, 
  ModeProbabilities,
  ContextRule,
  FileContext,
  ProjectContext,
  UserContext,
  EnvironmentContext 
} from '../types';
import { eventBus } from './EventBus';
import { memoryCache } from './CacheSystem';
import { performanceMonitor } from '../utils/performance';
import { modeRegistry } from './ModeRegistry';
import { ContextProvider } from './ContextInferenceEngine';
import { enhancedContextInference } from '../services/EnhancedContextInference';
import { smartSuggestionEngine } from '../services/SmartSuggestionEngine';
import { contextHistoryTracker } from '../services/ContextHistoryTracker';

/**
 * Enhanced Context Inference Engine - Integration Layer
 * 
 * This integrates the new ML-based services with the existing infrastructure:
 * - Enhanced context inference with pattern recognition
 * - Smart suggestion ranking and personalization  
 * - Context history tracking and learning
 */

export interface ContextListener {
  (snapshot: ContextSnapshot): void;
}

export class EnhancedContextInferenceEngine {
  private providers: Map<string, ContextProvider<any>> = new Map();
  private listeners: Set<ContextListener> = new Set();
  private currentSnapshot: ContextSnapshot | null = null;
  private currentMode: ModeId = 'architect';
  private isAnalyzing = false;
  private analysisInterval: NodeJS.Timeout | null = null;
  private analysisIntervalMs = 5000; // 5 seconds

  constructor() {
    this.setupEventListeners();
  }

  registerProvider<T>(provider: ContextProvider<T>): void {
    this.providers.set(provider.id, provider);
    
    // Subscribe to provider updates if the provider supports it
    if ('subscribe' in provider && typeof provider.subscribe === 'function') {
      provider.subscribe((data) => {
        this.handleProviderUpdate(provider.id, data);
      });
    }
  }

  unregisterProvider(providerId: string): void {
    this.providers.delete(providerId);
  }

  async analyze(): Promise<ContextSnapshot> {
    if (this.isAnalyzing) {
      // Return cached snapshot if analysis is in progress
      if (this.currentSnapshot) {
        return this.currentSnapshot;
      }
      // Wait for current analysis to complete
      return new Promise((resolve) => {
        const checkComplete = setInterval(() => {
          if (!this.isAnalyzing && this.currentSnapshot) {
            clearInterval(checkComplete);
            resolve(this.currentSnapshot);
          }
        }, 100);
      });
    }

    this.isAnalyzing = true;
    performanceMonitor.startTimer('enhanced.context.analysis');

    try {
      // Check cache first
      const cachedSnapshot = await memoryCache.get<ContextSnapshot>('context.snapshot');
      if (cachedSnapshot && Date.now() - cachedSnapshot.timestamp < 2000) {
        this.currentSnapshot = cachedSnapshot;
        performanceMonitor.endTimer('enhanced.context.analysis', { cache: 'hit' });
        return cachedSnapshot;
      }

      // Gather context from all providers in parallel
      const providerPromises = Array.from(this.providers.entries()).map(
        async ([id, provider]) => {
          try {
            const startTime = performance.now();
            const data = await provider.analyze();
            const duration = performance.now() - startTime;

            performanceMonitor.recordMetric({
              name: `context.provider.${id}`,
              value: duration,
              unit: 'ms',
              timestamp: Date.now(),
            });

            return { id, data };
          } catch (error) {
            console.error(`Provider ${id} failed:`, error);
            return { id, data: null };
          }
        }
      );

      const results = await Promise.all(providerPromises);

      // Build context snapshot
      const snapshot: ContextSnapshot = {
        fileContext: this.findProviderData(results, 'file') || this.getDefaultFileContext(),
        projectContext: this.findProviderData(results, 'project') || this.getDefaultProjectContext(),
        userContext: this.findProviderData(results, 'user') || this.getDefaultUserContext(),
        environmentContext: this.findProviderData(results, 'environment') || this.getDefaultEnvironmentContext(),
        timestamp: Date.now(),
      };

      this.currentSnapshot = snapshot;

      // Cache the snapshot
      await memoryCache.set('context.snapshot', snapshot, 5000);

      // Track context history
      contextHistoryTracker.trackContext(snapshot, this.currentMode);

      // Notify listeners
      this.notifyListeners(snapshot);

      // Emit event
      eventBus.emit({
        type: 'context.analyzed',
        payload: { ...snapshot, currentMode: this.currentMode },
        source: 'EnhancedContextInferenceEngine',
      });

      performanceMonitor.endTimer('enhanced.context.analysis', { cache: 'miss' });

      return snapshot;
    } catch (error) {
      performanceMonitor.endTimer('enhanced.context.analysis', { error: 'true' });
      throw error;
    } finally {
      this.isAnalyzing = false;
    }
  }

  subscribe(listener: ContextListener): () => void {
    this.listeners.add(listener);
    
    // Send current snapshot if available
    if (this.currentSnapshot) {
      listener(this.currentSnapshot);
    }

    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Get enhanced mode recommendation with ML-based analysis
   */
  async getRecommendedMode(snapshot?: ContextSnapshot): Promise<{
    mode: ModeId;
    confidence: number;
    reasoning: string[];
    probabilities: ModeProbabilities;
  } | null> {
    const context = snapshot || this.currentSnapshot;
    if (!context) return null;

    // Get ML-based probabilities
    const probabilities = await enhancedContextInference.calculateModeProbabilities(context);
    
    // Get recommendation with reasoning
    const recommendation = enhancedContextInference.getRecommendation(probabilities);
    
    if (recommendation) {
      return {
        ...recommendation,
        probabilities,
      };
    }

    return null;
  }

  /**
   * Get smart suggestions for current context
   */
  async getSmartSuggestions(input?: string) {
    const context = this.currentSnapshot;
    if (!context) return [];

    const mode = modeRegistry.getMode(this.currentMode);
    if (!mode) return [];

    return smartSuggestionEngine.generateSuggestions(context, mode, input);
  }

  /**
   * Get enhanced probabilities with ML analysis
   */
  async getProbabilities(snapshot?: ContextSnapshot): Promise<ModeProbabilities> {
    const context = snapshot || this.currentSnapshot;
    if (!context) {
      return this.getDefaultProbabilities();
    }

    return enhancedContextInference.calculateModeProbabilities(context);
  }

  /**
   * Get insights from context history
   */
  getInsights() {
    return contextHistoryTracker.getInsights();
  }

  /**
   * Get predictions based on historical data
   */
  getPredictions() {
    if (!this.currentSnapshot) return null;
    return contextHistoryTracker.predictNext(this.currentSnapshot);
  }

  /**
   * Track mode transition for learning
   */
  transitionToMode(newMode: ModeId): void {
    if (this.currentMode !== newMode && this.currentSnapshot) {
      // Record transition for learning
      enhancedContextInference.recordModeTransition(
        this.currentMode,
        newMode,
        this.currentSnapshot
      );

      // Update current mode
      this.currentMode = newMode;

      // Emit transition event
      eventBus.emit({
        type: 'mode.transitioned',
        payload: {
          from: this.currentMode,
          to: newMode,
          context: this.currentSnapshot,
        },
        source: 'EnhancedContextInferenceEngine',
      });
    }
  }

  startContinuousAnalysis(intervalMs?: number): void {
    if (this.analysisInterval) {
      this.stopContinuousAnalysis();
    }

    this.analysisIntervalMs = intervalMs || this.analysisIntervalMs;
    
    this.analysisInterval = setInterval(async () => {
      try {
        await this.analyze();
      } catch (error) {
        console.error('Continuous analysis error:', error);
      }
    }, this.analysisIntervalMs);

    // Run initial analysis
    this.analyze().catch(console.error);
  }

  stopContinuousAnalysis(): void {
    if (this.analysisInterval) {
      clearInterval(this.analysisInterval);
      this.analysisInterval = null;
    }
  }

  private findProviderData(results: Array<{ id: string; data: any }>, type: string): any {
    const result = results.find(r => r.id.includes(type));
    return result?.data || null;
  }

  private handleProviderUpdate(providerId: string, data: any): void {
    // Trigger incremental analysis if significant change
    if (this.isSignificantChange(providerId, data)) {
      this.analyze().catch(console.error);
    }
  }

  private isSignificantChange(providerId: string, data: any): boolean {
    // Define what constitutes a significant change per provider
    if (providerId.includes('file') && data.hasErrors !== this.currentSnapshot?.fileContext.hasErrors) {
      return true;
    }
    if (providerId.includes('git') && data.hasConflicts) {
      return true;
    }
    // Add more significant change detection logic
    return false;
  }

  private notifyListeners(snapshot: ContextSnapshot): void {
    this.listeners.forEach(listener => {
      try {
        listener(snapshot);
      } catch (error) {
        console.error('Context listener error:', error);
      }
    });
  }

  private setupEventListeners(): void {
    // Listen for context update requests
    eventBus.on('context.update', async () => {
      await this.analyze();
    });

    // Listen for suggestion usage
    eventBus.on('suggestion.used', (event) => {
      const { suggestionId, action, context } = event.payload;
      smartSuggestionEngine.trackSuggestionUsage(suggestionId, action, context);
    });

    // Listen for suggestion ratings
    eventBus.on('suggestion.rated', (event) => {
      const { suggestionId, rating } = event.payload;
      smartSuggestionEngine.rateSuggestion(suggestionId, rating);
    });
  }

  // Default context generators
  private getDefaultFileContext(): FileContext {
    return {
      path: '',
      type: 'unknown',
      hasErrors: false,
      hasWarnings: false,
      lastModified: Date.now(),
    };
  }

  private getDefaultProjectContext(): ProjectContext {
    return {
      type: 'unknown',
      rootPath: '',
      dependencies: {},
      structure: {
        directories: [],
        fileCount: 0,
        totalSize: 0,
        depth: 0,
        hasTests: false,
        hasDocs: false,
      },
      configuration: {},
    };
  }

  private getDefaultUserContext(): UserContext {
    return {
      recentActions: [],
      preferences: {
        theme: 'dark',
        shortcuts: {},
        autoTransition: true,
        suggestionLevel: 'standard',
      },
      patterns: [],
      sessionDuration: 0,
      lastActivity: Date.now(),
    };
  }

  private getDefaultEnvironmentContext(): EnvironmentContext {
    return {
      runningProcesses: [],
      systemResources: {
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
      },
      openFiles: [],
      activeTerminals: 0,
    };
  }

  private getDefaultProbabilities(): ModeProbabilities {
    return {
      architect: 0.2,
      debug: 0.2,
      review: 0.15,
      deploy: 0.15,
      experiment: 0.15,
      learn: 0.15,
    };
  }
}

// Singleton instance
export const enhancedContextInferenceEngine = new EnhancedContextInferenceEngine();
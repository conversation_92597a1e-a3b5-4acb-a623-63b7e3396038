export interface PerformanceMetrics {
  operationName: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private timers: Map<string, number> = new Map();

  start(operationName: string): void {
    this.timers.set(operationName, performance.now());
  }

  // Alias for backward compatibility
  startTimer(operationName: string): void {
    this.start(operationName);
  }

  end(operationName: string, metadata?: Record<string, any>): number {
    const startTime = this.timers.get(operationName);
    if (!startTime) {
      console.warn(`No start time found for operation: ${operationName}`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(operationName);

    this.metrics.push({
      operationName,
      duration,
      timestamp: Date.now(),
      metadata,
    });

    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    return duration;
  }

  getMetrics(operationName?: string): PerformanceMetrics[] {
    if (operationName) {
      return this.metrics.filter(m => m.operationName === operationName);
    }
    return [...this.metrics];
  }

  getAverageDuration(operationName: string): number {
    const operationMetrics = this.getMetrics(operationName);
    if (operationMetrics.length === 0) return 0;

    const total = operationMetrics.reduce((sum, m) => sum + m.duration, 0);
    return total / operationMetrics.length;
  }

  clear(): void {
    this.metrics = [];
    this.timers.clear();
  }
}

export const performanceMonitor = new PerformanceMonitor();
/**
 * Responsive design utilities and breakpoints
 */

export const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export type Breakpoint = keyof typeof breakpoints;

/**
 * Media query helpers
 */
export const media = {
  xs: `@media (min-width: ${breakpoints.xs}px)`,
  sm: `@media (min-width: ${breakpoints.sm}px)`,
  md: `@media (min-width: ${breakpoints.md}px)`,
  lg: `@media (min-width: ${breakpoints.lg}px)`,
  xl: `@media (min-width: ${breakpoints.xl}px)`,
  '2xl': `@media (min-width: ${breakpoints['2xl']}px)`,
  
  // Utility queries
  mobile: `@media (max-width: ${breakpoints.md - 1}px)`,
  tablet: `@media (min-width: ${breakpoints.md}px) and (max-width: ${breakpoints.lg - 1}px)`,
  desktop: `@media (min-width: ${breakpoints.lg}px)`,
  
  // Feature queries
  hover: '@media (hover: hover)',
  touch: '@media (hover: none) and (pointer: coarse)',
  reducedMotion: '@media (prefers-reduced-motion: reduce)',
  highContrast: '@media (prefers-contrast: high)',
  darkMode: '@media (prefers-color-scheme: dark)',
} as const;

/**
 * Hook to detect current breakpoint
 */
import { useState, useEffect } from 'react';

export function useBreakpoint(): Breakpoint {
  const [breakpoint, setBreakpoint] = useState<Breakpoint>('lg');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      
      if (width >= breakpoints['2xl']) {
        setBreakpoint('2xl');
      } else if (width >= breakpoints.xl) {
        setBreakpoint('xl');
      } else if (width >= breakpoints.lg) {
        setBreakpoint('lg');
      } else if (width >= breakpoints.md) {
        setBreakpoint('md');
      } else if (width >= breakpoints.sm) {
        setBreakpoint('sm');
      } else {
        setBreakpoint('xs');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
}

/**
 * Hook to check if we're on a mobile device
 */
export function useIsMobile(): boolean {
  const breakpoint = useBreakpoint();
  return breakpoint === 'xs' || breakpoint === 'sm';
}

/**
 * Hook to check if we're on a tablet
 */
export function useIsTablet(): boolean {
  const breakpoint = useBreakpoint();
  return breakpoint === 'md';
}

/**
 * Hook to check if we're on desktop
 */
export function useIsDesktop(): boolean {
  const breakpoint = useBreakpoint();
  return breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl';
}

/**
 * Responsive value helper
 */
export function getResponsiveValue<T>(
  values: Partial<Record<Breakpoint, T>>,
  currentBreakpoint: Breakpoint,
  defaultValue: T
): T {
  // Start from current breakpoint and work down
  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);
  
  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const bp = breakpointOrder[i];
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }
  
  return defaultValue;
}

/**
 * CSS classes for responsive utilities
 */
export const responsiveClasses = {
  // Container
  container: 'w-full mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl',
  
  // Grid
  grid: {
    base: 'grid gap-4',
    cols: {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
      6: 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6',
    },
  },
  
  // Flex
  flex: {
    stack: 'flex flex-col',
    stackReverse: 'flex flex-col-reverse',
    row: 'flex flex-row',
    rowReverse: 'flex flex-row-reverse',
    wrap: 'flex flex-wrap',
    center: 'flex items-center justify-center',
    between: 'flex items-center justify-between',
    around: 'flex items-center justify-around',
  },
  
  // Spacing
  spacing: {
    section: 'py-8 md:py-12 lg:py-16',
    component: 'p-4 md:p-6 lg:p-8',
    tight: 'p-2 md:p-3 lg:p-4',
  },
  
  // Text
  text: {
    h1: 'text-3xl md:text-4xl lg:text-5xl font-bold',
    h2: 'text-2xl md:text-3xl lg:text-4xl font-semibold',
    h3: 'text-xl md:text-2xl lg:text-3xl font-semibold',
    h4: 'text-lg md:text-xl lg:text-2xl font-medium',
    body: 'text-sm md:text-base',
    small: 'text-xs md:text-sm',
  },
  
  // Visibility
  visibility: {
    mobileOnly: 'block md:hidden',
    tabletOnly: 'hidden md:block lg:hidden',
    desktopOnly: 'hidden lg:block',
    hideMobile: 'hidden md:block',
    hideTablet: 'block md:hidden lg:block',
    hideDesktop: 'block lg:hidden',
  },
} as const;

/**
 * Touch-friendly size utilities
 */
export const touchTargetSize = {
  small: 'min-h-[36px] min-w-[36px]',
  medium: 'min-h-[44px] min-w-[44px]',
  large: 'min-h-[48px] min-w-[48px]',
} as const;

/**
 * Safe area insets for mobile devices
 */
export const safeArea = {
  top: 'pt-[env(safe-area-inset-top)]',
  right: 'pr-[env(safe-area-inset-right)]',
  bottom: 'pb-[env(safe-area-inset-bottom)]',
  left: 'pl-[env(safe-area-inset-left)]',
  all: 'p-[env(safe-area-inset-top)_env(safe-area-inset-right)_env(safe-area-inset-bottom)_env(safe-area-inset-left)]',
} as const;
import { PerformanceMetric, PerformanceThreshold } from '../types';
import { eventBus } from '../core/EventBus';

export class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private thresholds: Map<string, PerformanceThreshold> = new Map();
  private timers: Map<string, number> = new Map();

  constructor() {
    this.initializeDefaultThresholds();
  }

  startTimer(name: string): void {
    this.timers.set(name, performance.now());
  }

  endTimer(name: string, tags?: Record<string, string>): number {
    const startTime = this.timers.get(name);
    if (!startTime) {
      console.warn(`Timer ${name} was not started`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(name);

    this.recordMetric({
      name,
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
      tags,
    });

    return duration;
  }

  recordMetric(metric: PerformanceMetric): void {
    // Store metric
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }
    
    const metricHistory = this.metrics.get(metric.name)!;
    metricHistory.push(metric);

    // Keep only last 1000 metrics per name
    if (metricHistory.length > 1000) {
      metricHistory.shift();
    }

    // Check thresholds
    this.checkThreshold(metric);

    // Emit metric event
    eventBus.emit({
      type: 'performance.metric',
      payload: metric,
      source: 'PerformanceMonitor',
    });
  }

  setThreshold(metric: string, warning: number, critical: number): void {
    this.thresholds.set(metric, { metric, warning, critical });
  }

  getMetrics(name: string, limit?: number): PerformanceMetric[] {
    const metrics = this.metrics.get(name) || [];
    return limit ? metrics.slice(-limit) : metrics;
  }

  getStats(name: string): {
    min: number;
    max: number;
    avg: number;
    p95: number;
    p99: number;
    count: number;
  } | null {
    const metrics = this.metrics.get(name);
    if (!metrics || metrics.length === 0) return null;

    const values = metrics.map(m => m.value).sort((a, b) => a - b);
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      min: values[0],
      max: values[values.length - 1],
      avg: sum / values.length,
      p95: values[Math.floor(values.length * 0.95)],
      p99: values[Math.floor(values.length * 0.99)],
      count: values.length,
    };
  }

  async measure<T>(name: string, fn: () => Promise<T>): Promise<T> {
    this.startTimer(name);
    try {
      const result = await fn();
      this.endTimer(name);
      return result;
    } catch (error) {
      this.endTimer(name, { error: 'true' });
      throw error;
    }
  }

  measureSync<T>(name: string, fn: () => T): T {
    this.startTimer(name);
    try {
      const result = fn();
      this.endTimer(name);
      return result;
    } catch (error) {
      this.endTimer(name, { error: 'true' });
      throw error;
    }
  }

  private checkThreshold(metric: PerformanceMetric): void {
    const threshold = this.thresholds.get(metric.name);
    if (!threshold) return;

    if (metric.value >= threshold.critical) {
      eventBus.emit({
        type: 'performance.metric',
        payload: {
          metric,
          level: 'critical',
          threshold: threshold.critical,
        },
        source: 'PerformanceMonitor',
      });
    } else if (metric.value >= threshold.warning) {
      eventBus.emit({
        type: 'performance.metric',
        payload: {
          metric,
          level: 'warning',
          threshold: threshold.warning,
        },
        source: 'PerformanceMonitor',
      });
    }
  }

  private initializeDefaultThresholds(): void {
    // Context analysis thresholds
    this.setThreshold('context.analysis', 1500, 2000);
    
    // Mode transition thresholds
    this.setThreshold('mode.transition', 800, 1000);
    
    // Suggestion generation thresholds
    this.setThreshold('suggestion.generation', 150, 200);
    
    // Cache operation thresholds
    this.setThreshold('cache.get', 10, 50);
    this.setThreshold('cache.set', 20, 100);
  }

  generateReport(): {
    metrics: Record<string, any>;
    violations: Array<{ metric: string; level: string; count: number }>;
    summary: {
      totalMetrics: number;
      avgResponseTime: number;
      errorRate: number;
    };
  } {
    const report: any = {
      metrics: {},
      violations: [],
      summary: {
        totalMetrics: 0,
        avgResponseTime: 0,
        errorRate: 0,
      },
    };

    let totalDuration = 0;
    let totalCount = 0;
    let errorCount = 0;

    // Analyze each metric
    for (const [name, metrics] of this.metrics.entries()) {
      const stats = this.getStats(name);
      if (stats) {
        report.metrics[name] = stats;
        totalDuration += stats.avg * stats.count;
        totalCount += stats.count;

        // Count errors
        errorCount += metrics.filter(m => m.tags?.error === 'true').length;

        // Check for violations
        const threshold = this.thresholds.get(name);
        if (threshold) {
          const warnings = metrics.filter(m => m.value >= threshold.warning && m.value < threshold.critical).length;
          const criticals = metrics.filter(m => m.value >= threshold.critical).length;

          if (warnings > 0) {
            report.violations.push({ metric: name, level: 'warning', count: warnings });
          }
          if (criticals > 0) {
            report.violations.push({ metric: name, level: 'critical', count: criticals });
          }
        }
      }
    }

    // Calculate summary
    report.summary.totalMetrics = totalCount;
    report.summary.avgResponseTime = totalCount > 0 ? totalDuration / totalCount : 0;
    report.summary.errorRate = totalCount > 0 ? (errorCount / totalCount) * 100 : 0;

    return report;
  }

  reset(): void {
    this.metrics.clear();
    this.timers.clear();
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Also export the core performance monitor for compatibility
export { performanceMonitor as corePerformanceMonitor } from '../core/PerformanceMonitor';

// Utility functions
export function measureAsync<T>(name: string): MethodDecorator {
  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      return performanceMonitor.measure(
        `${target.constructor.name}.${String(propertyKey)}`,
        () => originalMethod.apply(this, args)
      );
    };

    return descriptor;
  };
}

export function measureSync<T>(name: string): MethodDecorator {
  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      return performanceMonitor.measureSync(
        `${target.constructor.name}.${String(propertyKey)}`,
        () => originalMethod.apply(this, args)
      );
    };

    return descriptor;
  };
}
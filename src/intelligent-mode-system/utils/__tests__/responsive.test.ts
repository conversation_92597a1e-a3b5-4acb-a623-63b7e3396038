import { renderHook, act } from '@testing-library/react-hooks';
import {
  breakpoints,
  useBreakpoint,
  useIsMobile,
  useIsTablet,
  useIsDesktop,
  getResponsiveValue,
} from '../responsive';

describe('Responsive Utilities', () => {
  // Mock window.innerWidth
  const setWindowWidth = (width: number) => {
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: width,
    });
    window.dispatchEvent(new Event('resize'));
  };

  describe('useBreakpoint', () => {
    it('detects xs breakpoint', () => {
      setWindowWidth(320);
      const { result } = renderHook(() => useBreakpoint());
      expect(result.current).toBe('xs');
    });

    it('detects sm breakpoint', () => {
      setWindowWidth(640);
      const { result } = renderHook(() => useBreakpoint());
      expect(result.current).toBe('sm');
    });

    it('detects md breakpoint', () => {
      setWindowWidth(768);
      const { result } = renderHook(() => useBreakpoint());
      expect(result.current).toBe('md');
    });

    it('detects lg breakpoint', () => {
      setWindowWidth(1024);
      const { result } = renderHook(() => useBreakpoint());
      expect(result.current).toBe('lg');
    });

    it('detects xl breakpoint', () => {
      setWindowWidth(1280);
      const { result } = renderHook(() => useBreakpoint());
      expect(result.current).toBe('xl');
    });

    it('detects 2xl breakpoint', () => {
      setWindowWidth(1536);
      const { result } = renderHook(() => useBreakpoint());
      expect(result.current).toBe('2xl');
    });

    it('updates on window resize', () => {
      setWindowWidth(320);
      const { result } = renderHook(() => useBreakpoint());
      
      expect(result.current).toBe('xs');
      
      act(() => {
        setWindowWidth(1024);
      });
      
      expect(result.current).toBe('lg');
    });

    it('cleans up resize listener on unmount', () => {
      const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');
      
      const { unmount } = renderHook(() => useBreakpoint());
      unmount();
      
      expect(removeEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function));
      
      removeEventListenerSpy.mockRestore();
    });
  });

  describe('useIsMobile', () => {
    it('returns true for xs breakpoint', () => {
      setWindowWidth(320);
      const { result } = renderHook(() => useIsMobile());
      expect(result.current).toBe(true);
    });

    it('returns true for sm breakpoint', () => {
      setWindowWidth(640);
      const { result } = renderHook(() => useIsMobile());
      expect(result.current).toBe(true);
    });

    it('returns false for md and above', () => {
      setWindowWidth(768);
      const { result } = renderHook(() => useIsMobile());
      expect(result.current).toBe(false);
      
      setWindowWidth(1024);
      expect(result.current).toBe(false);
    });

    it('updates on resize', () => {
      setWindowWidth(320);
      const { result } = renderHook(() => useIsMobile());
      
      expect(result.current).toBe(true);
      
      act(() => {
        setWindowWidth(1024);
      });
      
      expect(result.current).toBe(false);
    });
  });

  describe('useIsTablet', () => {
    it('returns true only for md breakpoint', () => {
      setWindowWidth(768);
      const { result } = renderHook(() => useIsTablet());
      expect(result.current).toBe(true);
    });

    it('returns false for other breakpoints', () => {
      setWindowWidth(640);
      const { result: mobile } = renderHook(() => useIsTablet());
      expect(mobile.current).toBe(false);
      
      setWindowWidth(1024);
      const { result: desktop } = renderHook(() => useIsTablet());
      expect(desktop.current).toBe(false);
    });
  });

  describe('useIsDesktop', () => {
    it('returns true for lg and above', () => {
      setWindowWidth(1024);
      const { result: lg } = renderHook(() => useIsDesktop());
      expect(lg.current).toBe(true);
      
      setWindowWidth(1280);
      const { result: xl } = renderHook(() => useIsDesktop());
      expect(xl.current).toBe(true);
      
      setWindowWidth(1536);
      const { result: xxl } = renderHook(() => useIsDesktop());
      expect(xxl.current).toBe(true);
    });

    it('returns false for mobile and tablet', () => {
      setWindowWidth(320);
      const { result: mobile } = renderHook(() => useIsDesktop());
      expect(mobile.current).toBe(false);
      
      setWindowWidth(768);
      const { result: tablet } = renderHook(() => useIsDesktop());
      expect(tablet.current).toBe(false);
    });
  });

  describe('getResponsiveValue', () => {
    it('returns exact match for current breakpoint', () => {
      const values = {
        xs: 'mobile',
        md: 'tablet',
        lg: 'desktop',
      };
      
      expect(getResponsiveValue(values, 'md', 'default')).toBe('tablet');
      expect(getResponsiveValue(values, 'lg', 'default')).toBe('desktop');
    });

    it('falls back to smaller breakpoints', () => {
      const values = {
        xs: 'mobile',
        lg: 'desktop',
      };
      
      // md should fall back to xs
      expect(getResponsiveValue(values, 'md', 'default')).toBe('mobile');
      
      // xl should fall back to lg
      expect(getResponsiveValue(values, 'xl', 'default')).toBe('desktop');
    });

    it('returns default when no matching breakpoint', () => {
      const values = {
        lg: 'desktop',
      };
      
      // xs and sm have no fallback
      expect(getResponsiveValue(values, 'xs', 'default')).toBe('default');
      expect(getResponsiveValue(values, 'sm', 'default')).toBe('default');
    });

    it('handles all breakpoint types', () => {
      const values = {
        xs: 1,
        sm: 2,
        md: 3,
        lg: 4,
        xl: 5,
        '2xl': 6,
      };
      
      expect(getResponsiveValue(values, '2xl', 0)).toBe(6);
      expect(getResponsiveValue(values, 'xl', 0)).toBe(5);
      expect(getResponsiveValue(values, 'lg', 0)).toBe(4);
    });

    it('works with different value types', () => {
      const stringValues = {
        xs: 'small',
        lg: 'large',
      };
      
      const numberValues = {
        xs: 12,
        lg: 24,
      };
      
      const booleanValues = {
        xs: false,
        lg: true,
      };
      
      expect(getResponsiveValue(stringValues, 'lg', 'default')).toBe('large');
      expect(getResponsiveValue(numberValues, 'xs', 0)).toBe(12);
      expect(getResponsiveValue(booleanValues, 'md', false)).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    it('handles boundary values correctly', () => {
      // Test exact boundary values
      setWindowWidth(639);
      const { result: belowSm } = renderHook(() => useBreakpoint());
      expect(belowSm.current).toBe('xs');
      
      setWindowWidth(640);
      const { result: atSm } = renderHook(() => useBreakpoint());
      expect(atSm.current).toBe('sm');
      
      setWindowWidth(767);
      const { result: belowMd } = renderHook(() => useBreakpoint());
      expect(belowMd.current).toBe('sm');
      
      setWindowWidth(768);
      const { result: atMd } = renderHook(() => useBreakpoint());
      expect(atMd.current).toBe('md');
    });

    it('handles very small screens', () => {
      setWindowWidth(200);
      const { result } = renderHook(() => useBreakpoint());
      expect(result.current).toBe('xs');
    });

    it('handles very large screens', () => {
      setWindowWidth(3000);
      const { result } = renderHook(() => useBreakpoint());
      expect(result.current).toBe('2xl');
    });

    it('handles rapid resize events', () => {
      const { result } = renderHook(() => useBreakpoint());
      
      act(() => {
        setWindowWidth(320);
        setWindowWidth(640);
        setWindowWidth(768);
        setWindowWidth(1024);
      });
      
      expect(result.current).toBe('lg');
    });
  });
});
import { renderHook, act } from '@testing-library/react-hooks';
import {
  announcer,
  useFocusTrap,
  useRovingTabIndex,
  useAccessibleShortcuts,
  getContrastRatio,
  meetsWCAGStandard,
} from '../accessibility';

describe('Accessibility Utilities', () => {
  describe('LiveRegionAnnouncer', () => {
    let container: HTMLDivElement | null;

    beforeEach(() => {
      // Clean up any existing announcer
      container = document.querySelector('[role="status"]');
      if (container) {
        container.remove();
      }
    });

    it('creates announcement container on first use', () => {
      announcer.announce('Test message');
      
      container = document.querySelector('[role="status"]');
      expect(container).toBeInTheDocument();
      expect(container).toHaveAttribute('aria-live', 'polite');
      expect(container).toHaveAttribute('aria-atomic', 'true');
      expect(container).toHaveClass('sr-only');
    });

    it('announces polite messages', () => {
      announcer.announce('Polite message', 'polite');
      
      container = document.querySelector('[role="status"]');
      expect(container?.textContent).toBe('Polite message');
      expect(container).toHaveAttribute('aria-live', 'polite');
    });

    it('announces assertive messages', () => {
      announcer.announce('Urgent message', 'assertive');
      
      container = document.querySelector('[role="status"]');
      expect(container?.textContent).toBe('Urgent message');
      expect(container).toHaveAttribute('aria-live', 'assertive');
    });

    it('clears announcement after timeout', async () => {
      jest.useFakeTimers();
      
      announcer.announce('Temporary message');
      container = document.querySelector('[role="status"]');
      expect(container?.textContent).toBe('Temporary message');
      
      act(() => {
        jest.advanceTimersByTime(1000);
      });
      
      expect(container?.textContent).toBe('');
      
      jest.useRealTimers();
    });
  });

  describe('useFocusTrap', () => {
    let container: HTMLDivElement;
    let button1: HTMLButtonElement;
    let button2: HTMLButtonElement;
    let button3: HTMLButtonElement;

    beforeEach(() => {
      container = document.createElement('div');
      button1 = document.createElement('button');
      button2 = document.createElement('button');
      button3 = document.createElement('button');
      
      button1.textContent = 'Button 1';
      button2.textContent = 'Button 2';
      button3.textContent = 'Button 3';
      
      container.appendChild(button1);
      container.appendChild(button2);
      container.appendChild(button3);
      document.body.appendChild(container);
    });

    afterEach(() => {
      document.body.removeChild(container);
    });

    it('traps focus within container when active', () => {
      const { result } = renderHook(() => useFocusTrap(true));
      
      act(() => {
        result.current.current = container;
      });

      // Focus should move to first element
      expect(document.activeElement).toBe(button1);
    });

    it('handles Tab key navigation', () => {
      const { result } = renderHook(() => useFocusTrap(true));
      
      act(() => {
        result.current.current = container;
      });

      // Tab from last element should go to first
      button3.focus();
      const tabEvent = new KeyboardEvent('keydown', {
        key: 'Tab',
        bubbles: true,
      });
      
      act(() => {
        container.dispatchEvent(tabEvent);
      });

      expect(tabEvent.defaultPrevented).toBe(true);
    });

    it('handles Shift+Tab navigation', () => {
      const { result } = renderHook(() => useFocusTrap(true));
      
      act(() => {
        result.current.current = container;
      });

      // Shift+Tab from first element should go to last
      button1.focus();
      const shiftTabEvent = new KeyboardEvent('keydown', {
        key: 'Tab',
        shiftKey: true,
        bubbles: true,
      });
      
      act(() => {
        container.dispatchEvent(shiftTabEvent);
      });

      expect(shiftTabEvent.defaultPrevented).toBe(true);
    });

    it('does not trap focus when inactive', () => {
      const { result } = renderHook(() => useFocusTrap(false));
      
      act(() => {
        result.current.current = container;
      });

      // Focus should not be trapped
      expect(document.activeElement).not.toBe(button1);
    });
  });

  describe('useRovingTabIndex', () => {
    let items: HTMLElement[];

    beforeEach(() => {
      items = Array.from({ length: 5 }, (_, i) => {
        const button = document.createElement('button');
        button.textContent = `Button ${i}`;
        return button;
      });
    });

    it('sets initial tabindex correctly', () => {
      const { result } = renderHook(() => useRovingTabIndex(items));

      expect(items[0]).toHaveAttribute('tabindex', '0');
      items.slice(1).forEach(item => {
        expect(item).toHaveAttribute('tabindex', '-1');
      });
    });

    it('handles arrow key navigation', () => {
      const { result } = renderHook(() => useRovingTabIndex(items));

      // Arrow right
      act(() => {
        result.current.handleKeyDown(new KeyboardEvent('keydown', {
          key: 'ArrowRight',
        }));
      });

      expect(result.current.focusedIndex).toBe(1);
      expect(items[1]).toHaveAttribute('tabindex', '0');
      expect(items[0]).toHaveAttribute('tabindex', '-1');

      // Arrow down
      act(() => {
        result.current.handleKeyDown(new KeyboardEvent('keydown', {
          key: 'ArrowDown',
        }));
      });

      expect(result.current.focusedIndex).toBe(2);
    });

    it('wraps around at boundaries', () => {
      const { result } = renderHook(() => useRovingTabIndex(items));

      // Go to end
      act(() => {
        result.current.handleKeyDown(new KeyboardEvent('keydown', {
          key: 'End',
        }));
      });

      expect(result.current.focusedIndex).toBe(4);

      // Arrow right should wrap to beginning
      act(() => {
        result.current.handleKeyDown(new KeyboardEvent('keydown', {
          key: 'ArrowRight',
        }));
      });

      expect(result.current.focusedIndex).toBe(0);
    });

    it('handles Home and End keys', () => {
      const { result } = renderHook(() => useRovingTabIndex(items));

      // End key
      act(() => {
        result.current.handleKeyDown(new KeyboardEvent('keydown', {
          key: 'End',
        }));
      });

      expect(result.current.focusedIndex).toBe(4);

      // Home key
      act(() => {
        result.current.handleKeyDown(new KeyboardEvent('keydown', {
          key: 'Home',
        }));
      });

      expect(result.current.focusedIndex).toBe(0);
    });
  });

  describe('useAccessibleShortcuts', () => {
    it('handles keyboard shortcuts', () => {
      const mockAction = jest.fn();
      const shortcuts = {
        'Ctrl+S': mockAction,
      };

      const { result } = renderHook(() => 
        useAccessibleShortcuts(shortcuts)
      );

      const event = new KeyboardEvent('keydown', {
        key: 's',
        ctrlKey: true,
      });

      act(() => {
        document.dispatchEvent(event);
      });

      expect(mockAction).toHaveBeenCalled();
      expect(event.defaultPrevented).toBe(true);
    });

    it('handles help toggle with ? key', () => {
      const shortcuts = {
        'Ctrl+S': jest.fn(),
      };

      const { result } = renderHook(() => 
        useAccessibleShortcuts(shortcuts, { enableHelp: true })
      );

      expect(result.current.showHelp).toBe(false);

      const event = new KeyboardEvent('keydown', {
        key: '?',
      });

      act(() => {
        document.dispatchEvent(event);
      });

      expect(result.current.showHelp).toBe(true);
    });

    it('announces actions when enabled', () => {
      const mockAction = jest.fn();
      const shortcuts = {
        'Ctrl+K': mockAction,
      };

      renderHook(() => 
        useAccessibleShortcuts(shortcuts, { announceActions: true })
      );

      const event = new KeyboardEvent('keydown', {
        key: 'k',
        ctrlKey: true,
      });

      act(() => {
        document.dispatchEvent(event);
      });

      expect(announcer.announce).toHaveBeenCalledWith('Executed Ctrl+K shortcut');
    });

    it('handles complex key combinations', () => {
      const mockAction = jest.fn();
      const shortcuts = {
        'Ctrl+Shift+Alt+X': mockAction,
      };

      renderHook(() => useAccessibleShortcuts(shortcuts));

      const event = new KeyboardEvent('keydown', {
        key: 'x',
        ctrlKey: true,
        shiftKey: true,
        altKey: true,
      });

      act(() => {
        document.dispatchEvent(event);
      });

      expect(mockAction).toHaveBeenCalled();
    });

    it('ignores non-matching shortcuts', () => {
      const mockAction = jest.fn();
      const shortcuts = {
        'Ctrl+S': mockAction,
      };

      renderHook(() => useAccessibleShortcuts(shortcuts));

      const event = new KeyboardEvent('keydown', {
        key: 's', // No Ctrl key
      });

      act(() => {
        document.dispatchEvent(event);
      });

      expect(mockAction).not.toHaveBeenCalled();
      expect(event.defaultPrevented).toBe(false);
    });
  });

  describe('Color Contrast Utilities', () => {
    describe('getContrastRatio', () => {
      it('calculates contrast ratio between black and white', () => {
        const ratio = getContrastRatio('#000000', '#FFFFFF');
        expect(ratio).toBeCloseTo(21, 1);
      });

      it('calculates contrast ratio between similar colors', () => {
        const ratio = getContrastRatio('#777777', '#888888');
        expect(ratio).toBeCloseTo(1.3, 1);
      });

      it('handles colors without # prefix', () => {
        const ratio = getContrastRatio('000000', 'FFFFFF');
        expect(ratio).toBeCloseTo(21, 1);
      });

      it('returns 1 for invalid colors', () => {
        const ratio = getContrastRatio('invalid', '#FFFFFF');
        expect(ratio).toBe(1);
      });
    });

    describe('meetsWCAGStandard', () => {
      it('validates AA standard for normal text', () => {
        // Black on white - should pass
        expect(meetsWCAGStandard('#000000', '#FFFFFF', 'AA', false)).toBe(true);
        
        // Dark gray on light gray - should fail
        expect(meetsWCAGStandard('#777777', '#AAAAAA', 'AA', false)).toBe(false);
      });

      it('validates AA standard for large text', () => {
        // Lower contrast requirement for large text
        expect(meetsWCAGStandard('#666666', '#FFFFFF', 'AA', true)).toBe(true);
      });

      it('validates AAA standard', () => {
        // Higher contrast requirement for AAA
        expect(meetsWCAGStandard('#555555', '#FFFFFF', 'AAA', false)).toBe(true);
        expect(meetsWCAGStandard('#777777', '#FFFFFF', 'AAA', false)).toBe(false);
      });

      it('validates AAA standard for large text', () => {
        expect(meetsWCAGStandard('#666666', '#FFFFFF', 'AAA', true)).toBe(true);
      });
    });
  });
});
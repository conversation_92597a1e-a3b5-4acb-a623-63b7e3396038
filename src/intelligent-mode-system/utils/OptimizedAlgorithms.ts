/**
 * Optimized Algorithms for ML Computations
 * 
 * Features:
 * - Fast feature extraction with minimal allocations
 * - Optimized similarity calculations
 * - Efficient scoring algorithms
 * - Memory-efficient data structures
 * - Vectorized operations where possible
 */

import { ContextSnapshot, ModeId } from '../types';

export interface FastFeature {
  id: number; // Use numeric IDs for faster comparison
  value: number;
  weight: number;
}

export interface SimilarityCache {
  signatures: Map<string, number[]>;
  similarities: Map<string, number>;
  maxSize: number;
}

export interface ScoreVector {
  features: Float32Array;
  weights: Float32Array;
  length: number;
}

/**
 * Optimized feature extraction with minimal memory allocation
 */
export class FastFeatureExtractor {
  private static featureIdMap = new Map<string, number>();
  private static nextFeatureId = 0;
  
  // Pre-allocated arrays for better performance
  private static tempFeatures = new Float32Array(100);
  private static tempWeights = new Float32Array(100);
  
  /**
   * Extract features as numeric vectors for fast computation
   */
  static extractOptimized(context: ContextSnapshot): ScoreVector {
    let featureCount = 0;
    
    // Reset temp arrays
    this.tempFeatures.fill(0);
    this.tempWeights.fill(0);
    
    // File features (optimized)
    featureCount = this.addFileFeatures(context.fileContext, featureCount);
    
    // Project features
    featureCount = this.addProjectFeatures(context.projectContext, featureCount);
    
    // User features
    featureCount = this.addUserFeatures(context.userContext, featureCount);
    
    // Environment features
    featureCount = this.addEnvironmentFeatures(context.environmentContext, featureCount);
    
    // Create final vector with exact size
    const features = new Float32Array(featureCount);
    const weights = new Float32Array(featureCount);
    
    for (let i = 0; i < featureCount; i++) {
      features[i] = this.tempFeatures[i];
      weights[i] = this.tempWeights[i];
    }
    
    return { features, weights, length: featureCount };
  }
  
  private static addFileFeatures(fileContext: any, startIndex: number): number {
    let index = startIndex;
    
    // File extension (use hash for faster comparison)
    const ext = fileContext.path.split('.').pop()?.toLowerCase() || '';
    const extHash = this.hashString(ext) % 1000;
    this.tempFeatures[index] = extHash / 1000; // Normalize
    this.tempWeights[index] = 0.8;
    index++;
    
    // Error state (binary feature)
    if (fileContext.hasErrors) {
      this.tempFeatures[index] = 1.0;
      this.tempWeights[index] = 0.9;
      index++;
    }
    
    // File freshness (exponential decay)
    const hoursSinceModified = (Date.now() - fileContext.lastModified) / 3600000;
    this.tempFeatures[index] = Math.exp(-hoursSinceModified / 24);
    this.tempWeights[index] = 0.6;
    index++;
    
    return index;
  }
  
  private static addProjectFeatures(projectContext: any, startIndex: number): number {
    let index = startIndex;
    
    // Project type hash
    const typeHash = this.hashString(projectContext.type) % 1000;
    this.tempFeatures[index] = typeHash / 1000;
    this.tempWeights[index] = 0.5;
    index++;
    
    // Project size (normalized)
    const sizeNorm = Math.min(projectContext.structure.fileCount / 1000, 1);
    this.tempFeatures[index] = sizeNorm;
    this.tempWeights[index] = 0.4;
    index++;
    
    // Has tests
    if (projectContext.structure.hasTests) {
      this.tempFeatures[index] = 1.0;
      this.tempWeights[index] = 0.6;
      index++;
    }
    
    return index;
  }
  
  private static addUserFeatures(userContext: any, startIndex: number): number {
    let index = startIndex;
    
    // Action velocity (actions per minute)
    const recentActions = userContext.recentActions.slice(-10);
    if (recentActions.length > 1) {
      const timeSpan = recentActions[recentActions.length - 1].timestamp - recentActions[0].timestamp;
      const velocity = Math.min(recentActions.length / (timeSpan / 60000) / 10, 1);
      this.tempFeatures[index] = velocity;
      this.tempWeights[index] = 0.6;
      index++;
    }
    
    // Session duration (normalized to 2 hours)
    const sessionNorm = Math.min(userContext.sessionDuration / 7200000, 1);
    this.tempFeatures[index] = sessionNorm;
    this.tempWeights[index] = 0.4;
    index++;
    
    return index;
  }
  
  private static addEnvironmentFeatures(envContext: any, startIndex: number): number {
    let index = startIndex;
    
    // Git conflicts
    if (envContext.gitStatus?.hasConflicts) {
      this.tempFeatures[index] = 1.0;
      this.tempWeights[index] = 0.9;
      index++;
    }
    
    // Git staged files
    if (envContext.gitStatus?.staged?.length > 0) {
      const stagedNorm = Math.min(envContext.gitStatus.staged.length / 10, 1);
      this.tempFeatures[index] = stagedNorm;
      this.tempWeights[index] = 0.7;
      index++;
    }
    
    return index;
  }
  
  /**
   * Fast string hashing for feature IDs
   */
  private static hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
}

/**
 * Optimized similarity calculation with caching
 */
export class FastSimilarityCalculator {
  private cache: SimilarityCache = {
    signatures: new Map(),
    similarities: new Map(),
    maxSize: 1000,
  };
  
  /**
   * Calculate similarity between two contexts using optimized vector operations
   */
  calculateSimilarity(ctx1: ContextSnapshot, ctx2: ContextSnapshot): number {
    // Generate cache keys
    const key1 = this.generateCacheKey(ctx1);
    const key2 = this.generateCacheKey(ctx2);
    const cacheKey = `${key1}:${key2}`;
    
    // Check cache
    if (this.cache.similarities.has(cacheKey)) {
      return this.cache.similarities.get(cacheKey)!;
    }
    
    // Get or compute feature vectors
    const vec1 = this.getOrComputeVector(key1, ctx1);
    const vec2 = this.getOrComputeVector(key2, ctx2);
    
    // Calculate cosine similarity using optimized vector operations
    const similarity = this.cosineSimilarity(vec1, vec2);
    
    // Cache result
    this.setCachedSimilarity(cacheKey, similarity);
    
    return similarity;
  }
  
  /**
   * Optimized cosine similarity calculation
   */
  private cosineSimilarity(vec1: number[], vec2: number[]): number {
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;
    
    const minLength = Math.min(vec1.length, vec2.length);
    
    // Vectorized computation
    for (let i = 0; i < minLength; i++) {
      const v1 = vec1[i];
      const v2 = vec2[i];
      
      dotProduct += v1 * v2;
      norm1 += v1 * v1;
      norm2 += v2 * v2;
    }
    
    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);
    return magnitude === 0 ? 0 : dotProduct / magnitude;
  }
  
  private generateCacheKey(context: ContextSnapshot): string {
    // Generate a compact key for caching
    const parts = [
      context.fileContext.path.split('/').pop() || '',
      context.fileContext.hasErrors ? '1' : '0',
      context.projectContext.type,
      context.userContext.recentActions.length.toString(),
    ];
    
    return parts.join(':');
  }
  
  private getOrComputeVector(key: string, context: ContextSnapshot): number[] {
    if (this.cache.signatures.has(key)) {
      return this.cache.signatures.get(key)!;
    }
    
    const vector = FastFeatureExtractor.extractOptimized(context);
    const signature = Array.from(vector.features.slice(0, vector.length));
    
    this.setCachedVector(key, signature);
    return signature;
  }
  
  private setCachedVector(key: string, vector: number[]): void {
    if (this.cache.signatures.size >= this.cache.maxSize) {
      // Remove oldest entry (simple FIFO)
      const firstKey = this.cache.signatures.keys().next().value;
      this.cache.signatures.delete(firstKey);
    }
    
    this.cache.signatures.set(key, vector);
  }
  
  private setCachedSimilarity(key: string, similarity: number): void {
    if (this.cache.similarities.size >= this.cache.maxSize) {
      const firstKey = this.cache.similarities.keys().next().value;
      this.cache.similarities.delete(firstKey);
    }
    
    this.cache.similarities.set(key, similarity);
  }
  
  /**
   * Clear cache to free memory
   */
  clearCache(): void {
    this.cache.signatures.clear();
    this.cache.similarities.clear();
  }
}

/**
 * Optimized scoring algorithm with vectorized operations
 */
export class FastScoringEngine {
  private modeAffinityVectors = new Map<ModeId, Float32Array>();
  
  constructor() {
    this.initializeModeAffinities();
  }
  
  /**
   * Calculate mode scores using optimized vector operations
   */
  calculateModeScores(features: ScoreVector): Map<ModeId, number> {
    const scores = new Map<ModeId, number>();
    
    this.modeAffinityVectors.forEach((affinityVector, modeId) => {
      const score = this.dotProduct(features.features, features.weights, affinityVector);
      scores.set(modeId, score);
    });
    
    return scores;
  }
  
  /**
   * Optimized dot product with weights
   */
  private dotProduct(features: Float32Array, weights: Float32Array, affinities: Float32Array): number {
    let sum = 0;
    const length = Math.min(features.length, affinities.length);
    
    // Unrolled loop for better performance
    let i = 0;
    for (; i < length - 3; i += 4) {
      sum += features[i] * weights[i] * affinities[i];
      sum += features[i + 1] * weights[i + 1] * affinities[i + 1];
      sum += features[i + 2] * weights[i + 2] * affinities[i + 2];
      sum += features[i + 3] * weights[i + 3] * affinities[i + 3];
    }
    
    // Handle remaining elements
    for (; i < length; i++) {
      sum += features[i] * weights[i] * affinities[i];
    }
    
    return sum;
  }
  
  /**
   * Initialize mode affinity vectors for fast computation
   */
  private initializeModeAffinities(): void {
    const modes: ModeId[] = ['architect', 'debug', 'review', 'deploy', 'experiment', 'learn'];
    
    modes.forEach(modeId => {
      const affinities = new Float32Array(100); // Match max features
      
      // Initialize with mode-specific affinities
      switch (modeId) {
        case 'debug':
          affinities[0] = 0.9; // Error state
          affinities[1] = 0.6; // File type
          break;
        case 'architect':
          affinities[0] = 0.8; // File type
          affinities[1] = 0.7; // Project structure
          break;
        case 'review':
          affinities[0] = 0.9; // Git changes
          affinities[1] = 0.7; // Staged files
          break;
        case 'deploy':
          affinities[0] = 0.8; // Build processes
          affinities[1] = 0.6; // Configuration files
          break;
        case 'experiment':
          affinities[0] = 0.6; // User velocity
          affinities[1] = 0.5; // Edit frequency
          break;
        case 'learn':
          affinities[0] = 0.7; // Documentation
          affinities[1] = 0.6; // Search activity
          break;
      }
      
      this.modeAffinityVectors.set(modeId, affinities);
    });
  }
}

/**
 * Memory-efficient pattern storage
 */
export class CompactPatternStore {
  private patterns = new Map<number, {
    signature: Uint16Array; // Compact signature
    affinities: Float32Array; // Mode affinities
    frequency: number;
    lastSeen: number;
  }>();
  
  private signatureCache = new Map<string, number>();
  private nextPatternId = 0;
  
  /**
   * Store pattern with compact representation
   */
  storePattern(signature: string, affinities: Record<ModeId, number>): number {
    // Check if pattern already exists
    if (this.signatureCache.has(signature)) {
      const patternId = this.signatureCache.get(signature)!;
      const pattern = this.patterns.get(patternId)!;
      pattern.frequency++;
      pattern.lastSeen = Date.now();
      return patternId;
    }
    
    // Create new pattern
    const patternId = this.nextPatternId++;
    const compactSignature = this.compressSignature(signature);
    const affinityArray = this.affinitieToArray(affinities);
    
    this.patterns.set(patternId, {
      signature: compactSignature,
      affinities: affinityArray,
      frequency: 1,
      lastSeen: Date.now(),
    });
    
    this.signatureCache.set(signature, patternId);
    return patternId;
  }
  
  /**
   * Find similar patterns efficiently
   */
  findSimilarPatterns(targetSignature: string, threshold = 0.7): Array<{
    id: number;
    similarity: number;
    affinities: Float32Array;
  }> {
    const targetCompressed = this.compressSignature(targetSignature);
    const results: Array<{ id: number; similarity: number; affinities: Float32Array }> = [];
    
    this.patterns.forEach((pattern, id) => {
      const similarity = this.calculateCompressedSimilarity(targetCompressed, pattern.signature);
      if (similarity >= threshold) {
        results.push({
          id,
          similarity,
          affinities: pattern.affinities,
        });
      }
    });
    
    return results.sort((a, b) => b.similarity - a.similarity);
  }
  
  /**
   * Compress signature for memory efficiency
   */
  private compressSignature(signature: string): Uint16Array {
    const parts = signature.split('|');
    const compressed = new Uint16Array(parts.length);
    
    for (let i = 0; i < parts.length; i++) {
      compressed[i] = FastFeatureExtractor['hashString'](parts[i]) % 65536;
    }
    
    return compressed;
  }
  
  /**
   * Convert affinities object to typed array
   */
  private affinitieToArray(affinities: Record<ModeId, number>): Float32Array {
    const array = new Float32Array(6); // 6 modes
    const modes: ModeId[] = ['architect', 'debug', 'review', 'deploy', 'experiment', 'learn'];
    
    modes.forEach((mode, index) => {
      array[index] = affinities[mode] || 0;
    });
    
    return array;
  }
  
  /**
   * Calculate similarity between compressed signatures
   */
  private calculateCompressedSimilarity(sig1: Uint16Array, sig2: Uint16Array): number {
    const minLength = Math.min(sig1.length, sig2.length);
    let matches = 0;
    
    for (let i = 0; i < minLength; i++) {
      if (sig1[i] === sig2[i]) {
        matches++;
      }
    }
    
    return matches / Math.max(sig1.length, sig2.length);
  }
  
  /**
   * Clean old patterns to manage memory
   */
  cleanOldPatterns(maxAge = 7 * 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge;
    const toDelete: number[] = [];
    
    this.patterns.forEach((pattern, id) => {
      if (pattern.lastSeen < cutoff && pattern.frequency < 5) {
        toDelete.push(id);
      }
    });
    
    toDelete.forEach(id => {
      this.patterns.delete(id);
    });
    
    // Clean signature cache
    this.signatureCache.clear();
    this.patterns.forEach((_, id) => {
      // Rebuild cache for remaining patterns
      // In a real implementation, we'd store the original signature
    });
  }
  
  /**
   * Get memory usage statistics
   */
  getMemoryStats(): { patterns: number; memoryUsage: number } {
    let memoryUsage = 0;
    
    this.patterns.forEach(pattern => {
      memoryUsage += pattern.signature.byteLength;
      memoryUsage += pattern.affinities.byteLength;
      memoryUsage += 16; // frequency + lastSeen
    });
    
    return {
      patterns: this.patterns.size,
      memoryUsage,
    };
  }
}

// Export singleton instances for reuse
export const fastSimilarityCalculator = new FastSimilarityCalculator();
export const fastScoringEngine = new FastScoringEngine();
export const compactPatternStore = new CompactPatternStore();
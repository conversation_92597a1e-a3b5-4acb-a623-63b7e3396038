/**
 * Accessibility utilities and helpers
 */

import { useEffect, useRef, useState, useCallback } from 'react';

/**
 * ARIA live region announcer for screen readers
 */
class LiveRegionAnnouncer {
  private static instance: LiveRegionAnnouncer;
  private container: HTMLDivElement | null = null;
  
  private constructor() {
    this.createContainer();
  }
  
  static getInstance(): LiveRegionAnnouncer {
    if (!LiveRegionAnnouncer.instance) {
      LiveRegionAnnouncer.instance = new LiveRegionAnnouncer();
    }
    return LiveRegionAnnouncer.instance;
  }
  
  private createContainer() {
    if (typeof document === 'undefined') return;
    
    this.container = document.createElement('div');
    this.container.setAttribute('role', 'status');
    this.container.setAttribute('aria-live', 'polite');
    this.container.setAttribute('aria-atomic', 'true');
    this.container.className = 'sr-only';
    document.body.appendChild(this.container);
  }
  
  announce(message: string, priority: 'polite' | 'assertive' = 'polite') {
    if (!this.container) return;
    
    this.container.setAttribute('aria-live', priority);
    this.container.textContent = message;
    
    // Clear after announcement
    setTimeout(() => {
      if (this.container) {
        this.container.textContent = '';
      }
    }, 1000);
  }
}

export const announcer = LiveRegionAnnouncer.getInstance();

/**
 * Hook for managing focus trap
 */
export function useFocusTrap(active = true) {
  const containerRef = useRef<HTMLElement>(null);
  
  useEffect(() => {
    if (!active || !containerRef.current) return;
    
    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'a[href], button:not([disabled]), textarea:not([disabled]), input:not([disabled]), select:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;
      
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };
    
    container.addEventListener('keydown', handleKeyDown);
    firstElement?.focus();
    
    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  }, [active]);
  
  return containerRef;
}

/**
 * Hook for managing roving tabindex
 */
export function useRovingTabIndex(items: HTMLElement[]) {
  const [focusedIndex, setFocusedIndex] = useState(0);
  
  useEffect(() => {
    items.forEach((item, index) => {
      item.setAttribute('tabindex', index === focusedIndex ? '0' : '-1');
    });
  }, [items, focusedIndex]);
  
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    const key = e.key;
    const currentIndex = focusedIndex;
    let nextIndex = currentIndex;
    
    switch (key) {
      case 'ArrowRight':
      case 'ArrowDown':
        e.preventDefault();
        nextIndex = (currentIndex + 1) % items.length;
        break;
      case 'ArrowLeft':
      case 'ArrowUp':
        e.preventDefault();
        nextIndex = (currentIndex - 1 + items.length) % items.length;
        break;
      case 'Home':
        e.preventDefault();
        nextIndex = 0;
        break;
      case 'End':
        e.preventDefault();
        nextIndex = items.length - 1;
        break;
    }
    
    if (nextIndex !== currentIndex) {
      setFocusedIndex(nextIndex);
      items[nextIndex]?.focus();
    }
  }, [focusedIndex, items]);
  
  return { focusedIndex, handleKeyDown };
}

/**
 * Hook for managing keyboard shortcuts with accessibility
 */
export function useAccessibleShortcuts(
  shortcuts: Record<string, () => void>,
  options: {
    enableHelp?: boolean;
    announceActions?: boolean;
  } = {}
) {
  const { enableHelp = true, announceActions = true } = options;
  const [showHelp, setShowHelp] = useState(false);
  
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Show help with ?
      if (enableHelp && e.key === '?' && !e.ctrlKey && !e.metaKey) {
        e.preventDefault();
        setShowHelp(prev => !prev);
        return;
      }
      
      // Build key combination string
      const modifiers = [];
      if (e.ctrlKey) modifiers.push('Ctrl');
      if (e.metaKey) modifiers.push('Cmd');
      if (e.altKey) modifiers.push('Alt');
      if (e.shiftKey) modifiers.push('Shift');
      
      const key = e.key.length === 1 ? e.key.toUpperCase() : e.key;
      const combination = [...modifiers, key].join('+');
      
      // Check for matching shortcut
      const action = shortcuts[combination];
      if (action) {
        e.preventDefault();
        action();
        
        if (announceActions) {
          announcer.announce(`Executed ${combination} shortcut`);
        }
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts, enableHelp, announceActions]);
  
  return { showHelp, setShowHelp };
}

/**
 * WCAG color contrast utilities
 */
export function getContrastRatio(color1: string, color2: string): number {
  // Convert hex to RGB
  const getRGB = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };
  
  // Calculate relative luminance
  const getLuminance = (rgb: { r: number; g: number; b: number }) => {
    const { r, g, b } = rgb;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  };
  
  const rgb1 = getRGB(color1);
  const rgb2 = getRGB(color2);
  
  if (!rgb1 || !rgb2) return 1;
  
  const l1 = getLuminance(rgb1);
  const l2 = getLuminance(rgb2);
  
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);
  
  return (lighter + 0.05) / (darker + 0.05);
}

/**
 * Check if color combination meets WCAG standards
 */
export function meetsWCAGStandard(
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA',
  largeText = false
): boolean {
  const ratio = getContrastRatio(foreground, background);
  
  if (level === 'AA') {
    return largeText ? ratio >= 3 : ratio >= 4.5;
  } else {
    return largeText ? ratio >= 4.5 : ratio >= 7;
  }
}

/**
 * Skip link component styles
 */
export const skipLinkStyles = `
  position: absolute;
  left: -9999px;
  z-index: 999;
  padding: 1em;
  background-color: black;
  color: white;
  text-decoration: none;
  
  &:focus {
    left: 50%;
    transform: translateX(-50%);
    top: 10px;
  }
`;

/**
 * Screen reader only utility class
 */
export const srOnly = `
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
`;

/**
 * Focus visible utility
 */
export const focusVisible = `
  outline: 2px solid transparent;
  outline-offset: 2px;
  
  &:focus-visible {
    outline-color: #3B82F6;
    outline-width: 2px;
  }
`;

/**
 * Accessibility classes
 */
export const a11yClasses = {
  // Screen reader only
  srOnly: 'sr-only',
  notSrOnly: 'not-sr-only',
  
  // Focus styles
  focusVisible: 'focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2',
  focusWithin: 'focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2',
  
  // Interactive states
  interactive: 'transition-colors duration-200 hover:bg-opacity-80 active:bg-opacity-60',
  disabled: 'opacity-50 cursor-not-allowed pointer-events-none',
  
  // High contrast mode
  highContrast: 'contrast-more:border-2 contrast-more:border-current',
  
  // Reduced motion
  reducedMotion: 'motion-reduce:transition-none motion-reduce:animation-none',
} as const;

/**
 * ARIA labels for common UI patterns
 */
export const ariaLabels = {
  // Navigation
  mainNav: 'Main navigation',
  breadcrumb: 'Breadcrumb navigation',
  pagination: 'Pagination navigation',
  
  // Buttons
  close: 'Close',
  menu: 'Open menu',
  search: 'Search',
  filter: 'Filter options',
  sort: 'Sort options',
  
  // Form elements
  required: 'Required field',
  error: 'Error',
  success: 'Success',
  info: 'Information',
  warning: 'Warning',
  
  // Loading states
  loading: 'Loading',
  saving: 'Saving',
  processing: 'Processing',
  
  // Mode-specific
  switchMode: (mode: string) => `Switch to ${mode}`,
  currentMode: (mode: string) => `Current mode: ${mode}`,
} as const;

/**
 * Semantic HTML role mappings
 */
export const semanticRoles = {
  // Landmarks
  banner: 'banner',
  navigation: 'navigation',
  main: 'main',
  complementary: 'complementary',
  contentinfo: 'contentinfo',
  
  // Structure
  article: 'article',
  section: 'section',
  heading: 'heading',
  list: 'list',
  listitem: 'listitem',
  
  // Interactive
  button: 'button',
  link: 'link',
  textbox: 'textbox',
  checkbox: 'checkbox',
  radio: 'radio',
  
  // Live regions
  alert: 'alert',
  status: 'status',
  log: 'log',
  marquee: 'marquee',
  timer: 'timer',
} as const;
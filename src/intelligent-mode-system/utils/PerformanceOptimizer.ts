import { performanceMonitor } from './performance';
import { intelligentCache } from '../core/IntelligentCacheSystem';
import { backgroundProcessor } from '../core/BackgroundProcessor';
import { eventBus } from '../core/EventBus';

/**
 * Performance Optimization System
 * 
 * Features:
 * - Real-time performance monitoring
 * - Automatic optimization triggers
 * - Resource usage optimization
 * - Performance bottleneck detection
 * - Adaptive configuration tuning
 */

export interface PerformanceMetrics {
  // Core metrics
  averageResponseTime: number;
  throughput: number;
  errorRate: number;
  memoryUsage: number;
  
  // Cache metrics
  cacheHitRate: number;
  cacheMemoryUsage: number;
  
  // Background processing metrics
  backgroundTaskThroughput: number;
  backgroundTaskBacklog: number;
  
  // ML computation metrics
  inferenceTime: number;
  suggestionGenerationTime: number;
  patternRecognitionTime: number;
}

export interface OptimizationSuggestion {
  type: 'cache' | 'computation' | 'background' | 'memory' | 'algorithm';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  action: string;
  estimatedImpact: number; // 0-1 scale
}

export interface PerformanceConfig {
  monitoringInterval: number;
  optimizationThresholds: {
    responseTime: number;
    errorRate: number;
    memoryUsage: number;
    cacheHitRate: number;
  };
  autoOptimizationEnabled: boolean;
  alertThresholds: {
    responseTime: number;
    errorRate: number;
    memoryUsage: number;
  };
}

class PerformanceOptimizer {
  private static instance: PerformanceOptimizer;
  
  // Configuration
  private config: PerformanceConfig = {
    monitoringInterval: 30000, // 30 seconds
    optimizationThresholds: {
      responseTime: 1000, // 1 second
      errorRate: 0.05, // 5%
      memoryUsage: 0.8, // 80%
      cacheHitRate: 0.6, // 60%
    },
    autoOptimizationEnabled: true,
    alertThresholds: {
      responseTime: 2000, // 2 seconds
      errorRate: 0.1, // 10%
      memoryUsage: 0.9, // 90%
    },
  };
  
  // State
  private metrics: PerformanceMetrics = {
    averageResponseTime: 0,
    throughput: 0,
    errorRate: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    cacheMemoryUsage: 0,
    backgroundTaskThroughput: 0,
    backgroundTaskBacklog: 0,
    inferenceTime: 0,
    suggestionGenerationTime: 0,
    patternRecognitionTime: 0,
  };
  
  private optimizationHistory: Array<{
    timestamp: number;
    type: string;
    action: string;
    result: 'success' | 'failure';
    impact: number;
  }> = [];
  
  private monitoringTimer: NodeJS.Timeout | null = null;
  private lastOptimization = 0;
  
  private constructor() {
    this.startMonitoring();
    this.setupEventListeners();
  }
  
  static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }
  
  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }
  
  /**
   * Get optimization suggestions
   */
  getOptimizationSuggestions(): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];
    
    // Response time optimization
    if (this.metrics.averageResponseTime > this.config.optimizationThresholds.responseTime) {
      suggestions.push({
        type: 'computation',
        severity: this.metrics.averageResponseTime > this.config.alertThresholds.responseTime ? 'high' : 'medium',
        description: `Average response time is ${this.metrics.averageResponseTime.toFixed(0)}ms`,
        action: 'Optimize computation algorithms and increase caching',
        estimatedImpact: 0.7,
      });
    }
    
    // Cache optimization
    if (this.metrics.cacheHitRate < this.config.optimizationThresholds.cacheHitRate) {
      suggestions.push({
        type: 'cache',
        severity: 'medium',
        description: `Cache hit rate is ${(this.metrics.cacheHitRate * 100).toFixed(1)}%`,
        action: 'Improve cache strategy and increase predictive preloading',
        estimatedImpact: 0.5,
      });
    }
    
    // Memory optimization
    if (this.metrics.memoryUsage > this.config.optimizationThresholds.memoryUsage) {
      suggestions.push({
        type: 'memory',
        severity: this.metrics.memoryUsage > this.config.alertThresholds.memoryUsage ? 'critical' : 'high',
        description: `Memory usage is ${(this.metrics.memoryUsage * 100).toFixed(1)}%`,
        action: 'Reduce memory usage and improve garbage collection',
        estimatedImpact: 0.6,
      });
    }
    
    // Background processing optimization
    if (this.metrics.backgroundTaskBacklog > 50) {
      suggestions.push({
        type: 'background',
        severity: 'medium',
        description: `Background task backlog is ${this.metrics.backgroundTaskBacklog}`,
        action: 'Increase background processing capacity or optimize task scheduling',
        estimatedImpact: 0.4,
      });
    }
    
    // Algorithm optimization
    if (this.metrics.inferenceTime > 500) {
      suggestions.push({
        type: 'algorithm',
        severity: 'medium',
        description: `ML inference time is ${this.metrics.inferenceTime.toFixed(0)}ms`,
        action: 'Optimize feature extraction and scoring algorithms',
        estimatedImpact: 0.3,
      });
    }
    
    return suggestions.sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      if (severityOrder[a.severity] !== severityOrder[b.severity]) {
        return severityOrder[b.severity] - severityOrder[a.severity];
      }
      return b.estimatedImpact - a.estimatedImpact;
    });
  }
  
  /**
   * Apply automatic optimizations
   */
  async applyOptimizations(): Promise<void> {
    if (!this.config.autoOptimizationEnabled) return;
    
    // Prevent too frequent optimizations
    if (Date.now() - this.lastOptimization < 60000) return; // 1 minute cooldown
    
    const suggestions = this.getOptimizationSuggestions();
    const criticalSuggestions = suggestions.filter(s => s.severity === 'critical' || s.severity === 'high');
    
    for (const suggestion of criticalSuggestions) {
      try {
        await this.applyOptimization(suggestion);
        this.lastOptimization = Date.now();
      } catch (error) {
        console.warn('Optimization failed:', error);
      }
    }
  }
  
  /**
   * Apply specific optimization
   */
  private async applyOptimization(suggestion: OptimizationSuggestion): Promise<void> {
    const startTime = Date.now();
    let success = false;
    
    try {
      switch (suggestion.type) {
        case 'cache':
          await this.optimizeCache();
          success = true;
          break;
          
        case 'memory':
          await this.optimizeMemory();
          success = true;
          break;
          
        case 'background':
          await this.optimizeBackgroundProcessing();
          success = true;
          break;
          
        case 'computation':
          await this.optimizeComputations();
          success = true;
          break;
          
        case 'algorithm':
          await this.optimizeAlgorithms();
          success = true;
          break;
      }
      
      // Record optimization
      this.optimizationHistory.push({
        timestamp: Date.now(),
        type: suggestion.type,
        action: suggestion.action,
        result: 'success',
        impact: suggestion.estimatedImpact,
      });
      
      // Emit optimization event
      eventBus.emit({
        type: 'performance.optimization_applied',
        payload: {
          type: suggestion.type,
          description: suggestion.description,
          duration: Date.now() - startTime,
        },
        source: 'PerformanceOptimizer',
      });
      
    } catch (error) {
      this.optimizationHistory.push({
        timestamp: Date.now(),
        type: suggestion.type,
        action: suggestion.action,
        result: 'failure',
        impact: 0,
      });
      
      throw error;
    }
  }
  
  /**
   * Optimization strategies
   */
  
  private async optimizeCache(): Promise<void> {
    const cacheMetrics = intelligentCache.getMetrics();
    
    // Adjust cache configuration based on performance
    if (cacheMetrics.hitRate < 0.6) {
      intelligentCache.updateConfig({
        defaultTTL: Math.min(intelligentCache['config'].defaultTTL * 1.5, 600000), // Increase TTL
        predictivePreloadEnabled: true,
        adaptiveTTLEnabled: true,
      });
    }
    
    // Clear low-value cache entries to make room for better ones
    if (cacheMetrics.memoryUsage > 80 * 1024 * 1024) { // 80MB
      // Trigger aggressive cleanup
      await intelligentCache['evictLeastValuable']();
    }
  }
  
  private async optimizeMemory(): Promise<void> {
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
    
    // Clear old metrics to free memory
    performanceMonitor['metrics'] = performanceMonitor['metrics'].slice(-1000);
    
    // Reduce cache memory usage
    intelligentCache.updateConfig({
      maxMemoryMB: Math.max(intelligentCache['config'].maxMemoryMB * 0.8, 50),
    });
    
    // Clean up old optimization history
    this.optimizationHistory = this.optimizationHistory.slice(-100);
  }
  
  private async optimizeBackgroundProcessing(): Promise<void> {
    const processorMetrics = backgroundProcessor.getMetrics();
    
    if (processorMetrics.tasksQueued > 50) {
      // Increase processing capacity
      backgroundProcessor.updateConfig({
        maxConcurrentTasks: Math.min(backgroundProcessor['config'].maxConcurrentTasks + 1, 8),
        processingInterval: Math.max(backgroundProcessor['config'].processingInterval - 20, 50),
      });
    }
    
    if (processorMetrics.errorRate > 0.1) {
      // Reduce load to improve reliability
      backgroundProcessor.updateConfig({
        maxConcurrentTasks: Math.max(backgroundProcessor['config'].maxConcurrentTasks - 1, 1),
        processingInterval: backgroundProcessor['config'].processingInterval + 50,
      });
    }
  }
  
  private async optimizeComputations(): Promise<void> {
    // Enable more aggressive caching for expensive computations
    const cacheConfig = {
      defaultTTL: 600000, // 10 minutes
      adaptiveTTLEnabled: true,
      predictivePreloadEnabled: true,
    };
    
    intelligentCache.updateConfig(cacheConfig);
    
    // Schedule background precomputation of likely needed data
    eventBus.emit({
      type: 'performance.precompute_requested',
      payload: { priority: 'low' },
      source: 'PerformanceOptimizer',
    });
  }
  
  private async optimizeAlgorithms(): Promise<void> {
    // This would typically involve switching to more efficient algorithms
    // For now, we'll optimize configuration parameters
    
    // Reduce feature extraction complexity
    const { enhancedContextInference } = await import('../services/EnhancedContextInference');
    
    // Access private config through type assertion for optimization
    const contextInference = enhancedContextInference as any;
    if (contextInference.config) {
      contextInference.config.featureExtraction.maxFeatures = Math.max(
        contextInference.config.featureExtraction.maxFeatures * 0.8,
        20
      );
    }
  }
  
  /**
   * Monitoring and metrics collection
   */
  
  private startMonitoring(): void {
    this.monitoringTimer = setInterval(() => {
      this.collectMetrics();
      
      if (this.config.autoOptimizationEnabled) {
        this.applyOptimizations().catch(console.warn);
      }
    }, this.config.monitoringInterval);
  }
  
  private collectMetrics(): void {
    // Collect performance metrics from various sources
    const performanceMetrics = performanceMonitor.getMetrics();
    const cacheMetrics = intelligentCache.getMetrics();
    const processorMetrics = backgroundProcessor.getMetrics();
    
    // Update core metrics
    this.metrics.averageResponseTime = performanceMetrics.averageResponseTime || 0;
    this.metrics.throughput = performanceMetrics.throughput || 0;
    this.metrics.errorRate = performanceMetrics.errorRate || 0;
    this.metrics.memoryUsage = this.estimateMemoryUsage();
    
    // Update cache metrics
    this.metrics.cacheHitRate = cacheMetrics.hitRate;
    this.metrics.cacheMemoryUsage = cacheMetrics.memoryUsage;
    
    // Update background processing metrics
    this.metrics.backgroundTaskThroughput = processorMetrics.throughput;
    this.metrics.backgroundTaskBacklog = processorMetrics.tasksQueued;
    
    // Update ML computation metrics
    this.updateMLMetrics();
    
    // Check for alerts
    this.checkAlerts();
    
    // Emit metrics event
    eventBus.emit({
      type: 'performance.metrics_updated',
      payload: { ...this.metrics },
      source: 'PerformanceOptimizer',
    });
  }
  
  private updateMLMetrics(): void {
    // Get recent timing data for ML operations
    const recentTimings = performanceMonitor['timers'] || new Map();
    
    // Calculate average inference time
    const inferenceTimings = Array.from(recentTimings.entries())
      .filter(([key]) => key.includes('inference'))
      .map(([, data]) => data.duration)
      .filter(d => d !== undefined);
    
    if (inferenceTimings.length > 0) {
      this.metrics.inferenceTime = inferenceTimings.reduce((a, b) => a + b, 0) / inferenceTimings.length;
    }
    
    // Calculate suggestion generation time
    const suggestionTimings = Array.from(recentTimings.entries())
      .filter(([key]) => key.includes('suggestion'))
      .map(([, data]) => data.duration)
      .filter(d => d !== undefined);
    
    if (suggestionTimings.length > 0) {
      this.metrics.suggestionGenerationTime = suggestionTimings.reduce((a, b) => a + b, 0) / suggestionTimings.length;
    }
  }
  
  private estimateMemoryUsage(): number {
    // Simple memory usage estimation
    // In a real implementation, this would use actual memory monitoring APIs
    const cacheMemory = intelligentCache.getMetrics().memoryUsage;
    const estimatedTotal = 200 * 1024 * 1024; // Estimate 200MB total
    
    return Math.min(cacheMemory / estimatedTotal, 1);
  }
  
  private checkAlerts(): void {
    const alerts: Array<{ type: string; message: string; severity: string }> = [];
    
    if (this.metrics.averageResponseTime > this.config.alertThresholds.responseTime) {
      alerts.push({
        type: 'response_time',
        message: `High response time: ${this.metrics.averageResponseTime.toFixed(0)}ms`,
        severity: 'high',
      });
    }
    
    if (this.metrics.errorRate > this.config.alertThresholds.errorRate) {
      alerts.push({
        type: 'error_rate',
        message: `High error rate: ${(this.metrics.errorRate * 100).toFixed(1)}%`,
        severity: 'high',
      });
    }
    
    if (this.metrics.memoryUsage > this.config.alertThresholds.memoryUsage) {
      alerts.push({
        type: 'memory_usage',
        message: `High memory usage: ${(this.metrics.memoryUsage * 100).toFixed(1)}%`,
        severity: 'critical',
      });
    }
    
    // Emit alerts
    alerts.forEach(alert => {
      eventBus.emit({
        type: 'performance.alert',
        payload: alert,
        source: 'PerformanceOptimizer',
      });
    });
  }
  
  private setupEventListeners(): void {
    // Listen for precompute requests
    eventBus.on('performance.precompute_requested', async (event) => {
      const { priority } = event.payload;
      
      // Schedule common computations in background
      await backgroundProcessor.scheduleHistoryAnalysis();
    });
  }
  
  /**
   * Configuration and control
   */
  
  updateConfig(updates: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...updates };
    
    // Restart monitoring with new config
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.startMonitoring();
    }
  }
  
  /**
   * Cleanup
   */
  destroy(): void {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
    }
  }
}

// Export singleton instance
export const performanceOptimizer = PerformanceOptimizer.getInstance();
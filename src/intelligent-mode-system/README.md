# Intelligent Mode System

A context-aware development environment system that automatically adapts to developer workflows.

## Features

- **6 Specialized Modes**: Architect, Debug, Review, Deploy, Experiment, and Learn
- **Context Inference**: Automatically detects the best mode based on your current activity
- **Performance Optimized**: < 2s context analysis, < 1s mode transitions
- **Privacy-First**: All analysis happens locally
- **Event-Driven Architecture**: Extensible and reactive system
- **Smart Caching**: Efficient resource usage with < 50MB memory footprint

## Quick Start

```tsx
import { useIntelligentMode } from '@intelligent-mode';

function MyComponent() {
  const { currentMode, context, switchMode, recordAction } = useIntelligentMode();
  
  return (
    <div>
      <p>Current Mode: {currentMode?.name}</p>
      <button onClick={() => switchMode('debug')}>Switch to Debug</button>
    </div>
  );
}
```

## Architecture

The system consists of several core components:

1. **Context Inference Engine**: Analyzes multiple context layers to recommend modes
2. **Mode Registry**: Manages mode definitions and transitions
3. **Context Providers**: Gather context from file, project, git, and user activity
4. **Event Bus**: Enables loose coupling between components
5. **Cache System**: Optimizes performance with intelligent caching
6. **Performance Monitor**: Tracks and ensures system performance

## Modes

### Architect Mode 🏗️
- System design and architecture planning
- Dependency analysis
- Component structure visualization

### Debug Mode 🐛
- Error detection and diagnostics
- Breakpoint management
- Stack trace analysis

### Review Mode 👀
- Code review assistance
- Quality checks
- Best practice suggestions

### Deploy Mode 🚀
- Deployment configuration
- CI/CD pipeline management
- Environment setup

### Experiment Mode 🧪
- Rapid prototyping
- Scratch pad functionality
- Quick testing

### Learn Mode 📚
- Documentation lookup
- Tutorial integration
- Knowledge base access

## Performance

- Context Analysis: < 2 seconds
- Mode Transitions: < 1 second
- Memory Usage: < 50MB
- Cache Hit Rate: ~80%

## Testing

```bash
npm test -- src/intelligent-mode-system
```

## Integration

See the `IntelligentModeDemo` component for a complete integration example.
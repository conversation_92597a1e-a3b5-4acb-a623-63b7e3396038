# Design Token System

A comprehensive design token system for the Intelligent Mode System in Claudia.

## Overview

The design token system provides a consistent, scalable approach to theming across all 6 intelligent modes:
- **Architect Mode** (Blue) - System design and architecture
- **Debug Mode** (Red) - Error analysis and debugging
- **Review Mode** (Cyan) - Code review and quality analysis
- **Deploy Mode** (Green) - Deployment and CI/CD
- **Experiment Mode** (Purple) - Creative exploration
- **Learn Mode** (Amber) - Educational guidance

## Structure

### Core Tokens

1. **Colors**: Comprehensive color palettes for each mode with 11 shades
2. **Typography**: Font families, sizes, weights, line heights, and letter spacing
3. **Spacing**: Consistent spacing scale from 0 to 24rem
4. **Radii**: Border radius scale for consistent rounding
5. **Shadows**: Elevation system with multiple shadow levels
6. **Animations**: Duration and easing presets
7. **Z-indices**: Layering system for overlays and modals
8. **Breakpoints**: Responsive design breakpoints

### Mode Themes

Each mode has a complete theme with:
- Primary, secondary, and accent colors with variants
- Background and surface colors
- Text colors with hierarchy
- Border colors
- Status colors (success, warning, error, info)
- Interactive states (hover, active, disabled)
- Special element colors (code blocks, tooltips)

## Usage

### In TypeScript/React

```typescript
import { modeThemes, applyThemeToCSS } from '@/intelligent-mode-system/design-tokens';

// Apply a mode theme
applyThemeToCSS(modeThemes.architect);

// Use design tokens
import { colors, spacing, typography } from '@/intelligent-mode-system/design-tokens';

const styles = {
  padding: spacing[4],
  fontSize: typography.sizes.base,
  color: colors.architect[500],
};
```

### In CSS

```css
/* Use CSS variables set by the theme */
.my-component {
  background-color: var(--mode-surface);
  color: var(--mode-text);
  border: 1px solid var(--mode-border);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  transition: all var(--duration-normal) var(--easing-in-out);
}

/* Mode-specific classes */
.mode-architect {
  --mode-primary: #3b82f6;
}
```

## CSS Variables

The system automatically sets CSS custom properties for all theme values:

- `--mode-primary`, `--mode-primary-light`, `--mode-primary-dark`, etc.
- `--mode-background`, `--mode-surface`, `--mode-text`, etc.
- `--spacing-1`, `--spacing-2`, `--spacing-4`, etc.
- `--radius-sm`, `--radius-md`, `--radius-lg`, etc.
- `--duration-fast`, `--duration-normal`, `--duration-slow`
- `--easing-in`, `--easing-out`, `--easing-in-out`

## Theme Switching

Theme switching is handled automatically by the ModeContext:

1. When a mode is activated, its theme is applied
2. CSS transitions are temporarily disabled for instant feedback
3. All components using CSS variables update automatically

## Customization

To create a custom theme:

```typescript
import { createModeTheme, applyThemeToCSS } from '@/intelligent-mode-system/design-tokens';

// Create a theme based on the gray color palette
const customTheme = createModeTheme('gray');

// Apply it
applyThemeToCSS(customTheme);
```

## Best Practices

1. **Use semantic tokens**: Prefer `var(--mode-primary)` over hardcoded colors
2. **Respect the scale**: Use spacing and typography scales for consistency
3. **Theme-aware components**: Build components that adapt to theme changes
4. **Performance**: CSS variables update instantly without re-rendering
5. **Accessibility**: Ensure sufficient color contrast in all modes
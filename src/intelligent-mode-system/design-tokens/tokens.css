/**
 * Design Token CSS Variables
 * 
 * This file defines CSS custom properties that correspond to the design tokens
 * defined in tokens.ts. These variables are dynamically updated when modes change.
 */

:root {
  /* Core color variables - dynamically set by mode */
  --mode-primary: #3b82f6;
  --mode-primary-light: #60a5fa;
  --mode-primary-dark: #2563eb;
  --mode-primary-foreground: #0a0a0a;
  
  --mode-secondary: #93c5fd;
  --mode-secondary-light: #bfdbfe;
  --mode-secondary-dark: #60a5fa;
  --mode-secondary-foreground: #f5f5f5;
  
  --mode-accent: #2563eb;
  --mode-accent-light: #3b82f6;
  --mode-accent-dark: #1d4ed8;
  --mode-accent-foreground: #fafafa;
  
  /* Background colors */
  --mode-background: #0a0a0a;
  --mode-background-secondary: #171717;
  --mode-background-tertiary: #262626;
  
  /* Surface colors */
  --mode-surface: #171717;
  --mode-surface-secondary: #262626;
  --mode-surface-tertiary: #404040;
  
  /* Text colors */
  --mode-text: #f5f5f5;
  --mode-text-secondary: #d4d4d4;
  --mode-text-tertiary: #737373;
  --mode-text-muted: #525252;
  
  /* Border colors */
  --mode-border: #404040;
  --mode-border-secondary: #262626;
  --mode-border-focus: var(--mode-primary);
  
  /* Status colors */
  --mode-success: #22c55e;
  --mode-success-foreground: #fafafa;
  --mode-warning: #f59e0b;
  --mode-warning-foreground: #0a0a0a;
  --mode-error: #ef4444;
  --mode-error-foreground: #fafafa;
  --mode-info: #0ea5e9;
  --mode-info-foreground: #fafafa;
  
  /* Interactive states */
  --mode-hover: #1e3a8a;
  --mode-active: #1e40af;
  --mode-disabled: #404040;
  
  /* Special elements */
  --mode-code-background: #171717;
  --mode-code-border: #404040;
  --mode-tooltip-background: #f5f5f5;
  --mode-tooltip-foreground: #171717;
  
  /* Typography */
  --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-mono: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
  
  /* Font sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* Font weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* Line heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
  
  /* Letter spacing */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  
  /* Spacing scale */
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0-5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1-5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2-5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3-5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-14: 3.5rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  
  /* Border radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-none: none;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  
  /* Animation durations */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;
  
  /* Animation easings */
  --easing-linear: linear;
  --easing-in: cubic-bezier(0.4, 0, 1, 1);
  --easing-out: cubic-bezier(0, 0, 0.2, 1);
  --easing-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Z-index scale */
  --z-base: 0;
  --z-dropdown: 1000;
  --z-sticky: 1100;
  --z-banner: 1200;
  --z-overlay: 1300;
  --z-modal: 1400;
  --z-popover: 1500;
  --z-toast: 1600;
  --z-tooltip: 1700;
}

/* Mode-specific classes for quick application */
.mode-architect {
  --mode-primary: #3b82f6;
  --mode-accent: #2563eb;
}

.mode-debug {
  --mode-primary: #ef4444;
  --mode-accent: #dc2626;
}

.mode-review {
  --mode-primary: #0ea5e9;
  --mode-accent: #0284c7;
}

.mode-deploy {
  --mode-primary: #22c55e;
  --mode-accent: #16a34a;
}

.mode-experiment {
  --mode-primary: #d946ef;
  --mode-accent: #c026d3;
}

.mode-learn {
  --mode-primary: #f59e0b;
  --mode-accent: #d97706;
}

/* Utility classes using design tokens */
.mode-primary-bg {
  background-color: var(--mode-primary);
  color: var(--mode-primary-foreground);
}

.mode-secondary-bg {
  background-color: var(--mode-secondary);
  color: var(--mode-secondary-foreground);
}

.mode-accent-bg {
  background-color: var(--mode-accent);
  color: var(--mode-accent-foreground);
}

.mode-surface {
  background-color: var(--mode-surface);
  color: var(--mode-text);
}

.mode-border {
  border-color: var(--mode-border);
}

.mode-text-primary {
  color: var(--mode-text);
}

.mode-text-secondary {
  color: var(--mode-text-secondary);
}

.mode-text-muted {
  color: var(--mode-text-muted);
}

/* Transitions for smooth mode changes */
* {
  transition: 
    background-color var(--duration-normal) var(--easing-in-out),
    border-color var(--duration-normal) var(--easing-in-out),
    color var(--duration-normal) var(--easing-in-out);
}

/* Disable transitions during mode switch for instant feedback */
.mode-switching * {
  transition: none !important;
}
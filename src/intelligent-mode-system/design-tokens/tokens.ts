/**
 * Design Token System for Intelligent Modes
 * 
 * A comprehensive design token system that provides consistent theming,
 * spacing, typography, and other design decisions across all 6 modes.
 */

import { ModeTheme } from '../types/mode.types';

// Base color primitives
export const colors = {
  // Neutral colors
  gray: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
    950: '#0a0a0a',
  },
  
  // Mode-specific color palettes
  architect: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6', // Primary
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554',
  },
  
  debug: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444', // Primary
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a',
  },
  
  review: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9', // Primary
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
    950: '#082f49',
  },
  
  deploy: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e', // Primary
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16',
  },
  
  experiment: {
    50: '#fdf4ff',
    100: '#fae8ff',
    200: '#f5d0fe',
    300: '#f0abfc',
    400: '#e879f9',
    500: '#d946ef', // Primary
    600: '#c026d3',
    700: '#a21caf',
    800: '#86198f',
    900: '#701a75',
    950: '#4a044e',
  },
  
  learn: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b', // Primary
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  },
} as const;

// Typography tokens
export const typography = {
  fonts: {
    sans: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    mono: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
  },
  
  sizes: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
  },
  
  weights: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
  
  lineHeights: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75',
  },
  
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
    wider: '0.05em',
  },
} as const;

// Spacing tokens
export const spacing = {
  0: '0',
  px: '1px',
  0.5: '0.125rem', // 2px
  1: '0.25rem',    // 4px
  1.5: '0.375rem', // 6px
  2: '0.5rem',     // 8px
  2.5: '0.625rem', // 10px
  3: '0.75rem',    // 12px
  3.5: '0.875rem', // 14px
  4: '1rem',       // 16px
  5: '1.25rem',    // 20px
  6: '1.5rem',     // 24px
  7: '1.75rem',    // 28px
  8: '2rem',       // 32px
  9: '2.25rem',    // 36px
  10: '2.5rem',    // 40px
  12: '3rem',      // 48px
  14: '3.5rem',    // 56px
  16: '4rem',      // 64px
  20: '5rem',      // 80px
  24: '6rem',      // 96px
} as const;

// Border radius tokens
export const radii = {
  none: '0',
  sm: '0.125rem',  // 2px
  base: '0.25rem', // 4px
  md: '0.375rem',  // 6px
  lg: '0.5rem',    // 8px
  xl: '0.75rem',   // 12px
  '2xl': '1rem',   // 16px
  '3xl': '1.5rem', // 24px
  full: '9999px',
} as const;

// Shadow tokens
export const shadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
} as const;

// Animation tokens
export const animations = {
  durations: {
    fast: '150ms',
    normal: '250ms',
    slow: '350ms',
    slower: '500ms',
  },
  
  easings: {
    linear: 'linear',
    in: 'cubic-bezier(0.4, 0, 1, 1)',
    out: 'cubic-bezier(0, 0, 0.2, 1)',
    inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
} as const;

// Z-index tokens
export const zIndices = {
  base: 0,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  toast: 1600,
  tooltip: 1700,
} as const;

// Breakpoint tokens
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Create mode-specific themes
export const createModeTheme = (modeId: keyof typeof colors): ModeTheme => {
  const modeColors = colors[modeId];
  const isLightMode = false; // Can be made dynamic later
  
  return {
    // Core colors
    primary: modeColors[500],
    primaryLight: modeColors[400],
    primaryDark: modeColors[600],
    primaryForeground: isLightMode ? colors.gray[50] : colors.gray[950],
    
    // Secondary colors
    secondary: modeColors[300],
    secondaryLight: modeColors[200],
    secondaryDark: modeColors[400],
    secondaryForeground: isLightMode ? colors.gray[900] : colors.gray[100],
    
    // Accent colors
    accent: modeColors[600],
    accentLight: modeColors[500],
    accentDark: modeColors[700],
    accentForeground: colors.gray[50],
    
    // Background colors
    background: isLightMode ? colors.gray[50] : colors.gray[950],
    backgroundSecondary: isLightMode ? colors.gray[100] : colors.gray[900],
    backgroundTertiary: isLightMode ? colors.gray[200] : colors.gray[800],
    
    // Surface colors
    surface: isLightMode ? colors.gray[100] : colors.gray[900],
    surfaceSecondary: isLightMode ? colors.gray[200] : colors.gray[800],
    surfaceTertiary: isLightMode ? colors.gray[300] : colors.gray[700],
    
    // Text colors
    text: isLightMode ? colors.gray[900] : colors.gray[100],
    textSecondary: isLightMode ? colors.gray[700] : colors.gray[300],
    textTertiary: isLightMode ? colors.gray[500] : colors.gray[500],
    textMuted: isLightMode ? colors.gray[400] : colors.gray[600],
    
    // Border colors
    border: isLightMode ? colors.gray[300] : colors.gray[700],
    borderSecondary: isLightMode ? colors.gray[200] : colors.gray[800],
    borderFocus: modeColors[500],
    
    // Status colors
    success: colors.deploy[500],
    successForeground: colors.gray[50],
    warning: colors.learn[500],
    warningForeground: colors.gray[950],
    error: colors.debug[500],
    errorForeground: colors.gray[50],
    info: colors.review[500],
    infoForeground: colors.gray[50],
    
    // Interactive states
    hover: isLightMode ? modeColors[100] : modeColors[900],
    active: isLightMode ? modeColors[200] : modeColors[800],
    disabled: isLightMode ? colors.gray[300] : colors.gray[700],
    
    // Special elements
    codeBackground: isLightMode ? colors.gray[100] : colors.gray[900],
    codeBorder: isLightMode ? colors.gray[300] : colors.gray[700],
    tooltipBackground: isLightMode ? colors.gray[900] : colors.gray[100],
    tooltipForeground: isLightMode ? colors.gray[100] : colors.gray[900],
  };
};

// Utility function to apply theme CSS variables
export const applyThemeToCSS = (theme: ModeTheme) => {
  const root = document.documentElement;
  
  // Apply all theme colors as CSS variables
  Object.entries(theme).forEach(([key, value]) => {
    const cssVarName = `--mode-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
    root.style.setProperty(cssVarName, value);
  });
  
  // Apply other design tokens
  root.style.setProperty('--font-sans', typography.fonts.sans);
  root.style.setProperty('--font-mono', typography.fonts.mono);
  
  // Apply spacing scale
  Object.entries(spacing).forEach(([key, value]) => {
    root.style.setProperty(`--spacing-${key}`, value);
  });
  
  // Apply radius scale
  Object.entries(radii).forEach(([key, value]) => {
    root.style.setProperty(`--radius-${key}`, value);
  });
  
  // Apply animation values
  Object.entries(animations.durations).forEach(([key, value]) => {
    root.style.setProperty(`--duration-${key}`, value);
  });
  
  Object.entries(animations.easings).forEach(([key, value]) => {
    root.style.setProperty(`--easing-${key}`, value);
  });
};

// Export pre-built themes for each mode
export const modeThemes = {
  architect: createModeTheme('architect'),
  debug: createModeTheme('debug'),
  review: createModeTheme('review'),
  deploy: createModeTheme('deploy'),
  experiment: createModeTheme('experiment'),
  learn: createModeTheme('learn'),
} as const;

// Type exports
export type ModeTheme = ReturnType<typeof createModeTheme>;
export type DesignToken = typeof colors | typeof typography | typeof spacing | typeof radii | typeof shadows | typeof animations | typeof zIndices | typeof breakpoints;
// Core exports
export { contextInferenceEngine } from './core/ContextInferenceEngine';
export { modeRegistry } from './core/ModeRegistry';
export { eventBus } from './core/EventBus';
export { cacheSystem } from './core/CacheSystem';
export { modeTransitionManager } from './core/ModeTransitionManager';

// Provider exports
export {
  fileContextProvider,
  projectContextProvider,
  gitContextProvider,
  userContextProvider,
  registerAllProviders,
} from './providers';

// Mode exports
export * from './modes';

// Hook exports
export { useIntelligentMode } from './hooks/useIntelligentMode';

// Component exports
export { default as ModeIndicator } from './components/ModeIndicator';
export { default as IntelligentModeDemo } from './demo/IntelligentModeDemo';

// Utility exports
export { performanceMonitor } from './core/PerformanceMonitor';

// Type exports
export * from './types';

// Main initialization function
import { contextInferenceEngine } from './core/ContextInferenceEngine';
import { modeRegistry } from './core/ModeRegistry';
import { eventBus } from './core/EventBus';
import { performanceMonitor } from './core/PerformanceMonitor';
import { modeTransitionManager } from './core/ModeTransitionManager';
import { registerAllProviders, fileContextProvider, projectContextProvider, gitContextProvider, userContextProvider } from './providers';

export interface IntelligentModeSystemConfig {
  autoStart?: boolean;
  analysisInterval?: number;
  enablePerformanceMonitoring?: boolean;
  enableContinuousAnalysis?: boolean;
}

export class IntelligentModeSystem {
  private initialized = false;
  private config: IntelligentModeSystemConfig;

  constructor(config: IntelligentModeSystemConfig = {}) {
    this.config = {
      autoStart: true,
      analysisInterval: 5000,
      enablePerformanceMonitoring: true,
      enableContinuousAnalysis: true,
      ...config,
    };
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      console.warn('Intelligent Mode System already initialized');
      return;
    }

    performanceMonitor.startTimer('system.initialization');

    try {
      // Register all context providers (including mode-specific ones)
      registerAllProviders();

      // Start performance monitoring
      if (this.config.enablePerformanceMonitoring) {
        this.setupPerformanceMonitoring();
      }

      // Set default mode if no mode is active
      if (!modeRegistry.getActiveMode()) {
        await modeRegistry.setActiveMode('architect');
      }

      // Start continuous context analysis
      if (this.config.enableContinuousAnalysis) {
        contextInferenceEngine.startContinuousAnalysis(this.config.analysisInterval);
      }

      // Perform initial analysis
      if (this.config.autoStart) {
        await contextInferenceEngine.analyze();
      }

      this.initialized = true;

      performanceMonitor.endTimer('system.initialization');

      eventBus.emit({
        type: 'mode.change',
        payload: { action: 'system-initialized' },
        source: 'IntelligentModeSystem',
      });

    } catch (error) {
      performanceMonitor.endTimer('system.initialization', { error: 'true' });
      console.error('Failed to initialize Intelligent Mode System:', error);
      throw error;
    }
  }

  async shutdown(): Promise<void> {
    if (!this.initialized) return;

    // Stop continuous analysis
    contextInferenceEngine.stopContinuousAnalysis();

    // Stop provider watching
    fileContextProvider.stopWatching();
    projectContextProvider.stopWatching();
    gitContextProvider.stopWatching();
    userContextProvider.stopWatching();

    // Clear caches
    await cacheSystem.clear();

    // Clear event listeners
    eventBus.clear();

    this.initialized = false;

    console.log('Intelligent Mode System shut down');
  }

  private setupPerformanceMonitoring(): void {
    // Monitor mode transitions
    eventBus.on('mode.transition.complete', (event) => {
      const duration = event.payload.duration;
      performanceMonitor.recordMetric({
        name: 'mode.transition',
        value: duration,
        unit: 'ms',
        timestamp: Date.now(),
        tags: {
          from: event.payload.from,
          to: event.payload.to,
        },
      });
    });

    // Monitor context analysis
    eventBus.on('context.analyzed', () => {
      const stats = performanceMonitor.getStats('context.analysis');
      if (stats && stats.avg > 1500) {
        console.warn('Context analysis performance degradation detected:', stats);
      }
    });

    // Monitor cache performance
    let cacheHits = 0;
    let cacheMisses = 0;

    eventBus.on('cache.hit', () => cacheHits++);
    eventBus.on('cache.miss', () => cacheMisses++);

    // Log performance report periodically
    setInterval(() => {
      const report = performanceMonitor.generateReport();
      const cacheHitRate = cacheHits / (cacheHits + cacheMisses) || 0;

      console.log('Performance Report:', {
        ...report.summary,
        cacheHitRate: `${(cacheHitRate * 100).toFixed(1)}%`,
        violations: report.violations,
      });

      // Reset cache counters
      cacheHits = 0;
      cacheMisses = 0;
    }, 60000); // Every minute
  }

  // Public API methods
  getCurrentMode() {
    return modeRegistry.getActiveMode();
  }

  async switchMode(modeId: string) {
    const context = await contextInferenceEngine.analyze();
    await modeRegistry.setActiveMode(modeId as any, context);
  }

  async getRecommendedMode() {
    const context = await contextInferenceEngine.analyze();
    return contextInferenceEngine.getRecommendedMode(context);
  }

  getModeProbabilities() {
    return contextInferenceEngine.getProbabilities();
  }

  recordUserAction(action: Parameters<typeof userContextProvider.recordAction>[0]) {
    userContextProvider.recordAction(action);
  }

  updateUserPreferences(preferences: Parameters<typeof userContextProvider.updatePreferences>[0]) {
    userContextProvider.updatePreferences(preferences);
  }

  getPerformanceReport() {
    return performanceMonitor.generateReport();
  }
}

// Create default instance
export const intelligentModeSystem = new IntelligentModeSystem();
/**
 * Vitest Configuration
 * 
 * Test configuration for the intelligent mode system.
 */

import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    // Test environment
    environment: 'node',
    
    // Global setup
    setupFiles: ['./__tests__/setup.ts'],
    
    // Test patterns
    include: [
      '**/__tests__/**/*.test.{js,ts}',
      '**/*.{test,spec}.{js,ts}'
    ],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**'
    ],
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage',
      include: [
        'core/**/*.ts',
        'utils/**/*.ts',
        'plugins/**/*.ts',
        'services/**/*.ts'
      ],
      exclude: [
        '**/__tests__/**',
        '**/*.test.ts',
        '**/*.spec.ts',
        '**/types.ts',
        '**/index.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // Test timeout
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // Reporter configuration
    reporter: ['verbose', 'json'],
    outputFile: {
      json: './test-results.json'
    },
    
    // Parallel execution
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1
      }
    },
    
    // Mock configuration
    clearMocks: true,
    restoreMocks: true,
    
    // Watch mode
    watch: false,
    
    // Global test configuration
    globals: false,
    isolate: true,
    
    // Performance
    maxConcurrency: 4,
    
    // Environment variables for tests
    env: {
      NODE_ENV: 'test',
      INTELLIGENT_MODE_DEBUG: 'false',
      VITEST_VERBOSE: 'false'
    }
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '.'),
      '@core': path.resolve(__dirname, './core'),
      '@utils': path.resolve(__dirname, './utils'),
      '@plugins': path.resolve(__dirname, './plugins'),
      '@services': path.resolve(__dirname, './services'),
      '@tests': path.resolve(__dirname, './__tests__')
    }
  },
  
  // Define configuration
  define: {
    'import.meta.vitest': false
  }
});
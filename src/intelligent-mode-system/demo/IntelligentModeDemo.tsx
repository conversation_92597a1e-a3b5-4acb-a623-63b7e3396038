import React, { useState } from 'react';
import { useIntelligentMode } from '../hooks/useIntelligentMode';
import ModeIndicator from '../components/ModeIndicator';
import { ModePanel } from '../components/ModeFeatures';
import { userContextProvider, fileContextProvider, gitContextProvider } from '../providers';

/**
 * Demo component showing how to integrate the Intelligent Mode System
 */
export const IntelligentModeDemo: React.FC = () => {
  const {
    currentMode,
    context,
    recordAction,
    refreshContext,
  } = useIntelligentMode();

  const [simulatedFile, setSimulatedFile] = useState('src/App.tsx');
  const [showDebug, setShowDebug] = useState(false);

  // Simulate file changes
  const handleFileChange = (path: string) => {
    setSimulatedFile(path);
    fileContextProvider.setCurrentFile(path);
    
    // Record navigation action
    recordAction({
      type: 'navigate',
      target: path,
    });
  };

  // Simulate user actions
  const simulateAction = (type: string, target?: string) => {
    recordAction({
      type: type as any,
      target,
      metadata: { demo: true },
    });

    // Refresh context to see immediate changes
    setTimeout(() => refreshContext(), 100);
  };

  return (
    <div className="intelligent-mode-demo p-6 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Intelligent Mode System Demo</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Mode Indicator */}
        <div className="lg:col-span-1">
          <h2 className="text-lg font-semibold mb-3">Mode Control</h2>
          <ModeIndicator />
        </div>

        {/* Simulation Controls */}
        <div className="lg:col-span-2 space-y-6">
          {/* File Simulation */}
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-md font-semibold mb-3">Simulate File Changes</h3>
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => handleFileChange('src/App.tsx')}
                className={`px-3 py-2 rounded ${
                  simulatedFile === 'src/App.tsx' 
                    ? 'bg-blue-600' 
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
              >
                App.tsx (React)
              </button>
              <button
                onClick={() => handleFileChange('src/error.ts')}
                className={`px-3 py-2 rounded ${
                  simulatedFile === 'src/error.ts' 
                    ? 'bg-blue-600' 
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
              >
                error.ts (Has Errors)
              </button>
              <button
                onClick={() => handleFileChange('README.md')}
                className={`px-3 py-2 rounded ${
                  simulatedFile === 'README.md' 
                    ? 'bg-blue-600' 
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
              >
                README.md (Docs)
              </button>
              <button
                onClick={() => handleFileChange('Dockerfile')}
                className={`px-3 py-2 rounded ${
                  simulatedFile === 'Dockerfile' 
                    ? 'bg-blue-600' 
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
              >
                Dockerfile (Deploy)
              </button>
              <button
                onClick={() => handleFileChange('scratch.js')}
                className={`px-3 py-2 rounded ${
                  simulatedFile === 'scratch.js' 
                    ? 'bg-blue-600' 
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
              >
                scratch.js (Experiment)
              </button>
              <button
                onClick={() => handleFileChange('.github/workflows/ci.yml')}
                className={`px-3 py-2 rounded ${
                  simulatedFile === '.github/workflows/ci.yml' 
                    ? 'bg-blue-600' 
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
              >
                CI/CD Config
              </button>
            </div>
            <p className="text-sm text-gray-400 mt-2">
              Current: {simulatedFile}
            </p>
          </div>

          {/* Action Simulation */}
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-md font-semibold mb-3">Simulate User Actions</h3>
            <div className="grid grid-cols-3 gap-2">
              <button
                onClick={() => simulateAction('edit', 'console.log')}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded"
              >
                Add Console.log
              </button>
              <button
                onClick={() => simulateAction('debug', 'breakpoint')}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded"
              >
                Set Breakpoint
              </button>
              <button
                onClick={() => simulateAction('search', 'documentation')}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded"
              >
                Search Docs
              </button>
              <button
                onClick={() => simulateAction('command', 'git diff')}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded"
              >
                Git Diff
              </button>
              <button
                onClick={() => simulateAction('command', 'npm build')}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded"
              >
                Run Build
              </button>
              <button
                onClick={() => {
                  // Simulate rapid editing
                  for (let i = 0; i < 10; i++) {
                    setTimeout(() => simulateAction('edit', `line ${i}`), i * 100);
                  }
                }}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded"
              >
                Rapid Editing
              </button>
            </div>
          </div>

          {/* Context Display */}
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-md font-semibold">Current Context</h3>
              <button
                onClick={() => setShowDebug(!showDebug)}
                className="text-sm text-blue-400 hover:text-blue-300"
              >
                {showDebug ? 'Hide' : 'Show'} Debug Info
              </button>
            </div>
            
            {context && (
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">File:</span>
                  <span>{context.fileContext.path}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Has Errors:</span>
                  <span className={context.fileContext.hasErrors ? 'text-red-400' : 'text-green-400'}>
                    {context.fileContext.hasErrors ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Project Type:</span>
                  <span>{context.projectContext.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Git Branch:</span>
                  <span>{context.environmentContext.gitStatus?.branch || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Recent Actions:</span>
                  <span>{context.userContext.recentActions.length}</span>
                </div>
                
                {showDebug && (
                  <div className="mt-4 p-3 bg-gray-900 rounded">
                    <pre className="text-xs overflow-auto">
                      {JSON.stringify(context, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Performance Metrics */}
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-md font-semibold mb-3">Performance Metrics</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Context Analysis:</span>
                <span className="ml-2 text-green-400">&lt; 2s</span>
              </div>
              <div>
                <span className="text-gray-400">Mode Transitions:</span>
                <span className="ml-2 text-green-400">&lt; 1s</span>
              </div>
              <div>
                <span className="text-gray-400">Cache Hit Rate:</span>
                <span className="ml-2 text-green-400">~80%</span>
              </div>
              <div>
                <span className="text-gray-400">Memory Usage:</span>
                <span className="ml-2 text-green-400">&lt; 50MB</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mode-specific Panel */}
      {currentMode && (
        <div className="mt-6">
          <ModePanel mode={currentMode} context={context} />
        </div>
      )}
    </div>
  );
};

export default IntelligentModeDemo;
import React, { useState, useEffect } from 'react';
import { useIntelligentMode } from '../hooks/useIntelligentMode';
import { ModeId, Mode } from '../types';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../components/ui/dialog';
import { 
  Loader2, 
  TrendingUp, 
  Zap, 
  Clock, 
  CheckCircle,
  AlertCircle,
  ArrowRight,
  X
} from 'lucide-react';

interface ModeTransitionSuggestion {
  fromMode: ModeId;
  toMode: ModeId;
  reason: string;
  confidence: number;
  timestamp: number;
}

export const ModeSelector: React.FC = () => {
  const {
    currentMode,
    recommendedMode,
    modeProbabilities,
    isTransitioning,
    switchMode,
    availableModes,
  } = useIntelligentMode();

  const [showTransitionSuggestion, setShowTransitionSuggestion] = useState(false);
  const [currentSuggestion, setCurrentSuggestion] = useState<ModeTransitionSuggestion | null>(null);
  const [showModeDetails, setShowModeDetails] = useState(false);
  const [selectedModeForDetails, setSelectedModeForDetails] = useState<Mode | null>(null);

  // Check for transition suggestions
  useEffect(() => {
    if (recommendedMode && recommendedMode !== currentMode?.id && !isTransitioning) {
      const confidence = modeProbabilities[recommendedMode] || 0;
      if (confidence > 0.7) {
        const suggestion: ModeTransitionSuggestion = {
          fromMode: currentMode?.id || 'architect',
          toMode: recommendedMode,
          reason: generateTransitionReason(currentMode?.id || 'architect', recommendedMode, confidence),
          confidence,
          timestamp: Date.now()
        };
        setCurrentSuggestion(suggestion);
        setShowTransitionSuggestion(true);
      }
    }
  }, [recommendedMode, currentMode?.id, modeProbabilities, isTransitioning]);

  const handleModeSwitch = async (modeId: ModeId) => {
    if (!isTransitioning && currentMode?.id !== modeId) {
      await switchMode(modeId);
      setShowTransitionSuggestion(false);
    }
  };

  const handleAcceptSuggestion = async () => {
    if (currentSuggestion) {
      await handleModeSwitch(currentSuggestion.toMode);
    }
  };

  const handleDismissSuggestion = () => {
    setShowTransitionSuggestion(false);
    setCurrentSuggestion(null);
  };

  const showModeDetailsDialog = (mode: Mode) => {
    setSelectedModeForDetails(mode);
    setShowModeDetails(true);
  };

  const getProbabilityColor = (probability: number): string => {
    if (probability > 0.7) return 'text-green-500';
    if (probability > 0.5) return 'text-yellow-500';
    if (probability > 0.3) return 'text-orange-500';
    return 'text-gray-400';
  };

  const formatProbability = (probability: number): string => {
    return `${Math.round(probability * 100)}%`;
  };

  return (
    <>
      <div className="mode-selector">
        {/* Main Mode Selector */}
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-gray-400">Current Mode</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <Select 
              value={currentMode?.id || 'architect'} 
              onValueChange={handleModeSwitch}
              disabled={isTransitioning}
            >
              <SelectTrigger className="mode-selector-trigger bg-gray-800 border-gray-600">
                <SelectValue>
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{currentMode?.icon}</span>
                    <div className="flex flex-col items-start">
                      <span className="font-medium">{currentMode?.name}</span>
                      <span className="text-xs text-gray-400">{currentMode?.description}</span>
                    </div>
                    {isTransitioning && <Loader2 className="h-4 w-4 animate-spin ml-auto" />}
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                {availableModes.map(mode => {
                  const probability = modeProbabilities[mode.id] || 0;
                  const isActive = currentMode?.id === mode.id;
                  const isRecommended = recommendedMode === mode.id;
                  
                  return (
                    <SelectItem 
                      key={mode.id} 
                      value={mode.id}
                      className="focus:bg-gray-700"
                    >
                      <div className="flex items-center gap-3 py-2 w-full">
                        <span className="text-lg">{mode.icon}</span>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{mode.name}</span>
                            {isRecommended && !isActive && (
                              <Badge variant="default" className="text-xs bg-blue-600">
                                <TrendingUp className="h-3 w-3 mr-1" />
                                Recommended
                              </Badge>
                            )}
                            {isActive && (
                              <Badge variant="secondary" className="text-xs">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Active
                              </Badge>
                            )}
                          </div>
                          <div className="text-xs text-gray-400 mt-1">
                            {mode.description}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <span className={`text-xs ${getProbabilityColor(probability)}`}>
                              {formatProbability(probability)} match
                            </span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                showModeDetailsDialog(mode);
                              }}
                              className="h-auto p-0 text-xs text-blue-400 hover:text-blue-300"
                            >
                              Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>

            {/* Mode Probabilities */}
            <div className="mt-4 space-y-2">
              <div className="text-xs text-gray-400 font-medium">Context Analysis</div>
              <div className="grid grid-cols-3 gap-2">
                {Object.entries(modeProbabilities)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 6)
                  .map(([modeId, probability]) => {
                    const mode = availableModes.find(m => m.id === modeId);
                    if (!mode) return null;

                    return (
                      <div 
                        key={modeId}
                        className="flex items-center gap-2 p-2 bg-gray-800 rounded-md"
                      >
                        <span className="text-sm">{mode.icon}</span>
                        <div className="flex-1 min-w-0">
                          <div className="text-xs font-medium truncate">{mode.name}</div>
                          <div className={`text-xs ${getProbabilityColor(probability)}`}>
                            {formatProbability(probability)}
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>

            {/* Quick Actions */}
            {currentMode && (
              <div className="mt-4 space-y-2">
                <div className="text-xs text-gray-400 font-medium">Quick Actions</div>
                <div className="flex flex-wrap gap-1">
                  {currentMode.shortcuts?.slice(0, 3).map((shortcut, index) => (
                    <Button
                      key={index}
                      size="sm"
                      variant="outline"
                      className="h-7 text-xs bg-gray-800 border-gray-600 hover:bg-gray-700"
                    >
                      <kbd className="text-xs">
                        {shortcut.modifiers?.join('+')}{shortcut.modifiers?.length ? '+' : ''}{shortcut.key}
                      </kbd>
                      <span className="ml-1">{shortcut.description}</span>
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Transition Suggestion Modal */}
        <ModeTransitionSuggestion
          show={showTransitionSuggestion}
          suggestion={currentSuggestion}
          onAccept={handleAcceptSuggestion}
          onDismiss={handleDismissSuggestion}
          availableModes={availableModes}
        />

        {/* Mode Details Modal */}
        <ModeDetailsModal
          show={showModeDetails}
          mode={selectedModeForDetails}
          onClose={() => setShowModeDetails(false)}
          onSwitch={handleModeSwitch}
          currentModeId={currentMode?.id}
        />
      </div>
    </>
  );
};

// Transition Suggestion Component
const ModeTransitionSuggestion: React.FC<{
  show: boolean;
  suggestion: ModeTransitionSuggestion | null;
  onAccept: () => void;
  onDismiss: () => void;
  availableModes: Mode[];
}> = ({ show, suggestion, onAccept, onDismiss, availableModes }) => {
  if (!show || !suggestion) return null;

  const fromMode = availableModes.find(m => m.id === suggestion.fromMode);
  const toMode = availableModes.find(m => m.id === suggestion.toMode);

  return (
    <Dialog open={show} onOpenChange={onDismiss}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-yellow-500" />
            Mode Transition Suggested
          </DialogTitle>
          <DialogDescription>
            Based on your current context, switching modes might improve your workflow.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Mode Transition Visual */}
          <div className="flex items-center justify-center gap-4 py-4">
            <div className="flex flex-col items-center">
              <div className="text-2xl mb-1">{fromMode?.icon}</div>
              <div className="text-sm font-medium">{fromMode?.name}</div>
              <div className="text-xs text-gray-400">Current</div>
            </div>
            
            <ArrowRight className="h-6 w-6 text-gray-400" />
            
            <div className="flex flex-col items-center">
              <div className="text-2xl mb-1">{toMode?.icon}</div>
              <div className="text-sm font-medium">{toMode?.name}</div>
              <div className="text-xs text-green-400">Suggested</div>
            </div>
          </div>

          {/* Reason */}
          <div className="bg-gray-800 rounded-lg p-3">
            <div className="text-sm font-medium mb-2 flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-blue-400" />
              Why this transition?
            </div>
            <p className="text-sm text-gray-300">{suggestion.reason}</p>
          </div>

          {/* Confidence */}
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-400">Confidence:</span>
            <div className="flex items-center gap-2">
              <div className="w-24 h-2 bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-green-500 transition-all duration-300"
                  style={{ width: `${suggestion.confidence * 100}%` }}
                />
              </div>
              <span className="text-green-400 font-medium">
                {Math.round(suggestion.confidence * 100)}%
              </span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-2">
            <Button 
              onClick={onAccept}
              className="flex-1 bg-blue-600 hover:bg-blue-700"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Switch to {toMode?.name}
            </Button>
            <Button 
              onClick={onDismiss}
              variant="outline" 
              className="flex-1 border-gray-600 hover:bg-gray-800"
            >
              <X className="h-4 w-4 mr-2" />
              Stay in {fromMode?.name}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Mode Details Modal
const ModeDetailsModal: React.FC<{
  show: boolean;
  mode: Mode | null;
  onClose: () => void;
  onSwitch: (modeId: ModeId) => void;
  currentModeId?: ModeId;
}> = ({ show, mode, onClose, onSwitch, currentModeId }) => {
  if (!show || !mode) return null;

  const isActive = currentModeId === mode.id;

  return (
    <Dialog open={show} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span className="text-2xl">{mode.icon}</span>
            <div>
              <div>{mode.name} Mode</div>
              {isActive && (
                <Badge variant="secondary" className="text-xs mt-1">
                  Currently Active
                </Badge>
              )}
            </div>
          </DialogTitle>
          <DialogDescription>{mode.description}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Features */}
          {mode.features && (
            <div>
              <h4 className="text-sm font-medium mb-2">Key Features</h4>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(mode.features).slice(0, 6).map(([key, feature]) => (
                  <div key={key} className="bg-gray-800 rounded p-2">
                    <div className="text-xs font-medium text-gray-300">{key}</div>
                    <div className="text-xs text-gray-500">
                      {typeof feature === 'object' && feature && 'enabled' in feature 
                        ? (feature.enabled ? 'Enabled' : 'Disabled')
                        : 'Available'
                      }
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Shortcuts */}
          {mode.shortcuts && mode.shortcuts.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Keyboard Shortcuts</h4>
              <div className="space-y-2">
                {mode.shortcuts.slice(0, 4).map((shortcut, index) => (
                  <div key={index} className="flex justify-between items-center text-sm">
                    <span className="text-gray-300">{shortcut.description}</span>
                    <kbd className="px-2 py-1 bg-gray-800 rounded text-gray-400 text-xs">
                      {shortcut.modifiers?.join('+')}{shortcut.modifiers?.length ? '+' : ''}{shortcut.key}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 pt-4">
            {!isActive && (
              <Button 
                onClick={() => {
                  onSwitch(mode.id);
                  onClose();
                }}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                Switch to {mode.name}
              </Button>
            )}
            <Button 
              onClick={onClose}
              variant="outline" 
              className={`${isActive ? 'flex-1' : ''} border-gray-600 hover:bg-gray-800`}
            >
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper function to generate transition reasons
const generateTransitionReason = (fromMode: ModeId, toMode: ModeId, confidence: number): string => {
  const reasons: Record<string, Record<string, string>> = {
    architect: {
      debug: "Errors detected in your current file. Debug mode can help identify and fix issues.",
      review: "You have uncommitted changes. Review mode can help examine your modifications.",
      deploy: "Build files detected. Deploy mode can assist with deployment workflows."
    },
    debug: {
      architect: "No active errors found. Consider switching to architect mode for system design.",
      review: "Debugging complete. Review mode can help validate your fixes.",
      experiment: "Multiple debug sessions detected. Experiment mode might help with testing."
    },
    review: {
      architect: "Review complete. Switch to architect mode for further development.",
      debug: "Issues found during review. Debug mode can help resolve them.",
      deploy: "Changes approved. Deploy mode can help with release workflow."
    }
  };

  const reason = reasons[fromMode]?.[toMode];
  if (reason) return reason;

  return `Context analysis suggests ${toMode} mode would be more suitable for your current workflow (${Math.round(confidence * 100)}% confidence).`;
};

export default ModeSelector;
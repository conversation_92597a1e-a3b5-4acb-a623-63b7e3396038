import React from 'react';
import { Box, Flex, Stack, Text, Card } from '../primitives';
import { Button } from '../primitives/Button';
import { Input, Textarea } from '../primitives/Input';
import { ModePanel, ModePanelCompound } from '../compounds/ModePanel';
import { ModeWidget, ModeWidgetCompound } from '../compounds/ModeWidget';
import { ModeForm, ModeFormCompound } from '../compounds/ModeForm';

/**
 * Test component to verify all composition patterns work correctly
 */
export const CompositionTest: React.FC = () => {
  return (
    <Stack spacing="lg" padding="lg">
      <Text size="lg" weight="bold">Composition Pattern Test</Text>
      
      {/* Test Primitives */}
      <Card padding="md">
        <Stack spacing="md">
          <Text size="md" weight="semibold">Primitive Components</Text>
          
          <Flex gap="sm">
            <Button variant="primary" size="md">Primary</Button>
            <Button variant="secondary" size="md">Secondary</Button>
            <Button variant="ghost" size="md">Ghost</Button>
            <Button variant="outline" size="md">Outline</Button>
            <Button variant="danger" size="md">Danger</Button>
          </Flex>
          
          <Input placeholder="Test input" />
          <Textarea placeholder="Test textarea" rows={3} />
        </Stack>
      </Card>
      
      {/* Test ModePanel Compound */}
      <ModePanelCompound feature={{ id: 'test', name: 'Test Feature', type: 'panel' }} collapsible>
        <ModePanelCompound.Header>
          <Text weight="semibold">Mode Panel Test</Text>
        </ModePanelCompound.Header>
        <ModePanelCompound.Content>
          <Text>This is the panel content</Text>
        </ModePanelCompound.Content>
        <ModePanelCompound.Footer>
          <Text size="sm" color="muted">Panel footer</Text>
        </ModePanelCompound.Footer>
      </ModePanelCompound>
      
      {/* Test ModeWidget */}
      <ModeWidgetCompound
        title="Test Widget"
        size="md"
        icon={<span>📊</span>}
        actions={
          <ModeWidgetCompound.Actions>
            <Button size="xs" variant="ghost">Action</Button>
          </ModeWidgetCompound.Actions>
        }
      >
        <Text>Widget content goes here</Text>
      </ModeWidgetCompound>
      
      {/* Test ModeForm */}
      <Card padding="md">
        <ModeFormCompound onSubmit={(e) => e.preventDefault()}>
          <Stack spacing="md">
            <Text size="md" weight="semibold">Form Test</Text>
            
            <ModeFormCompound.Field>
              <ModeFormCompound.Label htmlFor="name" required>
                Name
              </ModeFormCompound.Label>
              <ModeFormCompound.Input
                id="name"
                placeholder="Enter your name"
                hint="This is a hint"
              />
            </ModeFormCompound.Field>
            
            <ModeFormCompound.Field>
              <ModeFormCompound.Label htmlFor="description">
                Description
              </ModeFormCompound.Label>
              <ModeFormCompound.Textarea
                id="description"
                placeholder="Enter description"
                rows={4}
              />
            </ModeFormCompound.Field>
            
            <ModeFormCompound.Actions>
              <Button type="button" variant="ghost">Cancel</Button>
              <Button type="submit" variant="primary">Submit</Button>
            </ModeFormCompound.Actions>
          </Stack>
        </ModeFormCompound>
      </Card>
    </Stack>
  );
};
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ModeLayoutResponsive } from '../layout/ModeLayoutResponsive';
import { modes } from '../../registry/defaultModes';
import { createMockContext } from '../../utils/mockData';

// Mock the responsive hooks
jest.mock('../../utils/responsive', () => ({
  ...jest.requireActual('../../utils/responsive'),
  useBreakpoint: jest.fn(() => 'lg'),
  useIsMobile: jest.fn(() => false),
  useIsTablet: jest.fn(() => false),
  useIsDesktop: jest.fn(() => true),
}));

// Mock the accessibility utilities
jest.mock('../../utils/accessibility', () => ({
  ...jest.requireActual('../../utils/accessibility'),
  announcer: {
    announce: jest.fn(),
  },
  useFocusTrap: jest.fn(() => ({ current: null })),
  useAccessibleShortcuts: jest.fn(() => ({ showHelp: false })),
}));

describe('ModeLayoutResponsive', () => {
  const mockContext = createMockContext();
  const architectMode = modes.find(m => m.id === 'architect')!;
  const debugMode = modes.find(m => m.id === 'debug')!;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Desktop Layout', () => {
    it('renders mode layout with all components', () => {
      render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div data-testid="content">Main Content</div>
        </ModeLayoutResponsive>
      );

      // Check header
      expect(screen.getByRole('banner')).toBeInTheDocument();
      expect(screen.getByText('Architect Mode')).toBeInTheDocument();
      
      // Check sidebar
      expect(screen.getByRole('complementary')).toBeInTheDocument();
      expect(screen.getByLabelText('Mode tools sidebar')).toBeInTheDocument();
      
      // Check main content
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByTestId('content')).toBeInTheDocument();
      
      // Check skip link
      expect(screen.getByText('Skip to main content')).toBeInTheDocument();
    });

    it('handles sidebar collapse/expand', async () => {
      render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      const toggleButton = screen.getByLabelText('Collapse sidebar');
      expect(toggleButton).toBeInTheDocument();

      // Collapse sidebar
      await userEvent.click(toggleButton);
      expect(screen.getByLabelText('Expand sidebar')).toBeInTheDocument();

      // Expand sidebar
      await userEvent.click(screen.getByLabelText('Expand sidebar'));
      expect(screen.getByLabelText('Collapse sidebar')).toBeInTheDocument();
    });

    it('renders mode-specific panels', () => {
      render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      // Check architect mode panels
      expect(screen.getByText('diagram-viewer')).toBeInTheDocument();
      expect(screen.getByText('requirements')).toBeInTheDocument();
      expect(screen.getByText('decisions')).toBeInTheDocument();
    });

    it('handles panel collapse/expand', async () => {
      render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      const collapseButton = screen.getAllByLabelText(/Collapse .* panel/)[0];
      await userEvent.click(collapseButton);

      // Panel should be collapsed
      expect(screen.getByLabelText(/Expand .* panel/)).toBeInTheDocument();

      // Expand panel
      await userEvent.click(screen.getByLabelText(/Expand .* panel/));
      expect(screen.getAllByLabelText(/Collapse .* panel/).length).toBeGreaterThan(0);
    });
  });

  describe('Mobile Layout', () => {
    beforeEach(() => {
      const { useBreakpoint, useIsMobile } = require('../../utils/responsive');
      useBreakpoint.mockReturnValue('xs');
      useIsMobile.mockReturnValue(true);
    });

    it('renders mobile-optimized layout', () => {
      render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      // Check mobile menu button
      expect(screen.getByLabelText('Open menu')).toBeInTheDocument();
      
      // Sidebar should be hidden initially
      const sidebar = screen.getByRole('complementary');
      expect(sidebar).toHaveAttribute('aria-hidden', 'true');
    });

    it('handles mobile menu toggle', async () => {
      render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      const menuButton = screen.getByLabelText('Open menu');
      
      // Open menu
      await userEvent.click(menuButton);
      const sidebar = screen.getByRole('complementary');
      expect(sidebar).toHaveAttribute('aria-hidden', 'false');
      
      // Close button should be visible
      expect(screen.getByLabelText('Close')).toBeInTheDocument();
      
      // Close menu
      await userEvent.click(screen.getByLabelText('Close'));
      expect(sidebar).toHaveAttribute('aria-hidden', 'true');
    });

    it('renders panels in tabs for mobile', () => {
      render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      // Check for tab navigation
      expect(screen.getByRole('tablist')).toBeInTheDocument();
      expect(screen.getByLabelText('Architecture diagram viewer tab')).toBeInTheDocument();
      expect(screen.getByLabelText('Requirements panel tab')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      expect(screen.getByLabelText('Architect mode interface')).toBeInTheDocument();
      expect(screen.getByRole('banner')).toBeInTheDocument();
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('complementary')).toBeInTheDocument();
      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });

    it('announces panel state changes', async () => {
      const { announcer } = require('../../utils/accessibility');
      
      render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      const collapseButton = screen.getAllByLabelText(/Collapse .* panel/)[0];
      await userEvent.click(collapseButton);

      expect(announcer.announce).toHaveBeenCalledWith(expect.stringContaining('panel collapsed'));
    });

    it('supports keyboard navigation', async () => {
      render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      // Tab through interactive elements
      await userEvent.tab();
      expect(screen.getByText('Skip to main content')).toHaveFocus();
      
      await userEvent.tab();
      expect(screen.getByLabelText('Mode settings')).toHaveFocus();
    });

    it('handles keyboard shortcuts', async () => {
      const { useAccessibleShortcuts } = require('../../utils/accessibility');
      let shortcutHandler: any = {};
      
      useAccessibleShortcuts.mockImplementation((shortcuts) => {
        shortcutHandler = shortcuts;
        return { showHelp: false };
      });

      render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      // Test Escape key
      expect(shortcutHandler['Escape']).toBeDefined();
      
      // Test Ctrl+B for sidebar toggle
      expect(shortcutHandler['Ctrl+B']).toBeDefined();
    });
  });

  describe('Responsive Behavior', () => {
    it('adjusts panel sizes based on breakpoint', () => {
      const { useBreakpoint } = require('../../utils/responsive');
      
      // Test tablet breakpoint
      useBreakpoint.mockReturnValue('md');
      
      const { rerender } = render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      // Panels should have tablet-specific sizes
      const panels = screen.getAllByRole('region');
      expect(panels.length).toBeGreaterThan(0);
      
      // Test desktop breakpoint
      useBreakpoint.mockReturnValue('lg');
      rerender(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );
      
      // Panels should have desktop sizes
      expect(screen.getAllByRole('region').length).toBeGreaterThan(0);
    });

    it('hides low-priority panels on smaller screens', () => {
      const { useBreakpoint, useIsMobile } = require('../../utils/responsive');
      
      // Desktop - all panels visible
      useBreakpoint.mockReturnValue('lg');
      useIsMobile.mockReturnValue(false);
      
      const { rerender } = render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      const desktopPanels = screen.getAllByRole('region').length;
      
      // Tablet - some panels hidden
      useBreakpoint.mockReturnValue('md');
      rerender(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );
      
      const tabletPanels = screen.getAllByRole('region').length;
      expect(tabletPanels).toBeLessThanOrEqual(desktopPanels);
    });
  });

  describe('Mode Switching', () => {
    it('updates layout when mode changes', () => {
      const { rerender } = render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      expect(screen.getByText('Architect Mode')).toBeInTheDocument();
      expect(screen.getByText('diagram-viewer')).toBeInTheDocument();

      // Switch to debug mode
      rerender(
        <ModeLayoutResponsive
          mode={debugMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      expect(screen.getByText('Debug Mode')).toBeInTheDocument();
      expect(screen.getByText('log-viewer')).toBeInTheDocument();
      expect(screen.queryByText('diagram-viewer')).not.toBeInTheDocument();
    });

    it('preserves panel states across mode switches', async () => {
      const { rerender } = render(
        <ModeLayoutResponsive
          mode={architectMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      // Collapse a panel
      const collapseButton = screen.getAllByLabelText(/Collapse .* panel/)[0];
      await userEvent.click(collapseButton);

      // Switch modes
      rerender(
        <ModeLayoutResponsive
          mode={debugMode}
          context={mockContext}
        >
          <div>Content</div>
        </ModeLayoutResponsive>
      );

      // Panel state should be preserved
      expect(screen.getByLabelText(/Expand .* panel/)).toBeInTheDocument();
    });
  });
});
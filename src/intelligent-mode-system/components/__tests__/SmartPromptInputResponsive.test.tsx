import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SmartPromptInputResponsive } from '../SmartPromptInputResponsive';
import { modes } from '../../registry/defaultModes';
import { createMockContext } from '../../utils/mockData';

// Mock the responsive hooks
jest.mock('../../utils/responsive', () => ({
  ...jest.requireActual('../../utils/responsive'),
  useBreakpoint: jest.fn(() => 'lg'),
  useIsMobile: jest.fn(() => false),
}));

// Mock the accessibility utilities
jest.mock('../../utils/accessibility', () => ({
  ...jest.requireActual('../../utils/accessibility'),
  announcer: {
    announce: jest.fn(),
  },
  useFocusTrap: jest.fn(() => ({ current: null })),
  useAccessibleShortcuts: jest.fn(() => ({ showHelp: false })),
}));

// Mock the smart prompt hook
jest.mock('../../hooks/useSmartPrompt', () => ({
  useSmartPrompt: jest.fn(() => ({
    suggestions: [
      {
        id: 'test-1',
        text: 'Test suggestion 1',
        category: 'template',
        priority: 10,
        tags: ['test', 'template'],
        description: 'This is a test suggestion',
      },
      {
        id: 'test-2',
        text: 'Test suggestion 2',
        category: 'context',
        priority: 8,
        tags: ['test', 'context'],
        description: 'Another test suggestion',
      },
    ],
    recentPrompts: ['Recent prompt 1', 'Recent prompt 2'],
    isLoading: false,
    analytics: [],
    config: {
      enableSmartCompletions: true,
      enableContextSuggestions: true,
      enableTemplates: true,
      enableRecentPrompts: true,
      maxSuggestions: 10,
      maxRecentPrompts: 5,
      analytics: { enabled: true, trackUsage: true, trackEffectiveness: true },
      personalization: { enabled: true, adaptToUsagePatterns: true },
    },
    submitPrompt: jest.fn(),
    addToRecent: jest.fn(),
    clearRecent: jest.fn(),
    updateConfig: jest.fn(),
    trackUsage: jest.fn(),
    generateSuggestions: jest.fn(),
    filterSuggestions: jest.fn((category, query) => {
      const suggestions = [
        {
          id: 'test-1',
          text: 'Test suggestion 1',
          category: 'template',
          priority: 10,
          tags: ['test', 'template'],
          description: 'This is a test suggestion',
        },
        {
          id: 'test-2',
          text: 'Test suggestion 2',
          category: 'context',
          priority: 8,
          tags: ['test', 'context'],
          description: 'Another test suggestion',
        },
      ];
      
      if (category && category !== 'all') {
        return suggestions.filter(s => s.category === category);
      }
      if (query) {
        return suggestions.filter(s => 
          s.text.toLowerCase().includes(query.toLowerCase())
        );
      }
      return suggestions;
    }),
    rateSuggestion: jest.fn(),
  })),
}));

// Mock cmdk
jest.mock('cmdk', () => ({
  Command: ({ children }: any) => <div>{children}</div>,
  CommandInput: ({ placeholder, onChange }: any) => (
    <input 
      placeholder={placeholder} 
      onChange={(e) => onChange?.(e.target.value)}
      aria-label="Search"
    />
  ),
  CommandList: ({ children }: any) => <div>{children}</div>,
  CommandEmpty: ({ children }: any) => <div>{children}</div>,
  CommandGroup: ({ children }: any) => <div>{children}</div>,
  CommandItem: ({ children, onSelect }: any) => (
    <div onClick={onSelect}>{children}</div>
  ),
  CommandSeparator: () => <hr />,
}));

describe('SmartPromptInputResponsive', () => {
  const mockOnSubmit = jest.fn();
  const mockContext = createMockContext();
  const architectMode = modes.find(m => m.id === 'architect')!;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    it('renders input with placeholder', () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          placeholder="Custom placeholder"
        />
      );

      const input = screen.getByPlaceholderText('Custom placeholder');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('aria-label', 'Prompt input');
    });

    it('handles text input', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.type(input, 'Test prompt');
      
      expect(input).toHaveValue('Test prompt');
    });

    it('submits prompt on Enter key', async () => {
      const { useSmartPrompt } = require('../../hooks/useSmartPrompt');
      const mockSubmitPrompt = jest.fn();
      useSmartPrompt.mockReturnValue({
        ...useSmartPrompt(),
        submitPrompt: mockSubmitPrompt,
      });

      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.type(input, 'Test prompt{Enter}');

      expect(mockSubmitPrompt).toHaveBeenCalledWith('Test prompt', expect.any(Object));
    });

    it('submits prompt via button click', async () => {
      const { useSmartPrompt } = require('../../hooks/useSmartPrompt');
      const mockSubmitPrompt = jest.fn();
      useSmartPrompt.mockReturnValue({
        ...useSmartPrompt(),
        submitPrompt: mockSubmitPrompt,
      });

      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.type(input, 'Test prompt');
      
      const submitButton = screen.getByLabelText('Submit prompt');
      await userEvent.click(submitButton);

      expect(mockSubmitPrompt).toHaveBeenCalledWith('Test prompt', expect.any(Object));
    });

    it('disables submit button when input is empty', () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
        />
      );

      const submitButton = screen.getByLabelText('Submit prompt');
      expect(submitButton).toBeDisabled();
    });
  });

  describe('Suggestions', () => {
    it('shows suggestions when input is focused', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.click(input);

      expect(screen.getByText('Test suggestion 1')).toBeInTheDocument();
      expect(screen.getByText('Test suggestion 2')).toBeInTheDocument();
    });

    it('hides suggestions when disabled', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={false}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.click(input);

      expect(screen.queryByText('Test suggestion 1')).not.toBeInTheDocument();
    });

    it('filters suggestions by category', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.click(input);

      // Click on template category filter
      const templateFilter = screen.getByText('template');
      await userEvent.click(templateFilter);

      expect(screen.getByText('Test suggestion 1')).toBeInTheDocument();
      expect(screen.queryByText('Test suggestion 2')).not.toBeInTheDocument();
    });

    it('selects suggestion on click', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.click(input);

      const suggestion = screen.getByText('Test suggestion 1');
      await userEvent.click(suggestion);

      expect(input).toHaveValue('Test suggestion 1');
    });

    it('shows loading state', () => {
      const { useSmartPrompt } = require('../../hooks/useSmartPrompt');
      useSmartPrompt.mockReturnValue({
        ...useSmartPrompt(),
        isLoading: true,
      });

      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      const input = screen.getByRole('textbox');
      fireEvent.focus(input);

      expect(screen.getByText('Loading suggestions...')).toBeInTheDocument();
    });
  });

  describe('Recent Prompts', () => {
    it('shows recent prompts dropdown', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showRecent={true}
        />
      );

      const recentButton = screen.getByLabelText('Recent prompts');
      await userEvent.click(recentButton);

      expect(screen.getByText('Recent prompt 1')).toBeInTheDocument();
      expect(screen.getByText('Recent prompt 2')).toBeInTheDocument();
    });

    it('clears recent prompts', async () => {
      const { useSmartPrompt } = require('../../hooks/useSmartPrompt');
      const mockClearRecent = jest.fn();
      useSmartPrompt.mockReturnValue({
        ...useSmartPrompt(),
        clearRecent: mockClearRecent,
      });

      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showRecent={true}
        />
      );

      const recentButton = screen.getByLabelText('Recent prompts');
      await userEvent.click(recentButton);

      const clearButton = screen.getByLabelText('Clear recent prompts');
      await userEvent.click(clearButton);

      expect(mockClearRecent).toHaveBeenCalled();
    });

    it('selects recent prompt', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showRecent={true}
        />
      );

      const recentButton = screen.getByLabelText('Recent prompts');
      await userEvent.click(recentButton);

      const recentPrompt = screen.getByText('Recent prompt 1');
      await userEvent.click(recentPrompt);

      const input = screen.getByRole('textbox');
      expect(input).toHaveValue('Recent prompt 1');
    });

    it('hides recent button when no recent prompts', () => {
      const { useSmartPrompt } = require('../../hooks/useSmartPrompt');
      useSmartPrompt.mockReturnValue({
        ...useSmartPrompt(),
        recentPrompts: [],
      });

      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showRecent={true}
        />
      );

      expect(screen.queryByLabelText('Recent prompts')).not.toBeInTheDocument();
    });
  });

  describe('Voice Input', () => {
    it('shows voice input button when enabled', () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          enableVoiceInput={true}
        />
      );

      expect(screen.getByLabelText('Start voice recording')).toBeInTheDocument();
    });

    it('handles voice recording toggle', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          enableVoiceInput={true}
        />
      );

      const voiceButton = screen.getByLabelText('Start voice recording');
      await userEvent.click(voiceButton);

      expect(screen.getByLabelText('Stop voice recording')).toBeInTheDocument();
      expect(screen.getByLabelText('Stop voice recording')).toHaveAttribute('aria-pressed', 'true');
    });

    it('announces voice recording state', async () => {
      const { announcer } = require('../../utils/accessibility');
      
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          enableVoiceInput={true}
        />
      );

      const voiceButton = screen.getByLabelText('Start voice recording');
      await userEvent.click(voiceButton);

      expect(announcer.announce).toHaveBeenCalledWith('Voice recording started');
    });
  });

  describe('Mobile Behavior', () => {
    beforeEach(() => {
      const { useBreakpoint, useIsMobile } = require('../../utils/responsive');
      useBreakpoint.mockReturnValue('xs');
      useIsMobile.mockReturnValue(true);
    });

    it('shows suggestions in fixed bottom panel on mobile', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.click(input);

      const suggestionsPanel = screen.getByRole('listbox');
      expect(suggestionsPanel).toHaveClass('fixed', 'bottom-0');
    });

    it('shows filter button on mobile', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.click(input);

      const filterButton = screen.getByLabelText('Toggle filters');
      expect(filterButton).toBeInTheDocument();
    });

    it('limits visible suggestions on mobile', async () => {
      const { useSmartPrompt } = require('../../hooks/useSmartPrompt');
      useSmartPrompt.mockReturnValue({
        ...useSmartPrompt(),
        suggestions: Array.from({ length: 10 }, (_, i) => ({
          id: `test-${i}`,
          text: `Test suggestion ${i}`,
          category: 'template',
          priority: 10 - i,
          tags: ['test'],
          description: `Description ${i}`,
        })),
        filterSuggestions: jest.fn(() => 
          Array.from({ length: 10 }, (_, i) => ({
            id: `test-${i}`,
            text: `Test suggestion ${i}`,
            category: 'template',
            priority: 10 - i,
            tags: ['test'],
            description: `Description ${i}`,
          }))
        ),
      });

      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.click(input);

      // Should only show 5 suggestions on mobile
      const suggestions = screen.getAllByRole('option');
      expect(suggestions).toHaveLength(5);
    });

    it('supports touch gestures', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      const input = screen.getByRole('textbox');
      
      // Simulate swipe up
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientY: 100 } as Touch],
      });
      const touchEndEvent = new TouchEvent('touchend', {
        changedTouches: [{ clientY: 30 } as Touch],
      });

      input.dispatchEvent(touchStartEvent);
      input.dispatchEvent(touchEndEvent);

      // Suggestions should be open
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-label', 'Prompt input');
      expect(input).toHaveAttribute('aria-autocomplete', 'list');
      expect(input).toHaveAttribute('aria-expanded', 'false');
      expect(input).toHaveAttribute('aria-controls', 'suggestions-list');
    });

    it('announces suggestion selection', async () => {
      const { announcer } = require('../../utils/accessibility');
      
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.click(input);

      const suggestion = screen.getByText('Test suggestion 1');
      await userEvent.click(suggestion);

      expect(announcer.announce).toHaveBeenCalledWith('Selected suggestion: Test suggestion 1');
    });

    it('announces prompt submission', async () => {
      const { announcer } = require('../../utils/accessibility');
      const { useSmartPrompt } = require('../../hooks/useSmartPrompt');
      const mockSubmitPrompt = jest.fn();
      useSmartPrompt.mockReturnValue({
        ...useSmartPrompt(),
        submitPrompt: mockSubmitPrompt,
      });
      
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.type(input, 'Test prompt{Enter}');

      expect(announcer.announce).toHaveBeenCalledWith('Prompt submitted');
    });

    it('supports keyboard shortcuts', async () => {
      const { useAccessibleShortcuts } = require('../../utils/accessibility');
      let shortcutHandler: any = {};
      
      useAccessibleShortcuts.mockImplementation((shortcuts) => {
        shortcutHandler = shortcuts;
        return { showHelp: false };
      });

      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      // Test Escape key
      expect(shortcutHandler['Escape']).toBeDefined();
      
      // Test Ctrl+Enter
      expect(shortcutHandler['Ctrl+Enter']).toBeDefined();
      
      // Test Ctrl+/
      expect(shortcutHandler['Ctrl+/']).toBeDefined();
      
      // Test Ctrl+K
      expect(shortcutHandler['Ctrl+K']).toBeDefined();
    });

    it('provides screen reader announcements for suggestion count', async () => {
      render(
        <SmartPromptInputResponsive
          context={mockContext}
          currentMode={architectMode}
          onSubmit={mockOnSubmit}
          showSuggestions={true}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.click(input);

      const announcement = screen.getByText('2 suggestions available');
      expect(announcement).toHaveClass('sr-only');
    });
  });
});
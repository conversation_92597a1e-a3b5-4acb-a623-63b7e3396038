import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ModeSelectorResponsive } from '../ModeSelectorResponsive';
import { modes } from '../../registry/defaultModes';
import { createMockContext } from '../../utils/mockData';

// Mock the responsive hooks
jest.mock('../../utils/responsive', () => ({
  ...jest.requireActual('../../utils/responsive'),
  useBreakpoint: jest.fn(() => 'lg'),
  useIsMobile: jest.fn(() => false),
}));

// Mock the accessibility utilities
jest.mock('../../utils/accessibility', () => ({
  ...jest.requireActual('../../utils/accessibility'),
  announcer: {
    announce: jest.fn(),
  },
  useFocusTrap: jest.fn(() => ({ current: null })),
  useAccessibleShortcuts: jest.fn(() => ({ showHelp: false })),
}));

// Mock cmdk
jest.mock('cmdk', () => ({
  Command: ({ children }: any) => <div>{children}</div>,
  CommandInput: ({ placeholder, onChange }: any) => (
    <input 
      placeholder={placeholder} 
      onChange={(e) => onChange?.(e.target.value)}
      aria-label="Search modes"
    />
  ),
  CommandList: ({ children }: any) => <div>{children}</div>,
  CommandEmpty: ({ children }: any) => <div>{children}</div>,
  CommandGroup: ({ children }: any) => <div>{children}</div>,
  CommandItem: ({ children, onSelect }: any) => (
    <div onClick={onSelect}>{children}</div>
  ),
}));

describe('ModeSelectorResponsive', () => {
  const mockOnModeChange = jest.fn();
  const mockContext = createMockContext();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Default Variant', () => {
    it('renders all modes as cards', () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="default"
        />
      );

      modes.forEach(mode => {
        expect(screen.getByText(mode.name)).toBeInTheDocument();
        if (mode.id === modes[0].id) {
          expect(screen.getByText('Active')).toBeInTheDocument();
        }
      });
    });

    it('handles mode selection', async () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="default"
        />
      );

      const debugModeCard = screen.getByLabelText('Debug mode');
      await userEvent.click(debugModeCard);

      expect(mockOnModeChange).toHaveBeenCalledWith(
        expect.objectContaining({ id: 'debug' })
      );
    });

    it('shows descriptions when enabled', () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="default"
          showDescriptions={true}
        />
      );

      modes.forEach(mode => {
        expect(screen.getByText(mode.description)).toBeInTheDocument();
      });
    });

    it('supports keyboard navigation', async () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="default"
        />
      );

      const firstCard = screen.getByLabelText(`${modes[0].name} mode (active)`);
      const secondCard = screen.getByLabelText(`${modes[1].name} mode`);

      // Tab to first card
      await userEvent.tab();
      expect(firstCard).toHaveFocus();

      // Press Enter to select
      await userEvent.keyboard('{Enter}');
      expect(mockOnModeChange).toHaveBeenCalledWith(modes[0]);

      // Tab to next card
      await userEvent.tab();
      expect(secondCard).toHaveFocus();

      // Press Space to select
      await userEvent.keyboard(' ');
      expect(mockOnModeChange).toHaveBeenCalledWith(modes[1]);
    });
  });

  describe('Compact Variant', () => {
    it('renders as dropdown menu', () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="compact"
        />
      );

      const trigger = screen.getByLabelText(`Current mode: ${modes[0].name}. Click to change mode`);
      expect(trigger).toBeInTheDocument();
      expect(trigger).toHaveAttribute('aria-expanded', 'false');
    });

    it('opens dropdown on click', async () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="compact"
        />
      );

      const trigger = screen.getByRole('button');
      await userEvent.click(trigger);

      expect(trigger).toHaveAttribute('aria-expanded', 'true');
      
      // All modes should be visible in dropdown
      modes.forEach(mode => {
        expect(screen.getByText(mode.name)).toBeInTheDocument();
      });
    });

    it('shows search input when enabled', async () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="compact"
          allowSearch={true}
        />
      );

      const trigger = screen.getByRole('button');
      await userEvent.click(trigger);

      const searchInput = screen.getByPlaceholderText('Search modes...');
      expect(searchInput).toBeInTheDocument();
    });

    it('filters modes based on search', async () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="compact"
          allowSearch={true}
        />
      );

      const trigger = screen.getByRole('button');
      await userEvent.click(trigger);

      const searchInput = screen.getByPlaceholderText('Search modes...');
      await userEvent.type(searchInput, 'debug');

      // Only debug mode should be visible
      expect(screen.getByText('Debug')).toBeInTheDocument();
      expect(screen.queryByText('Architect')).not.toBeInTheDocument();
    });

    it('groups modes by category when enabled', async () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="compact"
          groupByCategory={true}
        />
      );

      const trigger = screen.getByRole('button');
      await userEvent.click(trigger);

      // Should have category labels
      expect(screen.getByText('analysis')).toBeInTheDocument();
    });
  });

  describe('Expanded Variant', () => {
    it('renders modes in grid layout', () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="expanded"
        />
      );

      const container = screen.getByRole('group');
      expect(container).toHaveClass('grid');
    });

    it('shows tags for each mode', () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="expanded"
        />
      );

      // Check that tags are displayed
      modes.forEach(mode => {
        mode.tags.forEach(tag => {
          expect(screen.getByLabelText(`Tag: ${tag}`)).toBeInTheDocument();
        });
      });
    });
  });

  describe('Mobile Behavior', () => {
    beforeEach(() => {
      const { useBreakpoint, useIsMobile } = require('../../utils/responsive');
      useBreakpoint.mockReturnValue('xs');
      useIsMobile.mockReturnValue(true);
    });

    it('forces compact variant on mobile', () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="expanded" // Should be overridden to compact
        />
      );

      // Should render as dropdown, not grid
      const trigger = screen.getByRole('button');
      expect(trigger).toHaveAttribute('aria-haspopup', 'menu');
    });

    it('hides mode names on mobile to save space', () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="compact"
        />
      );

      const trigger = screen.getByRole('button');
      const modeName = trigger.querySelector('.hideMobile');
      expect(modeName).toBeInTheDocument();
    });

    it('supports swipe gestures', async () => {
      const { useFocusTrap } = require('../../utils/accessibility');
      const mockRef = { current: document.createElement('div') };
      useFocusTrap.mockReturnValue(mockRef);

      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="compact"
        />
      );

      // Simulate swipe left
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch],
      });
      const touchEndEvent = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 20, clientY: 100 } as Touch],
      });

      mockRef.current.dispatchEvent(touchStartEvent);
      mockRef.current.dispatchEvent(touchEndEvent);

      // Should switch to next mode
      expect(mockOnModeChange).toHaveBeenCalledWith(modes[1]);
    });
  });

  describe('Accessibility', () => {
    it('announces mode changes', async () => {
      const { announcer } = require('../../utils/accessibility');
      
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="default"
        />
      );

      const debugModeCard = screen.getByLabelText('Debug mode');
      await userEvent.click(debugModeCard);

      expect(announcer.announce).toHaveBeenCalledWith('Switched to Debug mode');
    });

    it('has proper ARIA attributes', () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="compact"
        />
      );

      const trigger = screen.getByRole('button');
      expect(trigger).toHaveAttribute('aria-label');
      expect(trigger).toHaveAttribute('aria-expanded');
      expect(trigger).toHaveAttribute('aria-haspopup', 'menu');
    });

    it('supports keyboard shortcuts', async () => {
      const { useAccessibleShortcuts } = require('../../utils/accessibility');
      let shortcutHandler: any = {};
      
      useAccessibleShortcuts.mockImplementation((shortcuts) => {
        shortcutHandler = shortcuts;
        return { showHelp: false };
      });

      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="compact"
        />
      );

      // Open dropdown
      const trigger = screen.getByRole('button');
      await userEvent.click(trigger);

      // Test Escape key
      expect(shortcutHandler['Escape']).toBeDefined();
      shortcutHandler['Escape']();
      
      // Test Enter key
      expect(shortcutHandler['Enter']).toBeDefined();
      
      // Test arrow navigation
      expect(shortcutHandler['ArrowDown']).toBeDefined();
      expect(shortcutHandler['ArrowUp']).toBeDefined();
    });

    it('indicates current mode visually and semantically', () => {
      render(
        <ModeSelectorResponsive
          modes={modes}
          currentMode={modes[0]}
          onModeChange={mockOnModeChange}
          variant="default"
        />
      );

      const activeCard = screen.getByLabelText(`${modes[0].name} mode (active)`);
      expect(activeCard).toHaveAttribute('aria-pressed', 'true');
      
      const inactiveCard = screen.getByLabelText(`${modes[1].name} mode`);
      expect(inactiveCard).toHaveAttribute('aria-pressed', 'false');
    });
  });
});
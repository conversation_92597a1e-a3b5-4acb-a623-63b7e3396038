import React, { forwardRef } from 'react';
import { Box, BoxProps } from './Box';
import { cn } from '@/lib/utils';

export interface FlexProps extends BoxProps {
  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse';
  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  wrap?: 'wrap' | 'nowrap' | 'wrap-reverse';
  gap?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  inline?: boolean;
}

/**
 * Flex - A flexible box layout component
 * 
 * Built on top of Box, provides flexbox layout utilities
 * with design token integration.
 */
export const Flex = forwardRef<HTMLDivElement, FlexProps>(
  (
    {
      direction = 'row',
      align = 'stretch',
      justify = 'start',
      wrap = 'nowrap',
      gap = 'none',
      inline = false,
      className,
      ...props
    },
    ref
  ) => {
    const directionClasses = {
      'row': 'flex-row',
      'col': 'flex-col',
      'row-reverse': 'flex-row-reverse',
      'col-reverse': 'flex-col-reverse',
    };

    const alignClasses = {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch',
      baseline: 'items-baseline',
    };

    const justifyClasses = {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
      around: 'justify-around',
      evenly: 'justify-evenly',
    };

    const wrapClasses = {
      wrap: 'flex-wrap',
      nowrap: 'flex-nowrap',
      'wrap-reverse': 'flex-wrap-reverse',
    };

    const gapClasses = {
      none: '',
      xs: 'gap-[var(--spacing-1)]',
      sm: 'gap-[var(--spacing-2)]',
      md: 'gap-[var(--spacing-4)]',
      lg: 'gap-[var(--spacing-6)]',
      xl: 'gap-[var(--spacing-8)]',
    };

    const displayClass = inline ? 'inline-flex' : 'flex';

    return (
      <Box
        ref={ref}
        className={cn(
          displayClass,
          directionClasses[direction],
          alignClasses[align],
          justifyClasses[justify],
          wrapClasses[wrap],
          gapClasses[gap],
          className
        )}
        {...props}
      />
    );
  }
);

Flex.displayName = 'Flex';
import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';

export interface TextProps extends React.HTMLAttributes<HTMLElement> {
  as?: 'p' | 'span' | 'div' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'label' | 'small' | 'code' | 'pre';
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  color?: 'primary' | 'secondary' | 'muted' | 'accent' | 'success' | 'warning' | 'error' | 'info';
  align?: 'left' | 'center' | 'right' | 'justify';
  truncate?: boolean;
  mono?: boolean;
}

/**
 * Text - A typography component with design token integration
 * 
 * Provides consistent text styling across all modes with
 * automatic theme adaptation.
 */
export const Text = forwardRef<HTMLElement, TextProps>(
  (
    {
      as: Component = 'p',
      size = 'base',
      weight = 'normal',
      color = 'primary',
      align = 'left',
      truncate = false,
      mono = false,
      className,
      children,
      ...props
    },
    ref
  ) => {
    const sizeClasses = {
      xs: 'text-[var(--text-xs)]',
      sm: 'text-[var(--text-sm)]',
      base: 'text-[var(--text-base)]',
      lg: 'text-[var(--text-lg)]',
      xl: 'text-[var(--text-xl)]',
      '2xl': 'text-[var(--text-2xl)]',
      '3xl': 'text-[var(--text-3xl)]',
      '4xl': 'text-[var(--text-4xl)]',
    };

    const weightClasses = {
      normal: 'font-[var(--font-normal)]',
      medium: 'font-[var(--font-medium)]',
      semibold: 'font-[var(--font-semibold)]',
      bold: 'font-[var(--font-bold)]',
    };

    const colorClasses = {
      primary: 'text-[var(--mode-text)]',
      secondary: 'text-[var(--mode-text-secondary)]',
      muted: 'text-[var(--mode-text-muted)]',
      accent: 'text-[var(--mode-accent)]',
      success: 'text-[var(--mode-success)]',
      warning: 'text-[var(--mode-warning)]',
      error: 'text-[var(--mode-error)]',
      info: 'text-[var(--mode-info)]',
    };

    const alignClasses = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
      justify: 'text-justify',
    };

    const fontClass = mono ? 'font-[var(--font-mono)]' : 'font-[var(--font-sans)]';
    const truncateClass = truncate ? 'truncate' : '';

    // Apply default heading styles
    const headingDefaults: Record<string, string> = {
      h1: 'text-[var(--text-4xl)] font-[var(--font-bold)]',
      h2: 'text-[var(--text-3xl)] font-[var(--font-bold)]',
      h3: 'text-[var(--text-2xl)] font-[var(--font-semibold)]',
      h4: 'text-[var(--text-xl)] font-[var(--font-semibold)]',
      h5: 'text-[var(--text-lg)] font-[var(--font-medium)]',
      h6: 'text-[var(--text-base)] font-[var(--font-medium)]',
    };

    const isHeading = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(Component);
    const defaultClass = isHeading ? headingDefaults[Component] : '';

    return (
      <Component
        ref={ref as any}
        className={cn(
          defaultClass,
          !isHeading && sizeClasses[size],
          !isHeading && weightClasses[weight],
          colorClasses[color],
          alignClasses[align],
          fontClass,
          truncateClass,
          className
        )}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

Text.displayName = 'Text';
import React, { forwardRef } from 'react';
import { Box, BoxProps } from './Box';
import { cn } from '@/lib/utils';

export interface InputProps extends Omit<BoxProps, 'as'> {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'ghost';
  error?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  readOnly?: boolean;
}

/**
 * Input - Primitive input component
 * 
 * Uses design tokens for consistent theming across all modes.
 * Supports icons, error states, and multiple variants.
 */
export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      size = 'md',
      variant = 'default',
      error = false,
      leftIcon,
      rightIcon,
      className,
      disabled = false,
      ...props
    },
    ref
  ) => {
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10 text-sm',
      lg: 'h-12 text-base',
    };

    const paddingClasses = {
      sm: leftIcon ? 'pl-8 pr-3' : rightIcon ? 'pl-3 pr-8' : 'px-3',
      md: leftIcon ? 'pl-10 pr-4' : rightIcon ? 'pl-4 pr-10' : 'px-4',
      lg: leftIcon ? 'pl-12 pr-4' : rightIcon ? 'pl-4 pr-12' : 'px-4',
    };

    const variantClasses = {
      default: 'bg-transparent border border-[var(--mode-border)]',
      filled: 'bg-[var(--mode-surface)] border border-transparent',
      ghost: 'bg-transparent border border-transparent',
    };

    const iconSizeClasses = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
    };

    const iconPositionClasses = {
      sm: 'left-2',
      md: 'left-3',
      lg: 'left-3',
    };

    return (
      <Box className="relative">
        {leftIcon && (
          <Box
            className={cn(
              'absolute top-1/2 -translate-y-1/2 text-[var(--mode-text-muted)] pointer-events-none',
              iconPositionClasses[size],
              iconSizeClasses[size]
            )}
          >
            {leftIcon}
          </Box>
        )}
        <Box
          as="input"
          ref={ref}
          className={cn(
            // Base styles
            'w-full rounded-md transition-all duration-[var(--duration-fast)]',
            'text-[var(--mode-text)] placeholder:text-[var(--mode-text-muted)]',
            'focus:outline-none focus:ring-2 focus:ring-[var(--mode-primary)] focus:ring-offset-2 focus:ring-offset-[var(--mode-background)]',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            // Size
            sizeClasses[size],
            paddingClasses[size],
            // Variant
            variantClasses[variant],
            // Error state
            error && 'border-[var(--mode-error)] focus:ring-[var(--mode-error)]',
            // Hover states
            !disabled && !error && 'hover:border-[var(--mode-border-hover)]',
            className
          )}
          disabled={disabled}
          {...props}
        />
        {rightIcon && (
          <Box
            className={cn(
              'absolute top-1/2 right-3 -translate-y-1/2 text-[var(--mode-text-muted)] pointer-events-none',
              iconSizeClasses[size]
            )}
          >
            {rightIcon}
          </Box>
        )}
      </Box>
    );
  }
);

Input.displayName = 'Input';

// Textarea variant
export interface TextareaProps extends Omit<InputProps, 'leftIcon' | 'rightIcon' | 'type'> {
  rows?: number;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      size = 'md',
      variant = 'default',
      error = false,
      rows = 3,
      resize = 'vertical',
      className,
      disabled = false,
      ...props
    },
    ref
  ) => {
    const sizeClasses = {
      sm: 'text-sm py-2',
      md: 'text-sm py-2.5',
      lg: 'text-base py-3',
    };

    const paddingClasses = {
      sm: 'px-3',
      md: 'px-4',
      lg: 'px-4',
    };

    const variantClasses = {
      default: 'bg-transparent border border-[var(--mode-border)]',
      filled: 'bg-[var(--mode-surface)] border border-transparent',
      ghost: 'bg-transparent border border-transparent',
    };

    const resizeClasses = {
      none: 'resize-none',
      vertical: 'resize-y',
      horizontal: 'resize-x',
      both: 'resize',
    };

    return (
      <Box
        as="textarea"
        ref={ref}
        rows={rows}
        className={cn(
          // Base styles
          'w-full rounded-md transition-all duration-[var(--duration-fast)]',
          'text-[var(--mode-text)] placeholder:text-[var(--mode-text-muted)]',
          'focus:outline-none focus:ring-2 focus:ring-[var(--mode-primary)] focus:ring-offset-2 focus:ring-offset-[var(--mode-background)]',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          // Size
          sizeClasses[size],
          paddingClasses[size],
          // Variant
          variantClasses[variant],
          // Resize
          resizeClasses[resize],
          // Error state
          error && 'border-[var(--mode-error)] focus:ring-[var(--mode-error)]',
          // Hover states
          !disabled && !error && 'hover:border-[var(--mode-border-hover)]',
          className
        )}
        disabled={disabled}
        {...props}
      />
    );
  }
);

Textarea.displayName = 'Textarea';
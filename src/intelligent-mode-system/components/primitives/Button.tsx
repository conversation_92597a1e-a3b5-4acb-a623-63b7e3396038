import React, { forwardRef } from 'react';
import { Box, BoxProps } from './Box';
import { cn } from '@/lib/utils';

export interface ButtonProps extends Omit<BoxProps, 'as' | 'variant'> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline' | 'danger';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  loading?: boolean;
  disabled?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

/**
 * Button - Primitive button component
 * 
 * Uses design tokens for consistent theming across all modes.
 * Built on top of Box for maximum flexibility.
 */
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      fullWidth = false,
      loading = false,
      disabled = false,
      leftIcon,
      rightIcon,
      children,
      className,
      onClick,
      ...props
    },
    ref
  ) => {
    const sizeClasses = {
      xs: 'h-6 px-2 text-xs gap-1',
      sm: 'h-8 px-3 text-sm gap-1.5',
      md: 'h-10 px-4 text-sm gap-2',
      lg: 'h-12 px-6 text-base gap-2',
    };

    const variantClasses = {
      primary: 'bg-[var(--mode-primary)] text-[var(--mode-primary-foreground)] hover:bg-[var(--mode-primary-hover)] active:bg-[var(--mode-primary-active)]',
      secondary: 'bg-[var(--mode-secondary)] text-[var(--mode-secondary-foreground)] hover:bg-[var(--mode-secondary-hover)] active:bg-[var(--mode-secondary-active)]',
      ghost: 'bg-transparent text-[var(--mode-text)] hover:bg-[var(--mode-surface-hover)] active:bg-[var(--mode-surface-active)]',
      outline: 'bg-transparent border border-[var(--mode-border)] text-[var(--mode-text)] hover:bg-[var(--mode-surface-hover)] active:bg-[var(--mode-surface-active)]',
      danger: 'bg-[var(--mode-error)] text-[var(--mode-error-foreground)] hover:bg-[var(--mode-error-hover)] active:bg-[var(--mode-error-active)]',
    };

    const isDisabled = disabled || loading;

    return (
      <Box
        as="button"
        ref={ref}
        className={cn(
          // Base styles
          'inline-flex items-center justify-center font-medium rounded-md transition-all duration-[var(--duration-fast)]',
          'focus:outline-none focus:ring-2 focus:ring-[var(--mode-primary)] focus:ring-offset-2 focus:ring-offset-[var(--mode-background)]',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          // Size
          sizeClasses[size],
          // Variant
          variantClasses[variant],
          // Full width
          fullWidth && 'w-full',
          // Loading state
          loading && 'cursor-wait',
          className
        )}
        disabled={isDisabled}
        onClick={isDisabled ? undefined : onClick}
        {...props}
      >
        {loading ? (
          <>
            <LoadingSpinner size={size} />
            <span className="opacity-0">{children}</span>
          </>
        ) : (
          <>
            {leftIcon && <span className="flex-shrink-0">{leftIcon}</span>}
            {children}
            {rightIcon && <span className="flex-shrink-0">{rightIcon}</span>}
          </>
        )}
      </Box>
    );
  }
);

Button.displayName = 'Button';

// Loading spinner component
const LoadingSpinner: React.FC<{ size: ButtonProps['size'] }> = ({ size }) => {
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-3.5 h-3.5',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  return (
    <Box className={cn('absolute', sizeClasses[size || 'md'])}>
      <svg
        className="animate-spin"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
        />
      </svg>
    </Box>
  );
};
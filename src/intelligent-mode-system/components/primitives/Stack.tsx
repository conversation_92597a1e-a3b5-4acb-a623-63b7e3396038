import React, { forwardRef } from 'react';
import { Flex, FlexProps } from './Flex';

export interface StackProps extends Omit<FlexProps, 'direction'> {
  spacing?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  divider?: React.ReactNode;
}

/**
 * Stack - A vertical stack layout component
 * 
 * Simplifies vertical layouts with consistent spacing
 * and optional dividers between items.
 */
export const Stack = forwardRef<HTMLDivElement, StackProps>(
  ({ spacing = 'md', divider, children, gap, ...props }, ref) => {
    // Use gap if provided, otherwise use spacing
    const actualGap = gap || spacing;

    if (!divider) {
      return (
        <Flex ref={ref} direction="col" gap={actualGap} {...props}>
          {children}
        </Flex>
      );
    }

    // If divider is provided, we need to insert it between children
    const childArray = React.Children.toArray(children);
    const childrenWithDividers = childArray.reduce<React.ReactNode[]>(
      (acc, child, index) => {
        acc.push(child);
        if (index < childArray.length - 1) {
          acc.push(
            <div key={`divider-${index}`} className="w-full">
              {divider}
            </div>
          );
        }
        return acc;
      },
      []
    );

    return (
      <Flex ref={ref} direction="col" gap={actualGap} {...props}>
        {childrenWithDividers}
      </Flex>
    );
  }
);

Stack.displayName = 'Stack';

/**
 * HStack - A horizontal stack layout component
 * 
 * Simplifies horizontal layouts with consistent spacing
 * and optional dividers between items.
 */
export const HStack = forwardRef<HTMLDivElement, StackProps>(
  ({ spacing = 'md', divider, children, gap, ...props }, ref) => {
    // Use gap if provided, otherwise use spacing
    const actualGap = gap || spacing;

    if (!divider) {
      return (
        <Flex ref={ref} direction="row" gap={actualGap} {...props}>
          {children}
        </Flex>
      );
    }

    // If divider is provided, we need to insert it between children
    const childArray = React.Children.toArray(children);
    const childrenWithDividers = childArray.reduce<React.ReactNode[]>(
      (acc, child, index) => {
        acc.push(child);
        if (index < childArray.length - 1) {
          acc.push(
            <div key={`divider-${index}`} className="h-full">
              {divider}
            </div>
          );
        }
        return acc;
      },
      []
    );

    return (
      <Flex ref={ref} direction="row" gap={actualGap} {...props}>
        {childrenWithDividers}
      </Flex>
    );
  }
);

HStack.displayName = 'HStack';
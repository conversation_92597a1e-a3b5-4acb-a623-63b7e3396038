import React, { forwardRef } from 'react';
import { Box, BoxProps } from './Box';
import { Stack } from './Stack';
import { Text } from './Text';
import { cn } from '@/lib/utils';

export interface CardProps extends BoxProps {
  hoverable?: boolean;
  pressable?: boolean;
}

/**
 * Card - A container component with elevation and interaction states
 * 
 * Compound component pattern for flexible card layouts.
 */
export const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ hoverable = false, pressable = false, className, ...props }, ref) => {
    const interactionClasses = cn(
      hoverable && 'hover:shadow-[var(--shadow-lg)] transition-shadow duration-[var(--duration-normal)]',
      pressable && 'cursor-pointer active:scale-[0.98] transition-transform duration-[var(--duration-fast)]'
    );

    return (
      <Box
        ref={ref}
        variant="card"
        padding="lg"
        rounded="lg"
        border
        shadow="sm"
        className={cn(interactionClasses, className)}
        {...props}
      />
    );
  }
);

Card.displayName = 'Card';

// Card sub-components using compound pattern
interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}

export const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, ...props }, ref) => {
    return (
      <Box
        ref={ref}
        className={cn('border-b border-[var(--mode-border)] pb-[var(--spacing-4)]', className)}
        {...props}
      />
    );
  }
);

CardHeader.displayName = 'CardHeader';

interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {}

export const CardTitle = forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <Text
        ref={ref as any}
        as="h3"
        className={cn('mb-[var(--spacing-1)]', className)}
        {...props}
      >
        {children}
      </Text>
    );
  }
);

CardTitle.displayName = 'CardTitle';

interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

export const CardDescription = forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <Text
        ref={ref as any}
        size="sm"
        color="muted"
        className={className}
        {...props}
      >
        {children}
      </Text>
    );
  }
);

CardDescription.displayName = 'CardDescription';

interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {}

export const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, ...props }, ref) => {
    return (
      <Box
        ref={ref}
        className={cn('pt-[var(--spacing-4)]', className)}
        {...props}
      />
    );
  }
);

CardContent.displayName = 'CardContent';

interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}

export const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, ...props }, ref) => {
    return (
      <Box
        ref={ref}
        className={cn(
          'border-t border-[var(--mode-border)] pt-[var(--spacing-4)] mt-[var(--spacing-4)]',
          className
        )}
        {...props}
      />
    );
  }
);

CardFooter.displayName = 'CardFooter';

// Export compound component
export const CardCompound = Object.assign(Card, {
  Header: CardHeader,
  Title: CardTitle,
  Description: CardDescription,
  Content: CardContent,
  Footer: CardFooter,
});
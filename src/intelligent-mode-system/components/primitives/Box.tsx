import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';

export interface BoxProps extends React.HTMLAttributes<HTMLDivElement> {
  as?: React.ElementType;
  variant?: 'surface' | 'card' | 'panel' | 'transparent';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  margin?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  border?: boolean;
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

/**
 * Box - The fundamental building block component
 * 
 * A polymorphic component that can render as any HTML element
 * and provides consistent spacing, styling, and theming.
 */
export const Box = forwardRef<HTMLDivElement, BoxProps>(
  (
    {
      as: Component = 'div',
      variant = 'transparent',
      padding = 'none',
      margin = 'none',
      rounded = 'none',
      border = false,
      shadow = 'none',
      className,
      children,
      ...props
    },
    ref
  ) => {
    const variantClasses = {
      surface: 'bg-[var(--mode-surface)] text-[var(--mode-text)]',
      card: 'bg-[var(--mode-surface)] text-[var(--mode-text)] border-[var(--mode-border)]',
      panel: 'bg-[var(--mode-background)] text-[var(--mode-text)]',
      transparent: '',
    };

    const paddingClasses = {
      none: '',
      sm: 'p-[var(--spacing-2)]',
      md: 'p-[var(--spacing-4)]',
      lg: 'p-[var(--spacing-6)]',
      xl: 'p-[var(--spacing-8)]',
    };

    const marginClasses = {
      none: '',
      sm: 'm-[var(--spacing-2)]',
      md: 'm-[var(--spacing-4)]',
      lg: 'm-[var(--spacing-6)]',
      xl: 'm-[var(--spacing-8)]',
    };

    const roundedClasses = {
      none: '',
      sm: 'rounded-[var(--radius-sm)]',
      md: 'rounded-[var(--radius-md)]',
      lg: 'rounded-[var(--radius-lg)]',
      xl: 'rounded-[var(--radius-xl)]',
      full: 'rounded-[var(--radius-full)]',
    };

    const shadowClasses = {
      none: '',
      sm: 'shadow-[var(--shadow-sm)]',
      md: 'shadow-[var(--shadow-md)]',
      lg: 'shadow-[var(--shadow-lg)]',
      xl: 'shadow-[var(--shadow-xl)]',
    };

    const borderClass = border ? 'border border-[var(--mode-border)]' : '';

    return (
      <Component
        ref={ref}
        className={cn(
          variantClasses[variant],
          paddingClasses[padding],
          marginClasses[margin],
          roundedClasses[rounded],
          shadowClasses[shadow],
          borderClass,
          className
        )}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

Box.displayName = 'Box';
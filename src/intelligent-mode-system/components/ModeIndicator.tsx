import React from 'react';
import { useIntelligentMode } from '../hooks/useIntelligentMode';
import { ModeId } from '../types';

export const ModeIndicator: React.FC = () => {
  const {
    currentMode,
    recommendedMode,
    modeProbabilities,
    isTransitioning,
    switchMode,
    availableModes,
  } = useIntelligentMode();

  const handleModeClick = async (modeId: ModeId) => {
    if (!isTransitioning && currentMode?.id !== modeId) {
      await switchMode(modeId);
    }
  };

  const getProbabilityColor = (probability: number): string => {
    if (probability > 0.7) return 'text-green-500';
    if (probability > 0.4) return 'text-yellow-500';
    return 'text-gray-400';
  };

  const formatProbability = (probability: number): string => {
    return `${Math.round(probability * 100)}%`;
  };

  return (
    <div className="intelligent-mode-indicator p-4 bg-gray-900 rounded-lg">
      {/* Current Mode Display */}
      <div className="mb-4">
        <h3 className="text-sm font-semibold text-gray-400 mb-2">Current Mode</h3>
        {currentMode && (
          <div className="flex items-center space-x-2">
            <span className="text-2xl">{currentMode.icon}</span>
            <div>
              <div className="font-medium text-white">{currentMode.name}</div>
              <div className="text-xs text-gray-400">{currentMode.description}</div>
            </div>
            {isTransitioning && (
              <div className="ml-auto">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Recommended Mode */}
      {recommendedMode && recommendedMode !== currentMode?.id && (
        <div className="mb-4 p-2 bg-blue-900/20 rounded border border-blue-500/30">
          <div className="text-xs text-blue-400 mb-1">Recommended</div>
          <button
            onClick={() => handleModeClick(recommendedMode)}
            className="flex items-center space-x-2 hover:bg-blue-900/30 rounded p-1 transition-colors"
            disabled={isTransitioning}
          >
            <span>{availableModes.find(m => m.id === recommendedMode)?.icon}</span>
            <span className="text-sm text-blue-300">
              Switch to {availableModes.find(m => m.id === recommendedMode)?.name}
            </span>
          </button>
        </div>
      )}

      {/* Mode Selector */}
      <div className="mb-4">
        <h4 className="text-xs font-semibold text-gray-400 mb-2">All Modes</h4>
        <div className="grid grid-cols-3 gap-2">
          {availableModes.map((mode) => {
            const probability = modeProbabilities[mode.id] || 0;
            const isActive = currentMode?.id === mode.id;
            const isRecommended = recommendedMode === mode.id;

            return (
              <button
                key={mode.id}
                onClick={() => handleModeClick(mode.id)}
                disabled={isTransitioning || isActive}
                className={`
                  relative p-2 rounded-lg transition-all
                  ${isActive 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-800 hover:bg-gray-700 text-gray-300'
                  }
                  ${isTransitioning ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                `}
                title={`${mode.name} - ${mode.description}`}
              >
                <div className="flex flex-col items-center">
                  <span className="text-xl mb-1">{mode.icon}</span>
                  <span className="text-xs font-medium">{mode.name}</span>
                  <span className={`text-xs ${getProbabilityColor(probability)}`}>
                    {formatProbability(probability)}
                  </span>
                </div>
                {isRecommended && !isActive && (
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Keyboard Shortcuts */}
      <div className="text-xs text-gray-500">
        <div className="font-semibold mb-1">Shortcuts</div>
        {currentMode?.shortcuts.map((shortcut, index) => (
          <div key={index} className="flex justify-between">
            <span>{shortcut.description}</span>
            <kbd className="px-1 py-0.5 bg-gray-800 rounded text-gray-400">
              {shortcut.modifiers?.join('+')}{shortcut.modifiers?.length ? '+' : ''}{shortcut.key}
            </kbd>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ModeIndicator;
import React, { useState, useEffect, use<PERSON>emo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs';
import { 
  Network, 
  Download, 
  RefreshCw, 
  ZoomIn, 
  ZoomOut, 
  Maximize2,
  Settings,
  FileText,
  GitBranch,
  Database,
  Server
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface DiagramData {
  definition: string;
  nodes: DiagramNode[];
  edges: DiagramEdge[];
  metadata: {
    lastUpdated: number;
    complexity: 'low' | 'medium' | 'high';
    nodeCount: number;
    edgeCount: number;
  };
}

interface DiagramNode {
  id: string;
  label: string;
  type: 'component' | 'service' | 'database' | 'external';
  position: { x: number; y: number };
  metadata?: Record<string, any>;
}

interface DiagramEdge {
  id: string;
  source: string;
  target: string;
  type: 'dependency' | 'data-flow' | 'api-call' | 'inheritance';
  label?: string;
}

interface DiagramWidgetProps {
  context: ContextSnapshot;
  config?: {
    defaultType?: DiagramType;
    showControls?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number;
  };
}

type DiagramType = 'system' | 'component' | 'sequence' | 'dependency' | 'data-flow';

export const DiagramWidget: React.FC<DiagramWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [diagramType, setDiagramType] = useState<DiagramType>(
    config.defaultType || 'system'
  );
  const [diagramData, setDiagramData] = useState<DiagramData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [zoom, setZoom] = useState(100);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Generate diagram data based on context and type
  const generatedDiagram = useMemo(() => 
    generateDiagramData(diagramType, context), 
    [diagramType, context]
  );

  useEffect(() => {
    setIsLoading(true);
    // Simulate async diagram generation
    const timer = setTimeout(() => {
      setDiagramData(generatedDiagram);
      setIsLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, [generatedDiagram]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!config.autoRefresh) return;

    const interval = setInterval(() => {
      const newDiagram = generateDiagramData(diagramType, context);
      setDiagramData(newDiagram);
    }, config.refreshInterval || 30000);

    return () => clearInterval(interval);
  }, [diagramType, context, config.autoRefresh, config.refreshInterval]);

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => {
      setDiagramData(generateDiagramData(diagramType, context));
      setIsLoading(false);
    }, 500);
  };

  const handleExport = () => {
    if (!diagramData) return;
    
    // Create download link for SVG export
    const svgContent = generateSVGFromDiagram(diagramData);
    const blob = new Blob([svgContent], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${diagramType}-diagram-${Date.now()}.svg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'text-green-500';
      case 'medium': return 'text-yellow-500';
      case 'high': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <Card className={`diagram-widget ${isFullscreen ? 'fixed inset-0 z-50' : 'h-full'} bg-gray-900 border-gray-700`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Network className="h-5 w-5" />
            System Architecture
            {diagramData && (
              <Badge 
                variant="outline" 
                className={`ml-2 ${getComplexityColor(diagramData.metadata.complexity)}`}
              >
                {diagramData.metadata.complexity} complexity
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {/* Diagram Type Selector */}
            <Select value={diagramType} onValueChange={(value: DiagramType) => setDiagramType(value)}>
              <SelectTrigger className="w-40 bg-gray-800 border-gray-600">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                <SelectItem value="system">
                  <div className="flex items-center gap-2">
                    <Server className="h-4 w-4" />
                    System View
                  </div>
                </SelectItem>
                <SelectItem value="component">
                  <div className="flex items-center gap-2">
                    <GitBranch className="h-4 w-4" />
                    Components
                  </div>
                </SelectItem>
                <SelectItem value="sequence">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Sequence
                  </div>
                </SelectItem>
                <SelectItem value="dependency">
                  <div className="flex items-center gap-2">
                    <Network className="h-4 w-4" />
                    Dependencies
                  </div>
                </SelectItem>
                <SelectItem value="data-flow">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    Data Flow
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Controls */}
            {config.showControls !== false && (
              <div className="flex items-center gap-1">
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={handleRefresh}
                  disabled={isLoading}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
                </Button>
                
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => setZoom(Math.max(50, zoom - 25))}
                  disabled={zoom <= 50}
                  className="h-8 w-8 p-0"
                >
                  <ZoomOut className="h-3 w-3" />
                </Button>
                
                <Badge variant="outline" className="px-2 h-8 text-xs">
                  {zoom}%
                </Badge>
                
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => setZoom(Math.min(200, zoom + 25))}
                  disabled={zoom >= 200}
                  className="h-8 w-8 p-0"
                >
                  <ZoomIn className="h-3 w-3" />
                </Button>
                
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={handleExport}
                  disabled={!diagramData}
                  className="h-8 w-8 p-0"
                >
                  <Download className="h-3 w-3" />
                </Button>
                
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  className="h-8 w-8 p-0"
                >
                  <Maximize2 className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0 flex-1">
        <Tabs defaultValue="diagram" className="h-full flex flex-col">
          <TabsList className="mx-4 mb-2 bg-gray-800">
            <TabsTrigger value="diagram" className="text-xs">Diagram</TabsTrigger>
            <TabsTrigger value="details" className="text-xs">Details</TabsTrigger>
            <TabsTrigger value="code" className="text-xs">Mermaid Code</TabsTrigger>
          </TabsList>
          
          <TabsContent value="diagram" className="flex-1 m-0">
            <div className={`diagram-canvas relative ${isFullscreen ? 'h-screen' : 'h-96'} bg-gray-800 overflow-hidden`}>
              {isLoading ? (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-500" />
                    <p className="text-sm text-gray-400">Generating {diagramType} diagram...</p>
                  </div>
                </div>
              ) : diagramData ? (
                <div className="h-full flex items-center justify-center p-4">
                  <MermaidDiagram 
                    definition={diagramData.definition} 
                    type={diagramType}
                    zoom={zoom}
                  />
                </div>
              ) : (
                <div className="absolute inset-0 flex items-center justify-center text-gray-500">
                  <div className="text-center">
                    <Network className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No diagram data available</p>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={handleRefresh}
                      className="mt-2"
                    >
                      Generate Diagram
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="details" className="flex-1 m-0 p-4 overflow-auto">
            {diagramData ? (
              <div className="space-y-4">
                {/* Metadata */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-gray-800 rounded p-3">
                    <div className="text-xs text-gray-400 mb-1">Nodes</div>
                    <div className="text-lg font-semibold text-blue-400">
                      {diagramData.metadata.nodeCount}
                    </div>
                  </div>
                  <div className="bg-gray-800 rounded p-3">
                    <div className="text-xs text-gray-400 mb-1">Connections</div>
                    <div className="text-lg font-semibold text-green-400">
                      {diagramData.metadata.edgeCount}
                    </div>
                  </div>
                  <div className="bg-gray-800 rounded p-3">
                    <div className="text-xs text-gray-400 mb-1">Complexity</div>
                    <div className={`text-lg font-semibold ${getComplexityColor(diagramData.metadata.complexity)}`}>
                      {diagramData.metadata.complexity}
                    </div>
                  </div>
                  <div className="bg-gray-800 rounded p-3">
                    <div className="text-xs text-gray-400 mb-1">Updated</div>
                    <div className="text-sm text-gray-300">
                      {new Date(diagramData.metadata.lastUpdated).toLocaleTimeString()}
                    </div>
                  </div>
                </div>

                {/* Nodes List */}
                <div>
                  <h4 className="text-sm font-medium text-gray-300 mb-2">Components ({diagramData.nodes.length})</h4>
                  <div className="space-y-1 max-h-40 overflow-y-auto">
                    {diagramData.nodes.map(node => (
                      <div key={node.id} className="flex items-center justify-between p-2 bg-gray-800 rounded text-sm">
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${getNodeTypeColor(node.type)}`} />
                          <span>{node.label}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {node.type}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Connections List */}
                <div>
                  <h4 className="text-sm font-medium text-gray-300 mb-2">Connections ({diagramData.edges.length})</h4>
                  <div className="space-y-1 max-h-40 overflow-y-auto">
                    {diagramData.edges.map(edge => (
                      <div key={edge.id} className="p-2 bg-gray-800 rounded text-sm">
                        <div className="flex items-center justify-between">
                          <span>{edge.source} → {edge.target}</span>
                          <Badge variant="outline" className="text-xs">
                            {edge.type}
                          </Badge>
                        </div>
                        {edge.label && (
                          <div className="text-xs text-gray-400 mt-1">{edge.label}</div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                No diagram details available
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="code" className="flex-1 m-0 p-4">
            {diagramData ? (
              <div className="h-full">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-400">Mermaid Definition</span>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => navigator.clipboard.writeText(diagramData.definition)}
                  >
                    Copy Code
                  </Button>
                </div>
                <pre className="bg-gray-800 rounded p-3 text-sm text-gray-300 overflow-auto h-full">
                  <code>{diagramData.definition}</code>
                </pre>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                No diagram code available
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

// Mermaid Diagram Component (simplified - would use actual Mermaid library)
const MermaidDiagram: React.FC<{
  definition: string;
  type: DiagramType;
  zoom: number;
}> = ({ definition, type, zoom }) => {
  return (
    <div 
      className="mermaid-diagram w-full h-full flex items-center justify-center"
      style={{ transform: `scale(${zoom / 100})` }}
    >
      {/* This would be replaced with actual Mermaid rendering */}
      <div className="text-center max-w-md">
        <div className="text-6xl mb-4">📊</div>
        <div className="text-lg font-semibold text-gray-300 mb-2">
          {type.charAt(0).toUpperCase() + type.slice(1)} Diagram
        </div>
        <div className="text-sm text-gray-500 mb-4">
          Rendered at {zoom}% zoom
        </div>
        <div className="text-xs text-gray-600 font-mono bg-gray-800 rounded p-2">
          {definition.split('\n')[0]}...
        </div>
      </div>
    </div>
  );
};

// Helper Functions

const generateDiagramData = (type: DiagramType, context: ContextSnapshot): DiagramData => {
  const mockNodes: DiagramNode[] = [
    { id: 'frontend', label: 'Frontend App', type: 'component', position: { x: 0, y: 0 } },
    { id: 'api', label: 'API Gateway', type: 'service', position: { x: 200, y: 0 } },
    { id: 'auth', label: 'Auth Service', type: 'service', position: { x: 100, y: 100 } },
    { id: 'database', label: 'Database', type: 'database', position: { x: 200, y: 200 } },
    { id: 'external', label: 'External API', type: 'external', position: { x: 400, y: 100 } }
  ];

  const mockEdges: DiagramEdge[] = [
    { id: 'e1', source: 'frontend', target: 'api', type: 'api-call', label: 'HTTP requests' },
    { id: 'e2', source: 'api', target: 'auth', type: 'dependency', label: 'authentication' },
    { id: 'e3', source: 'api', target: 'database', type: 'data-flow', label: 'queries' },
    { id: 'e4', source: 'api', target: 'external', type: 'api-call', label: 'third-party' }
  ];

  let definition = '';
  
  switch (type) {
    case 'system':
      definition = `graph TD
    A[Frontend App] --> B[API Gateway]
    B --> C[Auth Service]
    B --> D[Database]
    B --> E[External API]`;
      break;
    case 'component':
      definition = `graph LR
    subgraph "Frontend"
      A[Components]
      B[Services]
      C[Store]
    end
    subgraph "Backend"
      D[Controllers]
      E[Services]
      F[Models]
    end
    A --> D
    B --> E
    C --> F`;
      break;
    case 'sequence':
      definition = `sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant D as Database
    U->>F: Login Request
    F->>A: POST /auth/login
    A->>D: Validate Credentials
    D-->>A: User Data
    A-->>F: JWT Token
    F-->>U: Login Success`;
      break;
    default:
      definition = `graph TD
    A[Component A] --> B[Component B]
    B --> C[Component C]`;
  }

  return {
    definition,
    nodes: mockNodes,
    edges: mockEdges,
    metadata: {
      lastUpdated: Date.now(),
      complexity: mockEdges.length > 3 ? 'high' : mockEdges.length > 1 ? 'medium' : 'low',
      nodeCount: mockNodes.length,
      edgeCount: mockEdges.length
    }
  };
};

const generateSVGFromDiagram = (diagramData: DiagramData): string => {
  // Simplified SVG generation - would be replaced with actual Mermaid rendering
  return `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600">
    <text x="400" y="300" text-anchor="middle" fill="#666">
      ${diagramData.definition.split('\n')[0]}
    </text>
  </svg>`;
};

const getNodeTypeColor = (type: string): string => {
  switch (type) {
    case 'component': return 'bg-blue-500';
    case 'service': return 'bg-green-500';
    case 'database': return 'bg-purple-500';
    case 'external': return 'bg-orange-500';
    default: return 'bg-gray-500';
  }
};

export default DiagramWidget;
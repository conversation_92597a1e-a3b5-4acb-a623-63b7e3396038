import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog';
import { 
  AlertTriangle, 
  Search, 
  Copy, 
  RefreshCw, 
  Filter,
  FileText,
  Code,
  ExternalLink,
  ChevronDown,
  ChevronRight,
  MapPin,
  Clock,
  Zap,
  Bug
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface StackFrame {
  id: string;
  functionName: string;
  fileName: string;
  lineNumber: number;
  columnNumber?: number;
  source?: string;
  isUserCode: boolean;
  isAsync: boolean;
  context?: {
    before: string[];
    line: string;
    after: string[];
  };
}

interface StackTrace {
  id: string;
  timestamp: number;
  errorType: string;
  errorMessage: string;
  frames: StackFrame[];
  threadId?: string;
  processId?: string;
  platform: 'javascript' | 'python' | 'java' | 'csharp' | 'other';
  severity: 'critical' | 'error' | 'warning';
  status: 'new' | 'investigating' | 'resolved' | 'ignored';
  occurrenceCount: number;
  lastOccurrence: number;
  tags: string[];
  environment: string;
  metadata?: Record<string, any>;
}

interface StackTraceWidgetProps {
  context: ContextSnapshot;
  config?: {
    maxTraces?: number;
    showFilters?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number;
    defaultFilter?: string;
    maxHeight?: string;
  };
}

type StatusFilter = 'all' | 'new' | 'investigating' | 'resolved' | 'ignored';
type SeverityFilter = 'all' | 'critical' | 'error' | 'warning';
type PlatformFilter = 'all' | 'javascript' | 'python' | 'java' | 'csharp' | 'other';

export const StackTraceWidget: React.FC<StackTraceWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [stackTraces, setStackTraces] = useState<StackTrace[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [severityFilter, setSeverityFilter] = useState<SeverityFilter>('all');
  const [platformFilter, setPlatformFilter] = useState<PlatformFilter>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTrace, setSelectedTrace] = useState<StackTrace | null>(null);
  const [expandedFrames, setExpandedFrames] = useState<Set<string>>(new Set());

  // Load stack traces on component mount
  useEffect(() => {
    loadStackTraces();
  }, [context]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!config.autoRefresh) return;

    const interval = setInterval(() => {
      loadStackTraces(false); // Don't show loading state for auto-refresh
    }, config.refreshInterval || 10000);

    return () => clearInterval(interval);
  }, [config.autoRefresh, config.refreshInterval]);

  const loadStackTraces = async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    
    // Simulate API call - would be replaced with actual stack trace fetching
    const mockTraces = generateMockStackTraces(context, config.maxTraces || 20);
    
    setTimeout(() => {
      setStackTraces(mockTraces);
      if (showLoading) setIsLoading(false);
    }, showLoading ? 500 : 0);
  };

  // Filter stack traces
  const filteredTraces = useMemo(() => {
    return stackTraces.filter(trace => {
      // Search filter
      const matchesSearch = !searchQuery || 
        trace.errorMessage.toLowerCase().includes(searchQuery.toLowerCase()) ||
        trace.errorType.toLowerCase().includes(searchQuery.toLowerCase()) ||
        trace.frames.some(frame => 
          frame.functionName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          frame.fileName.toLowerCase().includes(searchQuery.toLowerCase())
        );

      // Status filter
      const matchesStatus = statusFilter === 'all' || trace.status === statusFilter;

      // Severity filter
      const matchesSeverity = severityFilter === 'all' || trace.severity === severityFilter;

      // Platform filter
      const matchesPlatform = platformFilter === 'all' || trace.platform === platformFilter;

      return matchesSearch && matchesStatus && matchesSeverity && matchesPlatform;
    }).sort((a, b) => {
      // Sort by timestamp (most recent first)
      return b.timestamp - a.timestamp;
    });
  }, [stackTraces, searchQuery, statusFilter, severityFilter, platformFilter]);

  // Statistics
  const stats = useMemo(() => {
    const total = stackTraces.length;
    const critical = stackTraces.filter(t => t.severity === 'critical').length;
    const newTraces = stackTraces.filter(t => t.status === 'new').length;
    const resolved = stackTraces.filter(t => t.status === 'resolved').length;

    return { total, critical, new: newTraces, resolved };
  }, [stackTraces]);

  const handleRefresh = () => {
    loadStackTraces();
  };

  const handleStatusChange = (traceId: string, newStatus: StackTrace['status']) => {
    setStackTraces(prev => prev.map(trace => 
      trace.id === traceId 
        ? { ...trace, status: newStatus }
        : trace
    ));
  };

  const toggleFrameExpansion = (frameId: string) => {
    setExpandedFrames(prev => {
      const newSet = new Set(prev);
      if (newSet.has(frameId)) {
        newSet.delete(frameId);
      } else {
        newSet.add(frameId);
      }
      return newSet;
    });
  };

  const copyStackTrace = (trace: StackTrace) => {
    const stackText = `${trace.errorType}: ${trace.errorMessage}\n\n` +
      trace.frames.map(frame => 
        `  at ${frame.functionName} (${frame.fileName}:${frame.lineNumber}${frame.columnNumber ? `:${frame.columnNumber}` : ''})`
      ).join('\n');
    
    navigator.clipboard.writeText(stackText);
  };

  const getSeverityColor = (severity: StackTrace['severity']) => {
    switch (severity) {
      case 'critical': return 'text-red-500 bg-red-500/10 border-red-500/30';
      case 'error': return 'text-orange-500 bg-orange-500/10 border-orange-500/30';
      case 'warning': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getSeverityIcon = (severity: StackTrace['severity']) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'error': return <Bug className="h-4 w-4 text-orange-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'javascript': return '🟨';
      case 'python': return '🐍';
      case 'java': return '☕';
      case 'csharp': return '🔷';
      default: return '⚙️';
    }
  };

  return (
    <Card className="stack-trace-widget h-full bg-gray-900 border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Stack Traces
            <Badge variant="secondary" className="ml-2">
              {filteredTraces.length}/{stackTraces.length}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-4 gap-2 mt-3">
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-blue-400">{stats.total}</div>
            <div className="text-xs text-gray-400">Total</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-red-400">{stats.critical}</div>
            <div className="text-xs text-gray-400">Critical</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-yellow-400">{stats.new}</div>
            <div className="text-xs text-gray-400">New</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-green-400">{stats.resolved}</div>
            <div className="text-xs text-gray-400">Resolved</div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0 flex-1 flex flex-col">
        {/* Filters */}
        {config.showFilters !== false && (
          <div className="px-4 pb-3 space-y-2">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search stack traces..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 h-8"
              />
            </div>

            {/* Filter Selects */}
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={(value: StatusFilter) => setStatusFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="new">New</SelectItem>
                  <SelectItem value="investigating">Investigating</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="ignored">Ignored</SelectItem>
                </SelectContent>
              </Select>

              <Select value={severityFilter} onValueChange={(value: SeverityFilter) => setSeverityFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Severity" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Severity</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                  <SelectItem value="warning">Warning</SelectItem>
                </SelectContent>
              </Select>

              <Select value={platformFilter} onValueChange={(value: PlatformFilter) => setPlatformFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Platform" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Platforms</SelectItem>
                  <SelectItem value="javascript">JavaScript</SelectItem>
                  <SelectItem value="python">Python</SelectItem>
                  <SelectItem value="java">Java</SelectItem>
                  <SelectItem value="csharp">C#</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Stack Traces List */}
        <div 
          className="stack-traces-list flex-1 overflow-y-auto px-4"
          style={{ maxHeight: config.maxHeight || '400px' }}
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-400">Loading stack traces...</p>
            </div>
          ) : filteredTraces.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No stack traces found</p>
              <p className="text-sm mt-1">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredTraces.map(trace => (
                <StackTraceItem
                  key={trace.id}
                  trace={trace}
                  onStatusChange={handleStatusChange}
                  onSelect={() => setSelectedTrace(trace)}
                  onCopy={() => copyStackTrace(trace)}
                  expandedFrames={expandedFrames}
                  onToggleFrame={toggleFrameExpansion}
                />
              ))}
            </div>
          )}
        </div>

        {/* Stack Trace Details Modal */}
        {selectedTrace && (
          <StackTraceDetailsDialog
            trace={selectedTrace}
            open={!!selectedTrace}
            onOpenChange={(open) => !open && setSelectedTrace(null)}
            onStatusChange={handleStatusChange}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Stack Trace Item Component
const StackTraceItem: React.FC<{
  trace: StackTrace;
  onStatusChange: (id: string, status: StackTrace['status']) => void;
  onSelect: () => void;
  onCopy: () => void;
  expandedFrames: Set<string>;
  onToggleFrame: (frameId: string) => void;
}> = ({ trace, onStatusChange, onSelect, onCopy, expandedFrames, onToggleFrame }) => {
  const getSeverityColor = (severity: StackTrace['severity']) => {
    switch (severity) {
      case 'critical': return 'border-l-red-500';
      case 'error': return 'border-l-orange-500';
      case 'warning': return 'border-l-yellow-500';
      default: return 'border-l-gray-500';
    }
  };

  const getSeverityIcon = (severity: StackTrace['severity']) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'error': return <Bug className="h-4 w-4 text-orange-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'javascript': return '🟨';
      case 'python': return '🐍';
      case 'java': return '☕';
      case 'csharp': return '🔷';
      default: return '⚙️';
    }
  };

  const userFrames = trace.frames.filter(frame => frame.isUserCode);
  const topFrame = userFrames[0] || trace.frames[0];

  return (
    <div className={`stack-trace-item p-3 bg-gray-800 rounded-lg border-l-4 ${getSeverityColor(trace.severity)}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {getSeverityIcon(trace.severity)}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium text-white text-sm truncate">
                {trace.errorType}
              </span>
              <span className="text-lg">{getPlatformIcon(trace.platform)}</span>
              {trace.occurrenceCount > 1 && (
                <Badge variant="outline" className="text-xs border-gray-600">
                  {trace.occurrenceCount}x
                </Badge>
              )}
            </div>
            <p className="text-xs text-gray-400 truncate">
              {trace.errorMessage}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={(e) => {
              e.stopPropagation();
              onCopy();
            }}
            className="h-6 w-6 p-0"
          >
            <Copy className="h-3 w-3" />
          </Button>
          
          <Select 
            value={trace.status} 
            onValueChange={(value) => onStatusChange(trace.id, value as StackTrace['status'])}
          >
            <SelectTrigger className="w-24 h-6 text-xs bg-gray-700 border-gray-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="new">New</SelectItem>
              <SelectItem value="investigating">Investigating</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="ignored">Ignored</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Top Frame */}
      {topFrame && (
        <div className="mb-2">
          <div className="flex items-center gap-2 text-xs text-gray-300 bg-gray-900 rounded p-2">
            <MapPin className="h-3 w-3 text-blue-400" />
            <span className="font-mono">{topFrame.functionName}</span>
            <span className="text-gray-500">at</span>
            <span className="font-mono text-blue-400">{topFrame.fileName}:{topFrame.lineNumber}</span>
          </div>
        </div>
      )}

      {/* Metadata */}
      <div className="flex items-center justify-between text-xs text-gray-500">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>{new Date(trace.timestamp).toLocaleString()}</span>
          </div>
          <span>•</span>
          <span>{trace.environment}</span>
          {trace.frames.length > 1 && (
            <>
              <span>•</span>
              <span>{trace.frames.length} frames</span>
            </>
          )}
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={onSelect}
          className="h-auto p-0 text-xs text-blue-400 hover:text-blue-300"
        >
          View Details
        </Button>
      </div>

      {/* Tags */}
      {trace.tags.length > 0 && (
        <div className="flex items-center gap-1 mt-2">
          {trace.tags.slice(0, 3).map(tag => (
            <Badge key={tag} variant="outline" className="text-xs border-gray-600 text-gray-400">
              {tag}
            </Badge>
          ))}
          {trace.tags.length > 3 && (
            <span className="text-xs text-gray-500">+{trace.tags.length - 3}</span>
          )}
        </div>
      )}
    </div>
  );
};

// Stack Trace Details Dialog
const StackTraceDetailsDialog: React.FC<{
  trace: StackTrace;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStatusChange: (id: string, status: StackTrace['status']) => void;
}> = ({ trace, open, onOpenChange, onStatusChange }) => {
  const [expandedFrames, setExpandedFrames] = useState<Set<string>>(new Set());

  const toggleFrameExpansion = (frameId: string) => {
    setExpandedFrames(prev => {
      const newSet = new Set(prev);
      if (newSet.has(frameId)) {
        newSet.delete(frameId);
      } else {
        newSet.add(frameId);
      }
      return newSet;
    });
  };

  const copyStackTrace = () => {
    const stackText = `${trace.errorType}: ${trace.errorMessage}\n\n` +
      trace.frames.map(frame => 
        `  at ${frame.functionName} (${frame.fileName}:${frame.lineNumber}${frame.columnNumber ? `:${frame.columnNumber}` : ''})`
      ).join('\n');
    
    navigator.clipboard.writeText(stackText);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-5xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            {trace.errorType}
          </DialogTitle>
          <DialogDescription>
            {new Date(trace.timestamp).toLocaleString()} • {trace.environment} • {trace.platform}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Error Message */}
          <div>
            <h4 className="text-sm font-medium mb-2">Error Message</h4>
            <div className="text-sm text-red-400 bg-gray-800 rounded p-3 font-mono">
              {trace.errorMessage}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={`${
                trace.severity === 'critical' ? 'border-red-500 text-red-400' :
                trace.severity === 'error' ? 'border-orange-500 text-orange-400' :
                'border-yellow-500 text-yellow-400'
              }`}>
                {trace.severity}
              </Badge>
              <Badge variant="outline">
                {trace.occurrenceCount}x occurrences
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Button size="sm" variant="outline" onClick={copyStackTrace}>
                <Copy className="h-4 w-4 mr-1" />
                Copy Stack
              </Button>
              
              <Select 
                value={trace.status} 
                onValueChange={(value) => onStatusChange(trace.id, value as StackTrace['status'])}
              >
                <SelectTrigger className="w-32 bg-gray-800 border-gray-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="new">New</SelectItem>
                  <SelectItem value="investigating">Investigating</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="ignored">Ignored</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Stack Frames */}
          <div>
            <h4 className="text-sm font-medium mb-2">Stack Trace ({trace.frames.length} frames)</h4>
            <div className="space-y-1 max-h-96 overflow-y-auto">
              {trace.frames.map((frame, index) => (
                <StackFrameItem
                  key={frame.id}
                  frame={frame}
                  index={index}
                  isExpanded={expandedFrames.has(frame.id)}
                  onToggle={() => toggleFrameExpansion(frame.id)}
                />
              ))}
            </div>
          </div>

          {/* Metadata */}
          {trace.metadata && Object.keys(trace.metadata).length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Metadata</h4>
              <pre className="text-sm text-gray-300 bg-gray-800 rounded p-3 overflow-auto max-h-40 font-mono">
                {JSON.stringify(trace.metadata, null, 2)}
              </pre>
            </div>
          )}

          {/* Tags */}
          {trace.tags.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Tags</h4>
              <div className="flex flex-wrap gap-1">
                {trace.tags.map(tag => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Stack Frame Item Component
const StackFrameItem: React.FC<{
  frame: StackFrame;
  index: number;
  isExpanded: boolean;
  onToggle: () => void;
}> = ({ frame, index, isExpanded, onToggle }) => {
  return (
    <div className={`stack-frame p-2 rounded ${frame.isUserCode ? 'bg-gray-800' : 'bg-gray-900'} ${frame.isUserCode ? 'border border-blue-500/30' : ''}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <span className="text-xs text-gray-500 w-6">{index}</span>
          
          {frame.isUserCode && (
            <Badge variant="outline" className="text-xs h-4 px-1 border-blue-500/30 text-blue-400">
              User
            </Badge>
          )}
          
          {frame.isAsync && (
            <Badge variant="outline" className="text-xs h-4 px-1 border-purple-500/30 text-purple-400">
              Async
            </Badge>
          )}
          
          <div className="flex-1 min-w-0">
            <div className="text-sm font-mono text-gray-300 truncate">
              {frame.functionName}
            </div>
            <div className="text-xs text-gray-500 truncate">
              {frame.fileName}:{frame.lineNumber}{frame.columnNumber ? `:${frame.columnNumber}` : ''}
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            className="h-6 w-6 p-0"
            onClick={() => {
              // Open file in editor (would integrate with IDE)
              console.log('Open file:', frame.fileName, frame.lineNumber);
            }}
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
          
          {frame.context && (
            <Button
              size="sm"
              variant="ghost"
              onClick={onToggle}
              className="h-6 w-6 p-0"
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      </div>
      
      {/* Code Context */}
      {isExpanded && frame.context && (
        <div className="mt-2 text-xs font-mono">
          {frame.context.before.map((line, i) => (
            <div key={i} className="text-gray-500 py-0.5">
              <span className="inline-block w-8 text-right mr-2">{frame.lineNumber - frame.context!.before.length + i}</span>
              {line}
            </div>
          ))}
          <div className="text-red-400 py-0.5 bg-red-500/10">
            <span className="inline-block w-8 text-right mr-2">{frame.lineNumber}</span>
            {frame.context.line}
          </div>
          {frame.context.after.map((line, i) => (
            <div key={i} className="text-gray-500 py-0.5">
              <span className="inline-block w-8 text-right mr-2">{frame.lineNumber + i + 1}</span>
              {line}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Helper Functions
const generateMockStackTraces = (context: ContextSnapshot, maxTraces: number): StackTrace[] => {
  const mockTraces: StackTrace[] = [];
  const now = Date.now();
  
  const errorTypes = [
    'TypeError', 'ReferenceError', 'SyntaxError', 'RangeError',
    'NullPointerException', 'IndexOutOfBoundsException', 'IllegalArgumentException',
    'ArgumentNullException', 'InvalidOperationException', 'DivideByZeroException'
  ];
  
  const platforms: StackTrace['platform'][] = ['javascript', 'python', 'java', 'csharp'];
  const severities: StackTrace['severity'][] = ['critical', 'error', 'warning'];
  const statuses: StackTrace['status'][] = ['new', 'investigating', 'resolved', 'ignored'];
  
  for (let i = 0; i < maxTraces; i++) {
    const platform = platforms[Math.floor(Math.random() * platforms.length)];
    const severity = severities[Math.floor(Math.random() * severities.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const errorType = errorTypes[Math.floor(Math.random() * errorTypes.length)];
    
    // Generate frames based on platform
    const frames: StackFrame[] = [];
    const frameCount = Math.floor(Math.random() * 8) + 3;
    
    for (let j = 0; j < frameCount; j++) {
      const isUserCode = j < 3; // First 3 frames are usually user code
      frames.push({
        id: `frame-${i}-${j}`,
        functionName: isUserCode ? `userFunction${j + 1}` : `systemFunction${j + 1}`,
        fileName: isUserCode ? `src/components/Component${j + 1}.${platform === 'javascript' ? 'tsx' : platform === 'python' ? 'py' : platform === 'java' ? 'java' : 'cs'}` : `node_modules/library/index.js`,
        lineNumber: Math.floor(Math.random() * 200) + 1,
        columnNumber: Math.floor(Math.random() * 50) + 1,
        isUserCode,
        isAsync: Math.random() > 0.7,
        context: isUserCode ? {
          before: ['  // Some context before', '  const value = getData();'],
          line: '  throw new Error("Something went wrong");',
          after: ['  return null;', '}']
        } : undefined
      });
    }
    
    mockTraces.push({
      id: `trace-${i}`,
      timestamp: now - (Math.random() * 86400000), // Random time in last day
      errorType,
      errorMessage: `${errorType}: Cannot read property 'length' of undefined`,
      frames,
      platform,
      severity,
      status,
      occurrenceCount: Math.floor(Math.random() * 10) + 1,
      lastOccurrence: now - (Math.random() * 3600000),
      tags: ['frontend', 'user-interaction', platform],
      environment: Math.random() > 0.5 ? 'production' : 'staging',
      metadata: {
        userId: `user-${Math.random().toString(36).substr(2, 9)}`,
        sessionId: `sess-${Math.random().toString(36).substr(2, 9)}`,
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)'
      }
    });
  }

  // Add context-based stack trace
  if (context.fileContext.hasErrors) {
    mockTraces.unshift({
      id: 'trace-context-error',
      timestamp: now,
      errorType: 'SyntaxError',
      errorMessage: 'Unexpected token in current file',
      frames: [
        {
          id: 'frame-context-1',
          functionName: 'parseCode',
          fileName: context.fileContext.path || 'current-file.ts',
          lineNumber: 42,
          columnNumber: 15,
          isUserCode: true,
          isAsync: false,
          context: {
            before: ['function parseCode() {', '  const data = {'],
            line: '    invalidSyntax: ,',
            after: ['  };', '}']
          }
        }
      ],
      platform: 'javascript',
      severity: 'error',
      status: 'new',
      occurrenceCount: 1,
      lastOccurrence: now,
      tags: ['syntax-error', 'compilation'],
      environment: 'development'
    });
  }

  return mockTraces.sort((a, b) => b.timestamp - a.timestamp);
};

export default StackTraceWidget;
import React, { useState, useEffect, useMemo } from 'react';
import { ModeWidgetCompound as Widget } from '../compounds/ModeWidget';
import { Flex } from '../primitives/Flex';
import { Box } from '../primitives/Box';
import { Text } from '../primitives/Text';
import { Stack } from '../primitives/Stack';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs';
import { 
  Network, 
  Download, 
  RefreshCw, 
  ZoomIn, 
  ZoomOut, 
  Maximize2,
  Server,
  GitBranch,
  FileText,
  Database
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

// Types remain the same
interface DiagramData {
  definition: string;
  nodes: DiagramNode[];
  edges: DiagramEdge[];
  metadata: {
    lastUpdated: number;
    complexity: 'low' | 'medium' | 'high';
    nodeCount: number;
    edgeCount: number;
  };
}

interface DiagramNode {
  id: string;
  label: string;
  type: 'component' | 'service' | 'database' | 'external';
  position: { x: number; y: number };
  metadata?: Record<string, any>;
}

interface DiagramEdge {
  id: string;
  source: string;
  target: string;
  type: 'dependency' | 'data-flow' | 'api-call' | 'inheritance';
  label?: string;
}

interface DiagramWidgetProps {
  context: ContextSnapshot;
  config?: {
    defaultType?: DiagramType;
    showControls?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number;
  };
}

type DiagramType = 'system' | 'component' | 'sequence' | 'dependency' | 'data-flow';

/**
 * DiagramWidget - Refactored using composition patterns
 * 
 * Demonstrates the use of primitive and compound components
 * for building complex widgets with consistent theming.
 */
export const DiagramWidget: React.FC<DiagramWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [diagramType, setDiagramType] = useState<DiagramType>(
    config.defaultType || 'system'
  );
  const [diagramData, setDiagramData] = useState<DiagramData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [zoom, setZoom] = useState(100);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Generate diagram data based on context and type
  const generatedDiagram = useMemo(() => 
    generateDiagramData(diagramType, context), 
    [diagramType, context]
  );

  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => {
      setDiagramData(generatedDiagram);
      setIsLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, [generatedDiagram]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!config.autoRefresh) return;

    const interval = setInterval(() => {
      const newDiagram = generateDiagramData(diagramType, context);
      setDiagramData(newDiagram);
    }, config.refreshInterval || 30000);

    return () => clearInterval(interval);
  }, [diagramType, context, config.autoRefresh, config.refreshInterval]);

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => {
      setDiagramData(generateDiagramData(diagramType, context));
      setIsLoading(false);
    }, 500);
  };

  const handleExport = () => {
    if (!diagramData) return;
    
    const svgContent = generateSVGFromDiagram(diagramData);
    const blob = new Blob([svgContent], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${diagramType}-diagram-${Date.now()}.svg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getComplexityColor = (complexity: string): 'success' | 'warning' | 'error' => {
    switch (complexity) {
      case 'low': return 'success';
      case 'medium': return 'warning';
      case 'high': return 'error';
      default: return 'warning';
    }
  };

  // Widget Actions
  const widgetActions = (
    <Widget.Actions>
      <Select value={diagramType} onValueChange={(value: DiagramType) => setDiagramType(value)}>
        <SelectTrigger className="w-40">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="system">
            <Flex align="center" gap="sm">
              <Server className="h-4 w-4" />
              <Text size="sm">System View</Text>
            </Flex>
          </SelectItem>
          <SelectItem value="component">
            <Flex align="center" gap="sm">
              <GitBranch className="h-4 w-4" />
              <Text size="sm">Components</Text>
            </Flex>
          </SelectItem>
          <SelectItem value="sequence">
            <Flex align="center" gap="sm">
              <FileText className="h-4 w-4" />
              <Text size="sm">Sequence</Text>
            </Flex>
          </SelectItem>
          <SelectItem value="dependency">
            <Flex align="center" gap="sm">
              <Network className="h-4 w-4" />
              <Text size="sm">Dependencies</Text>
            </Flex>
          </SelectItem>
          <SelectItem value="data-flow">
            <Flex align="center" gap="sm">
              <Database className="h-4 w-4" />
              <Text size="sm">Data Flow</Text>
            </Flex>
          </SelectItem>
        </SelectContent>
      </Select>

      {config.showControls !== false && (
        <>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={handleRefresh}
            disabled={isLoading}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => setZoom(Math.max(50, zoom - 25))}
            disabled={zoom <= 50}
            className="h-8 w-8 p-0"
          >
            <ZoomOut className="h-3 w-3" />
          </Button>
          
          <Badge variant="outline" className="px-2 h-8 text-xs">
            {zoom}%
          </Badge>
          
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => setZoom(Math.min(200, zoom + 25))}
            disabled={zoom >= 200}
            className="h-8 w-8 p-0"
          >
            <ZoomIn className="h-3 w-3" />
          </Button>
          
          <Button 
            size="sm" 
            variant="outline" 
            onClick={handleExport}
            disabled={!diagramData}
            className="h-8 w-8 p-0"
          >
            <Download className="h-3 w-3" />
          </Button>
          
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="h-8 w-8 p-0"
          >
            <Maximize2 className="h-3 w-3" />
          </Button>
        </>
      )}
    </Widget.Actions>
  );

  return (
    <Widget
      size={isFullscreen ? 'full' : 'lg'}
      loading={isLoading}
      empty={!diagramData && !isLoading}
      title="System Architecture"
      icon={<Network className="h-5 w-5" />}
      actions={widgetActions}
      className={isFullscreen ? 'fixed inset-4 z-50' : ''}
    >
      {diagramData && (
        <Box className="h-full">
          <Tabs defaultValue="diagram" className="h-full flex flex-col">
            <TabsList className="mb-4">
              <TabsTrigger value="diagram">Diagram</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="code">Mermaid Code</TabsTrigger>
            </TabsList>
            
            <TabsContent value="diagram" className="flex-1">
              <Box 
                variant="surface" 
                rounded="md" 
                className="h-96 overflow-hidden"
                style={{ transform: `scale(${zoom / 100})` }}
              >
                <Flex align="center" justify="center" className="h-full">
                  <MermaidDiagram 
                    definition={diagramData.definition} 
                    type={diagramType}
                    zoom={zoom}
                  />
                </Flex>
              </Box>
            </TabsContent>
            
            <TabsContent value="details" className="flex-1 overflow-auto">
              <Stack spacing="lg">
                {/* Metadata Cards */}
                <Flex gap="md" wrap="wrap">
                  <MetricCard 
                    label="Nodes" 
                    value={diagramData.metadata.nodeCount}
                    color="info"
                  />
                  <MetricCard 
                    label="Connections" 
                    value={diagramData.metadata.edgeCount}
                    color="success"
                  />
                  <MetricCard 
                    label="Complexity" 
                    value={diagramData.metadata.complexity}
                    color={getComplexityColor(diagramData.metadata.complexity)}
                  />
                  <MetricCard 
                    label="Updated" 
                    value={new Date(diagramData.metadata.lastUpdated).toLocaleTimeString()}
                    color="muted"
                  />
                </Flex>

                {/* Nodes List */}
                <Stack spacing="sm">
                  <Text weight="medium" size="sm" color="secondary">
                    Components ({diagramData.nodes.length})
                  </Text>
                  <Stack spacing="xs" className="max-h-40 overflow-y-auto">
                    {diagramData.nodes.map(node => (
                      <NodeItem key={node.id} node={node} />
                    ))}
                  </Stack>
                </Stack>

                {/* Connections List */}
                <Stack spacing="sm">
                  <Text weight="medium" size="sm" color="secondary">
                    Connections ({diagramData.edges.length})
                  </Text>
                  <Stack spacing="xs" className="max-h-40 overflow-y-auto">
                    {diagramData.edges.map(edge => (
                      <EdgeItem key={edge.id} edge={edge} />
                    ))}
                  </Stack>
                </Stack>
              </Stack>
            </TabsContent>
            
            <TabsContent value="code" className="flex-1">
              <Stack spacing="sm">
                <Flex justify="between" align="center">
                  <Text size="sm" color="muted">Mermaid Definition</Text>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => navigator.clipboard.writeText(diagramData.definition)}
                  >
                    Copy Code
                  </Button>
                </Flex>
                <Box 
                  variant="surface" 
                  rounded="md" 
                  padding="md"
                  className="overflow-auto"
                >
                  <Text as="pre" mono size="sm">
                    <code>{diagramData.definition}</code>
                  </Text>
                </Box>
              </Stack>
            </TabsContent>
          </Tabs>
        </Box>
      )}
    </Widget>
  );
};

// Sub-components using primitives
const MetricCard: React.FC<{
  label: string;
  value: string | number;
  color: 'info' | 'success' | 'warning' | 'error' | 'muted';
}> = ({ label, value, color }) => (
  <Box variant="surface" padding="md" rounded="md" border className="min-w-[120px]">
    <Text size="xs" color="muted" className="mb-1">{label}</Text>
    <Text size="lg" weight="semibold" color={color}>
      {value}
    </Text>
  </Box>
);

const NodeItem: React.FC<{ node: DiagramNode }> = ({ node }) => (
  <Flex 
    align="center" 
    justify="between" 
    padding="sm" 
    variant="surface" 
    rounded="sm"
  >
    <Flex align="center" gap="sm">
      <Box 
        className={`w-2 h-2 rounded-full ${getNodeTypeColor(node.type)}`} 
      />
      <Text size="sm">{node.label}</Text>
    </Flex>
    <Badge variant="outline" className="text-xs">
      {node.type}
    </Badge>
  </Flex>
);

const EdgeItem: React.FC<{ edge: DiagramEdge }> = ({ edge }) => (
  <Box padding="sm" variant="surface" rounded="sm">
    <Flex align="center" justify="between">
      <Text size="sm">{edge.source} → {edge.target}</Text>
      <Badge variant="outline" className="text-xs">
        {edge.type}
      </Badge>
    </Flex>
    {edge.label && (
      <Text size="xs" color="muted" className="mt-1">{edge.label}</Text>
    )}
  </Box>
);

// Simplified Mermaid Diagram Component
const MermaidDiagram: React.FC<{
  definition: string;
  type: DiagramType;
  zoom: number;
}> = ({ definition, type, zoom }) => {
  return (
    <Stack align="center" spacing="md">
      <Text size="4xl">📊</Text>
      <Text size="lg" weight="semibold">
        {type.charAt(0).toUpperCase() + type.slice(1)} Diagram
      </Text>
      <Text size="sm" color="muted">
        Rendered at {zoom}% zoom
      </Text>
      <Box 
        variant="surface" 
        rounded="md" 
        padding="sm"
        className="max-w-md"
      >
        <Text size="xs" mono color="muted">
          {definition.split('\n')[0]}...
        </Text>
      </Box>
    </Stack>
  );
};

// Helper functions remain the same
const generateDiagramData = (type: DiagramType, context: ContextSnapshot): DiagramData => {
  // Implementation remains the same as original
  const mockNodes: DiagramNode[] = [
    { id: 'frontend', label: 'Frontend App', type: 'component', position: { x: 0, y: 0 } },
    { id: 'api', label: 'API Gateway', type: 'service', position: { x: 200, y: 0 } },
    { id: 'auth', label: 'Auth Service', type: 'service', position: { x: 100, y: 100 } },
    { id: 'database', label: 'Database', type: 'database', position: { x: 200, y: 200 } },
    { id: 'external', label: 'External API', type: 'external', position: { x: 400, y: 100 } }
  ];

  const mockEdges: DiagramEdge[] = [
    { id: 'e1', source: 'frontend', target: 'api', type: 'api-call', label: 'HTTP requests' },
    { id: 'e2', source: 'api', target: 'auth', type: 'dependency', label: 'authentication' },
    { id: 'e3', source: 'api', target: 'database', type: 'data-flow', label: 'queries' },
    { id: 'e4', source: 'api', target: 'external', type: 'api-call', label: 'third-party' }
  ];

  let definition = '';
  
  switch (type) {
    case 'system':
      definition = `graph TD
    A[Frontend App] --> B[API Gateway]
    B --> C[Auth Service]
    B --> D[Database]
    B --> E[External API]`;
      break;
    case 'component':
      definition = `graph LR
    subgraph "Frontend"
      A[Components]
      B[Services]
      C[Store]
    end
    subgraph "Backend"
      D[Controllers]
      E[Services]
      F[Models]
    end
    A --> D
    B --> E
    C --> F`;
      break;
    case 'sequence':
      definition = `sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant D as Database
    U->>F: Login Request
    F->>A: POST /auth/login
    A->>D: Validate Credentials
    D-->>A: User Data
    A-->>F: JWT Token
    F-->>U: Login Success`;
      break;
    default:
      definition = `graph TD
    A[Component A] --> B[Component B]
    B --> C[Component C]`;
  }

  return {
    definition,
    nodes: mockNodes,
    edges: mockEdges,
    metadata: {
      lastUpdated: Date.now(),
      complexity: mockEdges.length > 3 ? 'high' : mockEdges.length > 1 ? 'medium' : 'low',
      nodeCount: mockNodes.length,
      edgeCount: mockEdges.length
    }
  };
};

const generateSVGFromDiagram = (diagramData: DiagramData): string => {
  return `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600">
    <text x="400" y="300" text-anchor="middle" fill="#666">
      ${diagramData.definition.split('\n')[0]}
    </text>
  </svg>`;
};

const getNodeTypeColor = (type: string): string => {
  switch (type) {
    case 'component': return 'bg-[var(--mode-info)]';
    case 'service': return 'bg-[var(--mode-success)]';
    case 'database': return 'bg-[var(--mode-primary)]';
    case 'external': return 'bg-[var(--mode-warning)]';
    default: return 'bg-[var(--mode-text-muted)]';
  }
};

export default DiagramWidget;
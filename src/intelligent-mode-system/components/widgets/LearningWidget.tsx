import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { Textarea } from '../../../components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog';
import { 
  BookOpen, 
  Search, 
  Filter, 
  Clock, 
  CheckCircle, 
  XCircle,
  Star,
  Play,
  Pause,
  RotateCcw,
  BookmarkPlus,
  RefreshCw,
  Plus,
  Target,
  TrendingUp,
  Award,
  Calendar,
  User,
  Tag,
  ExternalLink,
  FileText,
  Video,
  Headphones,
  Code,
  Brain,
  Lightbulb,
  GraduationCap
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface LearningResource {
  id: string;
  title: string;
  description: string;
  type: 'article' | 'video' | 'course' | 'tutorial' | 'documentation' | 'podcast' | 'book' | 'exercise';
  category: 'frontend' | 'backend' | 'devops' | 'design' | 'general' | 'security' | 'testing' | 'architecture';
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  duration: number; // in minutes
  url?: string;
  author: string;
  provider: string;
  rating: number;
  reviewCount: number;
  completedAt?: number;
  startedAt?: number;
  progress: number; // 0-100
  bookmarked: boolean;
  tags: string[];
  prerequisites?: string[];
  learningObjectives: string[];
  lastAccessed?: number;
  estimatedCompletion?: number;
  relatedResources?: string[];
}

interface LearningPath {
  id: string;
  name: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number; // in hours
  resources: string[]; // resource IDs
  completedResources: string[];
  progress: number;
  category: string;
  skills: string[];
  createdAt: number;
  lastActivity?: number;
}

interface LearningWidgetProps {
  context: ContextSnapshot;
  config?: {
    maxResources?: number;
    showFilters?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number;
    showProgress?: boolean;
    maxHeight?: string;
  };
}

type StatusFilter = 'all' | 'not_started' | 'in_progress' | 'completed' | 'bookmarked';
type TypeFilter = 'all' | 'article' | 'video' | 'course' | 'tutorial' | 'documentation' | 'podcast' | 'book' | 'exercise';
type DifficultyFilter = 'all' | 'beginner' | 'intermediate' | 'advanced' | 'expert';

export const LearningWidget: React.FC<LearningWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [resources, setResources] = useState<LearningResource[]>([]);
  const [learningPaths, setLearningPaths] = useState<LearningPath[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [typeFilter, setTypeFilter] = useState<TypeFilter>('all');
  const [difficultyFilter, setDifficultyFilter] = useState<DifficultyFilter>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedResource, setSelectedResource] = useState<LearningResource | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [view, setView] = useState<'resources' | 'paths'>('resources');

  // Load learning resources on component mount
  useEffect(() => {
    loadLearningData();
  }, [context]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!config.autoRefresh) return;

    const interval = setInterval(() => {
      loadLearningData(false); // Don't show loading state for auto-refresh
    }, config.refreshInterval || 60000);

    return () => clearInterval(interval);
  }, [config.autoRefresh, config.refreshInterval]);

  const loadLearningData = async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    
    // Simulate API call - would be replaced with actual learning data fetching
    const mockResources = generateMockResources(context, config.maxResources || 20);
    const mockPaths = generateMockLearningPaths(mockResources);
    
    setTimeout(() => {
      setResources(mockResources);
      setLearningPaths(mockPaths);
      if (showLoading) setIsLoading(false);
    }, showLoading ? 800 : 0);
  };

  // Filter resources
  const filteredResources = useMemo(() => {
    return resources.filter(resource => {
      // Search filter
      const matchesSearch = !searchQuery || 
        resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.provider.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      // Status filter
      let matchesStatus = true;
      if (statusFilter === 'not_started') matchesStatus = resource.progress === 0;
      else if (statusFilter === 'in_progress') matchesStatus = resource.progress > 0 && resource.progress < 100;
      else if (statusFilter === 'completed') matchesStatus = resource.progress === 100;
      else if (statusFilter === 'bookmarked') matchesStatus = resource.bookmarked;

      // Type filter
      const matchesType = typeFilter === 'all' || resource.type === typeFilter;

      // Difficulty filter
      const matchesDifficulty = difficultyFilter === 'all' || resource.difficulty === difficultyFilter;

      return matchesSearch && matchesStatus && matchesType && matchesDifficulty;
    }).sort((a, b) => {
      // Sort by last accessed, then by rating
      if (a.lastAccessed && b.lastAccessed) {
        return b.lastAccessed - a.lastAccessed;
      }
      if (a.lastAccessed && !b.lastAccessed) return -1;
      if (!a.lastAccessed && b.lastAccessed) return 1;
      return b.rating - a.rating;
    });
  }, [resources, searchQuery, statusFilter, typeFilter, difficultyFilter]);

  // Statistics
  const stats = useMemo(() => {
    const total = resources.length;
    const completed = resources.filter(r => r.progress === 100).length;
    const inProgress = resources.filter(r => r.progress > 0 && r.progress < 100).length;
    const bookmarked = resources.filter(r => r.bookmarked).length;
    const totalHoursLearned = resources
      .filter(r => r.progress === 100)
      .reduce((acc, r) => acc + (r.duration / 60), 0);

    return { total, completed, inProgress, bookmarked, totalHoursLearned };
  }, [resources]);

  const handleRefresh = () => {
    loadLearningData();
  };

  const handleStartResource = (resourceId: string) => {
    setResources(prev => prev.map(r => 
      r.id === resourceId 
        ? { ...r, startedAt: Date.now(), lastAccessed: Date.now(), progress: Math.max(r.progress, 5) }
        : r
    ));
  };

  const handleBookmarkToggle = (resourceId: string) => {
    setResources(prev => prev.map(r => 
      r.id === resourceId 
        ? { ...r, bookmarked: !r.bookmarked }
        : r
    ));
  };

  const handleMarkComplete = (resourceId: string) => {
    setResources(prev => prev.map(r => 
      r.id === resourceId 
        ? { ...r, progress: 100, completedAt: Date.now(), lastAccessed: Date.now() }
        : r
    ));
  };

  const getTypeIcon = (type: LearningResource['type']) => {
    switch (type) {
      case 'article': return <FileText className="h-4 w-4" />;
      case 'video': return <Video className="h-4 w-4" />;
      case 'course': return <GraduationCap className="h-4 w-4" />;
      case 'tutorial': return <Code className="h-4 w-4" />;
      case 'documentation': return <BookOpen className="h-4 w-4" />;
      case 'podcast': return <Headphones className="h-4 w-4" />;
      case 'book': return <BookOpen className="h-4 w-4" />;
      case 'exercise': return <Brain className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: LearningResource['type']) => {
    switch (type) {
      case 'article': return 'text-blue-400 bg-blue-500/10 border-blue-500/30';
      case 'video': return 'text-red-400 bg-red-500/10 border-red-500/30';
      case 'course': return 'text-purple-400 bg-purple-500/10 border-purple-500/30';
      case 'tutorial': return 'text-green-400 bg-green-500/10 border-green-500/30';
      case 'documentation': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
      case 'podcast': return 'text-pink-400 bg-pink-500/10 border-pink-500/30';
      case 'book': return 'text-orange-400 bg-orange-500/10 border-orange-500/30';
      case 'exercise': return 'text-cyan-400 bg-cyan-500/10 border-cyan-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getDifficultyColor = (difficulty: LearningResource['difficulty']) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-400 bg-green-500/10 border-green-500/30';
      case 'intermediate': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
      case 'advanced': return 'text-orange-400 bg-orange-500/10 border-orange-500/30';
      case 'expert': return 'text-red-400 bg-red-500/10 border-red-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  return (
    <Card className="learning-widget h-full bg-gray-900 border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Learning
            <Badge variant="secondary" className="ml-2">
              {filteredResources.length}/{resources.length}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <div className="flex bg-gray-800 rounded border border-gray-600">
              <Button
                size="sm"
                variant={view === 'resources' ? 'default' : 'ghost'}
                onClick={() => setView('resources')}
                className="h-8 px-3 rounded-r-none"
              >
                Resources
              </Button>
              <Button
                size="sm"
                variant={view === 'paths' ? 'default' : 'ghost'}
                onClick={() => setView('paths')}
                className="h-8 px-3 rounded-l-none"
              >
                Paths
              </Button>
            </div>
            
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => setShowAddDialog(true)}
              className="h-8"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add
            </Button>
          </div>
        </div>

        {/* Statistics */}
        {config.showProgress !== false && (
          <div className="grid grid-cols-5 gap-2 mt-3">
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-blue-400">{stats.total}</div>
              <div className="text-xs text-gray-400">Total</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-green-400">{stats.completed}</div>
              <div className="text-xs text-gray-400">Completed</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-yellow-400">{stats.inProgress}</div>
              <div className="text-xs text-gray-400">In Progress</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-purple-400">{stats.bookmarked}</div>
              <div className="text-xs text-gray-400">Bookmarked</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-orange-400">
                {Math.round(stats.totalHoursLearned)}h
              </div>
              <div className="text-xs text-gray-400">Learned</div>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="p-0 flex-1 flex flex-col">
        {/* Filters */}
        {config.showFilters !== false && view === 'resources' && (
          <div className="px-4 pb-3 space-y-2">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search learning resources..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 h-8"
              />
            </div>

            {/* Filter Selects */}
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={(value: StatusFilter) => setStatusFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="not_started">Not Started</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="bookmarked">Bookmarked</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={(value: TypeFilter) => setTypeFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="article">Article</SelectItem>
                  <SelectItem value="video">Video</SelectItem>
                  <SelectItem value="course">Course</SelectItem>
                  <SelectItem value="tutorial">Tutorial</SelectItem>
                  <SelectItem value="documentation">Documentation</SelectItem>
                  <SelectItem value="podcast">Podcast</SelectItem>
                  <SelectItem value="book">Book</SelectItem>
                  <SelectItem value="exercise">Exercise</SelectItem>
                </SelectContent>
              </Select>

              <Select value={difficultyFilter} onValueChange={(value: DifficultyFilter) => setDifficultyFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Difficulty" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="beginner">Beginner</SelectItem>
                  <SelectItem value="intermediate">Intermediate</SelectItem>
                  <SelectItem value="advanced">Advanced</SelectItem>
                  <SelectItem value="expert">Expert</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Content */}
        <div 
          className="learning-content flex-1 overflow-y-auto px-4"
          style={{ maxHeight: config.maxHeight || '500px' }}
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-400">Loading learning resources...</p>
            </div>
          ) : view === 'resources' ? (
            filteredResources.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No learning resources found</p>
                <p className="text-sm mt-1">Try adjusting your search or filters</p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredResources.map(resource => (
                  <LearningResourceItem
                    key={resource.id}
                    resource={resource}
                    onStart={handleStartResource}
                    onBookmark={handleBookmarkToggle}
                    onComplete={handleMarkComplete}
                    onSelect={() => setSelectedResource(resource)}
                  />
                ))}
              </div>
            )
          ) : (
            <div className="space-y-3">
              {learningPaths.map(path => (
                <LearningPathItem
                  key={path.id}
                  path={path}
                  resources={resources}
                />
              ))}
            </div>
          )}
        </div>

        {/* Resource Details Modal */}
        {selectedResource && (
          <LearningResourceDialog
            resource={selectedResource}
            open={!!selectedResource}
            onOpenChange={(open) => !open && setSelectedResource(null)}
            onStart={handleStartResource}
            onBookmark={handleBookmarkToggle}
            onComplete={handleMarkComplete}
          />
        )}

        {/* Add Resource Dialog */}
        {showAddDialog && (
          <AddResourceDialog
            open={showAddDialog}
            onOpenChange={setShowAddDialog}
            context={context}
            onAdded={(resource) => {
              setResources(prev => [resource, ...prev]);
              setShowAddDialog(false);
            }}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Learning Resource Item Component
const LearningResourceItem: React.FC<{
  resource: LearningResource;
  onStart: (id: string) => void;
  onBookmark: (id: string) => void;
  onComplete: (id: string) => void;
  onSelect: () => void;
}> = ({ resource, onStart, onBookmark, onComplete, onSelect }) => {
  const getTypeIcon = (type: LearningResource['type']) => {
    switch (type) {
      case 'article': return <FileText className="h-4 w-4" />;
      case 'video': return <Video className="h-4 w-4" />;
      case 'course': return <GraduationCap className="h-4 w-4" />;
      case 'tutorial': return <Code className="h-4 w-4" />;
      case 'documentation': return <BookOpen className="h-4 w-4" />;
      case 'podcast': return <Headphones className="h-4 w-4" />;
      case 'book': return <BookOpen className="h-4 w-4" />;
      case 'exercise': return <Brain className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  return (
    <div className="learning-resource-item p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors">
      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="flex items-center gap-2">
            {getTypeIcon(resource.type)}
            <span className="font-medium text-white text-sm line-clamp-1">{resource.title}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={getTypeColor(resource.type)}>
              {resource.type}
            </Badge>
            
            <Badge variant="outline" className={getDifficultyColor(resource.difficulty)}>
              {resource.difficulty}
            </Badge>
            
            {resource.bookmarked && (
              <BookmarkPlus className="h-4 w-4 text-yellow-400" />
            )}
          </div>
        </div>
      </div>

      {/* Description */}
      <div className="text-sm text-gray-300 mb-2 line-clamp-2">
        {resource.description}
      </div>

      {/* Author and Provider */}
      <div className="flex items-center gap-4 mb-2 text-xs text-gray-400">
        <div className="flex items-center gap-1">
          <User className="h-3 w-3" />
          <span>{resource.author}</span>
        </div>
        <div className="flex items-center gap-1">
          <span>via {resource.provider}</span>
        </div>
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          <span>{formatDuration(resource.duration)}</span>
        </div>
      </div>

      {/* Rating and Progress */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2 text-xs">
          <div className="flex items-center gap-1">
            <Star className="h-3 w-3 text-yellow-400 fill-current" />
            <span className="text-gray-300">{resource.rating.toFixed(1)}</span>
            <span className="text-gray-500">({resource.reviewCount})</span>
          </div>
        </div>

        {resource.progress > 0 && (
          <div className="flex items-center gap-2 text-xs text-gray-400">
            <div className="w-16 bg-gray-700 rounded-full h-1">
              <div 
                className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                style={{ width: `${resource.progress}%` }}
              />
            </div>
            <span>{resource.progress}%</span>
          </div>
        )}
      </div>

      {/* Tags */}
      {resource.tags.length > 0 && (
        <div className="flex items-center gap-1 mb-2 flex-wrap">
          {resource.tags.slice(0, 3).map(tag => (
            <Badge key={tag} variant="outline" className="text-xs h-4 px-1 text-gray-400 border-gray-600">
              {tag}
            </Badge>
          ))}
          {resource.tags.length > 3 && (
            <span className="text-xs text-gray-500">+{resource.tags.length - 3}</span>
          )}
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {resource.progress === 0 && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onStart(resource.id);
              }}
              className="h-6 text-xs border-green-600 text-green-400 hover:bg-green-600/10"
            >
              <Play className="h-3 w-3 mr-1" />
              Start
            </Button>
          )}
          
          {resource.progress > 0 && resource.progress < 100 && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onComplete(resource.id);
              }}
              className="h-6 text-xs border-blue-600 text-blue-400 hover:bg-blue-600/10"
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Complete
            </Button>
          )}
          
          {resource.progress === 100 && (
            <Badge variant="outline" className="h-6 text-xs border-green-600 text-green-400">
              <CheckCircle className="h-3 w-3 mr-1" />
              Completed
            </Badge>
          )}
          
          <Button
            size="sm"
            variant="outline"
            onClick={(e) => {
              e.stopPropagation();
              onBookmark(resource.id);
            }}
            className="h-6 text-xs border-yellow-600 text-yellow-400 hover:bg-yellow-600/10"
          >
            <BookmarkPlus className={`h-3 w-3 mr-1 ${resource.bookmarked ? 'fill-current' : ''}`} />
            {resource.bookmarked ? 'Saved' : 'Save'}
          </Button>
          
          {resource.url && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                window.open(resource.url, '_blank');
              }}
              className="h-6 text-xs border-purple-600 text-purple-400 hover:bg-purple-600/10"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Open
            </Button>
          )}
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={onSelect}
          className="h-auto p-0 text-xs text-blue-400 hover:text-blue-300"
        >
          View Details
        </Button>
      </div>
    </div>
  );
};

// Learning Path Item Component
const LearningPathItem: React.FC<{
  path: LearningPath;
  resources: LearningResource[];
}> = ({ path, resources }) => {
  const pathResources = resources.filter(r => path.resources.includes(r.id));
  const completedCount = pathResources.filter(r => path.completedResources.includes(r.id)).length;

  return (
    <div className="learning-path-item p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors">
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          <Target className="h-4 w-4 text-purple-400" />
          <span className="font-medium text-white text-sm">{path.name}</span>
        </div>
        <Badge variant="outline" className={getDifficultyColor(path.difficulty)}>
          {path.difficulty}
        </Badge>
      </div>
      
      <div className="text-sm text-gray-300 mb-2 line-clamp-2">
        {path.description}
      </div>
      
      <div className="flex items-center justify-between mb-2 text-xs text-gray-400">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>{path.estimatedDuration}h</span>
          </div>
          <div className="flex items-center gap-1">
            <BookOpen className="h-3 w-3" />
            <span>{pathResources.length} resources</span>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <span>{completedCount}/{pathResources.length} completed</span>
          <div className="w-16 bg-gray-700 rounded-full h-1">
            <div 
              className="bg-purple-500 h-1 rounded-full transition-all duration-300"
              style={{ width: `${path.progress}%` }}
            />
          </div>
        </div>
      </div>
      
      {path.skills.length > 0 && (
        <div className="flex items-center gap-1 flex-wrap">
          {path.skills.slice(0, 3).map(skill => (
            <Badge key={skill} variant="outline" className="text-xs h-4 px-1 text-purple-400 border-purple-600">
              {skill}
            </Badge>
          ))}
          {path.skills.length > 3 && (
            <span className="text-xs text-gray-500">+{path.skills.length - 3}</span>
          )}
        </div>
      )}
    </div>
  );
};

// Learning Resource Dialog
const LearningResourceDialog: React.FC<{
  resource: LearningResource;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStart: (id: string) => void;
  onBookmark: (id: string) => void;
  onComplete: (id: string) => void;
}> = ({ resource, open, onOpenChange, onStart, onBookmark, onComplete }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            {resource.title}
          </DialogTitle>
          <DialogDescription>
            {resource.description}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Implementation would include full resource details, learning objectives, etc. */}
          <div className="text-center py-8 text-gray-500">
            <p>Full resource details would be displayed here</p>
            <p className="text-sm mt-1">Including learning objectives, prerequisites, and related resources</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Add Resource Dialog
const AddResourceDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  context: ContextSnapshot;
  onAdded: (resource: LearningResource) => void;
}> = ({ open, onOpenChange, context, onAdded }) => {
  const [title, setTitle] = useState('');
  const [url, setUrl] = useState('');
  const [type, setType] = useState<LearningResource['type']>('article');

  const handleAdd = () => {
    const newResource: LearningResource = {
      id: `resource-${Date.now()}`,
      title,
      description: 'Custom learning resource',
      type,
      category: 'general',
      difficulty: 'intermediate',
      duration: 30,
      url,
      author: 'Custom',
      provider: 'Custom',
      rating: 0,
      reviewCount: 0,
      progress: 0,
      bookmarked: false,
      tags: ['custom'],
      learningObjectives: []
    };
    
    onAdded(newResource);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700">
        <DialogHeader>
          <DialogTitle>Add Learning Resource</DialogTitle>
          <DialogDescription>
            Add a custom learning resource to your collection
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <Input
            placeholder="Resource title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="bg-gray-800 border-gray-600"
          />
          
          <Input
            placeholder="URL (optional)"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className="bg-gray-800 border-gray-600"
          />
          
          <Select value={type} onValueChange={(value: LearningResource['type']) => setType(value)}>
            <SelectTrigger className="bg-gray-800 border-gray-600">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="article">Article</SelectItem>
              <SelectItem value="video">Video</SelectItem>
              <SelectItem value="course">Course</SelectItem>
              <SelectItem value="tutorial">Tutorial</SelectItem>
              <SelectItem value="documentation">Documentation</SelectItem>
              <SelectItem value="podcast">Podcast</SelectItem>
              <SelectItem value="book">Book</SelectItem>
              <SelectItem value="exercise">Exercise</SelectItem>
            </SelectContent>
          </Select>
          
          <div className="flex gap-2">
            <Button onClick={handleAdd} disabled={!title} className="flex-1">
              <Plus className="h-4 w-4 mr-2" />
              Add Resource
            </Button>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper functions
const getDifficultyColor = (difficulty: LearningResource['difficulty']) => {
  switch (difficulty) {
    case 'beginner': return 'text-green-400 bg-green-500/10 border-green-500/30';
    case 'intermediate': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
    case 'advanced': return 'text-orange-400 bg-orange-500/10 border-orange-500/30';
    case 'expert': return 'text-red-400 bg-red-500/10 border-red-500/30';
    default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
  }
};

const getTypeColor = (type: LearningResource['type']) => {
  switch (type) {
    case 'article': return 'text-blue-400 bg-blue-500/10 border-blue-500/30';
    case 'video': return 'text-red-400 bg-red-500/10 border-red-500/30';
    case 'course': return 'text-purple-400 bg-purple-500/10 border-purple-500/30';
    case 'tutorial': return 'text-green-400 bg-green-500/10 border-green-500/30';
    case 'documentation': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
    case 'podcast': return 'text-pink-400 bg-pink-500/10 border-pink-500/30';
    case 'book': return 'text-orange-400 bg-orange-500/10 border-orange-500/30';
    case 'exercise': return 'text-cyan-400 bg-cyan-500/10 border-cyan-500/30';
    default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
  }
};

// Helper function to generate mock resources
const generateMockResources = (context: ContextSnapshot, maxResources: number): LearningResource[] => {
  const mockResources: LearningResource[] = [];
  const now = Date.now();
  
  const titles = [
    'React Hooks Deep Dive',
    'Advanced TypeScript Patterns',
    'Node.js Performance Optimization',
    'CSS Grid and Flexbox Mastery',
    'Docker for Developers',
    'GraphQL Best Practices',
    'JavaScript Testing Strategies',
    'Web Security Fundamentals',
    'UI/UX Design Principles',
    'Database Design Patterns'
  ];
  
  const types: LearningResource['type'][] = ['article', 'video', 'course', 'tutorial', 'documentation', 'podcast', 'book', 'exercise'];
  const categories: LearningResource['category'][] = ['frontend', 'backend', 'devops', 'design', 'general', 'security', 'testing', 'architecture'];
  const difficulties: LearningResource['difficulty'][] = ['beginner', 'intermediate', 'advanced', 'expert'];
  const authors = ['John Doe', 'Jane Smith', 'Alex Johnson', 'Sam Wilson', 'Chris Lee'];
  const providers = ['YouTube', 'Udemy', 'MDN', 'Dev.to', 'Medium', 'Coursera', 'Pluralsight'];
  
  for (let i = 0; i < maxResources; i++) {
    const title = titles[Math.floor(Math.random() * titles.length)];
    const type = types[Math.floor(Math.random() * types.length)];
    const category = categories[Math.floor(Math.random() * categories.length)];
    const difficulty = difficulties[Math.floor(Math.random() * difficulties.length)];
    const author = authors[Math.floor(Math.random() * authors.length)];
    const provider = providers[Math.floor(Math.random() * providers.length)];
    const progress = Math.random() > 0.7 ? Math.floor(Math.random() * 101) : 0;
    
    mockResources.push({
      id: `resource-${i}`,
      title: `${title} #${i + 1}`,
      description: `Learn ${title.toLowerCase()} with practical examples and best practices.`,
      type,
      category,
      difficulty,
      duration: Math.floor(Math.random() * 180) + 15,
      url: `https://example.com/resource-${i}`,
      author,
      provider,
      rating: Math.random() * 2 + 3, // 3-5 stars
      reviewCount: Math.floor(Math.random() * 1000) + 10,
      progress,
      completedAt: progress === 100 ? now - (Math.random() * 30 * 24 * 60 * 60 * 1000) : undefined,
      startedAt: progress > 0 ? now - (Math.random() * 7 * 24 * 60 * 60 * 1000) : undefined,
      bookmarked: Math.random() > 0.8,
      tags: [category, difficulty, type].slice(0, Math.floor(Math.random() * 3) + 1),
      prerequisites: difficulty !== 'beginner' ? ['Basic JavaScript', 'HTML/CSS'] : [],
      learningObjectives: [
        `Understand ${title.toLowerCase()}`,
        'Apply concepts in real projects',
        'Follow best practices'
      ],
      lastAccessed: progress > 0 ? now - (Math.random() * 24 * 60 * 60 * 1000) : undefined
    });
  }
  
  return mockResources.sort((a, b) => (b.lastAccessed || 0) - (a.lastAccessed || 0));
};

// Helper function to generate mock learning paths
const generateMockLearningPaths = (resources: LearningResource[]): LearningPath[] => {
  const paths: LearningPath[] = [
    {
      id: 'path-1',
      name: 'Frontend Development Mastery',
      description: 'Complete path from beginner to advanced frontend developer',
      difficulty: 'intermediate',
      estimatedDuration: 120,
      resources: resources.filter(r => r.category === 'frontend').slice(0, 8).map(r => r.id),
      completedResources: resources.filter(r => r.category === 'frontend' && r.progress === 100).slice(0, 3).map(r => r.id),
      progress: 35,
      category: 'frontend',
      skills: ['React', 'TypeScript', 'CSS', 'Testing'],
      createdAt: Date.now() - 30 * 24 * 60 * 60 * 1000,
      lastActivity: Date.now() - 2 * 24 * 60 * 60 * 1000
    },
    {
      id: 'path-2',
      name: 'Backend Engineering Path',
      description: 'Server-side development with Node.js and databases',
      difficulty: 'advanced',
      estimatedDuration: 80,
      resources: resources.filter(r => r.category === 'backend').slice(0, 6).map(r => r.id),
      completedResources: resources.filter(r => r.category === 'backend' && r.progress === 100).slice(0, 2).map(r => r.id),
      progress: 25,
      category: 'backend',
      skills: ['Node.js', 'Databases', 'APIs', 'Docker'],
      createdAt: Date.now() - 20 * 24 * 60 * 60 * 1000,
      lastActivity: Date.now() - 5 * 24 * 60 * 60 * 1000
    }
  ];
  
  return paths;
};

export default LearningWidget;
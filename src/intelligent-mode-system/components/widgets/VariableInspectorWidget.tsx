import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog';
import { 
  Search, 
  RefreshCw, 
  Eye, 
  EyeOff,
  ChevronDown,
  ChevronRight,
  Edit3,
  Trash2,
  Plus,
  Variables,
  Clock,
  Star,
  AlertTriangle,
  CheckCircle,
  Copy,
  Filter,
  Settings,
  FileText,
  Code,
  Hash,
  Type,
  Activity,
  PinIcon,
  Unpin
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface Variable {
  id: string;
  name: string;
  value: any;
  type: string;
  scope: 'global' | 'local' | 'closure' | 'module';
  isWatched: boolean;
  isPinned: boolean;
  lastModified: number;
  accessCount: number;
  size?: number;
  isExpandable: boolean;
  isEditable: boolean;
  children?: Variable[];
  parent?: string;
  depth: number;
}

interface WatchExpression {
  id: string;
  expression: string;
  result: any;
  error?: string;
  type: string;
  isActive: boolean;
  createdAt: number;
  lastEvaluated: number;
}

interface VariableInspectorWidgetProps {
  context: ContextSnapshot;
  config?: {
    autoRefresh?: boolean;
    refreshInterval?: number;
    maxVariables?: number;
    showFilters?: boolean;
    defaultScope?: Variable['scope'];
    maxHeight?: string;
    enableEditing?: boolean;
  };
}

type ScopeFilter = 'all' | 'global' | 'local' | 'closure' | 'module';
type TypeFilter = 'all' | 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function' | 'undefined' | 'null';

export const VariableInspectorWidget: React.FC<VariableInspectorWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [variables, setVariables] = useState<Variable[]>([]);
  const [watchExpressions, setWatchExpressions] = useState<WatchExpression[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [scopeFilter, setScopeFilter] = useState<ScopeFilter>(config.defaultScope || 'all');
  const [typeFilter, setTypeFilter] = useState<TypeFilter>('all');
  const [showWatchedOnly, setShowWatchedOnly] = useState(false);
  const [showPinnedOnly, setShowPinnedOnly] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [expandedVariables, setExpandedVariables] = useState<Set<string>>(new Set());
  const [selectedVariable, setSelectedVariable] = useState<Variable | null>(null);
  const [editingVariable, setEditingVariable] = useState<Variable | null>(null);
  const [newWatchExpression, setNewWatchExpression] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'type' | 'scope' | 'modified'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const watchInputRef = useRef<HTMLInputElement>(null);

  // Load variables on component mount
  useEffect(() => {
    loadVariables();
  }, [context]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!config.autoRefresh) return;

    const interval = setInterval(() => {
      loadVariables(false);
      evaluateWatchExpressions();
    }, config.refreshInterval || 3000);

    return () => clearInterval(interval);
  }, [config.autoRefresh, config.refreshInterval]);

  const loadVariables = async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    
    // Simulate API call - would be replaced with actual variable inspection
    const mockVariables = generateMockVariables(context, config.maxVariables || 50);
    
    setTimeout(() => {
      setVariables(mockVariables);
      if (showLoading) setIsLoading(false);
    }, showLoading ? 300 : 0);
  };

  const evaluateWatchExpressions = () => {
    setWatchExpressions(prev => prev.map(watch => {
      if (!watch.isActive) return watch;
      
      try {
        // Simulate expression evaluation
        const result = evaluateMockExpression(watch.expression, variables);
        return {
          ...watch,
          result,
          error: undefined,
          type: typeof result,
          lastEvaluated: Date.now()
        };
      } catch (error) {
        return {
          ...watch,
          result: undefined,
          error: error instanceof Error ? error.message : 'Evaluation error',
          lastEvaluated: Date.now()
        };
      }
    }));
  };

  // Filter variables
  const filteredVariables = useMemo(() => {
    let filtered = variables;

    // Scope filter
    if (scopeFilter !== 'all') {
      filtered = filtered.filter(variable => variable.scope === scopeFilter);
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(variable => variable.type === typeFilter);
    }

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(variable => 
        variable.name.toLowerCase().includes(query) ||
        variable.type.toLowerCase().includes(query) ||
        (typeof variable.value === 'string' && variable.value.toLowerCase().includes(query))
      );
    }

    // Watch filter
    if (showWatchedOnly) {
      filtered = filtered.filter(variable => variable.isWatched);
    }

    // Pinned filter
    if (showPinnedOnly) {
      filtered = filtered.filter(variable => variable.isPinned);
    }

    // Sort variables
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'scope':
          comparison = a.scope.localeCompare(b.scope);
          break;
        case 'modified':
          comparison = b.lastModified - a.lastModified;
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [variables, scopeFilter, typeFilter, searchQuery, showWatchedOnly, showPinnedOnly, sortBy, sortOrder]);

  // Statistics
  const stats = useMemo(() => {
    const total = variables.length;
    const watched = variables.filter(v => v.isWatched).length;
    const pinned = variables.filter(v => v.isPinned).length;
    const modified = variables.filter(v => Date.now() - v.lastModified < 300000).length; // Last 5 minutes

    return { total, watched, pinned, modified };
  }, [variables]);

  const handleRefresh = () => {
    loadVariables();
    evaluateWatchExpressions();
  };

  const toggleVariableExpansion = (variableId: string) => {
    setExpandedVariables(prev => {
      const newSet = new Set(prev);
      if (newSet.has(variableId)) {
        newSet.delete(variableId);
      } else {
        newSet.add(variableId);
      }
      return newSet;
    });
  };

  const toggleWatch = (variableId: string) => {
    setVariables(prev => prev.map(variable => 
      variable.id === variableId 
        ? { ...variable, isWatched: !variable.isWatched }
        : variable
    ));
  };

  const togglePin = (variableId: string) => {
    setVariables(prev => prev.map(variable => 
      variable.id === variableId 
        ? { ...variable, isPinned: !variable.isPinned }
        : variable
    ));
  };

  const addWatchExpression = () => {
    if (!newWatchExpression.trim()) return;

    const watchExpr: WatchExpression = {
      id: `watch-${Date.now()}`,
      expression: newWatchExpression.trim(),
      result: undefined,
      type: 'unknown',
      isActive: true,
      createdAt: Date.now(),
      lastEvaluated: 0
    };

    setWatchExpressions(prev => [...prev, watchExpr]);
    setNewWatchExpression('');
    
    // Evaluate immediately
    setTimeout(() => evaluateWatchExpressions(), 100);
  };

  const removeWatchExpression = (watchId: string) => {
    setWatchExpressions(prev => prev.filter(watch => watch.id !== watchId));
  };

  const toggleWatchExpression = (watchId: string) => {
    setWatchExpressions(prev => prev.map(watch => 
      watch.id === watchId 
        ? { ...watch, isActive: !watch.isActive }
        : watch
    ));
  };

  const copyVariableValue = (variable: Variable) => {
    const value = typeof variable.value === 'object' 
      ? JSON.stringify(variable.value, null, 2)
      : String(variable.value);
    navigator.clipboard.writeText(value);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'string': return <FileText className="h-3 w-3" />;
      case 'number': return <Hash className="h-3 w-3" />;
      case 'boolean': return <CheckCircle className="h-3 w-3" />;
      case 'object': return <Code className="h-3 w-3" />;
      case 'array': return <Code className="h-3 w-3" />;
      case 'function': return <Activity className="h-3 w-3" />;
      default: return <Type className="h-3 w-3" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'string': return 'text-green-400';
      case 'number': return 'text-blue-400';
      case 'boolean': return 'text-purple-400';
      case 'object': return 'text-yellow-400';
      case 'array': return 'text-orange-400';
      case 'function': return 'text-red-400';
      case 'undefined': return 'text-gray-400';
      case 'null': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getScopeColor = (scope: Variable['scope']) => {
    switch (scope) {
      case 'global': return 'text-red-400 bg-red-500/10 border-red-500/30';
      case 'local': return 'text-green-400 bg-green-500/10 border-green-500/30';
      case 'closure': return 'text-blue-400 bg-blue-500/10 border-blue-500/30';
      case 'module': return 'text-purple-400 bg-purple-500/10 border-purple-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  return (
    <Card className="variable-inspector-widget h-full bg-gray-900 border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Variables className="h-5 w-5" />
            Variable Inspector
            <Badge variant="secondary" className="ml-2">
              {filteredVariables.length}/{variables.length}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-4 gap-2 mt-3">
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-blue-400">{stats.total}</div>
            <div className="text-xs text-gray-400">Total</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-yellow-400">{stats.watched}</div>
            <div className="text-xs text-gray-400">Watched</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-green-400">{stats.pinned}</div>
            <div className="text-xs text-gray-400">Pinned</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-orange-400">{stats.modified}</div>
            <div className="text-xs text-gray-400">Modified</div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0 flex-1 flex flex-col">
        {/* Watch Expressions */}
        <div className="px-4 pb-3 border-b border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium">Watch Expressions</h4>
            <Badge variant="outline" className="text-xs">
              {watchExpressions.length}
            </Badge>
          </div>
          
          {/* Add Watch Expression */}
          <div className="flex gap-2 mb-2">
            <Input
              ref={watchInputRef}
              placeholder="Enter expression to watch..."
              value={newWatchExpression}
              onChange={(e) => setNewWatchExpression(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  addWatchExpression();
                }
              }}
              className="flex-1 bg-gray-800 border-gray-600 h-8 text-sm"
            />
            <Button
              size="sm"
              onClick={addWatchExpression}
              disabled={!newWatchExpression.trim()}
              className="h-8 w-8 p-0"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>

          {/* Watch Expressions List */}
          {watchExpressions.length > 0 && (
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {watchExpressions.map(watch => (
                <WatchExpressionItem
                  key={watch.id}
                  watch={watch}
                  onToggle={() => toggleWatchExpression(watch.id)}
                  onRemove={() => removeWatchExpression(watch.id)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Filters */}
        {config.showFilters !== false && (
          <div className="px-4 pb-3 space-y-2">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search variables..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 h-8"
              />
            </div>

            {/* Filter Selects */}
            <div className="flex gap-2">
              <Select value={scopeFilter} onValueChange={(value: ScopeFilter) => setScopeFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Scope" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Scopes</SelectItem>
                  <SelectItem value="global">Global</SelectItem>
                  <SelectItem value="local">Local</SelectItem>
                  <SelectItem value="closure">Closure</SelectItem>
                  <SelectItem value="module">Module</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={(value: TypeFilter) => setTypeFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="string">String</SelectItem>
                  <SelectItem value="number">Number</SelectItem>
                  <SelectItem value="boolean">Boolean</SelectItem>
                  <SelectItem value="object">Object</SelectItem>
                  <SelectItem value="array">Array</SelectItem>
                  <SelectItem value="function">Function</SelectItem>
                  <SelectItem value="undefined">Undefined</SelectItem>
                  <SelectItem value="null">Null</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={(value: typeof sortBy) => setSortBy(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Sort" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="type">Type</SelectItem>
                  <SelectItem value="scope">Scope</SelectItem>
                  <SelectItem value="modified">Modified</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Filter Toggles */}
            <div className="flex gap-2">
              <Button
                size="sm"
                variant={showWatchedOnly ? "default" : "outline"}
                onClick={() => setShowWatchedOnly(!showWatchedOnly)}
                className="h-7 text-xs"
              >
                <Eye className="h-3 w-3 mr-1" />
                Watched
              </Button>
              <Button
                size="sm"
                variant={showPinnedOnly ? "default" : "outline"}
                onClick={() => setShowPinnedOnly(!showPinnedOnly)}
                className="h-7 text-xs"
              >
                <PinIcon className="h-3 w-3 mr-1" />
                Pinned
              </Button>
            </div>
          </div>
        )}

        {/* Variables List */}
        <div 
          className="variables-list flex-1 overflow-y-auto px-4"
          style={{ maxHeight: config.maxHeight || '400px' }}
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-400">Loading variables...</p>
            </div>
          ) : filteredVariables.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Variables className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No variables found</p>
              <p className="text-sm mt-1">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="space-y-1">
              {filteredVariables.map(variable => (
                <VariableItem
                  key={variable.id}
                  variable={variable}
                  isExpanded={expandedVariables.has(variable.id)}
                  onToggleExpansion={() => toggleVariableExpansion(variable.id)}
                  onToggleWatch={() => toggleWatch(variable.id)}
                  onTogglePin={() => togglePin(variable.id)}
                  onSelect={() => setSelectedVariable(variable)}
                  onEdit={() => setEditingVariable(variable)}
                  onCopy={() => copyVariableValue(variable)}
                  enableEditing={config.enableEditing}
                />
              ))}
            </div>
          )}
        </div>

        {/* Variable Details Modal */}
        {selectedVariable && (
          <VariableDetailsDialog
            variable={selectedVariable}
            open={!!selectedVariable}
            onOpenChange={(open) => !open && setSelectedVariable(null)}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Watch Expression Item Component
const WatchExpressionItem: React.FC<{
  watch: WatchExpression;
  onToggle: () => void;
  onRemove: () => void;
}> = ({ watch, onToggle, onRemove }) => {
  const hasError = !!watch.error;
  const hasResult = watch.result !== undefined;

  return (
    <div className="watch-expression p-2 bg-gray-800 rounded-md">
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <Button
            size="sm"
            variant="ghost"
            onClick={onToggle}
            className={`h-5 w-5 p-0 ${watch.isActive ? 'text-green-400' : 'text-gray-500'}`}
          >
            {watch.isActive ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
          </Button>
          <code className="text-xs font-mono text-blue-400 truncate flex-1">
            {watch.expression}
          </code>
        </div>
        <Button
          size="sm"
          variant="ghost"
          onClick={onRemove}
          className="h-5 w-5 p-0 text-red-400"
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>
      
      {watch.isActive && (
        <div className="text-xs">
          {hasError ? (
            <div className="text-red-400 font-mono">
              Error: {watch.error}
            </div>
          ) : hasResult ? (
            <div className="text-gray-300 font-mono">
              = {typeof watch.result === 'object' 
                ? JSON.stringify(watch.result, null, 2) 
                : String(watch.result)}
            </div>
          ) : (
            <div className="text-gray-500">
              Evaluating...
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Variable Item Component
const VariableItem: React.FC<{
  variable: Variable;
  isExpanded: boolean;
  onToggleExpansion: () => void;
  onToggleWatch: () => void;
  onTogglePin: () => void;
  onSelect: () => void;
  onEdit: () => void;
  onCopy: () => void;
  enableEditing?: boolean;
}> = ({ 
  variable, 
  isExpanded, 
  onToggleExpansion, 
  onToggleWatch, 
  onTogglePin, 
  onSelect, 
  onEdit, 
  onCopy,
  enableEditing 
}) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'string': return <FileText className="h-3 w-3" />;
      case 'number': return <Hash className="h-3 w-3" />;
      case 'boolean': return <CheckCircle className="h-3 w-3" />;
      case 'object': return <Code className="h-3 w-3" />;
      case 'array': return <Code className="h-3 w-3" />;
      case 'function': return <Activity className="h-3 w-3" />;
      default: return <Type className="h-3 w-3" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'string': return 'text-green-400';
      case 'number': return 'text-blue-400';
      case 'boolean': return 'text-purple-400';
      case 'object': return 'text-yellow-400';
      case 'array': return 'text-orange-400';
      case 'function': return 'text-red-400';
      case 'undefined': return 'text-gray-400';
      case 'null': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getScopeColor = (scope: Variable['scope']) => {
    switch (scope) {
      case 'global': return 'border-red-500/30 text-red-400';
      case 'local': return 'border-green-500/30 text-green-400';
      case 'closure': return 'border-blue-500/30 text-blue-400';
      case 'module': return 'border-purple-500/30 text-purple-400';
      default: return 'border-gray-500/30 text-gray-400';
    }
  };

  const formatValue = (value: any, type: string): string => {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    if (type === 'string') return `"${value}"`;
    if (type === 'object' || type === 'array') {
      if (Array.isArray(value)) return `Array(${value.length})`;
      return `Object(${Object.keys(value).length} keys)`;
    }
    if (type === 'function') return `ƒ ${value.name || 'anonymous'}()`;
    return String(value);
  };

  return (
    <div className="variable-item">
      <div className="p-2 hover:bg-gray-800 rounded cursor-pointer">
        <div className="flex items-center gap-2">
          {/* Expansion Toggle */}
          {variable.isExpandable && (
            <Button
              size="sm"
              variant="ghost"
              onClick={onToggleExpansion}
              className="h-4 w-4 p-0"
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}
          
          {/* Variable Info */}
          <div className="flex-1 min-w-0" onClick={onSelect}>
            <div className="flex items-center gap-2 mb-1">
              <div className={`flex items-center gap-1 ${getTypeColor(variable.type)}`}>
                {getTypeIcon(variable.type)}
                <span className="text-xs uppercase font-medium">{variable.type}</span>
              </div>
              
              <span className="font-mono text-white font-medium">
                {variable.name}
              </span>
              
              <Badge variant="outline" className={`text-xs h-4 px-1 ${getScopeColor(variable.scope)}`}>
                {variable.scope}
              </Badge>
              
              {variable.isWatched && (
                <Eye className="h-3 w-3 text-yellow-400" />
              )}
              
              {variable.isPinned && (
                <PinIcon className="h-3 w-3 text-blue-400" />
              )}
            </div>
            
            <div className="text-xs text-gray-400 font-mono">
              = {formatValue(variable.value, variable.type)}
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={onToggleWatch}
              className={`h-6 w-6 p-0 ${variable.isWatched ? 'text-yellow-400' : 'text-gray-500'}`}
            >
              <Eye className="h-3 w-3" />
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={onTogglePin}
              className={`h-6 w-6 p-0 ${variable.isPinned ? 'text-blue-400' : 'text-gray-500'}`}
            >
              <PinIcon className="h-3 w-3" />
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={onCopy}
              className="h-6 w-6 p-0 text-gray-500"
            >
              <Copy className="h-3 w-3" />
            </Button>
            
            {enableEditing && variable.isEditable && (
              <Button
                size="sm"
                variant="ghost"
                onClick={onEdit}
                className="h-6 w-6 p-0 text-gray-500"
              >
                <Edit3 className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
      
      {/* Children Variables */}
      {isExpanded && variable.children && (
        <div className="ml-6 border-l border-gray-700 pl-2">
          {variable.children.map(child => (
            <VariableItem
              key={child.id}
              variable={child}
              isExpanded={false}
              onToggleExpansion={() => {}}
              onToggleWatch={onToggleWatch}
              onTogglePin={onTogglePin}
              onSelect={onSelect}
              onEdit={onEdit}
              onCopy={onCopy}
              enableEditing={enableEditing}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Variable Details Dialog
const VariableDetailsDialog: React.FC<{
  variable: Variable;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}> = ({ variable, open, onOpenChange }) => {
  const formatValue = (value: any): string => {
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Variables className="h-5 w-5" />
            Variable Details: {variable.name}
          </DialogTitle>
          <DialogDescription>
            {variable.scope} scope • {variable.type} type
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Basic Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Properties</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Type:</span>
                  <span>{variable.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Scope:</span>
                  <span>{variable.scope}</span>
                </div>
                {variable.size && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">Size:</span>
                    <span>{variable.size} bytes</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-400">Access Count:</span>
                  <span>{variable.accessCount}</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-2">Status</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Watched:</span>
                  <span>{variable.isWatched ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Pinned:</span>
                  <span>{variable.isPinned ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Editable:</span>
                  <span>{variable.isEditable ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Last Modified:</span>
                  <span>{new Date(variable.lastModified).toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Value */}
          <div>
            <h4 className="text-sm font-medium mb-2">Value</h4>
            <pre className="text-sm text-gray-300 bg-gray-800 rounded p-3 overflow-auto max-h-60 font-mono">
              {formatValue(variable.value)}
            </pre>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper Functions
const generateMockVariables = (context: ContextSnapshot, maxVariables: number): Variable[] => {
  const mockVariables: Variable[] = [];
  const now = Date.now();
  
  const scopes: Variable['scope'][] = ['global', 'local', 'closure', 'module'];
  const types = ['string', 'number', 'boolean', 'object', 'array', 'function', 'undefined'];
  
  // Generate root variables
  for (let i = 0; i < Math.min(maxVariables, 30); i++) {
    const scope = scopes[Math.floor(Math.random() * scopes.length)];
    const type = types[Math.floor(Math.random() * types.length)];
    
    let value: any;
    let isExpandable = false;
    let children: Variable[] | undefined;
    
    switch (type) {
      case 'string':
        value = `"Sample string ${i}"`;
        break;
      case 'number':
        value = Math.floor(Math.random() * 1000);
        break;
      case 'boolean':
        value = Math.random() > 0.5;
        break;
      case 'object':
        value = { key1: 'value1', key2: 42, nested: { prop: true } };
        isExpandable = true;
        children = [
          {
            id: `var-${i}-child-1`,
            name: 'key1',
            value: 'value1',
            type: 'string',
            scope: 'local',
            isWatched: false,
            isPinned: false,
            lastModified: now - Math.random() * 3600000,
            accessCount: Math.floor(Math.random() * 10),
            isExpandable: false,
            isEditable: true,
            depth: 1
          },
          {
            id: `var-${i}-child-2`,
            name: 'key2',
            value: 42,
            type: 'number',
            scope: 'local',
            isWatched: false,
            isPinned: false,
            lastModified: now - Math.random() * 3600000,
            accessCount: Math.floor(Math.random() * 10),
            isExpandable: false,
            isEditable: true,
            depth: 1
          }
        ];
        break;
      case 'array':
        value = ['item1', 'item2', 'item3'];
        isExpandable = true;
        break;
      case 'function':
        value = { name: `function${i}`, length: 2 };
        break;
      case 'undefined':
        value = undefined;
        break;
    }
    
    mockVariables.push({
      id: `var-${i}`,
      name: `variable${i}`,
      value,
      type,
      scope,
      isWatched: Math.random() > 0.8,
      isPinned: Math.random() > 0.9,
      lastModified: now - Math.random() * 3600000,
      accessCount: Math.floor(Math.random() * 50),
      size: Math.floor(Math.random() * 1000) + 100,
      isExpandable,
      isEditable: type !== 'function' && scope !== 'global',
      children,
      depth: 0
    });
  }
  
  // Add context-based variables
  if (context.fileContext.hasErrors) {
    mockVariables.unshift({
      id: 'var-context-error',
      name: 'lastError',
      value: 'SyntaxError: Unexpected token',
      type: 'string',
      scope: 'global',
      isWatched: true,
      isPinned: true,
      lastModified: now,
      accessCount: 1,
      size: 30,
      isExpandable: false,
      isEditable: false,
      depth: 0
    });
  }

  return mockVariables.sort((a, b) => a.name.localeCompare(b.name));
};

const evaluateMockExpression = (expression: string, variables: Variable[]): any => {
  // Simple mock expression evaluation
  // In a real implementation, this would use a proper JavaScript evaluator
  
  // Check for variable references
  const variable = variables.find(v => expression.includes(v.name));
  if (variable) {
    if (expression === variable.name) {
      return variable.value;
    }
    if (expression.includes('.length') && Array.isArray(variable.value)) {
      return variable.value.length;
    }
    if (expression.includes('.toString()')) {
      return String(variable.value);
    }
  }
  
  // Simple arithmetic
  if (/^\d+\s*[\+\-\*\/]\s*\d+$/.test(expression)) {
    return eval(expression);
  }
  
  // Return the expression itself for complex cases
  return expression;
};

export default VariableInspectorWidget;
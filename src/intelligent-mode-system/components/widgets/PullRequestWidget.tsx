import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { Textarea } from '../../../components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog';
import { 
  GitPullRequest, 
  Search, 
  Filter,
  Clock, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  MessageSquare,
  GitBranch,
  Plus,
  Eye,
  ThumbsUp,
  ThumbsDown,
  RefreshCw,
  ExternalLink,
  User,
  Calendar,
  Target,
  FileText,
  Code,
  GitCommit
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface PullRequest {
  id: string;
  number: number;
  title: string;
  description: string;
  author: {
    id: string;
    name: string;
    avatar: string;
  };
  status: 'draft' | 'open' | 'merged' | 'closed' | 'conflict';
  priority: 'low' | 'medium' | 'high' | 'critical';
  reviewStatus: 'pending' | 'approved' | 'changes_requested' | 'conflicted';
  createdAt: number;
  updatedAt: number;
  sourceBranch: string;
  targetBranch: string;
  commits: number;
  additions: number;
  deletions: number;
  filesChanged: number;
  reviewers: Array<{
    id: string;
    name: string;
    status: 'pending' | 'approved' | 'changes_requested';
    reviewedAt?: number;
  }>;
  comments: number;
  approvals: number;
  labels: string[];
  milestone?: string;
  assignees: string[];
  checks: Array<{
    name: string;
    status: 'pending' | 'success' | 'failure' | 'skipped';
    url?: string;
  }>;
  mergeable: boolean;
  draft: boolean;
  conflictFiles?: string[];
}

interface PullRequestWidgetProps {
  context: ContextSnapshot;
  config?: {
    maxPRs?: number;
    showFilters?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number;
    defaultView?: 'list' | 'kanban';
    maxHeight?: string;
  };
}

type StatusFilter = 'all' | 'draft' | 'open' | 'merged' | 'closed' | 'conflict';
type ReviewStatusFilter = 'all' | 'pending' | 'approved' | 'changes_requested' | 'conflicted';
type ViewMode = 'list' | 'kanban';

export const PullRequestWidget: React.FC<PullRequestWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [pullRequests, setPullRequests] = useState<PullRequest[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [reviewStatusFilter, setReviewStatusFilter] = useState<ReviewStatusFilter>('all');
  const [viewMode, setViewMode] = useState<ViewMode>(config.defaultView || 'list');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPR, setSelectedPR] = useState<PullRequest | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  // Load pull requests on component mount
  useEffect(() => {
    loadPullRequests();
  }, [context]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!config.autoRefresh) return;

    const interval = setInterval(() => {
      loadPullRequests(false); // Don't show loading state for auto-refresh
    }, config.refreshInterval || 30000);

    return () => clearInterval(interval);
  }, [config.autoRefresh, config.refreshInterval]);

  const loadPullRequests = async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    
    // Simulate API call - would be replaced with actual PR fetching
    const mockPRs = generateMockPullRequests(context, config.maxPRs || 15);
    
    setTimeout(() => {
      setPullRequests(mockPRs);
      if (showLoading) setIsLoading(false);
    }, showLoading ? 800 : 0);
  };

  // Filter pull requests
  const filteredPRs = useMemo(() => {
    return pullRequests.filter(pr => {
      // Search filter
      const matchesSearch = !searchQuery || 
        pr.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pr.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pr.author.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pr.sourceBranch.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pr.targetBranch.toLowerCase().includes(searchQuery.toLowerCase());

      // Status filter
      const matchesStatus = statusFilter === 'all' || pr.status === statusFilter;

      // Review status filter
      const matchesReviewStatus = reviewStatusFilter === 'all' || pr.reviewStatus === reviewStatusFilter;

      return matchesSearch && matchesStatus && matchesReviewStatus;
    }).sort((a, b) => {
      // Sort by priority, then by updated date
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return b.updatedAt - a.updatedAt;
    });
  }, [pullRequests, searchQuery, statusFilter, reviewStatusFilter]);

  // Statistics
  const stats = useMemo(() => {
    const total = pullRequests.length;
    const open = pullRequests.filter(pr => pr.status === 'open').length;
    const draft = pullRequests.filter(pr => pr.status === 'draft').length;
    const needsReview = pullRequests.filter(pr => pr.reviewStatus === 'pending' && pr.status === 'open').length;
    const readyToMerge = pullRequests.filter(pr => pr.reviewStatus === 'approved' && pr.mergeable).length;

    return { total, open, draft, needsReview, readyToMerge };
  }, [pullRequests]);

  const handleRefresh = () => {
    loadPullRequests();
  };

  const handleStatusChange = (prId: string, newStatus: PullRequest['status']) => {
    setPullRequests(prev => prev.map(pr => 
      pr.id === prId 
        ? { ...pr, status: newStatus, updatedAt: Date.now() }
        : pr
    ));
  };

  const handleReview = (prId: string, decision: 'approved' | 'changes_requested') => {
    setPullRequests(prev => prev.map(pr => 
      pr.id === prId 
        ? { 
            ...pr, 
            reviewStatus: decision,
            approvals: decision === 'approved' ? pr.approvals + 1 : pr.approvals,
            updatedAt: Date.now()
          }
        : pr
    ));
  };

  const getStatusColor = (status: PullRequest['status']) => {
    switch (status) {
      case 'draft': return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
      case 'open': return 'text-green-500 bg-green-500/10 border-green-500/30';
      case 'merged': return 'text-purple-500 bg-purple-500/10 border-purple-500/30';
      case 'closed': return 'text-red-500 bg-red-500/10 border-red-500/30';
      case 'conflict': return 'text-orange-500 bg-orange-500/10 border-orange-500/30';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getReviewStatusColor = (reviewStatus: PullRequest['reviewStatus']) => {
    switch (reviewStatus) {
      case 'approved': return 'text-green-500';
      case 'changes_requested': return 'text-red-500';
      case 'conflicted': return 'text-orange-500';
      default: return 'text-yellow-500';
    }
  };

  const getPriorityColor = (priority: PullRequest['priority']) => {
    switch (priority) {
      case 'critical': return 'text-red-500 bg-red-500/10 border-red-500/30';
      case 'high': return 'text-orange-500 bg-orange-500/10 border-orange-500/30';
      case 'medium': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30';
      case 'low': return 'text-blue-500 bg-blue-500/10 border-blue-500/30';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
    }
  };

  return (
    <Card className="pull-request-widget h-full bg-gray-900 border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <GitPullRequest className="h-5 w-5" />
            Pull Requests
            <Badge variant="secondary" className="ml-2">
              {filteredPRs.length}/{pullRequests.length}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => setShowCreateDialog(true)}
              className="h-8"
            >
              <Plus className="h-3 w-3 mr-1" />
              New PR
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-5 gap-2 mt-3">
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-blue-400">{stats.total}</div>
            <div className="text-xs text-gray-400">Total</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-green-400">{stats.open}</div>
            <div className="text-xs text-gray-400">Open</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-gray-400">{stats.draft}</div>
            <div className="text-xs text-gray-400">Draft</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-yellow-400">{stats.needsReview}</div>
            <div className="text-xs text-gray-400">Review</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-purple-400">{stats.readyToMerge}</div>
            <div className="text-xs text-gray-400">Ready</div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0 flex-1 flex flex-col">
        {/* Filters */}
        {config.showFilters !== false && (
          <div className="px-4 pb-3 space-y-2">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search pull requests..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 h-8"
              />
            </div>

            {/* Filter Selects and View Toggle */}
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={(value: StatusFilter) => setStatusFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="merged">Merged</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                  <SelectItem value="conflict">Conflict</SelectItem>
                </SelectContent>
              </Select>

              <Select value={reviewStatusFilter} onValueChange={(value: ReviewStatusFilter) => setReviewStatusFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Review Status" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Reviews</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="changes_requested">Changes Requested</SelectItem>
                  <SelectItem value="conflicted">Conflicted</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex bg-gray-800 rounded border border-gray-600">
                <Button
                  size="sm"
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  onClick={() => setViewMode('list')}
                  className="h-8 px-2 rounded-r-none"
                >
                  <FileText className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant={viewMode === 'kanban' ? 'default' : 'ghost'}
                  onClick={() => setViewMode('kanban')}
                  className="h-8 px-2 rounded-l-none"
                >
                  <Target className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Pull Requests List/Kanban */}
        <div 
          className="pull-requests-content flex-1 overflow-y-auto px-4"
          style={{ maxHeight: config.maxHeight || '500px' }}
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-400">Loading pull requests...</p>
            </div>
          ) : filteredPRs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <GitPullRequest className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No pull requests found</p>
              <p className="text-sm mt-1">Try adjusting your search or filters</p>
            </div>
          ) : viewMode === 'list' ? (
            <div className="space-y-3">
              {filteredPRs.map(pr => (
                <PullRequestItem
                  key={pr.id}
                  pr={pr}
                  onStatusChange={handleStatusChange}
                  onReview={handleReview}
                  onSelect={() => setSelectedPR(pr)}
                />
              ))}
            </div>
          ) : (
            <PullRequestKanban
              pullRequests={filteredPRs}
              onStatusChange={handleStatusChange}
              onReview={handleReview}
              onSelect={setSelectedPR}
            />
          )}
        </div>

        {/* Pull Request Details Modal */}
        {selectedPR && (
          <PullRequestDetailsDialog
            pr={selectedPR}
            open={!!selectedPR}
            onOpenChange={(open) => !open && setSelectedPR(null)}
            onStatusChange={handleStatusChange}
            onReview={handleReview}
          />
        )}

        {/* Create PR Dialog */}
        {showCreateDialog && (
          <CreatePullRequestDialog
            open={showCreateDialog}
            onOpenChange={setShowCreateDialog}
            context={context}
            onCreated={(pr) => {
              setPullRequests(prev => [pr, ...prev]);
              setShowCreateDialog(false);
            }}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Pull Request Item Component
const PullRequestItem: React.FC<{
  pr: PullRequest;
  onStatusChange: (id: string, status: PullRequest['status']) => void;
  onReview: (id: string, decision: 'approved' | 'changes_requested') => void;
  onSelect: () => void;
}> = ({ pr, onStatusChange, onReview, onSelect }) => {
  const getStatusColor = (status: PullRequest['status']) => {
    switch (status) {
      case 'draft': return 'border-l-gray-500';
      case 'open': return 'border-l-green-500';
      case 'merged': return 'border-l-purple-500';
      case 'closed': return 'border-l-red-500';
      case 'conflict': return 'border-l-orange-500';
      default: return 'border-l-gray-500';
    }
  };

  const allChecksPass = pr.checks.every(check => check.status === 'success' || check.status === 'skipped');
  const hasFailingChecks = pr.checks.some(check => check.status === 'failure');

  return (
    <div className={`pr-item p-3 bg-gray-800 rounded-lg border-l-4 ${getStatusColor(pr.status)} hover:bg-gray-750 transition-colors`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="text-lg">{pr.draft ? '📝' : '🔀'}</span>
            <span className="font-medium text-white text-sm">
              #{pr.number} {pr.title}
            </span>
          </div>
          
          <div className="flex items-center gap-1 ml-auto">
            <Badge variant="outline" className={getPriorityColor(pr.priority)}>
              {pr.priority}
            </Badge>
            
            {pr.status === 'conflict' && (
              <Badge variant="outline" className="border-orange-500 text-orange-400">
                <AlertCircle className="h-3 w-3 mr-1" />
                Conflicts
              </Badge>
            )}
            
            {pr.reviewStatus === 'approved' && pr.mergeable && (
              <Badge variant="outline" className="border-green-500 text-green-400">
                <CheckCircle className="h-3 w-3 mr-1" />
                Ready
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Branch Info */}
      <div className="flex items-center gap-2 mb-2 text-xs text-gray-400">
        <GitBranch className="h-3 w-3" />
        <span className="font-mono">{pr.sourceBranch}</span>
        <span>→</span>
        <span className="font-mono">{pr.targetBranch}</span>
        
        <div className="flex items-center gap-3 ml-auto">
          <div className="flex items-center gap-1">
            <GitCommit className="h-3 w-3" />
            <span>{pr.commits}</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-green-400">+{pr.additions}</span>
            <span className="text-red-400">-{pr.deletions}</span>
          </div>
          <div className="flex items-center gap-1">
            <FileText className="h-3 w-3" />
            <span>{pr.filesChanged}</span>
          </div>
        </div>
      </div>

      {/* Author and Reviewers */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2 text-xs text-gray-400">
          <User className="h-3 w-3" />
          <span>{pr.author.name}</span>
          <span>•</span>
          <Clock className="h-3 w-3" />
          <span>{new Date(pr.createdAt).toLocaleDateString()}</span>
        </div>
        
        <div className="flex items-center gap-2">
          {pr.comments > 0 && (
            <div className="flex items-center gap-1 text-xs text-gray-400">
              <MessageSquare className="h-3 w-3" />
              <span>{pr.comments}</span>
            </div>
          )}
          
          <div className="flex items-center gap-1 text-xs text-gray-400">
            <Eye className="h-3 w-3" />
            <span>{pr.approvals}/{pr.reviewers.length}</span>
          </div>
        </div>
      </div>

      {/* Checks Status */}
      {pr.checks.length > 0 && (
        <div className="flex items-center gap-2 mb-2">
          <div className="flex items-center gap-1 text-xs">
            {allChecksPass ? (
              <CheckCircle className="h-3 w-3 text-green-500" />
            ) : hasFailingChecks ? (
              <XCircle className="h-3 w-3 text-red-500" />
            ) : (
              <Clock className="h-3 w-3 text-yellow-500" />
            )}
            <span className="text-gray-400">
              {pr.checks.filter(c => c.status === 'success').length}/{pr.checks.length} checks passing
            </span>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {pr.status === 'open' && pr.reviewStatus === 'pending' && (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  onReview(pr.id, 'approved');
                }}
                className="h-6 text-xs border-green-600 text-green-400 hover:bg-green-600/10"
              >
                <ThumbsUp className="h-3 w-3 mr-1" />
                Approve
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  onReview(pr.id, 'changes_requested');
                }}
                className="h-6 text-xs border-red-600 text-red-400 hover:bg-red-600/10"
              >
                <ThumbsDown className="h-3 w-3 mr-1" />
                Request Changes
              </Button>
            </>
          )}
          
          {pr.reviewStatus === 'approved' && pr.mergeable && pr.status === 'open' && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onStatusChange(pr.id, 'merged');
              }}
              className="h-6 text-xs border-purple-600 text-purple-400 hover:bg-purple-600/10"
            >
              <GitPullRequest className="h-3 w-3 mr-1" />
              Merge
            </Button>
          )}
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={onSelect}
          className="h-auto p-0 text-xs text-blue-400 hover:text-blue-300"
        >
          View Details
        </Button>
      </div>
    </div>
  );
};

// Kanban View Component
const PullRequestKanban: React.FC<{
  pullRequests: PullRequest[];
  onStatusChange: (id: string, status: PullRequest['status']) => void;
  onReview: (id: string, decision: 'approved' | 'changes_requested') => void;
  onSelect: (pr: PullRequest) => void;
}> = ({ pullRequests, onStatusChange, onReview, onSelect }) => {
  const columns = [
    { id: 'draft', title: 'Draft', status: 'draft' as const },
    { id: 'open', title: 'Open', status: 'open' as const },
    { id: 'review', title: 'In Review', status: 'open' as const, filter: (pr: PullRequest) => pr.reviewStatus === 'pending' },
    { id: 'approved', title: 'Approved', status: 'open' as const, filter: (pr: PullRequest) => pr.reviewStatus === 'approved' },
    { id: 'merged', title: 'Merged', status: 'merged' as const },
  ];

  return (
    <div className="kanban-board flex gap-4 overflow-x-auto pb-4">
      {columns.map(column => {
        const columnPRs = pullRequests.filter(pr => {
          if (column.filter) {
            return pr.status === column.status && column.filter(pr);
          }
          return pr.status === column.status;
        });

        return (
          <div key={column.id} className="kanban-column min-w-64 bg-gray-800 rounded-lg p-3">
            <div className="column-header flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-gray-300">{column.title}</h4>
              <Badge variant="outline" className="text-xs">
                {columnPRs.length}
              </Badge>
            </div>
            
            <div className="column-content space-y-2">
              {columnPRs.map(pr => (
                <div
                  key={pr.id}
                  className="kanban-card p-2 bg-gray-900 rounded border border-gray-700 hover:border-gray-600 cursor-pointer transition-colors"
                  onClick={() => onSelect(pr)}
                >
                  <div className="text-sm font-medium text-white mb-1 line-clamp-2">
                    #{pr.number} {pr.title}
                  </div>
                  <div className="text-xs text-gray-400 mb-2">
                    by {pr.author.name}
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-1">
                      <span className="text-green-400">+{pr.additions}</span>
                      <span className="text-red-400">-{pr.deletions}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MessageSquare className="h-3 w-3" />
                      <span>{pr.comments}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};

// Details Dialog and Create Dialog components would be implemented here
// For brevity, I'll include simplified versions

const PullRequestDetailsDialog: React.FC<{
  pr: PullRequest;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStatusChange: (id: string, status: PullRequest['status']) => void;
  onReview: (id: string, decision: 'approved' | 'changes_requested') => void;
}> = ({ pr, open, onOpenChange, onStatusChange, onReview }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitPullRequest className="h-5 w-5" />
            #{pr.number} {pr.title}
          </DialogTitle>
          <DialogDescription>
            {pr.author.name} wants to merge {pr.commits} commits from {pr.sourceBranch} into {pr.targetBranch}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="text-sm text-gray-300">{pr.description}</div>
          
          {/* Implementation would include full PR details, comments, files changed, etc. */}
          <div className="text-center py-8 text-gray-500">
            <p>Full PR details would be displayed here</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const CreatePullRequestDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  context: ContextSnapshot;
  onCreated: (pr: PullRequest) => void;
}> = ({ open, onOpenChange, context, onCreated }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');

  const handleCreate = () => {
    // Implementation would create actual PR
    const newPR: PullRequest = {
      id: `pr-${Date.now()}`,
      number: Math.floor(Math.random() * 1000) + 1,
      title,
      description,
      author: { id: 'current-user', name: 'Current User', avatar: '' },
      status: 'draft',
      priority: 'medium',
      reviewStatus: 'pending',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      sourceBranch: 'feature/new-feature',
      targetBranch: 'main',
      commits: 1,
      additions: 50,
      deletions: 10,
      filesChanged: 3,
      reviewers: [],
      comments: 0,
      approvals: 0,
      labels: [],
      assignees: [],
      checks: [],
      mergeable: true,
      draft: true
    };
    
    onCreated(newPR);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700">
        <DialogHeader>
          <DialogTitle>Create Pull Request</DialogTitle>
          <DialogDescription>
            Create a new pull request for your changes
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <Input
            placeholder="Pull request title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="bg-gray-800 border-gray-600"
          />
          <Textarea
            placeholder="Describe your changes..."
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="bg-gray-800 border-gray-600 min-h-[100px]"
          />
          
          <div className="flex gap-2">
            <Button onClick={handleCreate} disabled={!title} className="flex-1">
              Create Pull Request
            </Button>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper function to generate mock pull requests
const generateMockPullRequests = (context: ContextSnapshot, maxPRs: number): PullRequest[] => {
  const mockPRs: PullRequest[] = [];
  const now = Date.now();
  
  const titles = [
    'Add user authentication system',
    'Fix memory leak in data processing',
    'Implement dark mode toggle',
    'Update dependencies to latest versions',
    'Add unit tests for API endpoints',
    'Optimize database queries',
    'Fix responsive design issues',
    'Add error handling for file uploads',
    'Implement search functionality',
    'Update documentation'
  ];
  
  const authors = ['Alice Smith', 'Bob Johnson', 'Carol Davis', 'David Wilson', 'Eve Brown'];
  const branches = ['feature/auth', 'bugfix/memory-leak', 'feature/dark-mode', 'chore/deps'];
  
  for (let i = 0; i < maxPRs; i++) {
    const author = authors[Math.floor(Math.random() * authors.length)];
    const title = titles[Math.floor(Math.random() * titles.length)];
    const status = (['draft', 'open', 'merged', 'closed'] as const)[Math.floor(Math.random() * 4)];
    const reviewStatus = (['pending', 'approved', 'changes_requested'] as const)[Math.floor(Math.random() * 3)];
    
    mockPRs.push({
      id: `pr-${i}`,
      number: 100 + i,
      title: `${title} (#${100 + i})`,
      description: `This PR ${title.toLowerCase()}. It includes comprehensive changes and improvements.`,
      author: { id: `user-${i}`, name: author, avatar: '' },
      status,
      priority: (['low', 'medium', 'high', 'critical'] as const)[Math.floor(Math.random() * 4)],
      reviewStatus,
      createdAt: now - (Math.random() * 7 * 24 * 60 * 60 * 1000), // Last 7 days
      updatedAt: now - (Math.random() * 24 * 60 * 60 * 1000), // Last 24 hours
      sourceBranch: branches[Math.floor(Math.random() * branches.length)],
      targetBranch: 'main',
      commits: Math.floor(Math.random() * 10) + 1,
      additions: Math.floor(Math.random() * 500) + 10,
      deletions: Math.floor(Math.random() * 200) + 5,
      filesChanged: Math.floor(Math.random() * 20) + 1,
      reviewers: [
        { id: 'reviewer-1', name: 'John Reviewer', status: reviewStatus === 'approved' ? 'approved' : 'pending' }
      ],
      comments: Math.floor(Math.random() * 10),
      approvals: reviewStatus === 'approved' ? 1 : 0,
      labels: ['frontend', 'enhancement'],
      assignees: [author],
      checks: [
        { name: 'CI Build', status: Math.random() > 0.2 ? 'success' : 'failure' },
        { name: 'Tests', status: Math.random() > 0.1 ? 'success' : 'failure' },
        { name: 'Lint', status: 'success' }
      ],
      mergeable: status === 'open' && reviewStatus === 'approved',
      draft: status === 'draft'
    });
  }
  
  return mockPRs.sort((a, b) => b.updatedAt - a.updatedAt);
};

// Helper functions for colors would be defined here...
const getPriorityColor = (priority: PullRequest['priority']) => {
  switch (priority) {
    case 'critical': return 'text-red-500 bg-red-500/10 border-red-500/30';
    case 'high': return 'text-orange-500 bg-orange-500/10 border-orange-500/30';
    case 'medium': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30';
    case 'low': return 'text-blue-500 bg-blue-500/10 border-blue-500/30';
    default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
  }
};

export default PullRequestWidget;
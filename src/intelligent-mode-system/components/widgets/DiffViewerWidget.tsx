import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog';
import { 
  GitCompare, 
  Search, 
  RefreshCw, 
  Eye,
  EyeOff,
  ChevronDown,
  ChevronRight,
  Copy,
  Download,
  FileText,
  Plus,
  Minus,
  RotateCcw,
  Check,
  X,
  Clock,
  GitBranch,
  GitCommit,
  Filter,
  Settings,
  Maximize2,
  ArrowUpDown,
  Split,
  Layers,
  Hash
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface DiffLine {
  type: 'unchanged' | 'added' | 'removed' | 'modified';
  oldLineNumber?: number;
  newLineNumber?: number;
  content: string;
  highlight?: boolean;
}

interface DiffFile {
  id: string;
  path: string;
  oldPath?: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed' | 'copied';
  additions: number;
  deletions: number;
  binary: boolean;
  lines: DiffLine[];
  hunks: DiffHunk[];
}

interface DiffHunk {
  id: string;
  oldStart: number;
  oldLines: number;
  newStart: number;
  newLines: number;
  header: string;
  lines: DiffLine[];
}

interface DiffViewerWidgetProps {
  context: ContextSnapshot;
  config?: {
    showLineNumbers?: boolean;
    showWhitespace?: boolean;
    contextLines?: number;
    highlightChanges?: boolean;
    splitView?: boolean;
    maxHeight?: string;
    enableComments?: boolean;
  };
}

type ViewMode = 'unified' | 'split' | 'inline';
type FilterType = 'all' | 'added' | 'modified' | 'deleted' | 'renamed';

export const DiffViewerWidget: React.FC<DiffViewerWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [diffFiles, setDiffFiles] = useState<DiffFile[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<FilterType>('all');
  const [viewMode, setViewMode] = useState<ViewMode>(config.splitView ? 'split' : 'unified');
  const [showLineNumbers, setShowLineNumbers] = useState(config.showLineNumbers ?? true);
  const [showWhitespace, setShowWhitespace] = useState(config.showWhitespace ?? false);
  const [contextLines, setContextLines] = useState(config.contextLines ?? 3);
  const [isLoading, setIsLoading] = useState(true);
  const [expandedFiles, setExpandedFiles] = useState<Set<string>>(new Set());
  const [selectedFile, setSelectedFile] = useState<DiffFile | null>(null);
  const [collapsedHunks, setCollapsedHunks] = useState<Set<string>>(new Set());

  const diffContainerRef = useRef<HTMLDivElement>(null);

  // Load diff data on component mount
  useEffect(() => {
    loadDiffData();
  }, [context]);

  const loadDiffData = async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    
    // Simulate API call - would be replaced with actual Git diff fetching
    const mockDiffs = generateMockDiffFiles(context);
    
    setTimeout(() => {
      setDiffFiles(mockDiffs);
      // Expand first file by default
      if (mockDiffs.length > 0) {
        setExpandedFiles(new Set([mockDiffs[0].id]));
      }
      if (showLoading) setIsLoading(false);
    }, showLoading ? 500 : 0);
  };

  // Filter diff files
  const filteredFiles = useMemo(() => {
    let filtered = diffFiles;

    // Filter by status
    if (filterType !== 'all') {
      filtered = filtered.filter(file => file.status === filterType);
    }

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(file => 
        file.path.toLowerCase().includes(query) ||
        file.lines.some(line => line.content.toLowerCase().includes(query))
      );
    }

    return filtered;
  }, [diffFiles, filterType, searchQuery]);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalFiles = diffFiles.length;
    const totalAdditions = diffFiles.reduce((sum, file) => sum + file.additions, 0);
    const totalDeletions = diffFiles.reduce((sum, file) => sum + file.deletions, 0);
    const modifiedFiles = diffFiles.filter(f => f.status === 'modified').length;

    return { totalFiles, totalAdditions, totalDeletions, modifiedFiles };
  }, [diffFiles]);

  const handleRefresh = () => {
    loadDiffData();
  };

  const toggleFileExpansion = (fileId: string) => {
    setExpandedFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(fileId)) {
        newSet.delete(fileId);
      } else {
        newSet.add(fileId);
      }
      return newSet;
    });
  };

  const toggleHunkCollapse = (hunkId: string) => {
    setCollapsedHunks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(hunkId)) {
        newSet.delete(hunkId);
      } else {
        newSet.add(hunkId);
      }
      return newSet;
    });
  };

  const copyDiff = (file: DiffFile) => {
    const diffText = file.lines.map(line => {
      const prefix = line.type === 'added' ? '+' : line.type === 'removed' ? '-' : ' ';
      return `${prefix}${line.content}`;
    }).join('\n');
    
    navigator.clipboard.writeText(diffText);
  };

  const exportDiff = () => {
    const diffContent = filteredFiles.map(file => {
      const header = `diff --git a/${file.path} b/${file.path}\n`;
      const content = file.lines.map(line => {
        const prefix = line.type === 'added' ? '+' : line.type === 'removed' ? '-' : ' ';
        return `${prefix}${line.content}`;
      }).join('\n');
      
      return header + content;
    }).join('\n\n');

    const blob = new Blob([diffContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `changes-${Date.now()}.diff`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getStatusColor = (status: DiffFile['status']) => {
    switch (status) {
      case 'added': return 'text-green-400 bg-green-500/10 border-green-500/30';
      case 'deleted': return 'text-red-400 bg-red-500/10 border-red-500/30';
      case 'modified': return 'text-blue-400 bg-blue-500/10 border-blue-500/30';
      case 'renamed': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
      case 'copied': return 'text-purple-400 bg-purple-500/10 border-purple-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: DiffFile['status']) => {
    switch (status) {
      case 'added': return <Plus className="h-3 w-3" />;
      case 'deleted': return <Minus className="h-3 w-3" />;
      case 'modified': return <FileText className="h-3 w-3" />;
      case 'renamed': return <RotateCcw className="h-3 w-3" />;
      case 'copied': return <Copy className="h-3 w-3" />;
      default: return <FileText className="h-3 w-3" />;
    }
  };

  return (
    <Card className="diff-viewer-widget h-full bg-gray-900 border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <GitCompare className="h-5 w-5" />
            Diff Viewer
            <Badge variant="secondary" className="ml-2">
              {filteredFiles.length}/{diffFiles.length} files
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            
            <Button 
              size="sm" 
              variant="outline" 
              onClick={exportDiff}
              disabled={filteredFiles.length === 0}
              className="h-8 w-8 p-0"
            >
              <Download className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-4 gap-2 mt-3">
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-blue-400">{stats.totalFiles}</div>
            <div className="text-xs text-gray-400">Files</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-green-400">+{stats.totalAdditions}</div>
            <div className="text-xs text-gray-400">Added</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-red-400">-{stats.totalDeletions}</div>
            <div className="text-xs text-gray-400">Deleted</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-yellow-400">{stats.modifiedFiles}</div>
            <div className="text-xs text-gray-400">Modified</div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0 flex-1 flex flex-col">
        {/* Controls */}
        <div className="px-4 pb-3 space-y-2 border-b border-gray-700">
          {/* Search and Filter */}
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search files and changes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 h-8"
              />
            </div>
            
            <Select value={filterType} onValueChange={(value: FilterType) => setFilterType(value)}>
              <SelectTrigger className="w-32 bg-gray-800 border-gray-600 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                <SelectItem value="all">All Files</SelectItem>
                <SelectItem value="added">Added</SelectItem>
                <SelectItem value="modified">Modified</SelectItem>
                <SelectItem value="deleted">Deleted</SelectItem>
                <SelectItem value="renamed">Renamed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* View Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Select value={viewMode} onValueChange={(value: ViewMode) => setViewMode(value)}>
                <SelectTrigger className="w-24 bg-gray-800 border-gray-600 h-7">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="unified">Unified</SelectItem>
                  <SelectItem value="split">Split</SelectItem>
                  <SelectItem value="inline">Inline</SelectItem>
                </SelectContent>
              </Select>

              <Button
                size="sm"
                variant={showLineNumbers ? "default" : "outline"}
                onClick={() => setShowLineNumbers(!showLineNumbers)}
                className="h-7 text-xs"
              >
                <Hash className="h-3 w-3 mr-1" />
                Numbers
              </Button>

              <Button
                size="sm"
                variant={showWhitespace ? "default" : "outline"}
                onClick={() => setShowWhitespace(!showWhitespace)}
                className="h-7 text-xs"
              >
                <Eye className="h-3 w-3 mr-1" />
                Whitespace
              </Button>
            </div>

            <div className="flex items-center gap-2 text-xs text-gray-400">
              <span>Context: {contextLines} lines</span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setContextLines(Math.max(1, contextLines - 1))}
                className="h-6 w-6 p-0"
              >
                <Minus className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setContextLines(contextLines + 1)}
                className="h-6 w-6 p-0"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Diff Files List */}
        <div 
          ref={diffContainerRef}
          className="diff-files-list flex-1 overflow-y-auto"
          style={{ maxHeight: config.maxHeight || '500px' }}
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-400">Loading diff...</p>
            </div>
          ) : filteredFiles.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <GitCompare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No changes found</p>
              <p className="text-sm mt-1">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="space-y-2 p-4">
              {filteredFiles.map(file => (
                <DiffFileItem
                  key={file.id}
                  file={file}
                  isExpanded={expandedFiles.has(file.id)}
                  onToggleExpansion={() => toggleFileExpansion(file.id)}
                  onSelect={() => setSelectedFile(file)}
                  onCopy={() => copyDiff(file)}
                  viewMode={viewMode}
                  showLineNumbers={showLineNumbers}
                  showWhitespace={showWhitespace}
                  contextLines={contextLines}
                  collapsedHunks={collapsedHunks}
                  onToggleHunk={toggleHunkCollapse}
                />
              ))}
            </div>
          )}
        </div>

        {/* File Details Modal */}
        {selectedFile && (
          <DiffFileDetailsDialog
            file={selectedFile}
            open={!!selectedFile}
            onOpenChange={(open) => !open && setSelectedFile(null)}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Diff File Item Component
const DiffFileItem: React.FC<{
  file: DiffFile;
  isExpanded: boolean;
  onToggleExpansion: () => void;
  onSelect: () => void;
  onCopy: () => void;
  viewMode: ViewMode;
  showLineNumbers: boolean;
  showWhitespace: boolean;
  contextLines: number;
  collapsedHunks: Set<string>;
  onToggleHunk: (hunkId: string) => void;
}> = ({ 
  file, 
  isExpanded, 
  onToggleExpansion, 
  onSelect, 
  onCopy,
  viewMode,
  showLineNumbers,
  showWhitespace,
  contextLines,
  collapsedHunks,
  onToggleHunk
}) => {
  const getStatusColor = (status: DiffFile['status']) => {
    switch (status) {
      case 'added': return 'border-l-green-500';
      case 'deleted': return 'border-l-red-500';
      case 'modified': return 'border-l-blue-500';
      case 'renamed': return 'border-l-yellow-500';
      case 'copied': return 'border-l-purple-500';
      default: return 'border-l-gray-500';
    }
  };

  const getStatusIcon = (status: DiffFile['status']) => {
    switch (status) {
      case 'added': return <Plus className="h-4 w-4 text-green-500" />;
      case 'deleted': return <Minus className="h-4 w-4 text-red-500" />;
      case 'modified': return <FileText className="h-4 w-4 text-blue-500" />;
      case 'renamed': return <RotateCcw className="h-4 w-4 text-yellow-500" />;
      case 'copied': return <Copy className="h-4 w-4 text-purple-500" />;
      default: return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className={`diff-file-item bg-gray-800 rounded-lg border-l-4 ${getStatusColor(file.status)}`}>
      {/* File Header */}
      <div className="p-3 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <Button
              size="sm"
              variant="ghost"
              onClick={onToggleExpansion}
              className="h-6 w-6 p-0"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
            
            {getStatusIcon(file.status)}
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <span className="font-mono text-white font-medium truncate">
                  {file.path}
                </span>
                {file.oldPath && file.oldPath !== file.path && (
                  <span className="text-xs text-gray-400">
                    ← {file.oldPath}
                  </span>
                )}
              </div>
              <div className="flex items-center gap-4 mt-1 text-xs text-gray-400">
                <span className="capitalize">{file.status}</span>
                {file.binary ? (
                  <span>Binary file</span>
                ) : (
                  <>
                    <span className="text-green-400">+{file.additions}</span>
                    <span className="text-red-400">-{file.deletions}</span>
                    <span>{file.lines.length} lines</span>
                  </>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={onCopy}
              className="h-6 w-6 p-0"
            >
              <Copy className="h-3 w-3" />
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={onSelect}
              className="h-6 w-6 p-0"
            >
              <Maximize2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* File Content */}
      {isExpanded && !file.binary && (
        <div className="diff-content">
          {file.hunks.length > 0 ? (
            file.hunks.map(hunk => (
              <DiffHunkItem
                key={hunk.id}
                hunk={hunk}
                isCollapsed={collapsedHunks.has(hunk.id)}
                onToggle={() => onToggleHunk(hunk.id)}
                viewMode={viewMode}
                showLineNumbers={showLineNumbers}
                showWhitespace={showWhitespace}
              />
            ))
          ) : (
            <DiffLinesView
              lines={file.lines}
              viewMode={viewMode}
              showLineNumbers={showLineNumbers}
              showWhitespace={showWhitespace}
            />
          )}
        </div>
      )}
    </div>
  );
};

// Diff Hunk Item Component
const DiffHunkItem: React.FC<{
  hunk: DiffHunk;
  isCollapsed: boolean;
  onToggle: () => void;
  viewMode: ViewMode;
  showLineNumbers: boolean;
  showWhitespace: boolean;
}> = ({ hunk, isCollapsed, onToggle, viewMode, showLineNumbers, showWhitespace }) => {
  return (
    <div className="diff-hunk">
      {/* Hunk Header */}
      <div 
        className="hunk-header bg-gray-700 px-3 py-2 cursor-pointer hover:bg-gray-600 transition-colors"
        onClick={onToggle}
      >
        <div className="flex items-center gap-2">
          {isCollapsed ? (
            <ChevronRight className="h-3 w-3" />
          ) : (
            <ChevronDown className="h-3 w-3" />
          )}
          <code className="text-xs text-blue-400 font-mono">
            {hunk.header}
          </code>
          <span className="text-xs text-gray-400">
            ({hunk.lines.length} lines)
          </span>
        </div>
      </div>

      {/* Hunk Content */}
      {!isCollapsed && (
        <DiffLinesView
          lines={hunk.lines}
          viewMode={viewMode}
          showLineNumbers={showLineNumbers}
          showWhitespace={showWhitespace}
          startOldLine={hunk.oldStart}
          startNewLine={hunk.newStart}
        />
      )}
    </div>
  );
};

// Diff Lines View Component
const DiffLinesView: React.FC<{
  lines: DiffLine[];
  viewMode: ViewMode;
  showLineNumbers: boolean;
  showWhitespace: boolean;
  startOldLine?: number;
  startNewLine?: number;
}> = ({ lines, viewMode, showLineNumbers, showWhitespace, startOldLine = 1, startNewLine = 1 }) => {
  const getLineBackground = (type: DiffLine['type']) => {
    switch (type) {
      case 'added': return 'bg-green-500/10 border-l-2 border-l-green-500';
      case 'removed': return 'bg-red-500/10 border-l-2 border-l-red-500';
      case 'modified': return 'bg-blue-500/10 border-l-2 border-l-blue-500';
      default: return '';
    }
  };

  const getLinePrefix = (type: DiffLine['type']) => {
    switch (type) {
      case 'added': return '+';
      case 'removed': return '-';
      default: return ' ';
    }
  };

  const formatContent = (content: string) => {
    if (!showWhitespace) return content;
    return content.replace(/\t/g, '→   ').replace(/ /g, '·');
  };

  if (viewMode === 'split') {
    return (
      <div className="diff-split-view grid grid-cols-2 gap-0 border-t border-gray-700">
        {/* Old Version */}
        <div className="old-version border-r border-gray-700">
          <div className="bg-gray-800 px-2 py-1 text-xs text-gray-400 border-b border-gray-700">
            Original
          </div>
          <div className="diff-lines font-mono text-sm">
            {lines.filter(line => line.type !== 'added').map((line, index) => (
              <div key={index} className={`diff-line flex ${getLineBackground(line.type)}`}>
                {showLineNumbers && (
                  <div className="line-number w-12 px-2 py-0.5 text-xs text-gray-500 select-none bg-gray-800 border-r border-gray-700">
                    {line.oldLineNumber || ''}
                  </div>
                )}
                <div className="line-prefix w-4 px-1 py-0.5 text-xs text-gray-500 select-none">
                  {getLinePrefix(line.type)}
                </div>
                <div className="line-content flex-1 px-2 py-0.5 whitespace-pre">
                  {formatContent(line.content)}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* New Version */}
        <div className="new-version">
          <div className="bg-gray-800 px-2 py-1 text-xs text-gray-400 border-b border-gray-700">
            Modified
          </div>
          <div className="diff-lines font-mono text-sm">
            {lines.filter(line => line.type !== 'removed').map((line, index) => (
              <div key={index} className={`diff-line flex ${getLineBackground(line.type)}`}>
                {showLineNumbers && (
                  <div className="line-number w-12 px-2 py-0.5 text-xs text-gray-500 select-none bg-gray-800 border-r border-gray-700">
                    {line.newLineNumber || ''}
                  </div>
                )}
                <div className="line-prefix w-4 px-1 py-0.5 text-xs text-gray-500 select-none">
                  {getLinePrefix(line.type)}
                </div>
                <div className="line-content flex-1 px-2 py-0.5 whitespace-pre">
                  {formatContent(line.content)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="diff-unified-view border-t border-gray-700">
      <div className="diff-lines font-mono text-sm">
        {lines.map((line, index) => (
          <div key={index} className={`diff-line flex ${getLineBackground(line.type)}`}>
            {showLineNumbers && (
              <>
                <div className="old-line-number w-12 px-2 py-0.5 text-xs text-gray-500 select-none bg-gray-800 border-r border-gray-700">
                  {line.oldLineNumber || ''}
                </div>
                <div className="new-line-number w-12 px-2 py-0.5 text-xs text-gray-500 select-none bg-gray-800 border-r border-gray-700">
                  {line.newLineNumber || ''}
                </div>
              </>
            )}
            <div className="line-prefix w-4 px-1 py-0.5 text-xs text-gray-500 select-none">
              {getLinePrefix(line.type)}
            </div>
            <div className="line-content flex-1 px-2 py-0.5 whitespace-pre">
              {formatContent(line.content)}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Diff File Details Dialog
const DiffFileDetailsDialog: React.FC<{
  file: DiffFile;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}> = ({ file, open, onOpenChange }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitCompare className="h-5 w-5" />
            File Details: {file.path}
          </DialogTitle>
          <DialogDescription>
            {file.status} • +{file.additions} -{file.deletions} lines
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* File Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium mb-2">File Information</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Status:</span>
                  <span className="capitalize">{file.status}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Path:</span>
                  <span className="font-mono">{file.path}</span>
                </div>
                {file.oldPath && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">Old Path:</span>
                    <span className="font-mono">{file.oldPath}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-400">Binary:</span>
                  <span>{file.binary ? 'Yes' : 'No'}</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-2">Changes</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Additions:</span>
                  <span className="text-green-400">+{file.additions}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Deletions:</span>
                  <span className="text-red-400">-{file.deletions}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Lines:</span>
                  <span>{file.lines.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Hunks:</span>
                  <span>{file.hunks.length}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Preview */}
          {!file.binary && (
            <div>
              <h4 className="text-sm font-medium mb-2">Changes Preview</h4>
              <div className="max-h-60 overflow-auto bg-gray-800 rounded border">
                <DiffLinesView
                  lines={file.lines.slice(0, 50)}
                  viewMode="unified"
                  showLineNumbers={true}
                  showWhitespace={false}
                />
                {file.lines.length > 50 && (
                  <div className="text-center py-2 text-xs text-gray-400 border-t border-gray-700">
                    ... and {file.lines.length - 50} more lines
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper Functions
const generateMockDiffFiles = (context: ContextSnapshot): DiffFile[] => {
  const mockFiles: DiffFile[] = [];
  
  // Generate different types of file changes
  const fileTypes = [
    { path: 'src/components/Header.tsx', status: 'modified' as const },
    { path: 'src/utils/helpers.ts', status: 'modified' as const },
    { path: 'src/styles/globals.css', status: 'modified' as const },
    { path: 'src/pages/NewFeature.tsx', status: 'added' as const },
    { path: 'src/components/OldComponent.tsx', status: 'deleted' as const },
    { path: 'src/config/settings.json', status: 'renamed' as const, oldPath: 'src/config/config.json' }
  ];

  fileTypes.forEach((fileInfo, index) => {
    const lines: DiffLine[] = [];
    const hunks: DiffHunk[] = [];
    
    let additions = 0;
    let deletions = 0;
    let oldLineNum = 1;
    let newLineNum = 1;

    // Generate mock diff lines based on file status
    if (fileInfo.status === 'added') {
      for (let i = 0; i < 20; i++) {
        lines.push({
          type: 'added',
          newLineNumber: newLineNum++,
          content: `  // New line ${i + 1} of added content`
        });
        additions++;
      }
    } else if (fileInfo.status === 'deleted') {
      for (let i = 0; i < 15; i++) {
        lines.push({
          type: 'removed',
          oldLineNumber: oldLineNum++,
          content: `  // Old line ${i + 1} to be deleted`
        });
        deletions++;
      }
    } else {
      // Modified file
      for (let i = 0; i < 30; i++) {
        if (i % 5 === 0) {
          // Remove line
          lines.push({
            type: 'removed',
            oldLineNumber: oldLineNum++,
            content: `  // Old implementation ${i}`
          });
          deletions++;
          
          // Add replacement
          lines.push({
            type: 'added',
            newLineNumber: newLineNum++,
            content: `  // New implementation ${i}`
          });
          additions++;
        } else {
          // Unchanged line
          lines.push({
            type: 'unchanged',
            oldLineNumber: oldLineNum++,
            newLineNumber: newLineNum++,
            content: `  const unchanged${i} = 'value';`
          });
        }
      }
    }

    // Create hunks for non-trivial changes
    if (lines.length > 10) {
      const hunkSize = Math.ceil(lines.length / 2);
      for (let i = 0; i < lines.length; i += hunkSize) {
        const hunkLines = lines.slice(i, i + hunkSize);
        const firstLine = hunkLines[0];
        const lastLine = hunkLines[hunkLines.length - 1];
        
        hunks.push({
          id: `hunk-${index}-${i}`,
          oldStart: firstLine.oldLineNumber || 1,
          oldLines: hunkLines.filter(l => l.oldLineNumber).length,
          newStart: firstLine.newLineNumber || 1,
          newLines: hunkLines.filter(l => l.newLineNumber).length,
          header: `@@ -${firstLine.oldLineNumber || 0},${hunkLines.length} +${firstLine.newLineNumber || 0},${hunkLines.length} @@`,
          lines: hunkLines
        });
      }
    }

    mockFiles.push({
      id: `file-${index}`,
      path: fileInfo.path,
      oldPath: fileInfo.oldPath,
      status: fileInfo.status,
      additions,
      deletions,
      binary: fileInfo.path.endsWith('.png') || fileInfo.path.endsWith('.jpg'),
      lines,
      hunks
    });
  });

  // Add context-based files
  if (context.fileContext.path) {
    mockFiles.unshift({
      id: 'file-current',
      path: context.fileContext.path,
      status: 'modified',
      additions: 5,
      deletions: 2,
      binary: false,
      lines: [
        {
          type: 'unchanged',
          oldLineNumber: 1,
          newLineNumber: 1,
          content: 'import React from "react";'
        },
        {
          type: 'removed',
          oldLineNumber: 2,
          content: '// Old comment'
        },
        {
          type: 'added',
          newLineNumber: 2,
          content: '// Updated comment with better description'
        },
        {
          type: 'unchanged',
          oldLineNumber: 3,
          newLineNumber: 3,
          content: 'export default function Component() {'
        }
      ],
      hunks: []
    });
  }

  return mockFiles;
};

export default DiffViewerWidget;
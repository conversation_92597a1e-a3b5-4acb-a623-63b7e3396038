import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { Textarea } from '../../../components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../../components/ui/dialog';
import { 
  FileText, 
  Plus, 
  Search, 
  Calendar,
  User,
  Target,
  CheckCircle,
  AlertTriangle,
  Clock,
  TrendingUp,
  ArrowRight,
  Tag
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface Decision {
  id: string;
  title: string;
  description: string;
  status: 'proposed' | 'approved' | 'implemented' | 'rejected' | 'superseded';
  impact: 'low' | 'medium' | 'high' | 'critical';
  category: 'architecture' | 'technology' | 'design' | 'process' | 'security';
  decisionMaker: string;
  stakeholders: string[];
  dateCreated: number;
  dateDecided?: number;
  dateImplemented?: number;
  rationale: string;
  alternatives: Alternative[];
  consequences: Consequence[];
  dependencies: string[];
  tags: string[];
  relatedRequirements: string[];
  reviewDate?: number;
}

interface Alternative {
  id: string;
  title: string;
  description: string;
  pros: string[];
  cons: string[];
  effort: 'low' | 'medium' | 'high';
  risk: 'low' | 'medium' | 'high';
  selected: boolean;
}

interface Consequence {
  id: string;
  type: 'positive' | 'negative' | 'neutral';
  description: string;
  likelihood: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  mitigation?: string;
}

interface DecisionLogWidgetProps {
  context: ContextSnapshot;
  config?: {
    showFilters?: boolean;
    allowEditing?: boolean;
    defaultFilter?: string;
    maxHeight?: string;
  };
}

type StatusFilter = 'all' | 'proposed' | 'approved' | 'implemented' | 'rejected' | 'superseded';
type CategoryFilter = 'all' | 'architecture' | 'technology' | 'design' | 'process' | 'security';
type ImpactFilter = 'all' | 'low' | 'medium' | 'high' | 'critical';

export const DecisionLogWidget: React.FC<DecisionLogWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [decisions, setDecisions] = useState<Decision[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [categoryFilter, setCategoryFilter] = useState<CategoryFilter>('all');
  const [impactFilter, setImpactFilter] = useState<ImpactFilter>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [selectedDecision, setSelectedDecision] = useState<Decision | null>(null);

  // Load decisions on component mount
  useEffect(() => {
    loadDecisions();
  }, [context]);

  const loadDecisions = async () => {
    setIsLoading(true);
    // Simulate API call - would be replaced with actual data fetching
    const mockDecisions = generateMockDecisions(context);
    setTimeout(() => {
      setDecisions(mockDecisions);
      setIsLoading(false);
    }, 500);
  };

  // Filter and search decisions
  const filteredDecisions = useMemo(() => {
    return decisions.filter(decision => {
      // Search filter
      const matchesSearch = !searchQuery || 
        decision.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        decision.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        decision.rationale.toLowerCase().includes(searchQuery.toLowerCase()) ||
        decision.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      // Status filter
      const matchesStatus = statusFilter === 'all' || decision.status === statusFilter;

      // Category filter
      const matchesCategory = categoryFilter === 'all' || decision.category === categoryFilter;

      // Impact filter
      const matchesImpact = impactFilter === 'all' || decision.impact === impactFilter;

      return matchesSearch && matchesStatus && matchesCategory && matchesImpact;
    }).sort((a, b) => {
      // Sort by date created (most recent first)
      return b.dateCreated - a.dateCreated;
    });
  }, [decisions, searchQuery, statusFilter, categoryFilter, impactFilter]);

  // Statistics
  const stats = useMemo(() => {
    const total = decisions.length;
    const proposed = decisions.filter(d => d.status === 'proposed').length;
    const approved = decisions.filter(d => d.status === 'approved').length;
    const implemented = decisions.filter(d => d.status === 'implemented').length;
    const critical = decisions.filter(d => d.impact === 'critical').length;
    const overdue = decisions.filter(d => 
      d.reviewDate && d.reviewDate < Date.now() && d.status !== 'implemented'
    ).length;

    return { total, proposed, approved, implemented, critical, overdue };
  }, [decisions]);

  const handleStatusChange = (decisionId: string, newStatus: Decision['status']) => {
    setDecisions(prev => prev.map(decision => 
      decision.id === decisionId 
        ? { 
            ...decision, 
            status: newStatus,
            dateDecided: newStatus === 'approved' ? Date.now() : decision.dateDecided,
            dateImplemented: newStatus === 'implemented' ? Date.now() : decision.dateImplemented
          }
        : decision
    ));
  };

  const handleAddDecision = (newDecision: Omit<Decision, 'id' | 'dateCreated'>) => {
    const decision: Decision = {
      ...newDecision,
      id: `decision-${Date.now()}`,
      dateCreated: Date.now()
    };
    setDecisions(prev => [decision, ...prev]);
    setShowAddDialog(false);
  };

  const getStatusColor = (status: Decision['status']) => {
    switch (status) {
      case 'proposed': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30';
      case 'approved': return 'text-green-500 bg-green-500/10 border-green-500/30';
      case 'implemented': return 'text-blue-500 bg-blue-500/10 border-blue-500/30';
      case 'rejected': return 'text-red-500 bg-red-500/10 border-red-500/30';
      case 'superseded': return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getImpactColor = (impact: Decision['impact']) => {
    switch (impact) {
      case 'critical': return 'text-red-500';
      case 'high': return 'text-orange-500';
      case 'medium': return 'text-yellow-500';
      case 'low': return 'text-green-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: Decision['status']) => {
    switch (status) {
      case 'proposed': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'approved': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'implemented': return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'rejected': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'superseded': return <ArrowRight className="h-4 w-4 text-gray-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card className="decision-log-widget h-full bg-gray-900 border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Decision Log
            <Badge variant="secondary" className="ml-2">
              {filteredDecisions.length}/{decisions.length}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {config.allowEditing !== false && (
              <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
                <DialogTrigger asChild>
                  <Button size="sm" variant="outline" className="h-8 w-8 p-0">
                    <Plus className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <AddDecisionDialog onAdd={handleAddDecision} />
              </Dialog>
            )}
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-6 gap-2 mt-3">
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-blue-400">{stats.total}</div>
            <div className="text-xs text-gray-400">Total</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-yellow-400">{stats.proposed}</div>
            <div className="text-xs text-gray-400">Proposed</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-green-400">{stats.approved}</div>
            <div className="text-xs text-gray-400">Approved</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-blue-400">{stats.implemented}</div>
            <div className="text-xs text-gray-400">Done</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-red-400">{stats.critical}</div>
            <div className="text-xs text-gray-400">Critical</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-orange-400">{stats.overdue}</div>
            <div className="text-xs text-gray-400">Overdue</div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0 flex-1 flex flex-col">
        {/* Filters */}
        {config.showFilters !== false && (
          <div className="px-4 pb-3 space-y-2">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search decisions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 h-8"
              />
            </div>

            {/* Filter Selects */}
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={(value: StatusFilter) => setStatusFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="proposed">Proposed</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="implemented">Implemented</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="superseded">Superseded</SelectItem>
                </SelectContent>
              </Select>

              <Select value={categoryFilter} onValueChange={(value: CategoryFilter) => setCategoryFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="architecture">Architecture</SelectItem>
                  <SelectItem value="technology">Technology</SelectItem>
                  <SelectItem value="design">Design</SelectItem>
                  <SelectItem value="process">Process</SelectItem>
                  <SelectItem value="security">Security</SelectItem>
                </SelectContent>
              </Select>

              <Select value={impactFilter} onValueChange={(value: ImpactFilter) => setImpactFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Impact" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Impact</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Decisions List */}
        <div 
          className="decisions-list flex-1 overflow-y-auto px-4"
          style={{ maxHeight: config.maxHeight || '400px' }}
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-400">Loading decisions...</p>
            </div>
          ) : filteredDecisions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No decisions found</p>
              <p className="text-sm mt-1">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredDecisions.map(decision => (
                <DecisionItem
                  key={decision.id}
                  decision={decision}
                  onStatusChange={handleStatusChange}
                  onSelect={() => setSelectedDecision(decision)}
                  allowEditing={config.allowEditing !== false}
                />
              ))}
            </div>
          )}
        </div>

        {/* Decision Details Modal */}
        {selectedDecision && (
          <DecisionDetailsDialog
            decision={selectedDecision}
            open={!!selectedDecision}
            onOpenChange={(open) => !open && setSelectedDecision(null)}
            onUpdate={(updated) => {
              setDecisions(prev => prev.map(decision => 
                decision.id === updated.id ? updated : decision
              ));
              setSelectedDecision(updated);
            }}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Decision Item Component
const DecisionItem: React.FC<{
  decision: Decision;
  onStatusChange: (id: string, status: Decision['status']) => void;
  onSelect: () => void;
  allowEditing: boolean;
}> = ({ decision, onStatusChange, onSelect, allowEditing }) => {
  const getImpactColor = (impact: Decision['impact']) => {
    switch (impact) {
      case 'critical': return 'border-l-red-500';
      case 'high': return 'border-l-orange-500';
      case 'medium': return 'border-l-yellow-500';
      case 'low': return 'border-l-green-500';
      default: return 'border-l-gray-500';
    }
  };

  const getStatusIcon = (status: Decision['status']) => {
    switch (status) {
      case 'proposed': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'approved': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'implemented': return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'rejected': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'superseded': return <ArrowRight className="h-4 w-4 text-gray-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getCategoryIcon = (category: Decision['category']) => {
    switch (category) {
      case 'architecture': return <Target className="h-3 w-3 text-blue-500" />;
      case 'technology': return <TrendingUp className="h-3 w-3 text-green-500" />;
      case 'design': return <FileText className="h-3 w-3 text-purple-500" />;
      case 'process': return <Clock className="h-3 w-3 text-yellow-500" />;
      case 'security': return <AlertTriangle className="h-3 w-3 text-red-500" />;
      default: return <FileText className="h-3 w-3 text-gray-500" />;
    }
  };

  return (
    <div 
      className={`decision-item p-3 bg-gray-800 rounded-lg border-l-4 ${getImpactColor(decision.impact)} cursor-pointer hover:bg-gray-700 transition-colors`}
      onClick={onSelect}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          {getStatusIcon(decision.status)}
          <span className="font-medium text-white text-sm">{decision.title}</span>
        </div>
        <div className="flex items-center gap-1">
          {getCategoryIcon(decision.category)}
          <Badge variant="outline" className="text-xs border-gray-600">
            {decision.impact}
          </Badge>
        </div>
      </div>

      <p className="text-xs text-gray-400 mb-2 line-clamp-2">
        {decision.description}
      </p>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-xs text-gray-500">
          <span className="capitalize">{decision.category}</span>
          <span>•</span>
          <div className="flex items-center gap-1">
            <User className="h-3 w-3" />
            <span>{decision.decisionMaker}</span>
          </div>
          <span>•</span>
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>{new Date(decision.dateCreated).toLocaleDateString()}</span>
          </div>
        </div>

        {allowEditing && (
          <Select 
            value={decision.status} 
            onValueChange={(value) => onStatusChange(decision.id, value as Decision['status'])}
          >
            <SelectTrigger className="w-28 h-6 text-xs bg-gray-700 border-gray-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="proposed">Proposed</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="implemented">Implemented</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
              <SelectItem value="superseded">Superseded</SelectItem>
            </SelectContent>
          </Select>
        )}
      </div>

      {decision.tags.length > 0 && (
        <div className="flex items-center gap-1 mt-2">
          {decision.tags.slice(0, 3).map(tag => (
            <Badge key={tag} variant="outline" className="text-xs border-gray-600 text-gray-400">
              <Tag className="h-2 w-2 mr-1" />
              {tag}
            </Badge>
          ))}
          {decision.tags.length > 3 && (
            <span className="text-xs text-gray-500">+{decision.tags.length - 3}</span>
          )}
        </div>
      )}
    </div>
  );
};

// Add Decision Dialog
const AddDecisionDialog: React.FC<{
  onAdd: (decision: Omit<Decision, 'id' | 'dateCreated'>) => void;
}> = ({ onAdd }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    status: 'proposed' as Decision['status'],
    impact: 'medium' as Decision['impact'],
    category: 'architecture' as Decision['category'],
    decisionMaker: '',
    stakeholders: [] as string[],
    rationale: '',
    alternatives: [] as Alternative[],
    consequences: [] as Consequence[],
    dependencies: [] as string[],
    tags: [] as string[],
    relatedRequirements: [] as string[]
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim()) return;

    onAdd(formData);

    // Reset form
    setFormData({
      title: '',
      description: '',
      status: 'proposed',
      impact: 'medium',
      category: 'architecture',
      decisionMaker: '',
      stakeholders: [],
      rationale: '',
      alternatives: [],
      consequences: [],
      dependencies: [],
      tags: [],
      relatedRequirements: []
    });
  };

  return (
    <DialogContent className="bg-gray-900 border-gray-700 max-w-2xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Add New Decision</DialogTitle>
        <DialogDescription>
          Document a new architectural or design decision
        </DialogDescription>
      </DialogHeader>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2">
            <Input
              placeholder="Decision title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="bg-gray-800 border-gray-600"
              required
            />
          </div>

          <Select 
            value={formData.category} 
            onValueChange={(value: Decision['category']) => setFormData(prev => ({ ...prev, category: value }))}
          >
            <SelectTrigger className="bg-gray-800 border-gray-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="architecture">Architecture</SelectItem>
              <SelectItem value="technology">Technology</SelectItem>
              <SelectItem value="design">Design</SelectItem>
              <SelectItem value="process">Process</SelectItem>
              <SelectItem value="security">Security</SelectItem>
            </SelectContent>
          </Select>

          <Select 
            value={formData.impact} 
            onValueChange={(value: Decision['impact']) => setFormData(prev => ({ ...prev, impact: value }))}
          >
            <SelectTrigger className="bg-gray-800 border-gray-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="low">Low Impact</SelectItem>
              <SelectItem value="medium">Medium Impact</SelectItem>
              <SelectItem value="high">High Impact</SelectItem>
              <SelectItem value="critical">Critical Impact</SelectItem>
            </SelectContent>
          </Select>

          <div className="col-span-2">
            <Textarea
              placeholder="Decision description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="bg-gray-800 border-gray-600"
              rows={3}
            />
          </div>

          <div className="col-span-2">
            <Textarea
              placeholder="Rationale and reasoning behind this decision"
              value={formData.rationale}
              onChange={(e) => setFormData(prev => ({ ...prev, rationale: e.target.value }))}
              className="bg-gray-800 border-gray-600"
              rows={3}
            />
          </div>

          <Input
            placeholder="Decision maker"
            value={formData.decisionMaker}
            onChange={(e) => setFormData(prev => ({ ...prev, decisionMaker: e.target.value }))}
            className="bg-gray-800 border-gray-600"
          />

          <Select 
            value={formData.status} 
            onValueChange={(value: Decision['status']) => setFormData(prev => ({ ...prev, status: value }))}
          >
            <SelectTrigger className="bg-gray-800 border-gray-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="proposed">Proposed</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="implemented">Implemented</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2 pt-4">
          <Button type="submit" className="flex-1">
            Add Decision
          </Button>
        </div>
      </form>
    </DialogContent>
  );
};

// Decision Details Dialog
const DecisionDetailsDialog: React.FC<{
  decision: Decision;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (decision: Decision) => void;
}> = ({ decision, open, onOpenChange, onUpdate }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {decision.title}
          </DialogTitle>
          <DialogDescription>
            {decision.category} • Created {new Date(decision.dateCreated).toLocaleDateString()}
            {decision.dateDecided && ` • Decided ${new Date(decision.dateDecided).toLocaleDateString()}`}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Impact */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Status & Impact</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{decision.status}</Badge>
                  <Badge variant="outline">{decision.impact} impact</Badge>
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-2">Key Information</h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>Decision Maker: {decision.decisionMaker}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>Created: {new Date(decision.dateCreated).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Description */}
          <div>
            <h4 className="text-sm font-medium mb-2">Description</h4>
            <p className="text-sm text-gray-300 bg-gray-800 rounded p-3">
              {decision.description}
            </p>
          </div>

          {/* Rationale */}
          <div>
            <h4 className="text-sm font-medium mb-2">Rationale</h4>
            <p className="text-sm text-gray-300 bg-gray-800 rounded p-3">
              {decision.rationale}
            </p>
          </div>

          {/* Alternatives */}
          {decision.alternatives.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Alternatives Considered</h4>
              <div className="space-y-2">
                {decision.alternatives.map(alt => (
                  <div key={alt.id} className="bg-gray-800 rounded p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">{alt.title}</span>
                      {alt.selected && (
                        <Badge variant="default" className="text-xs">Selected</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-400 mb-2">{alt.description}</p>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-green-400">Pros:</span>
                        <ul className="list-disc list-inside text-gray-400">
                          {alt.pros.map((pro, index) => (
                            <li key={index}>{pro}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <span className="text-red-400">Cons:</span>
                        <ul className="list-disc list-inside text-gray-400">
                          {alt.cons.map((con, index) => (
                            <li key={index}>{con}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Consequences */}
          {decision.consequences.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Consequences</h4>
              <div className="space-y-2">
                {decision.consequences.map(consequence => (
                  <div key={consequence.id} className="bg-gray-800 rounded p-3">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${
                          consequence.type === 'positive' ? 'border-green-500 text-green-400' :
                          consequence.type === 'negative' ? 'border-red-500 text-red-400' :
                          'border-gray-500 text-gray-400'
                        }`}
                      >
                        {consequence.type}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {consequence.likelihood} likelihood • {consequence.impact} impact
                      </span>
                    </div>
                    <p className="text-sm text-gray-300">{consequence.description}</p>
                    {consequence.mitigation && (
                      <p className="text-xs text-gray-400 mt-1">
                        <strong>Mitigation:</strong> {consequence.mitigation}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Tags */}
          {decision.tags.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Tags</h4>
              <div className="flex flex-wrap gap-1">
                {decision.tags.map(tag => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    <Tag className="h-2 w-2 mr-1" />
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper Functions
const generateMockDecisions = (context: ContextSnapshot): Decision[] => {
  const mockDecisions: Decision[] = [
    {
      id: 'decision-1',
      title: 'Adopt TypeScript for Frontend Development',
      description: 'Migrate existing JavaScript codebase to TypeScript to improve type safety and developer experience.',
      status: 'implemented',
      impact: 'high',
      category: 'technology',
      decisionMaker: 'Tech Lead',
      stakeholders: ['Frontend Team', 'DevOps', 'QA'],
      dateCreated: Date.now() - 2592000000, // 30 days ago
      dateDecided: Date.now() - 2419200000, // 28 days ago
      dateImplemented: Date.now() - 1209600000, // 14 days ago
      rationale: 'TypeScript provides better tooling, catch errors at compile time, and improves code maintainability.',
      alternatives: [
        {
          id: 'alt-1',
          title: 'Continue with JavaScript',
          description: 'Keep using JavaScript with JSDoc for type hints',
          pros: ['No migration effort', 'Team familiarity'],
          cons: ['Runtime errors', 'Poor tooling'],
          effort: 'low',
          risk: 'medium',
          selected: false
        },
        {
          id: 'alt-2',
          title: 'Gradual TypeScript adoption',
          description: 'Incrementally add TypeScript to new features',
          pros: ['Low risk', 'Gradual learning'],
          cons: ['Mixed codebase', 'Longer migration'],
          effort: 'medium',
          risk: 'low',
          selected: true
        }
      ],
      consequences: [
        {
          id: 'cons-1',
          type: 'positive',
          description: 'Reduced runtime errors and improved developer productivity',
          likelihood: 'high',
          impact: 'high'
        },
        {
          id: 'cons-2',
          type: 'negative',
          description: 'Initial learning curve and setup overhead',
          likelihood: 'medium',
          impact: 'low',
          mitigation: 'Provide training sessions and gradual migration plan'
        }
      ],
      dependencies: [],
      tags: ['frontend', 'typescript', 'migration'],
      relatedRequirements: ['req-1']
    },
    {
      id: 'decision-2',
      title: 'Implement Microservices Architecture',
      description: 'Break down monolithic backend into microservices for better scalability and team autonomy.',
      status: 'approved',
      impact: 'critical',
      category: 'architecture',
      decisionMaker: 'Principal Architect',
      stakeholders: ['Backend Team', 'DevOps', 'Product Team'],
      dateCreated: Date.now() - 1209600000, // 14 days ago
      dateDecided: Date.now() - 604800000, // 7 days ago
      rationale: 'Current monolith is becoming difficult to maintain and deploy. Microservices will enable independent scaling and development.',
      alternatives: [
        {
          id: 'alt-3',
          title: 'Modular Monolith',
          description: 'Refactor existing monolith into well-defined modules',
          pros: ['Simpler deployment', 'Data consistency'],
          cons: ['Coupled deployments', 'Single point of failure'],
          effort: 'medium',
          risk: 'low',
          selected: false
        }
      ],
      consequences: [
        {
          id: 'cons-3',
          type: 'positive',
          description: 'Independent scaling and deployment of services',
          likelihood: 'high',
          impact: 'high'
        },
        {
          id: 'cons-4',
          type: 'negative',
          description: 'Increased operational complexity and distributed system challenges',
          likelihood: 'high',
          impact: 'medium',
          mitigation: 'Invest in monitoring, service mesh, and DevOps automation'
        }
      ],
      dependencies: [],
      tags: ['backend', 'microservices', 'architecture'],
      relatedRequirements: ['req-2'],
      reviewDate: Date.now() + 2592000000 // 30 days from now
    },
    {
      id: 'decision-3',
      title: 'Database Sharding Strategy',
      description: 'Implement horizontal sharding for the user database to handle increased load.',
      status: 'proposed',
      impact: 'high',
      category: 'architecture',
      decisionMaker: 'Database Architect',
      stakeholders: ['Backend Team', 'DevOps'],
      dateCreated: Date.now() - 259200000, // 3 days ago
      rationale: 'Current database is reaching capacity limits and queries are slowing down.',
      alternatives: [],
      consequences: [],
      dependencies: ['decision-2'],
      tags: ['database', 'scaling', 'performance'],
      relatedRequirements: []
    }
  ];

  // Add context-based decisions
  if (context.fileContext.hasErrors) {
    mockDecisions.push({
      id: 'decision-error',
      title: 'Error Handling Strategy',
      description: 'Standardize error handling across the application to improve reliability.',
      status: 'proposed',
      impact: 'medium',
      category: 'design',
      decisionMaker: 'Tech Lead',
      stakeholders: ['Development Team'],
      dateCreated: Date.now(),
      rationale: 'Current file has errors. We need a consistent error handling approach.',
      alternatives: [],
      consequences: [],
      dependencies: [],
      tags: ['error-handling', 'reliability'],
      relatedRequirements: []
    });
  }

  return mockDecisions;
};

export default DecisionLogWidget;
import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { Textarea } from '../../../components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog';
import { 
  MessageSquare, 
  Search, 
  Filter,
  Clock, 
  CheckCircle, 
  AlertCircle,
  FileText,
  Code,
  Plus,
  Eye,
  ThumbsUp,
  ThumbsDown,
  RefreshCw,
  User,
  Calendar,
  MapPin,
  Quote,
  <PERSON><PERSON>,
  Edit,
  Trash2,
  Flag,
  Link,
  ExternalLink
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface CodeComment {
  id: string;
  author: {
    id: string;
    name: string;
    avatar: string;
    role: 'reviewer' | 'author' | 'maintainer';
  };
  content: string;
  filePath: string;
  lineNumber: number;
  lineRange?: {
    start: number;
    end: number;
  };
  type: 'comment' | 'suggestion' | 'issue' | 'question' | 'praise';
  severity: 'info' | 'minor' | 'major' | 'critical';
  status: 'open' | 'resolved' | 'acknowledged' | 'wont_fix';
  createdAt: number;
  updatedAt: number;
  resolvedAt?: number;
  resolvedBy?: string;
  replies: CodeComment[];
  reactions: {
    type: 'like' | 'dislike' | 'confused' | 'heart' | 'rocket';
    users: string[];
  }[];
  suggestedChange?: {
    original: string;
    suggested: string;
    applied: boolean;
  };
  relatedToCommit?: string;
  tags: string[];
  isBot: boolean;
  threadId: string;
}

interface ReviewSession {
  id: string;
  reviewerName: string;
  startedAt: number;
  completedAt?: number;
  status: 'in_progress' | 'completed' | 'abandoned';
  commentsCount: number;
  issuesFound: number;
  overallRating?: 'approve' | 'request_changes' | 'comment';
  summary?: string;
}

interface CodeReviewWidgetProps {
  context: ContextSnapshot;
  config?: {
    maxComments?: number;
    showFilters?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number;
    defaultView?: 'list' | 'threaded' | 'files';
    maxHeight?: string;
    showResolvedComments?: boolean;
  };
}

type CommentTypeFilter = 'all' | 'comment' | 'suggestion' | 'issue' | 'question' | 'praise';
type StatusFilter = 'all' | 'open' | 'resolved' | 'acknowledged' | 'wont_fix';
type SeverityFilter = 'all' | 'info' | 'minor' | 'major' | 'critical';
type ViewMode = 'list' | 'threaded' | 'files';

export const CodeReviewWidget: React.FC<CodeReviewWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [comments, setComments] = useState<CodeComment[]>([]);
  const [reviewSessions, setReviewSessions] = useState<ReviewSession[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<CommentTypeFilter>('all');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>(config.showResolvedComments ? 'all' : 'open');
  const [severityFilter, setSeverityFilter] = useState<SeverityFilter>('all');
  const [viewMode, setViewMode] = useState<ViewMode>(config.defaultView || 'list');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedComment, setSelectedComment] = useState<CodeComment | null>(null);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');

  // Load comments on component mount
  useEffect(() => {
    loadComments();
    loadReviewSessions();
  }, [context]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!config.autoRefresh) return;

    const interval = setInterval(() => {
      loadComments(false); // Don't show loading state for auto-refresh
      loadReviewSessions();
    }, config.refreshInterval || 15000);

    return () => clearInterval(interval);
  }, [config.autoRefresh, config.refreshInterval]);

  const loadComments = async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    
    // Simulate API call - would be replaced with actual comment fetching
    const mockComments = generateMockComments(context, config.maxComments || 25);
    
    setTimeout(() => {
      setComments(mockComments);
      if (showLoading) setIsLoading(false);
    }, showLoading ? 600 : 0);
  };

  const loadReviewSessions = async () => {
    // Simulate loading review sessions
    const mockSessions = generateMockReviewSessions();
    setReviewSessions(mockSessions);
  };

  // Filter comments
  const filteredComments = useMemo(() => {
    return comments.filter(comment => {
      // Search filter
      const matchesSearch = !searchQuery || 
        comment.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        comment.author.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        comment.filePath.toLowerCase().includes(searchQuery.toLowerCase());

      // Type filter
      const matchesType = typeFilter === 'all' || comment.type === typeFilter;

      // Status filter
      const matchesStatus = statusFilter === 'all' || comment.status === statusFilter;

      // Severity filter
      const matchesSeverity = severityFilter === 'all' || comment.severity === severityFilter;

      return matchesSearch && matchesType && matchesStatus && matchesSeverity;
    }).sort((a, b) => {
      // Sort by severity first, then by creation date
      const severityOrder = { critical: 4, major: 3, minor: 2, info: 1 };
      const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
      if (severityDiff !== 0) return severityDiff;
      return b.createdAt - a.createdAt;
    });
  }, [comments, searchQuery, typeFilter, statusFilter, severityFilter]);

  // Group comments by file for files view
  const commentsByFile = useMemo(() => {
    const grouped = new Map<string, CodeComment[]>();
    filteredComments.forEach(comment => {
      if (!grouped.has(comment.filePath)) {
        grouped.set(comment.filePath, []);
      }
      grouped.get(comment.filePath)!.push(comment);
    });
    return grouped;
  }, [filteredComments]);

  // Statistics
  const stats = useMemo(() => {
    const total = comments.length;
    const open = comments.filter(c => c.status === 'open').length;
    const issues = comments.filter(c => c.type === 'issue').length;
    const suggestions = comments.filter(c => c.type === 'suggestion').length;
    const critical = comments.filter(c => c.severity === 'critical').length;

    return { total, open, issues, suggestions, critical };
  }, [comments]);

  const handleRefresh = () => {
    loadComments();
    loadReviewSessions();
  };

  const handleStatusChange = (commentId: string, newStatus: CodeComment['status']) => {
    setComments(prev => prev.map(comment => 
      comment.id === commentId 
        ? { 
            ...comment, 
            status: newStatus,
            resolvedAt: newStatus === 'resolved' ? Date.now() : undefined,
            resolvedBy: newStatus === 'resolved' ? 'current-user' : undefined,
            updatedAt: Date.now()
          }
        : comment
    ));
  };

  const handleReaction = (commentId: string, reactionType: CodeComment['reactions'][0]['type']) => {
    setComments(prev => prev.map(comment => {
      if (comment.id !== commentId) return comment;
      
      const existingReaction = comment.reactions.find(r => r.type === reactionType);
      if (existingReaction) {
        // Toggle reaction
        const userIndex = existingReaction.users.indexOf('current-user');
        if (userIndex >= 0) {
          existingReaction.users.splice(userIndex, 1);
        } else {
          existingReaction.users.push('current-user');
        }
      } else {
        // Add new reaction
        comment.reactions.push({
          type: reactionType,
          users: ['current-user']
        });
      }
      
      return { ...comment, updatedAt: Date.now() };
    }));
  };

  const handleReply = (commentId: string) => {
    if (!replyContent.trim()) return;

    const newReply: CodeComment = {
      id: `reply-${Date.now()}`,
      author: { id: 'current-user', name: 'Current User', avatar: '', role: 'reviewer' },
      content: replyContent,
      filePath: '',
      lineNumber: 0,
      type: 'comment',
      severity: 'info',
      status: 'open',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      replies: [],
      reactions: [],
      tags: [],
      isBot: false,
      threadId: commentId
    };

    setComments(prev => prev.map(comment => 
      comment.id === commentId 
        ? { ...comment, replies: [...comment.replies, newReply] }
        : comment
    ));

    setReplyContent('');
    setReplyingTo(null);
  };

  const getTypeColor = (type: CodeComment['type']) => {
    switch (type) {
      case 'issue': return 'text-red-500 bg-red-500/10 border-red-500/30';
      case 'suggestion': return 'text-blue-500 bg-blue-500/10 border-blue-500/30';
      case 'question': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30';
      case 'praise': return 'text-green-500 bg-green-500/10 border-green-500/30';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getSeverityColor = (severity: CodeComment['severity']) => {
    switch (severity) {
      case 'critical': return 'text-red-500';
      case 'major': return 'text-orange-500';
      case 'minor': return 'text-yellow-500';
      default: return 'text-blue-500';
    }
  };

  const getTypeIcon = (type: CodeComment['type']) => {
    switch (type) {
      case 'issue': return <AlertCircle className="h-4 w-4" />;
      case 'suggestion': return <Edit className="h-4 w-4" />;
      case 'question': return <MessageSquare className="h-4 w-4" />;
      case 'praise': return <ThumbsUp className="h-4 w-4" />;
      default: return <MessageSquare className="h-4 w-4" />;
    }
  };

  return (
    <Card className="code-review-widget h-full bg-gray-900 border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Code Reviews
            <Badge variant="secondary" className="ml-2">
              {filteredComments.length}/{comments.length}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            
            <Button 
              size="sm" 
              variant="outline"
              className="h-8"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Comment
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-5 gap-2 mt-3">
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-blue-400">{stats.total}</div>
            <div className="text-xs text-gray-400">Total</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-yellow-400">{stats.open}</div>
            <div className="text-xs text-gray-400">Open</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-red-400">{stats.issues}</div>
            <div className="text-xs text-gray-400">Issues</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-blue-400">{stats.suggestions}</div>
            <div className="text-xs text-gray-400">Suggestions</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-red-500">{stats.critical}</div>
            <div className="text-xs text-gray-400">Critical</div>
          </div>
        </div>

        {/* Active Review Sessions */}
        {reviewSessions.length > 0 && (
          <div className="mt-3">
            <div className="text-xs text-gray-400 font-medium mb-2">Active Reviews</div>
            <div className="flex gap-2 flex-wrap">
              {reviewSessions.filter(s => s.status === 'in_progress').map(session => (
                <Badge key={session.id} variant="outline" className="text-xs border-blue-500 text-blue-400">
                  <User className="h-3 w-3 mr-1" />
                  {session.reviewerName}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="p-0 flex-1 flex flex-col">
        {/* Filters */}
        {config.showFilters !== false && (
          <div className="px-4 pb-3 space-y-2">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search comments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 h-8"
              />
            </div>

            {/* Filter Selects and View Toggle */}
            <div className="flex gap-2">
              <Select value={typeFilter} onValueChange={(value: CommentTypeFilter) => setTypeFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="comment">Comments</SelectItem>
                  <SelectItem value="suggestion">Suggestions</SelectItem>
                  <SelectItem value="issue">Issues</SelectItem>
                  <SelectItem value="question">Questions</SelectItem>
                  <SelectItem value="praise">Praise</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={(value: StatusFilter) => setStatusFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="acknowledged">Acknowledged</SelectItem>
                  <SelectItem value="wont_fix">Won't Fix</SelectItem>
                </SelectContent>
              </Select>

              <Select value={severityFilter} onValueChange={(value: SeverityFilter) => setSeverityFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Severity" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Severity</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="major">Major</SelectItem>
                  <SelectItem value="minor">Minor</SelectItem>
                  <SelectItem value="info">Info</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex bg-gray-800 rounded border border-gray-600">
                <Button
                  size="sm"
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  onClick={() => setViewMode('list')}
                  className="h-8 px-2 rounded-r-none"
                >
                  <MessageSquare className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant={viewMode === 'threaded' ? 'default' : 'ghost'}
                  onClick={() => setViewMode('threaded')}
                  className="h-8 px-2 rounded-none"
                >
                  <Reply className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant={viewMode === 'files' ? 'default' : 'ghost'}
                  onClick={() => setViewMode('files')}
                  className="h-8 px-2 rounded-l-none"
                >
                  <FileText className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Comments Content */}
        <div 
          className="comments-content flex-1 overflow-y-auto px-4"
          style={{ maxHeight: config.maxHeight || '500px' }}
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-400">Loading comments...</p>
            </div>
          ) : filteredComments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No comments found</p>
              <p className="text-sm mt-1">Try adjusting your search or filters</p>
            </div>
          ) : viewMode === 'files' ? (
            <FileGroupedComments
              commentsByFile={commentsByFile}
              onStatusChange={handleStatusChange}
              onReaction={handleReaction}
              onReply={handleReply}
              onSelect={setSelectedComment}
              replyingTo={replyingTo}
              setReplyingTo={setReplyingTo}
              replyContent={replyContent}
              setReplyContent={setReplyContent}
            />
          ) : (
            <div className="space-y-3">
              {filteredComments.map(comment => (
                <CommentItem
                  key={comment.id}
                  comment={comment}
                  onStatusChange={handleStatusChange}
                  onReaction={handleReaction}
                  onReply={handleReply}
                  onSelect={() => setSelectedComment(comment)}
                  showThreaded={viewMode === 'threaded'}
                  replyingTo={replyingTo}
                  setReplyingTo={setReplyingTo}
                  replyContent={replyContent}
                  setReplyContent={setReplyContent}
                />
              ))}
            </div>
          )}
        </div>

        {/* Comment Details Modal */}
        {selectedComment && (
          <CommentDetailsDialog
            comment={selectedComment}
            open={!!selectedComment}
            onOpenChange={(open) => !open && setSelectedComment(null)}
            onStatusChange={handleStatusChange}
            onReaction={handleReaction}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Comment Item Component
const CommentItem: React.FC<{
  comment: CodeComment;
  onStatusChange: (id: string, status: CodeComment['status']) => void;
  onReaction: (id: string, reaction: CodeComment['reactions'][0]['type']) => void;
  onReply: (id: string) => void;
  onSelect: () => void;
  showThreaded?: boolean;
  replyingTo: string | null;
  setReplyingTo: (id: string | null) => void;
  replyContent: string;
  setReplyContent: (content: string) => void;
}> = ({ 
  comment, 
  onStatusChange, 
  onReaction, 
  onReply, 
  onSelect, 
  showThreaded = false,
  replyingTo,
  setReplyingTo,
  replyContent,
  setReplyContent
}) => {
  const getTypeColor = (type: CodeComment['type']) => {
    switch (type) {
      case 'issue': return 'border-l-red-500';
      case 'suggestion': return 'border-l-blue-500';
      case 'question': return 'border-l-yellow-500';
      case 'praise': return 'border-l-green-500';
      default: return 'border-l-gray-500';
    }
  };

  const getTypeIcon = (type: CodeComment['type']) => {
    switch (type) {
      case 'issue': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'suggestion': return <Edit className="h-4 w-4 text-blue-500" />;
      case 'question': return <MessageSquare className="h-4 w-4 text-yellow-500" />;
      case 'praise': return <ThumbsUp className="h-4 w-4 text-green-500" />;
      default: return <MessageSquare className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: CodeComment['severity']) => {
    switch (severity) {
      case 'critical': return 'text-red-500';
      case 'major': return 'text-orange-500';
      case 'minor': return 'text-yellow-500';
      default: return 'text-blue-500';
    }
  };

  return (
    <div className={`comment-item p-3 bg-gray-800 rounded-lg border-l-4 ${getTypeColor(comment.type)}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          {getTypeIcon(comment.type)}
          <div>
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium text-white text-sm">{comment.author.name}</span>
              <Badge variant="outline" className="text-xs border-gray-600">
                {comment.author.role}
              </Badge>
              {comment.isBot && (
                <Badge variant="outline" className="text-xs border-purple-500 text-purple-400">
                  Bot
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-400">
              <Clock className="h-3 w-3" />
              <span>{new Date(comment.createdAt).toLocaleDateString()}</span>
              <span>•</span>
              <span className={getSeverityColor(comment.severity)}>{comment.severity}</span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline" className={`text-xs ${{
            open: 'border-yellow-500 text-yellow-400',
            resolved: 'border-green-500 text-green-400',
            acknowledged: 'border-blue-500 text-blue-400',
            wont_fix: 'border-red-500 text-red-400'
          }[comment.status]}`}>
            {comment.status.replace('_', ' ')}
          </Badge>
          
          <Select 
            value={comment.status} 
            onValueChange={(value) => onStatusChange(comment.id, value as CodeComment['status'])}
          >
            <SelectTrigger className="w-20 h-6 text-xs bg-gray-700 border-gray-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="open">Open</SelectItem>
              <SelectItem value="acknowledged">Acknowledged</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="wont_fix">Won't Fix</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* File Location */}
      {comment.filePath && (
        <div className="flex items-center gap-2 mb-2 text-xs text-gray-400 bg-gray-900 rounded p-2">
          <MapPin className="h-3 w-3 text-blue-400" />
          <span className="font-mono">{comment.filePath}</span>
          {comment.lineNumber > 0 && (
            <>
              <span>:</span>
              <span className="text-blue-400">L{comment.lineNumber}</span>
            </>
          )}
          <Button
            size="sm"
            variant="ghost"
            className="h-auto p-0 ml-auto text-xs text-blue-400 hover:text-blue-300"
          >
            <ExternalLink className="h-3 w-3 mr-1" />
            Open
          </Button>
        </div>
      )}

      {/* Content */}
      <div className="mb-3">
        <p className="text-sm text-gray-300 whitespace-pre-wrap">{comment.content}</p>
        
        {/* Suggested Change */}
        {comment.suggestedChange && (
          <div className="mt-2 p-2 bg-gray-900 rounded border border-gray-700">
            <div className="text-xs text-gray-400 mb-1">Suggested change:</div>
            <div className="text-xs font-mono">
              <div className="text-red-400 bg-red-500/10 p-1 rounded mb-1">
                - {comment.suggestedChange.original}
              </div>
              <div className="text-green-400 bg-green-500/10 p-1 rounded">
                + {comment.suggestedChange.suggested}
              </div>
            </div>
            {!comment.suggestedChange.applied && (
              <Button size="sm" className="mt-2 h-6 text-xs">
                Apply Suggestion
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Reactions */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {comment.reactions.map(reaction => (
            <Button
              key={reaction.type}
              size="sm"
              variant="ghost"
              onClick={() => onReaction(comment.id, reaction.type)}
              className={`h-6 text-xs px-2 ${
                reaction.users.includes('current-user') ? 'bg-blue-600/20 text-blue-400' : ''
              }`}
            >
              {reaction.type === 'like' && '👍'}
              {reaction.type === 'dislike' && '👎'}
              {reaction.type === 'confused' && '😕'}
              {reaction.type === 'heart' && '❤️'}
              {reaction.type === 'rocket' && '🚀'}
              <span className="ml-1">{reaction.users.length}</span>
            </Button>
          ))}
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
            className="h-6 text-xs px-2"
          >
            <Reply className="h-3 w-3 mr-1" />
            Reply
          </Button>
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={onSelect}
          className="h-auto p-0 text-xs text-blue-400 hover:text-blue-300"
        >
          View Details
        </Button>
      </div>

      {/* Reply Input */}
      {replyingTo === comment.id && (
        <div className="mt-2 space-y-2">
          <Textarea
            placeholder="Write a reply..."
            value={replyContent}
            onChange={(e) => setReplyContent(e.target.value)}
            className="bg-gray-900 border-gray-600 text-sm min-h-[60px]"
          />
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={() => onReply(comment.id)}
              disabled={!replyContent.trim()}
              className="h-6 text-xs"
            >
              Post Reply
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setReplyingTo(null);
                setReplyContent('');
              }}
              className="h-6 text-xs"
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      {/* Replies (if threaded view) */}
      {showThreaded && comment.replies.length > 0 && (
        <div className="mt-3 pl-4 border-l-2 border-gray-700 space-y-2">
          {comment.replies.map(reply => (
            <div key={reply.id} className="text-sm">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-gray-300">{reply.author.name}</span>
                <span className="text-xs text-gray-500">
                  {new Date(reply.createdAt).toLocaleDateString()}
                </span>
              </div>
              <p className="text-gray-400">{reply.content}</p>
            </div>
          ))}
        </div>
      )}

      {/* Tags */}
      {comment.tags.length > 0 && (
        <div className="flex items-center gap-1 mt-2">
          {comment.tags.map(tag => (
            <Badge key={tag} variant="outline" className="text-xs border-gray-600 text-gray-400">
              {tag}
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
};

// File Grouped Comments Component
const FileGroupedComments: React.FC<{
  commentsByFile: Map<string, CodeComment[]>;
  onStatusChange: (id: string, status: CodeComment['status']) => void;
  onReaction: (id: string, reaction: CodeComment['reactions'][0]['type']) => void;
  onReply: (id: string) => void;
  onSelect: (comment: CodeComment) => void;
  replyingTo: string | null;
  setReplyingTo: (id: string | null) => void;
  replyContent: string;
  setReplyContent: (content: string) => void;
}> = ({ 
  commentsByFile, 
  onStatusChange, 
  onReaction, 
  onReply, 
  onSelect,
  replyingTo,
  setReplyingTo,
  replyContent,
  setReplyContent
}) => {
  return (
    <div className="space-y-4">
      {Array.from(commentsByFile.entries()).map(([filePath, comments]) => (
        <div key={filePath} className="file-group">
          <div className="file-header flex items-center gap-2 mb-3 p-2 bg-gray-800 rounded">
            <FileText className="h-4 w-4 text-blue-400" />
            <span className="font-medium text-white">{filePath}</span>
            <Badge variant="outline" className="text-xs">
              {comments.length} {comments.length === 1 ? 'comment' : 'comments'}
            </Badge>
          </div>
          
          <div className="file-comments space-y-2 pl-4">
            {comments.map(comment => (
              <CommentItem
                key={comment.id}
                comment={comment}
                onStatusChange={onStatusChange}
                onReaction={onReaction}
                onReply={onReply}
                onSelect={() => onSelect(comment)}
                replyingTo={replyingTo}
                setReplyingTo={setReplyingTo}
                replyContent={replyContent}
                setReplyContent={setReplyContent}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

// Comment Details Dialog
const CommentDetailsDialog: React.FC<{
  comment: CodeComment;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStatusChange: (id: string, status: CodeComment['status']) => void;
  onReaction: (id: string, reaction: CodeComment['reactions'][0]['type']) => void;
}> = ({ comment, open, onOpenChange, onStatusChange, onReaction }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Comment Details
          </DialogTitle>
          <DialogDescription>
            {comment.author.name} • {new Date(comment.createdAt).toLocaleString()}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="text-sm text-gray-300 whitespace-pre-wrap">{comment.content}</div>
          
          {/* Full comment details would be implemented here */}
          <div className="text-center py-8 text-gray-500">
            <p>Full comment details and thread would be displayed here</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper Functions
const generateMockComments = (context: ContextSnapshot, maxComments: number): CodeComment[] => {
  const mockComments: CodeComment[] = [];
  const now = Date.now();
  
  const authors = [
    { id: 'user-1', name: 'Sarah Chen', role: 'reviewer' as const },
    { id: 'user-2', name: 'Mike Johnson', role: 'maintainer' as const },
    { id: 'user-3', name: 'Alex Rivera', role: 'author' as const },
    { id: 'user-4', name: 'CodeQL Bot', role: 'reviewer' as const }
  ];
  
  const commentContents = [
    'This function looks good but consider adding error handling for edge cases.',
    'Great implementation! Very clean and readable.',
    'I think we can optimize this by using a Map instead of an array.',
    'This might cause a memory leak. Consider using weak references.',
    'The variable name could be more descriptive.',
    'Excellent use of TypeScript generics here!',
    'This violates our coding standards for function length.',
    'Consider extracting this into a separate utility function.',
    'The algorithm complexity seems high. Can we do better?',
    'Perfect! This follows our established patterns.'
  ];
  
  for (let i = 0; i < maxComments; i++) {
    const author = authors[Math.floor(Math.random() * authors.length)];
    const content = commentContents[Math.floor(Math.random() * commentContents.length)];
    const type = (['comment', 'suggestion', 'issue', 'question', 'praise'] as const)[Math.floor(Math.random() * 5)];
    const severity = (['info', 'minor', 'major', 'critical'] as const)[Math.floor(Math.random() * 4)];
    const status = (['open', 'resolved', 'acknowledged'] as const)[Math.floor(Math.random() * 3)];
    
    mockComments.push({
      id: `comment-${i}`,
      author: { ...author, avatar: '' },
      content,
      filePath: `src/components/Component${Math.floor(Math.random() * 5) + 1}.tsx`,
      lineNumber: Math.floor(Math.random() * 200) + 1,
      type,
      severity,
      status,
      createdAt: now - (Math.random() * 7 * 24 * 60 * 60 * 1000), // Last 7 days
      updatedAt: now - (Math.random() * 24 * 60 * 60 * 1000), // Last 24 hours
      replies: [],
      reactions: [
        { type: 'like', users: Math.random() > 0.7 ? ['user-1', 'user-2'] : [] },
        { type: 'heart', users: Math.random() > 0.9 ? ['user-3'] : [] }
      ],
      tags: ['review', type === 'issue' ? 'bug' : 'enhancement'],
      isBot: author.name.includes('Bot'),
      threadId: `thread-${i}`,
      suggestedChange: type === 'suggestion' ? {
        original: 'const result = array.filter(item => item.active);',
        suggested: 'const result = array.filter(item => Boolean(item.active));',
        applied: false
      } : undefined
    });
  }
  
  return mockComments.sort((a, b) => b.createdAt - a.createdAt);
};

const generateMockReviewSessions = (): ReviewSession[] => {
  return [
    {
      id: 'session-1',
      reviewerName: 'Sarah Chen',
      startedAt: Date.now() - 3600000, // 1 hour ago
      status: 'in_progress',
      commentsCount: 5,
      issuesFound: 2
    },
    {
      id: 'session-2',
      reviewerName: 'Mike Johnson',
      startedAt: Date.now() - 7200000, // 2 hours ago
      completedAt: Date.now() - 1800000, // 30 minutes ago
      status: 'completed',
      commentsCount: 8,
      issuesFound: 1,
      overallRating: 'approve',
      summary: 'Looks good overall, just a few minor suggestions.'
    }
  ];
};

export default CodeReviewWidget;
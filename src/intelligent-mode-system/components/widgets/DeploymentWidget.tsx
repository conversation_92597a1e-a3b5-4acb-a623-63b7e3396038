import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { Textarea } from '../../../components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog';
import { 
  Rocket, 
  Search, 
  Filter, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Play,
  Pause,
  RotateCcw,
  Eye,
  RefreshCw,
  ExternalLink,
  Server,
  Globe,
  GitCommit,
  Zap,
  Monitor,
  Plus,
  Settings,
  Activity,
  TrendingUp
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface Deployment {
  id: string;
  name: string;
  environment: 'development' | 'staging' | 'production' | 'testing';
  status: 'pending' | 'running' | 'success' | 'failed' | 'cancelled' | 'rollback';
  priority: 'critical' | 'high' | 'medium' | 'low';
  startedAt: number;
  completedAt?: number;
  duration?: number;
  version: string;
  branch: string;
  commit: string;
  commitMessage: string;
  deployedBy: string;
  url?: string;
  logs?: string[];
  steps: Array<{
    id: string;
    name: string;
    status: 'pending' | 'running' | 'success' | 'failed' | 'skipped';
    duration?: number;
    output?: string;
  }>;
  metrics: {
    buildTime: number;
    deployTime: number;
    bundleSize?: string;
    testsPassed?: number;
    testsTotal?: number;
  };
  rollbackAvailable: boolean;
  health: {
    status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
    uptime?: number;
    responseTime?: number;
    errorRate?: number;
  };
}

interface DeploymentWidgetProps {
  context: ContextSnapshot;
  config?: {
    maxDeployments?: number;
    showFilters?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number;
    showMetrics?: boolean;
    maxHeight?: string;
  };
}

type StatusFilter = 'all' | 'pending' | 'running' | 'success' | 'failed' | 'cancelled';
type EnvironmentFilter = 'all' | 'development' | 'staging' | 'production' | 'testing';

export const DeploymentWidget: React.FC<DeploymentWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [deployments, setDeployments] = useState<Deployment[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [environmentFilter, setEnvironmentFilter] = useState<EnvironmentFilter>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDeployment, setSelectedDeployment] = useState<Deployment | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  // Load deployments on component mount
  useEffect(() => {
    loadDeployments();
  }, [context]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!config.autoRefresh) return;

    const interval = setInterval(() => {
      loadDeployments(false); // Don't show loading state for auto-refresh
    }, config.refreshInterval || 30000);

    return () => clearInterval(interval);
  }, [config.autoRefresh, config.refreshInterval]);

  const loadDeployments = async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    
    // Simulate API call - would be replaced with actual deployment fetching
    const mockDeployments = generateMockDeployments(context, config.maxDeployments || 20);
    
    setTimeout(() => {
      setDeployments(mockDeployments);
      if (showLoading) setIsLoading(false);
    }, showLoading ? 800 : 0);
  };

  // Filter deployments
  const filteredDeployments = useMemo(() => {
    return deployments.filter(deployment => {
      // Search filter
      const matchesSearch = !searchQuery || 
        deployment.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        deployment.version.toLowerCase().includes(searchQuery.toLowerCase()) ||
        deployment.branch.toLowerCase().includes(searchQuery.toLowerCase()) ||
        deployment.deployedBy.toLowerCase().includes(searchQuery.toLowerCase()) ||
        deployment.commitMessage.toLowerCase().includes(searchQuery.toLowerCase());

      // Status filter
      const matchesStatus = statusFilter === 'all' || deployment.status === statusFilter;

      // Environment filter
      const matchesEnvironment = environmentFilter === 'all' || deployment.environment === environmentFilter;

      return matchesSearch && matchesStatus && matchesEnvironment;
    }).sort((a, b) => b.startedAt - a.startedAt);
  }, [deployments, searchQuery, statusFilter, environmentFilter]);

  // Statistics
  const stats = useMemo(() => {
    const total = deployments.length;
    const running = deployments.filter(d => d.status === 'running').length;
    const success = deployments.filter(d => d.status === 'success').length;
    const failed = deployments.filter(d => d.status === 'failed').length;
    const avgDeployTime = deployments
      .filter(d => d.duration)
      .reduce((acc, d) => acc + (d.duration || 0), 0) / Math.max(1, deployments.filter(d => d.duration).length);

    return { total, running, success, failed, avgDeployTime };
  }, [deployments]);

  const handleRefresh = () => {
    loadDeployments();
  };

  const handleDeploy = (deploymentId: string) => {
    setDeployments(prev => prev.map(d => 
      d.id === deploymentId 
        ? { ...d, status: 'running', startedAt: Date.now() }
        : d
    ));
  };

  const handleRollback = (deploymentId: string) => {
    setDeployments(prev => prev.map(d => 
      d.id === deploymentId 
        ? { ...d, status: 'rollback', startedAt: Date.now() }
        : d
    ));
  };

  const handleCancel = (deploymentId: string) => {
    setDeployments(prev => prev.map(d => 
      d.id === deploymentId 
        ? { ...d, status: 'cancelled', completedAt: Date.now() }
        : d
    ));
  };

  const getStatusColor = (status: Deployment['status']) => {
    switch (status) {
      case 'pending': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30';
      case 'running': return 'text-blue-500 bg-blue-500/10 border-blue-500/30';
      case 'success': return 'text-green-500 bg-green-500/10 border-green-500/30';
      case 'failed': return 'text-red-500 bg-red-500/10 border-red-500/30';
      case 'cancelled': return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
      case 'rollback': return 'text-orange-500 bg-orange-500/10 border-orange-500/30';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getEnvironmentColor = (environment: Deployment['environment']) => {
    switch (environment) {
      case 'production': return 'text-red-400 bg-red-500/10 border-red-500/30';
      case 'staging': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
      case 'development': return 'text-blue-400 bg-blue-500/10 border-blue-500/30';
      case 'testing': return 'text-purple-400 bg-purple-500/10 border-purple-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getHealthColor = (health: Deployment['health']['status']) => {
    switch (health) {
      case 'healthy': return 'text-green-500';
      case 'degraded': return 'text-yellow-500';
      case 'unhealthy': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getPriorityColor = (priority: Deployment['priority']) => {
    switch (priority) {
      case 'critical': return 'text-red-500 bg-red-500/10 border-red-500/30';
      case 'high': return 'text-orange-500 bg-orange-500/10 border-orange-500/30';
      case 'medium': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30';
      case 'low': return 'text-blue-500 bg-blue-500/10 border-blue-500/30';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
    }
  };

  return (
    <Card className="deployment-widget h-full bg-gray-900 border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Rocket className="h-5 w-5" />
            Deployments
            <Badge variant="secondary" className="ml-2">
              {filteredDeployments.length}/{deployments.length}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => setShowCreateDialog(true)}
              className="h-8"
            >
              <Plus className="h-3 w-3 mr-1" />
              Deploy
            </Button>
          </div>
        </div>

        {/* Statistics */}
        {config.showMetrics !== false && (
          <div className="grid grid-cols-5 gap-2 mt-3">
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-blue-400">{stats.total}</div>
              <div className="text-xs text-gray-400">Total</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-blue-500">{stats.running}</div>
              <div className="text-xs text-gray-400">Running</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-green-400">{stats.success}</div>
              <div className="text-xs text-gray-400">Success</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-red-400">{stats.failed}</div>
              <div className="text-xs text-gray-400">Failed</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-purple-400">
                {Math.round(stats.avgDeployTime)}s
              </div>
              <div className="text-xs text-gray-400">Avg Time</div>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="p-0 flex-1 flex flex-col">
        {/* Filters */}
        {config.showFilters !== false && (
          <div className="px-4 pb-3 space-y-2">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search deployments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 h-8"
              />
            </div>

            {/* Filter Selects */}
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={(value: StatusFilter) => setStatusFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="running">Running</SelectItem>
                  <SelectItem value="success">Success</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>

              <Select value={environmentFilter} onValueChange={(value: EnvironmentFilter) => setEnvironmentFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Environment" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Environments</SelectItem>
                  <SelectItem value="development">Development</SelectItem>
                  <SelectItem value="staging">Staging</SelectItem>
                  <SelectItem value="production">Production</SelectItem>
                  <SelectItem value="testing">Testing</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Deployments List */}
        <div 
          className="deployments-content flex-1 overflow-y-auto px-4"
          style={{ maxHeight: config.maxHeight || '500px' }}
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-400">Loading deployments...</p>
            </div>
          ) : filteredDeployments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Rocket className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No deployments found</p>
              <p className="text-sm mt-1">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredDeployments.map(deployment => (
                <DeploymentItem
                  key={deployment.id}
                  deployment={deployment}
                  onDeploy={handleDeploy}
                  onRollback={handleRollback}
                  onCancel={handleCancel}
                  onSelect={() => setSelectedDeployment(deployment)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Deployment Details Modal */}
        {selectedDeployment && (
          <DeploymentDetailsDialog
            deployment={selectedDeployment}
            open={!!selectedDeployment}
            onOpenChange={(open) => !open && setSelectedDeployment(null)}
            onDeploy={handleDeploy}
            onRollback={handleRollback}
            onCancel={handleCancel}
          />
        )}

        {/* Create Deployment Dialog */}
        {showCreateDialog && (
          <CreateDeploymentDialog
            open={showCreateDialog}
            onOpenChange={setShowCreateDialog}
            context={context}
            onCreated={(deployment) => {
              setDeployments(prev => [deployment, ...prev]);
              setShowCreateDialog(false);
            }}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Deployment Item Component
const DeploymentItem: React.FC<{
  deployment: Deployment;
  onDeploy: (id: string) => void;
  onRollback: (id: string) => void;
  onCancel: (id: string) => void;
  onSelect: () => void;
}> = ({ deployment, onDeploy, onRollback, onCancel, onSelect }) => {
  const getStatusIcon = (status: Deployment['status']) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'running': return <Play className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled': return <Pause className="h-4 w-4 text-gray-500" />;
      case 'rollback': return <RotateCcw className="h-4 w-4 text-orange-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return 'N/A';
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
  };

  return (
    <div className="deployment-item p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors">
      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="flex items-center gap-2">
            {getStatusIcon(deployment.status)}
            <span className="font-medium text-white text-sm">{deployment.name}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={getEnvironmentColor(deployment.environment)}>
              {deployment.environment}
            </Badge>
            
            <Badge variant="outline" className={getStatusColor(deployment.status)}>
              {deployment.status}
            </Badge>
            
            {deployment.health.status !== 'unknown' && (
              <div className={`flex items-center gap-1 text-xs ${getHealthColor(deployment.health.status)}`}>
                <Activity className="h-3 w-3" />
                <span>{deployment.health.status}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Version and Branch Info */}
      <div className="flex items-center gap-4 mb-2 text-xs text-gray-400">
        <div className="flex items-center gap-1">
          <GitCommit className="h-3 w-3" />
          <span className="font-mono">{deployment.version}</span>
        </div>
        <div className="flex items-center gap-1">
          <span>from</span>
          <span className="font-mono text-blue-400">{deployment.branch}</span>
        </div>
        <div className="flex items-center gap-1">
          <span>by</span>
          <span>{deployment.deployedBy}</span>
        </div>
      </div>

      {/* Metrics */}
      <div className="flex items-center justify-between mb-2 text-xs text-gray-400">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>{formatDuration(deployment.duration)}</span>
          </div>
          
          {deployment.metrics.bundleSize && (
            <div className="flex items-center gap-1">
              <Zap className="h-3 w-3" />
              <span>{deployment.metrics.bundleSize}</span>
            </div>
          )}
          
          {deployment.metrics.testsTotal && (
            <div className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              <span>{deployment.metrics.testsPassed}/{deployment.metrics.testsTotal}</span>
            </div>
          )}
        </div>

        <div className="text-xs text-gray-500">
          {new Date(deployment.startedAt).toLocaleString()}
        </div>
      </div>

      {/* Commit Message */}
      <div className="text-sm text-gray-300 mb-3 line-clamp-2">
        {deployment.commitMessage}
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {deployment.status === 'pending' && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onDeploy(deployment.id);
              }}
              className="h-6 text-xs border-green-600 text-green-400 hover:bg-green-600/10"
            >
              <Play className="h-3 w-3 mr-1" />
              Deploy
            </Button>
          )}
          
          {deployment.status === 'running' && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onCancel(deployment.id);
              }}
              className="h-6 text-xs border-red-600 text-red-400 hover:bg-red-600/10"
            >
              <Pause className="h-3 w-3 mr-1" />
              Cancel
            </Button>
          )}
          
          {deployment.status === 'success' && deployment.rollbackAvailable && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onRollback(deployment.id);
              }}
              className="h-6 text-xs border-orange-600 text-orange-400 hover:bg-orange-600/10"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Rollback
            </Button>
          )}
          
          {deployment.url && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                window.open(deployment.url, '_blank');
              }}
              className="h-6 text-xs border-blue-600 text-blue-400 hover:bg-blue-600/10"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              View
            </Button>
          )}
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={onSelect}
          className="h-auto p-0 text-xs text-blue-400 hover:text-blue-300"
        >
          View Details
        </Button>
      </div>
    </div>
  );
};

// Deployment Details Dialog
const DeploymentDetailsDialog: React.FC<{
  deployment: Deployment;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDeploy: (id: string) => void;
  onRollback: (id: string) => void;
  onCancel: (id: string) => void;
}> = ({ deployment, open, onOpenChange, onDeploy, onRollback, onCancel }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Rocket className="h-5 w-5" />
            {deployment.name}
          </DialogTitle>
          <DialogDescription>
            Deployment details for {deployment.version} to {deployment.environment}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Implementation would include full deployment details, logs, steps, etc. */}
          <div className="text-center py-8 text-gray-500">
            <p>Full deployment details would be displayed here</p>
            <p className="text-sm mt-1">Including logs, steps, metrics, and health information</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Create Deployment Dialog
const CreateDeploymentDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  context: ContextSnapshot;
  onCreated: (deployment: Deployment) => void;
}> = ({ open, onOpenChange, context, onCreated }) => {
  const [name, setName] = useState('');
  const [environment, setEnvironment] = useState<Deployment['environment']>('development');
  const [branch, setBranch] = useState('main');

  const handleCreate = () => {
    const newDeployment: Deployment = {
      id: `deploy-${Date.now()}`,
      name: name || `Deploy ${branch}`,
      environment,
      status: 'pending',
      priority: 'medium',
      startedAt: Date.now(),
      version: `v1.${Math.floor(Math.random() * 100)}.0`,
      branch,
      commit: Math.random().toString(36).substring(2, 10),
      commitMessage: 'Latest changes from ' + branch,
      deployedBy: 'Current User',
      logs: [],
      steps: [
        { id: 's1', name: 'Build', status: 'pending' },
        { id: 's2', name: 'Test', status: 'pending' },
        { id: 's3', name: 'Deploy', status: 'pending' },
      ],
      metrics: {
        buildTime: 0,
        deployTime: 0,
        bundleSize: '2.5MB',
        testsPassed: 0,
        testsTotal: 10,
      },
      rollbackAvailable: false,
      health: {
        status: 'unknown'
      }
    };
    
    onCreated(newDeployment);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700">
        <DialogHeader>
          <DialogTitle>Create Deployment</DialogTitle>
          <DialogDescription>
            Deploy your application to the selected environment
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <Input
            placeholder="Deployment name (optional)"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="bg-gray-800 border-gray-600"
          />
          
          <Select value={environment} onValueChange={(value: Deployment['environment']) => setEnvironment(value)}>
            <SelectTrigger className="bg-gray-800 border-gray-600">
              <SelectValue placeholder="Select environment" />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="development">Development</SelectItem>
              <SelectItem value="staging">Staging</SelectItem>
              <SelectItem value="production">Production</SelectItem>
              <SelectItem value="testing">Testing</SelectItem>
            </SelectContent>
          </Select>
          
          <Input
            placeholder="Branch name"
            value={branch}
            onChange={(e) => setBranch(e.target.value)}
            className="bg-gray-800 border-gray-600"
          />
          
          <div className="flex gap-2">
            <Button onClick={handleCreate} disabled={!branch} className="flex-1">
              <Rocket className="h-4 w-4 mr-2" />
              Deploy
            </Button>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper function to generate mock deployments
const generateMockDeployments = (context: ContextSnapshot, maxDeployments: number): Deployment[] => {
  const mockDeployments: Deployment[] = [];
  const now = Date.now();
  
  const names = [
    'Frontend App',
    'API Server',
    'Database Migration',
    'CDN Update',
    'Security Patches',
    'Feature Release',
    'Hotfix Deploy',
    'Performance Update'
  ];
  
  const environments: Deployment['environment'][] = ['development', 'staging', 'production', 'testing'];
  const statuses: Deployment['status'][] = ['pending', 'running', 'success', 'failed', 'cancelled'];
  const priorities: Deployment['priority'][] = ['critical', 'high', 'medium', 'low'];
  const branches = ['main', 'develop', 'feature/auth', 'hotfix/security', 'release/v2.0'];
  
  for (let i = 0; i < maxDeployments; i++) {
    const name = names[Math.floor(Math.random() * names.length)];
    const environment = environments[Math.floor(Math.random() * environments.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const priority = priorities[Math.floor(Math.random() * priorities.length)];
    const branch = branches[Math.floor(Math.random() * branches.length)];
    const duration = status === 'success' || status === 'failed' ? Math.floor(Math.random() * 300) + 30 : undefined;
    
    mockDeployments.push({
      id: `deploy-${i}`,
      name: `${name} #${100 + i}`,
      environment,
      status,
      priority,
      startedAt: now - (Math.random() * 7 * 24 * 60 * 60 * 1000), // Last 7 days
      completedAt: duration ? now - (Math.random() * 24 * 60 * 60 * 1000) : undefined,
      duration,
      version: `v1.${Math.floor(Math.random() * 100)}.${Math.floor(Math.random() * 10)}`,
      branch,
      commit: Math.random().toString(36).substring(2, 10),
      commitMessage: `Update ${name.toLowerCase()} with latest changes`,
      deployedBy: ['Alice Johnson', 'Bob Smith', 'Carol Davis'][Math.floor(Math.random() * 3)],
      url: environment === 'production' ? `https://${name.toLowerCase().replace(' ', '-')}.app` : undefined,
      logs: [],
      steps: [
        { id: 's1', name: 'Build', status: status === 'pending' ? 'pending' : 'success' },
        { id: 's2', name: 'Test', status: status === 'pending' ? 'pending' : status === 'running' ? 'running' : 'success' },
        { id: 's3', name: 'Deploy', status: status === 'success' ? 'success' : status === 'failed' ? 'failed' : 'pending' },
      ],
      metrics: {
        buildTime: Math.floor(Math.random() * 120) + 30,
        deployTime: duration || 0,
        bundleSize: `${(Math.random() * 5 + 1).toFixed(1)}MB`,
        testsPassed: Math.floor(Math.random() * 50) + 40,
        testsTotal: 50,
      },
      rollbackAvailable: status === 'success' && environment === 'production',
      health: {
        status: status === 'success' ? 
          (['healthy', 'degraded', 'unhealthy'][Math.floor(Math.random() * 3)] as any) : 
          'unknown',
        uptime: status === 'success' ? Math.random() * 100 : undefined,
        responseTime: status === 'success' ? Math.floor(Math.random() * 500) + 100 : undefined,
        errorRate: status === 'success' ? Math.random() * 5 : undefined,
      }
    });
  }
  
  return mockDeployments.sort((a, b) => b.startedAt - a.startedAt);
};

export default DeploymentWidget;
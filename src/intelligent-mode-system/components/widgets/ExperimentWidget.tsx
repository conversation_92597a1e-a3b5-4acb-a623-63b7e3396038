import React, { useState, useEffect, use<PERSON>emo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { Textarea } from '../../../components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog';
import { 
  Beaker, 
  Search, 
  Filter, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Play,
  Pause,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Eye,
  RefreshCw,
  Plus,
  Settings,
  Users,
  Target,
  Zap,
  Activity,
  Calendar,
  Hash,
  Percent,
  ArrowRight
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface Experiment {
  id: string;
  name: string;
  description: string;
  type: 'a_b_test' | 'feature_flag' | 'canary' | 'multivariate' | 'split_test';
  status: 'draft' | 'running' | 'paused' | 'completed' | 'cancelled';
  startedAt?: number;
  endedAt?: number;
  duration?: number;
  owner: string;
  hypothesis: string;
  variants: Array<{
    id: string;
    name: string;
    description: string;
    traffic: number;
    isControl: boolean;
    metrics: {
      conversions: number;
      visitors: number;
      conversionRate: number;
      confidence: number;
      significance: boolean;
    };
  }>;
  targetAudience: {
    percentage: number;
    segments: string[];
    location?: string[];
    device?: string[];
  };
  metrics: {
    totalVisitors: number;
    totalConversions: number;
    overallConversionRate: number;
    statisticalPower: number;
    confidenceLevel: number;
  };
  results?: {
    winner?: string;
    lift: number;
    confidence: number;
    significance: boolean;
    recommendedAction: 'implement' | 'discard' | 'iterate' | 'extend';
  };
  tags: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
}

interface ExperimentWidgetProps {
  context: ContextSnapshot;
  config?: {
    maxExperiments?: number;
    showFilters?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number;
    showMetrics?: boolean;
    maxHeight?: string;
  };
}

type StatusFilter = 'all' | 'draft' | 'running' | 'paused' | 'completed' | 'cancelled';
type TypeFilter = 'all' | 'a_b_test' | 'feature_flag' | 'canary' | 'multivariate' | 'split_test';

export const ExperimentWidget: React.FC<ExperimentWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [experiments, setExperiments] = useState<Experiment[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [typeFilter, setTypeFilter] = useState<TypeFilter>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedExperiment, setSelectedExperiment] = useState<Experiment | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  // Load experiments on component mount
  useEffect(() => {
    loadExperiments();
  }, [context]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!config.autoRefresh) return;

    const interval = setInterval(() => {
      loadExperiments(false); // Don't show loading state for auto-refresh
    }, config.refreshInterval || 30000);

    return () => clearInterval(interval);
  }, [config.autoRefresh, config.refreshInterval]);

  const loadExperiments = async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    
    // Simulate API call - would be replaced with actual experiment fetching
    const mockExperiments = generateMockExperiments(context, config.maxExperiments || 15);
    
    setTimeout(() => {
      setExperiments(mockExperiments);
      if (showLoading) setIsLoading(false);
    }, showLoading ? 800 : 0);
  };

  // Filter experiments
  const filteredExperiments = useMemo(() => {
    return experiments.filter(experiment => {
      // Search filter
      const matchesSearch = !searchQuery || 
        experiment.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        experiment.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        experiment.hypothesis.toLowerCase().includes(searchQuery.toLowerCase()) ||
        experiment.owner.toLowerCase().includes(searchQuery.toLowerCase()) ||
        experiment.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      // Status filter
      const matchesStatus = statusFilter === 'all' || experiment.status === statusFilter;

      // Type filter
      const matchesType = typeFilter === 'all' || experiment.type === typeFilter;

      return matchesSearch && matchesStatus && matchesType;
    }).sort((a, b) => {
      // Sort by priority, then by start date
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return (b.startedAt || 0) - (a.startedAt || 0);
    });
  }, [experiments, searchQuery, statusFilter, typeFilter]);

  // Statistics
  const stats = useMemo(() => {
    const total = experiments.length;
    const running = experiments.filter(e => e.status === 'running').length;
    const completed = experiments.filter(e => e.status === 'completed').length;
    const successful = experiments.filter(e => e.results?.significance).length;
    const avgLift = experiments
      .filter(e => e.results?.lift)
      .reduce((acc, e) => acc + (e.results?.lift || 0), 0) / Math.max(1, experiments.filter(e => e.results?.lift).length);

    return { total, running, completed, successful, avgLift };
  }, [experiments]);

  const handleRefresh = () => {
    loadExperiments();
  };

  const handleStartExperiment = (experimentId: string) => {
    setExperiments(prev => prev.map(e => 
      e.id === experimentId 
        ? { ...e, status: 'running', startedAt: Date.now() }
        : e
    ));
  };

  const handlePauseExperiment = (experimentId: string) => {
    setExperiments(prev => prev.map(e => 
      e.id === experimentId 
        ? { ...e, status: 'paused' }
        : e
    ));
  };

  const handleStopExperiment = (experimentId: string) => {
    setExperiments(prev => prev.map(e => 
      e.id === experimentId 
        ? { ...e, status: 'completed', endedAt: Date.now() }
        : e
    ));
  };

  const getStatusColor = (status: Experiment['status']) => {
    switch (status) {
      case 'draft': return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
      case 'running': return 'text-green-500 bg-green-500/10 border-green-500/30';
      case 'paused': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30';
      case 'completed': return 'text-blue-500 bg-blue-500/10 border-blue-500/30';
      case 'cancelled': return 'text-red-500 bg-red-500/10 border-red-500/30';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getTypeColor = (type: Experiment['type']) => {
    switch (type) {
      case 'a_b_test': return 'text-blue-400 bg-blue-500/10 border-blue-500/30';
      case 'feature_flag': return 'text-green-400 bg-green-500/10 border-green-500/30';
      case 'canary': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
      case 'multivariate': return 'text-purple-400 bg-purple-500/10 border-purple-500/30';
      case 'split_test': return 'text-pink-400 bg-pink-500/10 border-pink-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getPriorityColor = (priority: Experiment['priority']) => {
    switch (priority) {
      case 'critical': return 'text-red-500 bg-red-500/10 border-red-500/30';
      case 'high': return 'text-orange-500 bg-orange-500/10 border-orange-500/30';
      case 'medium': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30';
      case 'low': return 'text-blue-500 bg-blue-500/10 border-blue-500/30';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
    }
  };

  return (
    <Card className="experiment-widget h-full bg-gray-900 border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Beaker className="h-5 w-5" />
            Experiments
            <Badge variant="secondary" className="ml-2">
              {filteredExperiments.length}/{experiments.length}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => setShowCreateDialog(true)}
              className="h-8"
            >
              <Plus className="h-3 w-3 mr-1" />
              New Test
            </Button>
          </div>
        </div>

        {/* Statistics */}
        {config.showMetrics !== false && (
          <div className="grid grid-cols-5 gap-2 mt-3">
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-blue-400">{stats.total}</div>
              <div className="text-xs text-gray-400">Total</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-green-400">{stats.running}</div>
              <div className="text-xs text-gray-400">Running</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-blue-500">{stats.completed}</div>
              <div className="text-xs text-gray-400">Complete</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-purple-400">{stats.successful}</div>
              <div className="text-xs text-gray-400">Significant</div>
            </div>
            <div className="bg-gray-800 rounded p-2 text-center">
              <div className="text-lg font-semibold text-yellow-400">
                {stats.avgLift > 0 ? '+' : ''}{stats.avgLift.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-400">Avg Lift</div>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="p-0 flex-1 flex flex-col">
        {/* Filters */}
        {config.showFilters !== false && (
          <div className="px-4 pb-3 space-y-2">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search experiments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 h-8"
              />
            </div>

            {/* Filter Selects */}
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={(value: StatusFilter) => setStatusFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="running">Running</SelectItem>
                  <SelectItem value="paused">Paused</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={(value: TypeFilter) => setTypeFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="a_b_test">A/B Test</SelectItem>
                  <SelectItem value="feature_flag">Feature Flag</SelectItem>
                  <SelectItem value="canary">Canary</SelectItem>
                  <SelectItem value="multivariate">Multivariate</SelectItem>
                  <SelectItem value="split_test">Split Test</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Experiments List */}
        <div 
          className="experiments-content flex-1 overflow-y-auto px-4"
          style={{ maxHeight: config.maxHeight || '500px' }}
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-400">Loading experiments...</p>
            </div>
          ) : filteredExperiments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Beaker className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No experiments found</p>
              <p className="text-sm mt-1">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredExperiments.map(experiment => (
                <ExperimentItem
                  key={experiment.id}
                  experiment={experiment}
                  onStart={handleStartExperiment}
                  onPause={handlePauseExperiment}
                  onStop={handleStopExperiment}
                  onSelect={() => setSelectedExperiment(experiment)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Experiment Details Modal */}
        {selectedExperiment && (
          <ExperimentDetailsDialog
            experiment={selectedExperiment}
            open={!!selectedExperiment}
            onOpenChange={(open) => !open && setSelectedExperiment(null)}
            onStart={handleStartExperiment}
            onPause={handlePauseExperiment}
            onStop={handleStopExperiment}
          />
        )}

        {/* Create Experiment Dialog */}
        {showCreateDialog && (
          <CreateExperimentDialog
            open={showCreateDialog}
            onOpenChange={setShowCreateDialog}
            context={context}
            onCreated={(experiment) => {
              setExperiments(prev => [experiment, ...prev]);
              setShowCreateDialog(false);
            }}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Experiment Item Component
const ExperimentItem: React.FC<{
  experiment: Experiment;
  onStart: (id: string) => void;
  onPause: (id: string) => void;
  onStop: (id: string) => void;
  onSelect: () => void;
}> = ({ experiment, onStart, onPause, onStop, onSelect }) => {
  const getStatusIcon = (status: Experiment['status']) => {
    switch (status) {
      case 'draft': return <Settings className="h-4 w-4 text-gray-500" />;
      case 'running': return <Play className="h-4 w-4 text-green-500 animate-pulse" />;
      case 'paused': return <Pause className="h-4 w-4 text-yellow-500" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'cancelled': return <XCircle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatType = (type: Experiment['type']) => {
    switch (type) {
      case 'a_b_test': return 'A/B Test';
      case 'feature_flag': return 'Feature Flag';
      case 'canary': return 'Canary';
      case 'multivariate': return 'Multivariate';
      case 'split_test': return 'Split Test';
      default: return type;
    }
  };

  const controlVariant = experiment.variants.find(v => v.isControl);
  const testVariant = experiment.variants.find(v => !v.isControl);

  return (
    <div className="experiment-item p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors">
      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="flex items-center gap-2">
            {getStatusIcon(experiment.status)}
            <span className="font-medium text-white text-sm">{experiment.name}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={getTypeColor(experiment.type)}>
              {formatType(experiment.type)}
            </Badge>
            
            <Badge variant="outline" className={getStatusColor(experiment.status)}>
              {experiment.status}
            </Badge>
            
            <Badge variant="outline" className={getPriorityColor(experiment.priority)}>
              {experiment.priority}
            </Badge>
          </div>
        </div>
      </div>

      {/* Description and Hypothesis */}
      <div className="mb-2">
        <div className="text-sm text-gray-300 mb-1 line-clamp-1">
          {experiment.description}
        </div>
        <div className="text-xs text-gray-400 line-clamp-1">
          <span className="font-medium">Hypothesis:</span> {experiment.hypothesis}
        </div>
      </div>

      {/* Metrics and Variants */}
      {experiment.status === 'running' || experiment.status === 'completed' ? (
        <div className="flex items-center justify-between mb-2 text-xs">
          <div className="flex items-center gap-4 text-gray-400">
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <span>{experiment.metrics.totalVisitors.toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              <span>{experiment.metrics.overallConversionRate.toFixed(1)}%</span>
            </div>
            <div className="flex items-center gap-1">
              <BarChart3 className="h-3 w-3" />
              <span>{experiment.metrics.confidenceLevel}%</span>
            </div>
          </div>

          {experiment.results && (
            <div className="flex items-center gap-1">
              {experiment.results.lift > 0 ? (
                <TrendingUp className="h-3 w-3 text-green-400" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-400" />
              )}
              <span className={experiment.results.lift > 0 ? 'text-green-400' : 'text-red-400'}>
                {experiment.results.lift > 0 ? '+' : ''}{experiment.results.lift.toFixed(1)}%
              </span>
              {experiment.results.significance && (
                <CheckCircle className="h-3 w-3 text-green-400 ml-1" />
              )}
            </div>
          )}
        </div>
      ) : (
        <div className="flex items-center gap-4 mb-2 text-xs text-gray-400">
          <div className="flex items-center gap-1">
            <Percent className="h-3 w-3" />
            <span>{experiment.targetAudience.percentage}% traffic</span>
          </div>
          <div className="flex items-center gap-1">
            <Hash className="h-3 w-3" />
            <span>{experiment.variants.length} variants</span>
          </div>
        </div>
      )}

      {/* Variant Performance (for running/completed experiments) */}
      {(experiment.status === 'running' || experiment.status === 'completed') && controlVariant && testVariant && (
        <div className="flex items-center gap-2 mb-2 text-xs">
          <div className="flex-1 bg-gray-700 rounded p-2">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Control</span>
              <span className="text-white">{controlVariant.metrics.conversionRate.toFixed(1)}%</span>
            </div>
          </div>
          <ArrowRight className="h-3 w-3 text-gray-500" />
          <div className="flex-1 bg-gray-700 rounded p-2">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Variant</span>
              <span className={testVariant.metrics.conversionRate > controlVariant.metrics.conversionRate ? 'text-green-400' : 'text-red-400'}>
                {testVariant.metrics.conversionRate.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Owner and Tags */}
      <div className="flex items-center justify-between mb-2 text-xs text-gray-400">
        <div className="flex items-center gap-2">
          <span>by {experiment.owner}</span>
          {experiment.startedAt && (
            <>
              <span>•</span>
              <span>{new Date(experiment.startedAt).toLocaleDateString()}</span>
            </>
          )}
        </div>
        
        {experiment.tags.length > 0 && (
          <div className="flex items-center gap-1">
            {experiment.tags.slice(0, 2).map(tag => (
              <Badge key={tag} variant="outline" className="text-xs h-4 px-1">
                {tag}
              </Badge>
            ))}
            {experiment.tags.length > 2 && (
              <span className="text-gray-500">+{experiment.tags.length - 2}</span>
            )}
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {experiment.status === 'draft' && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onStart(experiment.id);
              }}
              className="h-6 text-xs border-green-600 text-green-400 hover:bg-green-600/10"
            >
              <Play className="h-3 w-3 mr-1" />
              Start
            </Button>
          )}
          
          {experiment.status === 'running' && (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  onPause(experiment.id);
                }}
                className="h-6 text-xs border-yellow-600 text-yellow-400 hover:bg-yellow-600/10"
              >
                <Pause className="h-3 w-3 mr-1" />
                Pause
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  onStop(experiment.id);
                }}
                className="h-6 text-xs border-blue-600 text-blue-400 hover:bg-blue-600/10"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Stop
              </Button>
            </>
          )}
          
          {experiment.status === 'paused' && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onStart(experiment.id);
              }}
              className="h-6 text-xs border-green-600 text-green-400 hover:bg-green-600/10"
            >
              <Play className="h-3 w-3 mr-1" />
              Resume
            </Button>
          )}
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={onSelect}
          className="h-auto p-0 text-xs text-blue-400 hover:text-blue-300"
        >
          View Details
        </Button>
      </div>
    </div>
  );
};

// Experiment Details Dialog
const ExperimentDetailsDialog: React.FC<{
  experiment: Experiment;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStart: (id: string) => void;
  onPause: (id: string) => void;
  onStop: (id: string) => void;
}> = ({ experiment, open, onOpenChange, onStart, onPause, onStop }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Beaker className="h-5 w-5" />
            {experiment.name}
          </DialogTitle>
          <DialogDescription>
            {experiment.description}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Implementation would include full experiment details, results, charts, etc. */}
          <div className="text-center py-8 text-gray-500">
            <p>Full experiment details would be displayed here</p>
            <p className="text-sm mt-1">Including results, variant performance, statistical analysis, and recommendations</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Create Experiment Dialog
const CreateExperimentDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  context: ContextSnapshot;
  onCreated: (experiment: Experiment) => void;
}> = ({ open, onOpenChange, context, onCreated }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [hypothesis, setHypothesis] = useState('');
  const [type, setType] = useState<Experiment['type']>('a_b_test');

  const handleCreate = () => {
    const newExperiment: Experiment = {
      id: `exp-${Date.now()}`,
      name,
      description,
      type,
      status: 'draft',
      owner: 'Current User',
      hypothesis,
      variants: [
        {
          id: 'control',
          name: 'Control',
          description: 'Original version',
          traffic: 50,
          isControl: true,
          metrics: {
            conversions: 0,
            visitors: 0,
            conversionRate: 0,
            confidence: 0,
            significance: false
          }
        },
        {
          id: 'variant-a',
          name: 'Variant A',
          description: 'Test version',
          traffic: 50,
          isControl: false,
          metrics: {
            conversions: 0,
            visitors: 0,
            conversionRate: 0,
            confidence: 0,
            significance: false
          }
        }
      ],
      targetAudience: {
        percentage: 100,
        segments: ['all-users']
      },
      metrics: {
        totalVisitors: 0,
        totalConversions: 0,
        overallConversionRate: 0,
        statisticalPower: 80,
        confidenceLevel: 95
      },
      tags: ['new'],
      priority: 'medium'
    };
    
    onCreated(newExperiment);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700">
        <DialogHeader>
          <DialogTitle>Create Experiment</DialogTitle>
          <DialogDescription>
            Set up a new A/B test or experiment
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <Input
            placeholder="Experiment name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="bg-gray-800 border-gray-600"
          />
          
          <Textarea
            placeholder="Describe what you're testing..."
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="bg-gray-800 border-gray-600 min-h-[60px]"
          />
          
          <Textarea
            placeholder="Your hypothesis (what do you expect to happen?)..."
            value={hypothesis}
            onChange={(e) => setHypothesis(e.target.value)}
            className="bg-gray-800 border-gray-600 min-h-[60px]"
          />
          
          <Select value={type} onValueChange={(value: Experiment['type']) => setType(value)}>
            <SelectTrigger className="bg-gray-800 border-gray-600">
              <SelectValue placeholder="Select experiment type" />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="a_b_test">A/B Test</SelectItem>
              <SelectItem value="feature_flag">Feature Flag</SelectItem>
              <SelectItem value="canary">Canary Release</SelectItem>
              <SelectItem value="multivariate">Multivariate Test</SelectItem>
              <SelectItem value="split_test">Split Test</SelectItem>
            </SelectContent>
          </Select>
          
          <div className="flex gap-2">
            <Button onClick={handleCreate} disabled={!name || !description} className="flex-1">
              <Beaker className="h-4 w-4 mr-2" />
              Create Experiment
            </Button>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper function to generate mock experiments
const generateMockExperiments = (context: ContextSnapshot, maxExperiments: number): Experiment[] => {
  const mockExperiments: Experiment[] = [];
  const now = Date.now();
  
  const names = [
    'Button Color Test',
    'Checkout Flow Optimization',
    'Landing Page Hero',
    'Pricing Display Test',
    'Navigation Menu Layout',
    'Form Field Validation',
    'Product Image Size',
    'Call-to-Action Text'
  ];
  
  const types: Experiment['type'][] = ['a_b_test', 'feature_flag', 'canary', 'multivariate', 'split_test'];
  const statuses: Experiment['status'][] = ['draft', 'running', 'paused', 'completed', 'cancelled'];
  const priorities: Experiment['priority'][] = ['low', 'medium', 'high', 'critical'];
  const owners = ['Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson'];
  
  for (let i = 0; i < maxExperiments; i++) {
    const name = names[Math.floor(Math.random() * names.length)];
    const type = types[Math.floor(Math.random() * types.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const priority = priorities[Math.floor(Math.random() * priorities.length)];
    const owner = owners[Math.floor(Math.random() * owners.length)];
    
    const isRunningOrCompleted = status === 'running' || status === 'completed';
    const totalVisitors = isRunningOrCompleted ? Math.floor(Math.random() * 10000) + 1000 : 0;
    const controlConversions = isRunningOrCompleted ? Math.floor(totalVisitors * 0.5 * (Math.random() * 0.1 + 0.05)) : 0;
    const variantConversions = isRunningOrCompleted ? Math.floor(totalVisitors * 0.5 * (Math.random() * 0.1 + 0.05)) : 0;
    const controlRate = controlConversions / (totalVisitors * 0.5) * 100;
    const variantRate = variantConversions / (totalVisitors * 0.5) * 100;
    const lift = isRunningOrCompleted ? ((variantRate - controlRate) / controlRate * 100) : 0;
    
    mockExperiments.push({
      id: `exp-${i}`,
      name: `${name} #${100 + i}`,
      description: `Testing ${name.toLowerCase()} to improve conversion rates and user experience.`,
      type,
      status,
      startedAt: isRunningOrCompleted ? now - (Math.random() * 30 * 24 * 60 * 60 * 1000) : undefined,
      endedAt: status === 'completed' ? now - (Math.random() * 7 * 24 * 60 * 60 * 1000) : undefined,
      owner,
      hypothesis: `Changing the ${name.toLowerCase()} will increase conversion rates by 15-20%.`,
      variants: [
        {
          id: 'control',
          name: 'Control',
          description: 'Original version',
          traffic: 50,
          isControl: true,
          metrics: {
            conversions: controlConversions,
            visitors: Math.floor(totalVisitors * 0.5),
            conversionRate: controlRate,
            confidence: isRunningOrCompleted ? Math.random() * 100 : 0,
            significance: false
          }
        },
        {
          id: 'variant-a',
          name: 'Variant A',
          description: 'Test version',
          traffic: 50,
          isControl: false,
          metrics: {
            conversions: variantConversions,
            visitors: Math.floor(totalVisitors * 0.5),
            conversionRate: variantRate,
            confidence: isRunningOrCompleted ? Math.random() * 100 : 0,
            significance: isRunningOrCompleted && Math.abs(lift) > 5 && Math.random() > 0.3
          }
        }
      ],
      targetAudience: {
        percentage: Math.floor(Math.random() * 50) + 50,
        segments: ['new-users', 'returning-users'][Math.floor(Math.random() * 2)] === 'new-users' ? ['new-users'] : ['returning-users'],
        location: Math.random() > 0.5 ? ['US', 'CA'] : undefined,
        device: Math.random() > 0.5 ? ['mobile', 'desktop'] : undefined
      },
      metrics: {
        totalVisitors,
        totalConversions: controlConversions + variantConversions,
        overallConversionRate: totalVisitors > 0 ? (controlConversions + variantConversions) / totalVisitors * 100 : 0,
        statisticalPower: 80,
        confidenceLevel: 95
      },
      results: isRunningOrCompleted && status === 'completed' ? {
        winner: Math.abs(lift) > 5 ? (lift > 0 ? 'variant-a' : 'control') : undefined,
        lift,
        confidence: Math.random() * 100,
        significance: Math.abs(lift) > 5 && Math.random() > 0.3,
        recommendedAction: Math.abs(lift) > 10 ? 'implement' : Math.abs(lift) > 5 ? 'extend' : 'iterate'
      } : undefined,
      tags: ['conversion', 'ui-ux', 'frontend'].slice(0, Math.floor(Math.random() * 3) + 1),
      priority
    });
  }
  
  return mockExperiments.sort((a, b) => (b.startedAt || 0) - (a.startedAt || 0));
};

export default ExperimentWidget;
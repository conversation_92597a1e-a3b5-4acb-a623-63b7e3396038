import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { Textarea } from '../../../components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../../components/ui/dialog';
import { 
  CheckSquare, 
  Plus, 
  Filter, 
  Search, 
  AlertTriangle,
  CheckCircle,
  Clock,
  User,
  Target,
  Zap,
  Shield
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface Requirement {
  id: string;
  title: string;
  description: string;
  type: 'functional' | 'non-functional' | 'business' | 'technical';
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'draft' | 'approved' | 'in-progress' | 'completed' | 'blocked';
  assignee?: string;
  dueDate?: string;
  acceptance_criteria: string[];
  dependencies: string[];
  tags: string[];
  created_at: number;
  updated_at: number;
  estimated_effort?: number; // in hours
  actual_effort?: number; // in hours
}

interface RequirementsWidgetProps {
  context: ContextSnapshot;
  config?: {
    showFilters?: boolean;
    allowEditing?: boolean;
    defaultFilter?: string;
    maxHeight?: string;
  };
}

type FilterType = 'all' | 'functional' | 'non-functional' | 'business' | 'technical';
type StatusFilter = 'all' | 'draft' | 'approved' | 'in-progress' | 'completed' | 'blocked';
type PriorityFilter = 'all' | 'low' | 'medium' | 'high' | 'critical';

export const RequirementsWidget: React.FC<RequirementsWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [requirements, setRequirements] = useState<Requirement[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<FilterType>('all');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [priorityFilter, setPriorityFilter] = useState<PriorityFilter>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [selectedRequirement, setSelectedRequirement] = useState<Requirement | null>(null);

  // Load requirements on component mount
  useEffect(() => {
    loadRequirements();
  }, [context]);

  const loadRequirements = async () => {
    setIsLoading(true);
    // Simulate API call - would be replaced with actual data fetching
    const mockRequirements = generateMockRequirements(context);
    setTimeout(() => {
      setRequirements(mockRequirements);
      setIsLoading(false);
    }, 500);
  };

  // Filter and search requirements
  const filteredRequirements = useMemo(() => {
    return requirements.filter(req => {
      // Search filter
      const matchesSearch = !searchQuery || 
        req.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        req.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        req.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      // Type filter
      const matchesType = typeFilter === 'all' || req.type === typeFilter;

      // Status filter
      const matchesStatus = statusFilter === 'all' || req.status === statusFilter;

      // Priority filter
      const matchesPriority = priorityFilter === 'all' || req.priority === priorityFilter;

      return matchesSearch && matchesType && matchesStatus && matchesPriority;
    }).sort((a, b) => {
      // Sort by priority first, then by updated date
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return b.updated_at - a.updated_at;
    });
  }, [requirements, searchQuery, typeFilter, statusFilter, priorityFilter]);

  // Statistics
  const stats = useMemo(() => {
    const total = requirements.length;
    const completed = requirements.filter(r => r.status === 'completed').length;
    const inProgress = requirements.filter(r => r.status === 'in-progress').length;
    const blocked = requirements.filter(r => r.status === 'blocked').length;
    const highPriority = requirements.filter(r => r.priority === 'high' || r.priority === 'critical').length;

    return { total, completed, inProgress, blocked, highPriority };
  }, [requirements]);

  const handleStatusChange = (reqId: string, newStatus: Requirement['status']) => {
    setRequirements(prev => prev.map(req => 
      req.id === reqId 
        ? { ...req, status: newStatus, updated_at: Date.now() }
        : req
    ));
  };

  const handleAddRequirement = (newReq: Omit<Requirement, 'id' | 'created_at' | 'updated_at'>) => {
    const requirement: Requirement = {
      ...newReq,
      id: `req-${Date.now()}`,
      created_at: Date.now(),
      updated_at: Date.now()
    };
    setRequirements(prev => [requirement, ...prev]);
    setShowAddDialog(false);
  };

  const getPriorityColor = (priority: Requirement['priority']) => {
    switch (priority) {
      case 'critical': return 'text-red-500 bg-red-500/10 border-red-500/30';
      case 'high': return 'text-orange-500 bg-orange-500/10 border-orange-500/30';
      case 'medium': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30';
      case 'low': return 'text-green-500 bg-green-500/10 border-green-500/30';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: Requirement['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in-progress': return <Clock className="h-4 w-4 text-blue-500" />;
      case 'blocked': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'approved': return <CheckSquare className="h-4 w-4 text-green-500" />;
      default: return <CheckSquare className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTypeIcon = (type: Requirement['type']) => {
    switch (type) {
      case 'functional': return <Zap className="h-4 w-4 text-blue-500" />;
      case 'non-functional': return <Shield className="h-4 w-4 text-purple-500" />;
      case 'business': return <Target className="h-4 w-4 text-green-500" />;
      case 'technical': return <CheckSquare className="h-4 w-4 text-orange-500" />;
      default: return <CheckSquare className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card className="requirements-widget h-full bg-gray-900 border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            Requirements
            <Badge variant="secondary" className="ml-2">
              {filteredRequirements.length}/{requirements.length}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {config.allowEditing !== false && (
              <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
                <DialogTrigger asChild>
                  <Button size="sm" variant="outline" className="h-8 w-8 p-0">
                    <Plus className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <AddRequirementDialog onAdd={handleAddRequirement} />
              </Dialog>
            )}
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-5 gap-2 mt-3">
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-blue-400">{stats.total}</div>
            <div className="text-xs text-gray-400">Total</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-green-400">{stats.completed}</div>
            <div className="text-xs text-gray-400">Done</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-yellow-400">{stats.inProgress}</div>
            <div className="text-xs text-gray-400">Active</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-red-400">{stats.blocked}</div>
            <div className="text-xs text-gray-400">Blocked</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-orange-400">{stats.highPriority}</div>
            <div className="text-xs text-gray-400">High Pri</div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0 flex-1 flex flex-col">
        {/* Filters */}
        {config.showFilters !== false && (
          <div className="px-4 pb-3 space-y-2">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search requirements..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 h-8"
              />
            </div>

            {/* Filter Selects */}
            <div className="flex gap-2">
              <Select value={typeFilter} onValueChange={(value: FilterType) => setTypeFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="functional">Functional</SelectItem>
                  <SelectItem value="non-functional">Non-Functional</SelectItem>
                  <SelectItem value="business">Business</SelectItem>
                  <SelectItem value="technical">Technical</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={(value: StatusFilter) => setStatusFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="blocked">Blocked</SelectItem>
                </SelectContent>
              </Select>

              <Select value={priorityFilter} onValueChange={(value: PriorityFilter) => setPriorityFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Requirements List */}
        <div 
          className="requirements-list flex-1 overflow-y-auto px-4"
          style={{ maxHeight: config.maxHeight || '400px' }}
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-400">Loading requirements...</p>
            </div>
          ) : filteredRequirements.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <CheckSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No requirements found</p>
              <p className="text-sm mt-1">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredRequirements.map(requirement => (
                <RequirementItem
                  key={requirement.id}
                  requirement={requirement}
                  onStatusChange={handleStatusChange}
                  onSelect={() => setSelectedRequirement(requirement)}
                  allowEditing={config.allowEditing !== false}
                />
              ))}
            </div>
          )}
        </div>

        {/* Requirement Details Modal */}
        {selectedRequirement && (
          <RequirementDetailsDialog
            requirement={selectedRequirement}
            open={!!selectedRequirement}
            onOpenChange={(open) => !open && setSelectedRequirement(null)}
            onUpdate={(updated) => {
              setRequirements(prev => prev.map(req => 
                req.id === updated.id ? updated : req
              ));
              setSelectedRequirement(updated);
            }}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Requirement Item Component
const RequirementItem: React.FC<{
  requirement: Requirement;
  onStatusChange: (id: string, status: Requirement['status']) => void;
  onSelect: () => void;
  allowEditing: boolean;
}> = ({ requirement, onStatusChange, onSelect, allowEditing }) => {
  const getPriorityColor = (priority: Requirement['priority']) => {
    switch (priority) {
      case 'critical': return 'border-l-red-500';
      case 'high': return 'border-l-orange-500';
      case 'medium': return 'border-l-yellow-500';
      case 'low': return 'border-l-green-500';
      default: return 'border-l-gray-500';
    }
  };

  const getStatusIcon = (status: Requirement['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in-progress': return <Clock className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'blocked': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'approved': return <CheckSquare className="h-4 w-4 text-green-500" />;
      default: return <CheckSquare className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTypeIcon = (type: Requirement['type']) => {
    switch (type) {
      case 'functional': return <Zap className="h-3 w-3 text-blue-500" />;
      case 'non-functional': return <Shield className="h-3 w-3 text-purple-500" />;
      case 'business': return <Target className="h-3 w-3 text-green-500" />;
      case 'technical': return <CheckSquare className="h-3 w-3 text-orange-500" />;
      default: return <CheckSquare className="h-3 w-3 text-gray-500" />;
    }
  };

  return (
    <div 
      className={`requirement-item p-3 bg-gray-800 rounded-lg border-l-4 ${getPriorityColor(requirement.priority)} cursor-pointer hover:bg-gray-700 transition-colors`}
      onClick={onSelect}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          {getStatusIcon(requirement.status)}
          <span className="font-medium text-white text-sm">{requirement.title}</span>
        </div>
        <div className="flex items-center gap-1">
          {getTypeIcon(requirement.type)}
          <Badge variant="outline" className="text-xs border-gray-600">
            {requirement.priority}
          </Badge>
        </div>
      </div>

      <p className="text-xs text-gray-400 mb-2 line-clamp-2">
        {requirement.description}
      </p>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-xs text-gray-500">
          <span className="capitalize">{requirement.type}</span>
          {requirement.assignee && (
            <>
              <span>•</span>
              <div className="flex items-center gap-1">
                <User className="h-3 w-3" />
                <span>{requirement.assignee}</span>
              </div>
            </>
          )}
          {requirement.dueDate && (
            <>
              <span>•</span>
              <span>{new Date(requirement.dueDate).toLocaleDateString()}</span>
            </>
          )}
        </div>

        {allowEditing && (
          <Select 
            value={requirement.status} 
            onValueChange={(value) => onStatusChange(requirement.id, value as Requirement['status'])}
          >
            <SelectTrigger className="w-24 h-6 text-xs bg-gray-700 border-gray-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="blocked">Blocked</SelectItem>
            </SelectContent>
          </Select>
        )}
      </div>

      {requirement.tags.length > 0 && (
        <div className="flex items-center gap-1 mt-2">
          {requirement.tags.slice(0, 3).map(tag => (
            <Badge key={tag} variant="outline" className="text-xs border-gray-600 text-gray-400">
              {tag}
            </Badge>
          ))}
          {requirement.tags.length > 3 && (
            <span className="text-xs text-gray-500">+{requirement.tags.length - 3}</span>
          )}
        </div>
      )}
    </div>
  );
};

// Add Requirement Dialog
const AddRequirementDialog: React.FC<{
  onAdd: (req: Omit<Requirement, 'id' | 'created_at' | 'updated_at'>) => void;
}> = ({ onAdd }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'functional' as Requirement['type'],
    category: '',
    priority: 'medium' as Requirement['priority'],
    status: 'draft' as Requirement['status'],
    assignee: '',
    dueDate: '',
    acceptance_criteria: [''],
    dependencies: [],
    tags: [],
    estimated_effort: 0
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim()) return;

    onAdd({
      ...formData,
      acceptance_criteria: formData.acceptance_criteria.filter(c => c.trim()),
      tags: formData.tags.filter(t => t.trim())
    });

    // Reset form
    setFormData({
      title: '',
      description: '',
      type: 'functional',
      category: '',
      priority: 'medium',
      status: 'draft',
      assignee: '',
      dueDate: '',
      acceptance_criteria: [''],
      dependencies: [],
      tags: [],
      estimated_effort: 0
    });
  };

  return (
    <DialogContent className="bg-gray-900 border-gray-700 max-w-2xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Add New Requirement</DialogTitle>
        <DialogDescription>
          Create a new requirement for your project
        </DialogDescription>
      </DialogHeader>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2">
            <Input
              placeholder="Requirement title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="bg-gray-800 border-gray-600"
              required
            />
          </div>

          <Select 
            value={formData.type} 
            onValueChange={(value: Requirement['type']) => setFormData(prev => ({ ...prev, type: value }))}
          >
            <SelectTrigger className="bg-gray-800 border-gray-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="functional">Functional</SelectItem>
              <SelectItem value="non-functional">Non-Functional</SelectItem>
              <SelectItem value="business">Business</SelectItem>
              <SelectItem value="technical">Technical</SelectItem>
            </SelectContent>
          </Select>

          <Select 
            value={formData.priority} 
            onValueChange={(value: Requirement['priority']) => setFormData(prev => ({ ...prev, priority: value }))}
          >
            <SelectTrigger className="bg-gray-800 border-gray-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>

          <div className="col-span-2">
            <Textarea
              placeholder="Detailed description of the requirement"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="bg-gray-800 border-gray-600"
              rows={3}
            />
          </div>

          <Input
            placeholder="Category (e.g., Authentication, UI, API)"
            value={formData.category}
            onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
            className="bg-gray-800 border-gray-600"
          />

          <Input
            placeholder="Assignee"
            value={formData.assignee}
            onChange={(e) => setFormData(prev => ({ ...prev, assignee: e.target.value }))}
            className="bg-gray-800 border-gray-600"
          />
        </div>

        <div className="flex gap-2 pt-4">
          <Button type="submit" className="flex-1">
            Add Requirement
          </Button>
        </div>
      </form>
    </DialogContent>
  );
};

// Requirement Details Dialog
const RequirementDetailsDialog: React.FC<{
  requirement: Requirement;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (req: Requirement) => void;
}> = ({ requirement, open, onOpenChange, onUpdate }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {requirement.type === 'functional' && <Zap className="h-5 w-5 text-blue-500" />}
            {requirement.type === 'non-functional' && <Shield className="h-5 w-5 text-purple-500" />}
            {requirement.type === 'business' && <Target className="h-5 w-5 text-green-500" />}
            {requirement.type === 'technical' && <CheckSquare className="h-5 w-5 text-orange-500" />}
            {requirement.title}
          </DialogTitle>
          <DialogDescription>
            {requirement.category} • Created {new Date(requirement.created_at).toLocaleDateString()}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-2">Description</h4>
            <p className="text-sm text-gray-300 bg-gray-800 rounded p-3">
              {requirement.description}
            </p>
          </div>

          {requirement.acceptance_criteria.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Acceptance Criteria</h4>
              <ul className="space-y-1">
                {requirement.acceptance_criteria.map((criteria, index) => (
                  <li key={index} className="text-sm text-gray-300 flex items-start gap-2">
                    <CheckSquare className="h-4 w-4 mt-0.5 text-green-500" />
                    {criteria}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Status & Priority</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{requirement.status}</Badge>
                  <Badge variant="outline">{requirement.priority}</Badge>
                </div>
              </div>
            </div>
            
            {requirement.assignee && (
              <div>
                <h4 className="text-sm font-medium mb-2">Assignee</h4>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span className="text-sm">{requirement.assignee}</span>
                </div>
              </div>
            )}
          </div>

          {requirement.tags.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Tags</h4>
              <div className="flex flex-wrap gap-1">
                {requirement.tags.map(tag => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper Functions
const generateMockRequirements = (context: ContextSnapshot): Requirement[] => {
  const mockReqs: Requirement[] = [
    {
      id: 'req-1',
      title: 'User Authentication System',
      description: 'Implement secure user authentication with JWT tokens, password hashing, and session management.',
      type: 'functional',
      category: 'Authentication',
      priority: 'high',
      status: 'in-progress',
      assignee: 'John Doe',
      dueDate: '2024-02-15',
      acceptance_criteria: [
        'Users can register with email and password',
        'Users can login and receive JWT token',
        'Passwords are hashed using bcrypt',
        'Sessions expire after 24 hours'
      ],
      dependencies: [],
      tags: ['security', 'backend', 'api'],
      created_at: Date.now() - 86400000,
      updated_at: Date.now() - 3600000,
      estimated_effort: 16
    },
    {
      id: 'req-2',
      title: 'Response Time < 200ms',
      description: 'All API endpoints must respond within 200ms under normal load conditions.',
      type: 'non-functional',
      category: 'Performance',
      priority: 'medium',
      status: 'approved',
      acceptance_criteria: [
        'All GET endpoints respond in < 200ms',
        'POST/PUT endpoints respond in < 500ms',
        'Performance tests pass with 95th percentile compliance'
      ],
      dependencies: ['req-1'],
      tags: ['performance', 'api', 'testing'],
      created_at: Date.now() - 172800000,
      updated_at: Date.now() - 7200000,
      estimated_effort: 8
    },
    {
      id: 'req-3',
      title: 'Dark Mode Support',
      description: 'Implement dark mode theme throughout the application with user preference storage.',
      type: 'functional',
      category: 'UI/UX',
      priority: 'low',
      status: 'draft',
      acceptance_criteria: [
        'Toggle between light and dark themes',
        'User preference is saved locally',
        'All components support both themes',
        'Smooth theme transitions'
      ],
      dependencies: [],
      tags: ['frontend', 'ui', 'theme'],
      created_at: Date.now() - *********,
      updated_at: Date.now() - 14400000,
      estimated_effort: 12
    }
  ];

  // Add context-based requirements
  if (context.fileContext.hasErrors) {
    mockReqs.push({
      id: 'req-error',
      title: 'Fix Current File Errors',
      description: 'Resolve all errors detected in the current file to ensure code quality.',
      type: 'technical',
      category: 'Bug Fix',
      priority: 'high',
      status: 'draft',
      acceptance_criteria: ['All syntax errors resolved', 'Code passes linting', 'Tests are passing'],
      dependencies: [],
      tags: ['bugfix', 'quality'],
      created_at: Date.now(),
      updated_at: Date.now()
    });
  }

  return mockReqs;
};

export default RequirementsWidget;
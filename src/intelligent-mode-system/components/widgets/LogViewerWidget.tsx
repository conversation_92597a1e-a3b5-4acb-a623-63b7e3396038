import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../../components/ui/dialog';
import { 
  FileText, 
  Search, 
  Download, 
  RefreshCw, 
  Filter,
  Clock,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Zap,
  Settings,
  Play,
  Pause,
  Trash2,
  Eye,
  EyeOff
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

interface LogEntry {
  id: string;
  timestamp: number;
  level: 'error' | 'warn' | 'info' | 'debug' | 'trace';
  message: string;
  source: string;
  category: string;
  metadata?: Record<string, any>;
  stackTrace?: string[];
  correlationId?: string;
  userId?: string;
  sessionId?: string;
}

interface LogViewerWidgetProps {
  context: ContextSnapshot;
  config?: {
    autoRefresh?: boolean;
    refreshInterval?: number;
    maxEntries?: number;
    showFilters?: boolean;
    defaultLevel?: LogEntry['level'];
    maxHeight?: string;
  };
}

type LevelFilter = 'all' | 'error' | 'warn' | 'info' | 'debug' | 'trace';
type SourceFilter = 'all' | string;

export const LogViewerWidget: React.FC<LogViewerWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [levelFilter, setLevelFilter] = useState<LevelFilter>(config.defaultLevel || 'all');
  const [sourceFilter, setSourceFilter] = useState<SourceFilter>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isPaused, setIsPaused] = useState(false);
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null);
  const [autoScroll, setAutoScroll] = useState(true);
  const [showTimestamps, setShowTimestamps] = useState(true);
  const [showSources, setShowSources] = useState(true);
  
  const logContainerRef = useRef<HTMLDivElement>(null);
  const shouldScrollRef = useRef(true);

  // Load logs on component mount
  useEffect(() => {
    loadLogs();
  }, [context]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!config.autoRefresh || isPaused) return;

    const interval = setInterval(() => {
      loadLogs(false); // Don't show loading state for auto-refresh
    }, config.refreshInterval || 5000);

    return () => clearInterval(interval);
  }, [config.autoRefresh, config.refreshInterval, isPaused]);

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (autoScroll && shouldScrollRef.current && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [filteredLogs, autoScroll]);

  const loadLogs = async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    
    // Simulate API call - would be replaced with actual log fetching
    const newLogs = generateMockLogs(context, config.maxEntries || 100);
    
    setTimeout(() => {
      if (!isPaused) {
        setLogs(newLogs);
      }
      if (showLoading) setIsLoading(false);
    }, showLoading ? 500 : 0);
  };

  // Filter logs based on search and filters
  useEffect(() => {
    let filtered = logs;

    // Level filter
    if (levelFilter !== 'all') {
      filtered = filtered.filter(log => log.level === levelFilter);
    }

    // Source filter
    if (sourceFilter !== 'all') {
      filtered = filtered.filter(log => log.source === sourceFilter);
    }

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(log => 
        log.message.toLowerCase().includes(query) ||
        log.source.toLowerCase().includes(query) ||
        log.category.toLowerCase().includes(query) ||
        (log.correlationId && log.correlationId.toLowerCase().includes(query))
      );
    }

    setFilteredLogs(filtered);
  }, [logs, levelFilter, sourceFilter, searchQuery]);

  // Available sources for filter
  const availableSources = useMemo(() => {
    const sources = Array.from(new Set(logs.map(log => log.source)));
    return sources.sort();
  }, [logs]);

  // Statistics
  const stats = useMemo(() => {
    const total = logs.length;
    const errors = logs.filter(l => l.level === 'error').length;
    const warnings = logs.filter(l => l.level === 'warn').length;
    const recent = logs.filter(l => Date.now() - l.timestamp < 300000).length; // Last 5 minutes

    return { total, errors, warnings, recent };
  }, [logs]);

  const handleRefresh = () => {
    loadLogs();
  };

  const handleClear = () => {
    setLogs([]);
    setFilteredLogs([]);
  };

  const handlePauseToggle = () => {
    setIsPaused(!isPaused);
  };

  const handleExport = () => {
    const logData = filteredLogs.map(log => ({
      timestamp: new Date(log.timestamp).toISOString(),
      level: log.level,
      source: log.source,
      message: log.message,
      category: log.category,
      metadata: log.metadata
    }));

    const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `logs-${Date.now()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleScroll = () => {
    if (logContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = logContainerRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      shouldScrollRef.current = isAtBottom;
      setAutoScroll(isAtBottom);
    }
  };

  const getLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return 'text-red-500 bg-red-500/10 border-red-500/30';
      case 'warn': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30';
      case 'info': return 'text-blue-500 bg-blue-500/10 border-blue-500/30';
      case 'debug': return 'text-green-500 bg-green-500/10 border-green-500/30';
      case 'trace': return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getLevelIcon = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return <XCircle className="h-4 w-4" />;
      case 'warn': return <AlertTriangle className="h-4 w-4" />;
      case 'info': return <Info className="h-4 w-4" />;
      case 'debug': return <CheckCircle className="h-4 w-4" />;
      case 'trace': return <Zap className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  return (
    <Card className="log-viewer-widget h-full bg-gray-900 border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Log Viewer
            <Badge variant="secondary" className="ml-2">
              {filteredLogs.length}/{logs.length}
            </Badge>
            {isPaused && (
              <Badge variant="outline" className="text-yellow-500 border-yellow-500/30">
                Paused
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handlePauseToggle}
              className={`h-8 w-8 p-0 ${isPaused ? 'text-green-500' : 'text-yellow-500'}`}
            >
              {isPaused ? <Play className="h-3 w-3" /> : <Pause className="h-3 w-3" />}
            </Button>
            
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleExport}
              disabled={filteredLogs.length === 0}
              className="h-8 w-8 p-0"
            >
              <Download className="h-3 w-3" />
            </Button>
            
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleClear}
              disabled={logs.length === 0}
              className="h-8 w-8 p-0"
            >
              <Trash2 className="h-3 w-3" />
            </Button>

            <Dialog>
              <DialogTrigger asChild>
                <Button size="sm" variant="outline" className="h-8 w-8 p-0">
                  <Settings className="h-3 w-3" />
                </Button>
              </DialogTrigger>
              <LogViewerSettings
                showTimestamps={showTimestamps}
                showSources={showSources}
                autoScroll={autoScroll}
                onShowTimestampsChange={setShowTimestamps}
                onShowSourcesChange={setShowSources}
                onAutoScrollChange={setAutoScroll}
              />
            </Dialog>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-4 gap-2 mt-3">
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-blue-400">{stats.total}</div>
            <div className="text-xs text-gray-400">Total</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-red-400">{stats.errors}</div>
            <div className="text-xs text-gray-400">Errors</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-yellow-400">{stats.warnings}</div>
            <div className="text-xs text-gray-400">Warnings</div>
          </div>
          <div className="bg-gray-800 rounded p-2 text-center">
            <div className="text-lg font-semibold text-green-400">{stats.recent}</div>
            <div className="text-xs text-gray-400">Recent</div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0 flex-1 flex flex-col">
        {/* Filters */}
        {config.showFilters !== false && (
          <div className="px-4 pb-3 space-y-2">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search logs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-800 border-gray-600 h-8"
              />
            </div>

            {/* Filter Selects */}
            <div className="flex gap-2">
              <Select value={levelFilter} onValueChange={(value: LevelFilter) => setLevelFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Level" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                  <SelectItem value="warn">Warning</SelectItem>
                  <SelectItem value="info">Info</SelectItem>
                  <SelectItem value="debug">Debug</SelectItem>
                  <SelectItem value="trace">Trace</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sourceFilter} onValueChange={(value: SourceFilter) => setSourceFilter(value)}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 h-8">
                  <SelectValue placeholder="Source" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all">All Sources</SelectItem>
                  {availableSources.map(source => (
                    <SelectItem key={source} value={source}>{source}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Log List */}
        <div 
          ref={logContainerRef}
          className="log-list flex-1 overflow-y-auto px-4 font-mono text-sm"
          style={{ maxHeight: config.maxHeight || '400px' }}
          onScroll={handleScroll}
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-400">Loading logs...</p>
            </div>
          ) : filteredLogs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No logs found</p>
              <p className="text-sm mt-1">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="space-y-1">
              {filteredLogs.map(log => (
                <LogEntry
                  key={log.id}
                  log={log}
                  onSelect={() => setSelectedLog(log)}
                  showTimestamp={showTimestamps}
                  showSource={showSources}
                />
              ))}
            </div>
          )}
        </div>

        {/* Auto-scroll indicator */}
        {!autoScroll && (
          <div className="px-4 py-2 bg-yellow-900/20 border-t border-yellow-500/30">
            <div className="flex items-center justify-between text-sm">
              <span className="text-yellow-400 flex items-center gap-2">
                <EyeOff className="h-4 w-4" />
                Auto-scroll disabled
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setAutoScroll(true);
                  if (logContainerRef.current) {
                    logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
                  }
                }}
                className="h-6 text-xs"
              >
                <Eye className="h-3 w-3 mr-1" />
                Resume
              </Button>
            </div>
          </div>
        )}

        {/* Log Details Modal */}
        {selectedLog && (
          <LogDetailsDialog
            log={selectedLog}
            open={!!selectedLog}
            onOpenChange={(open) => !open && setSelectedLog(null)}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Log Entry Component
const LogEntry: React.FC<{
  log: LogEntry;
  onSelect: () => void;
  showTimestamp: boolean;
  showSource: boolean;
}> = ({ log, onSelect, showTimestamp, showSource }) => {
  const getLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return 'text-red-400';
      case 'warn': return 'text-yellow-400';
      case 'info': return 'text-blue-400';
      case 'debug': return 'text-green-400';
      case 'trace': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getLevelIcon = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return <XCircle className="h-3 w-3" />;
      case 'warn': return <AlertTriangle className="h-3 w-3" />;
      case 'info': return <Info className="h-3 w-3" />;
      case 'debug': return <CheckCircle className="h-3 w-3" />;
      case 'trace': return <Zap className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  return (
    <div 
      className="log-entry p-2 hover:bg-gray-800 rounded cursor-pointer transition-colors"
      onClick={onSelect}
    >
      <div className="flex items-start gap-2 text-xs">
        {/* Timestamp */}
        {showTimestamp && (
          <span className="text-gray-500 flex-shrink-0 w-20">
            {new Date(log.timestamp).toLocaleTimeString()}
          </span>
        )}
        
        {/* Level */}
        <div className={`flex items-center gap-1 flex-shrink-0 ${getLevelColor(log.level)}`}>
          {getLevelIcon(log.level)}
          <span className="w-12 uppercase font-medium">{log.level}</span>
        </div>
        
        {/* Source */}
        {showSource && (
          <span className="text-gray-400 flex-shrink-0 w-24 truncate">
            {log.source}
          </span>
        )}
        
        {/* Message */}
        <span className="text-gray-300 flex-1 leading-relaxed">
          {log.message}
        </span>
        
        {/* Indicators */}
        <div className="flex items-center gap-1 flex-shrink-0">
          {log.stackTrace && (
            <Badge variant="outline" className="text-xs h-4 px-1 border-red-500/30 text-red-400">
              Stack
            </Badge>
          )}
          {log.correlationId && (
            <Badge variant="outline" className="text-xs h-4 px-1 border-blue-500/30 text-blue-400">
              Corr
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
};

// Log Viewer Settings Dialog
const LogViewerSettings: React.FC<{
  showTimestamps: boolean;
  showSources: boolean;
  autoScroll: boolean;
  onShowTimestampsChange: (show: boolean) => void;
  onShowSourcesChange: (show: boolean) => void;
  onAutoScrollChange: (auto: boolean) => void;
}> = ({
  showTimestamps,
  showSources,
  autoScroll,
  onShowTimestampsChange,
  onShowSourcesChange,
  onAutoScrollChange
}) => {
  return (
    <DialogContent className="bg-gray-900 border-gray-700 max-w-md">
      <DialogHeader>
        <DialogTitle>Log Viewer Settings</DialogTitle>
        <DialogDescription>
          Customize the log viewer display options
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">Show Timestamps</label>
          <Button
            size="sm"
            variant={showTimestamps ? "default" : "outline"}
            onClick={() => onShowTimestampsChange(!showTimestamps)}
          >
            {showTimestamps ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
          </Button>
        </div>

        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">Show Sources</label>
          <Button
            size="sm"
            variant={showSources ? "default" : "outline"}
            onClick={() => onShowSourcesChange(!showSources)}
          >
            {showSources ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
          </Button>
        </div>

        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">Auto Scroll</label>
          <Button
            size="sm"
            variant={autoScroll ? "default" : "outline"}
            onClick={() => onAutoScrollChange(!autoScroll)}
          >
            {autoScroll ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
          </Button>
        </div>
      </div>
    </DialogContent>
  );
};

// Log Details Dialog
const LogDetailsDialog: React.FC<{
  log: LogEntry;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}> = ({ log, open, onOpenChange }) => {
  const getLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return 'text-red-400';
      case 'warn': return 'text-yellow-400';
      case 'info': return 'text-blue-400';
      case 'debug': return 'text-green-400';
      case 'trace': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Log Entry Details
          </DialogTitle>
          <DialogDescription>
            {new Date(log.timestamp).toLocaleString()} • {log.source}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Basic Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Level & Source</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={getLevelColor(log.level)}>
                    {log.level.toUpperCase()}
                  </Badge>
                  <span className="text-sm">{log.source}</span>
                </div>
                <div className="text-sm text-gray-400">
                  Category: {log.category}
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-2">Identifiers</h4>
              <div className="space-y-1 text-sm">
                {log.correlationId && (
                  <div>Correlation ID: <span className="font-mono text-blue-400">{log.correlationId}</span></div>
                )}
                {log.sessionId && (
                  <div>Session ID: <span className="font-mono text-green-400">{log.sessionId}</span></div>
                )}
                {log.userId && (
                  <div>User ID: <span className="font-mono text-yellow-400">{log.userId}</span></div>
                )}
              </div>
            </div>
          </div>

          {/* Message */}
          <div>
            <h4 className="text-sm font-medium mb-2">Message</h4>
            <pre className="text-sm text-gray-300 bg-gray-800 rounded p-3 whitespace-pre-wrap font-mono">
              {log.message}
            </pre>
          </div>

          {/* Stack Trace */}
          {log.stackTrace && log.stackTrace.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Stack Trace</h4>
              <pre className="text-sm text-gray-300 bg-gray-800 rounded p-3 overflow-auto max-h-40 font-mono">
                {log.stackTrace.join('\n')}
              </pre>
            </div>
          )}

          {/* Metadata */}
          {log.metadata && Object.keys(log.metadata).length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Metadata</h4>
              <pre className="text-sm text-gray-300 bg-gray-800 rounded p-3 overflow-auto max-h-40 font-mono">
                {JSON.stringify(log.metadata, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper Functions
const generateMockLogs = (context: ContextSnapshot, maxEntries: number): LogEntry[] => {
  const mockLogs: LogEntry[] = [];
  const now = Date.now();
  
  // Generate various log entries
  const sources = ['frontend', 'backend', 'database', 'auth-service', 'api-gateway'];
  const categories = ['application', 'security', 'performance', 'database', 'network'];
  const levels: LogEntry['level'][] = ['error', 'warn', 'info', 'debug', 'trace'];
  
  for (let i = 0; i < maxEntries; i++) {
    const timestamp = now - (Math.random() * 3600000); // Random time in last hour
    const level = levels[Math.floor(Math.random() * levels.length)];
    const source = sources[Math.floor(Math.random() * sources.length)];
    const category = categories[Math.floor(Math.random() * categories.length)];
    
    let message = '';
    let stackTrace: string[] | undefined;
    let metadata: Record<string, any> | undefined;

    switch (level) {
      case 'error':
        message = `Failed to process request: ${Math.random() > 0.5 ? 'Database connection timeout' : 'Validation error in user input'}`;
        stackTrace = [
          'Error: Database connection timeout',
          '  at Database.connect (/app/db.js:45:23)',
          '  at UserService.getUser (/app/services/user.js:12:8)',
          '  at /app/controllers/user.js:28:15'
        ];
        metadata = { requestId: `req-${Math.random().toString(36).substr(2, 9)}`, errorCode: 'DB_TIMEOUT' };
        break;
      case 'warn':
        message = `High memory usage detected: ${Math.floor(Math.random() * 40 + 60)}% of allocated heap`;
        metadata = { memoryUsage: Math.floor(Math.random() * 1000 + 500), threshold: 80 };
        break;
      case 'info':
        message = `User ${Math.random().toString(36).substr(2, 6)} successfully authenticated`;
        metadata = { userId: `user-${Math.random().toString(36).substr(2, 9)}`, loginMethod: 'oauth' };
        break;
      case 'debug':
        message = `Cache hit for key: ${Math.random().toString(36).substr(2, 12)}`;
        metadata = { cacheKey: `cache-${Math.random().toString(36).substr(2, 12)}`, ttl: 3600 };
        break;
      case 'trace':
        message = `Function execution completed in ${Math.floor(Math.random() * 100 + 10)}ms`;
        metadata = { executionTime: Math.floor(Math.random() * 100 + 10), function: 'processRequest' };
        break;
    }

    mockLogs.push({
      id: `log-${i}`,
      timestamp,
      level,
      message,
      source,
      category,
      metadata,
      stackTrace,
      correlationId: Math.random() > 0.7 ? `corr-${Math.random().toString(36).substr(2, 9)}` : undefined,
      sessionId: Math.random() > 0.8 ? `sess-${Math.random().toString(36).substr(2, 9)}` : undefined,
      userId: Math.random() > 0.6 ? `user-${Math.random().toString(36).substr(2, 9)}` : undefined
    });
  }

  // Add context-based logs
  if (context.fileContext.hasErrors) {
    mockLogs.unshift({
      id: 'log-context-error',
      timestamp: now,
      level: 'error',
      message: 'Syntax error detected in current file - compilation failed',
      source: 'compiler',
      category: 'application',
      metadata: { 
        file: context.fileContext.path, 
        errors: context.fileContext.errors?.length || 1
      }
    });
  }

  return mockLogs.sort((a, b) => b.timestamp - a.timestamp);
};

export default LogViewerWidget;
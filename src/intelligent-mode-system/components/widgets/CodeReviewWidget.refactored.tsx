import React, { useState, useEffect, useMemo } from 'react';
import { ModeWidgetCompound as Widget } from '../compounds/ModeWidget';
import { ModePanelCompound as Panel } from '../compounds/ModePanel';
import { ModeFormCompound as Form } from '../compounds/ModeForm';
import { Flex } from '../primitives/Flex';
import { Box } from '../primitives/Box';
import { Text } from '../primitives/Text';
import { Stack } from '../primitives/Stack';
import { Card } from '../primitives/Card';
import { Button } from '../primitives/Button';
import { Input } from '../primitives/Input';
import { Badge } from '../../../components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog';
import { 
  MessageSquare, 
  Search, 
  Clock, 
  AlertCircle,
  FileText,
  Plus,
  ThumbsUp,
  RefreshCw,
  User,
  Reply,
  Edit,
  ExternalLink,
  MapPin
} from 'lucide-react';
import { ContextSnapshot } from '../../types';

// Types remain the same
interface CodeComment {
  id: string;
  author: {
    id: string;
    name: string;
    avatar: string;
    role: 'reviewer' | 'author' | 'maintainer';
  };
  content: string;
  filePath: string;
  lineNumber: number;
  lineRange?: {
    start: number;
    end: number;
  };
  type: 'comment' | 'suggestion' | 'issue' | 'question' | 'praise';
  severity: 'info' | 'minor' | 'major' | 'critical';
  status: 'open' | 'resolved' | 'acknowledged' | 'wont_fix';
  createdAt: number;
  updatedAt: number;
  resolvedAt?: number;
  resolvedBy?: string;
  replies: CodeComment[];
  reactions: {
    type: 'like' | 'dislike' | 'confused' | 'heart' | 'rocket';
    users: string[];
  }[];
  suggestedChange?: {
    original: string;
    suggested: string;
    applied: boolean;
  };
  relatedToCommit?: string;
  tags: string[];
  isBot: boolean;
  threadId: string;
}

interface ReviewSession {
  id: string;
  reviewerName: string;
  startedAt: number;
  completedAt?: number;
  status: 'in_progress' | 'completed' | 'abandoned';
  commentsCount: number;
  issuesFound: number;
  overallRating?: 'approve' | 'request_changes' | 'comment';
  summary?: string;
}

interface CodeReviewWidgetProps {
  context: ContextSnapshot;
  config?: {
    maxComments?: number;
    showFilters?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number;
    defaultView?: 'list' | 'threaded' | 'files';
    maxHeight?: string;
    showResolvedComments?: boolean;
  };
}

type CommentTypeFilter = 'all' | 'comment' | 'suggestion' | 'issue' | 'question' | 'praise';
type StatusFilter = 'all' | 'open' | 'resolved' | 'acknowledged' | 'wont_fix';
type SeverityFilter = 'all' | 'info' | 'minor' | 'major' | 'critical';
type ViewMode = 'list' | 'threaded' | 'files';

/**
 * CodeReviewWidget - Refactored using composition patterns
 * 
 * Demonstrates the use of primitive and compound components
 * for building complex code review interfaces with consistent theming.
 */
export const CodeReviewWidget: React.FC<CodeReviewWidgetProps> = ({ 
  context, 
  config = {} 
}) => {
  const [comments, setComments] = useState<CodeComment[]>([]);
  const [reviewSessions, setReviewSessions] = useState<ReviewSession[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<CommentTypeFilter>('all');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>(config.showResolvedComments ? 'all' : 'open');
  const [severityFilter, setSeverityFilter] = useState<SeverityFilter>('all');
  const [viewMode, setViewMode] = useState<ViewMode>(config.defaultView || 'list');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedComment, setSelectedComment] = useState<CodeComment | null>(null);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');

  // Load comments on component mount
  useEffect(() => {
    loadComments();
    loadReviewSessions();
  }, [context]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!config.autoRefresh) return;

    const interval = setInterval(() => {
      loadComments(false);
      loadReviewSessions();
    }, config.refreshInterval || 15000);

    return () => clearInterval(interval);
  }, [config.autoRefresh, config.refreshInterval]);

  const loadComments = async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    
    const mockComments = generateMockComments(context, config.maxComments || 25);
    
    setTimeout(() => {
      setComments(mockComments);
      if (showLoading) setIsLoading(false);
    }, showLoading ? 600 : 0);
  };

  const loadReviewSessions = async () => {
    const mockSessions = generateMockReviewSessions();
    setReviewSessions(mockSessions);
  };

  // Filter comments
  const filteredComments = useMemo(() => {
    return comments.filter(comment => {
      const matchesSearch = !searchQuery || 
        comment.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        comment.author.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        comment.filePath.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesType = typeFilter === 'all' || comment.type === typeFilter;
      const matchesStatus = statusFilter === 'all' || comment.status === statusFilter;
      const matchesSeverity = severityFilter === 'all' || comment.severity === severityFilter;

      return matchesSearch && matchesType && matchesStatus && matchesSeverity;
    }).sort((a, b) => {
      const severityOrder = { critical: 4, major: 3, minor: 2, info: 1 };
      const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
      if (severityDiff !== 0) return severityDiff;
      return b.createdAt - a.createdAt;
    });
  }, [comments, searchQuery, typeFilter, statusFilter, severityFilter]);

  // Group comments by file for files view
  const commentsByFile = useMemo(() => {
    const grouped = new Map<string, CodeComment[]>();
    filteredComments.forEach(comment => {
      if (!grouped.has(comment.filePath)) {
        grouped.set(comment.filePath, []);
      }
      grouped.get(comment.filePath)!.push(comment);
    });
    return grouped;
  }, [filteredComments]);

  // Statistics
  const stats = useMemo(() => {
    const total = comments.length;
    const open = comments.filter(c => c.status === 'open').length;
    const issues = comments.filter(c => c.type === 'issue').length;
    const suggestions = comments.filter(c => c.type === 'suggestion').length;
    const critical = comments.filter(c => c.severity === 'critical').length;

    return { total, open, issues, suggestions, critical };
  }, [comments]);

  const handleRefresh = () => {
    loadComments();
    loadReviewSessions();
  };

  const handleStatusChange = (commentId: string, newStatus: CodeComment['status']) => {
    setComments(prev => prev.map(comment => 
      comment.id === commentId 
        ? { 
            ...comment, 
            status: newStatus,
            resolvedAt: newStatus === 'resolved' ? Date.now() : undefined,
            resolvedBy: newStatus === 'resolved' ? 'current-user' : undefined,
            updatedAt: Date.now()
          }
        : comment
    ));
  };

  const handleReaction = (commentId: string, reactionType: CodeComment['reactions'][0]['type']) => {
    setComments(prev => prev.map(comment => {
      if (comment.id !== commentId) return comment;
      
      const existingReaction = comment.reactions.find(r => r.type === reactionType);
      if (existingReaction) {
        const userIndex = existingReaction.users.indexOf('current-user');
        if (userIndex >= 0) {
          existingReaction.users.splice(userIndex, 1);
        } else {
          existingReaction.users.push('current-user');
        }
      } else {
        comment.reactions.push({
          type: reactionType,
          users: ['current-user']
        });
      }
      
      return { ...comment, updatedAt: Date.now() };
    }));
  };

  const handleReply = (commentId: string) => {
    if (!replyContent.trim()) return;

    const newReply: CodeComment = {
      id: `reply-${Date.now()}`,
      author: { id: 'current-user', name: 'Current User', avatar: '', role: 'reviewer' },
      content: replyContent,
      filePath: '',
      lineNumber: 0,
      type: 'comment',
      severity: 'info',
      status: 'open',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      replies: [],
      reactions: [],
      tags: [],
      isBot: false,
      threadId: commentId
    };

    setComments(prev => prev.map(comment => 
      comment.id === commentId 
        ? { ...comment, replies: [...comment.replies, newReply] }
        : comment
    ));

    setReplyContent('');
    setReplyingTo(null);
  };

  // Widget Actions
  const widgetActions = (
    <Widget.Actions>
      <Button 
        size="sm" 
        variant="ghost" 
        onClick={handleRefresh}
        disabled={isLoading}
        leftIcon={<RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />}
      />
      
      <Button 
        size="sm" 
        variant="primary"
        leftIcon={<Plus className="h-3 w-3" />}
      >
        Add Comment
      </Button>
    </Widget.Actions>
  );

  return (
    <Widget
      size="lg"
      loading={isLoading}
      empty={!isLoading && comments.length === 0}
      title="Code Reviews"
      icon={<MessageSquare className="h-5 w-5" />}
      actions={widgetActions}
    >
      <Stack spacing="md">
        {/* Statistics */}
        <Flex gap="sm" wrap="wrap">
          <StatCard label="Total" value={stats.total} color="primary" />
          <StatCard label="Open" value={stats.open} color="warning" />
          <StatCard label="Issues" value={stats.issues} color="error" />
          <StatCard label="Suggestions" value={stats.suggestions} color="info" />
          <StatCard label="Critical" value={stats.critical} color="error" />
        </Flex>

        {/* Active Review Sessions */}
        {reviewSessions.filter(s => s.status === 'in_progress').length > 0 && (
          <Stack spacing="sm">
            <Text size="sm" weight="medium" color="secondary">Active Reviews</Text>
            <Flex gap="sm" wrap="wrap">
              {reviewSessions.filter(s => s.status === 'in_progress').map(session => (
                <Badge key={session.id} variant="outline" className="text-xs">
                  <User className="h-3 w-3 mr-1" />
                  {session.reviewerName}
                </Badge>
              ))}
            </Flex>
          </Stack>
        )}

        {/* Filters */}
        {config.showFilters !== false && (
          <Stack spacing="sm">
            <Input
              size="sm"
              placeholder="Search comments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
            />

            <Flex gap="sm">
              <FilterSelect
                value={typeFilter}
                onChange={setTypeFilter}
                options={[
                  { value: 'all', label: 'All Types' },
                  { value: 'comment', label: 'Comments' },
                  { value: 'suggestion', label: 'Suggestions' },
                  { value: 'issue', label: 'Issues' },
                  { value: 'question', label: 'Questions' },
                  { value: 'praise', label: 'Praise' }
                ]}
              />

              <FilterSelect
                value={statusFilter}
                onChange={setStatusFilter}
                options={[
                  { value: 'all', label: 'All Status' },
                  { value: 'open', label: 'Open' },
                  { value: 'resolved', label: 'Resolved' },
                  { value: 'acknowledged', label: 'Acknowledged' },
                  { value: 'wont_fix', label: "Won't Fix" }
                ]}
              />

              <FilterSelect
                value={severityFilter}
                onChange={setSeverityFilter}
                options={[
                  { value: 'all', label: 'All Severity' },
                  { value: 'critical', label: 'Critical' },
                  { value: 'major', label: 'Major' },
                  { value: 'minor', label: 'Minor' },
                  { value: 'info', label: 'Info' }
                ]}
              />

              <ViewModeToggle viewMode={viewMode} onChange={setViewMode} />
            </Flex>
          </Stack>
        )}

        {/* Comments List */}
        <Box 
          className="overflow-y-auto"
          style={{ maxHeight: config.maxHeight || '500px' }}
        >
          {filteredComments.length === 0 ? (
            <EmptyState />
          ) : viewMode === 'files' ? (
            <FileGroupedComments
              commentsByFile={commentsByFile}
              onStatusChange={handleStatusChange}
              onReaction={handleReaction}
              onReply={handleReply}
              onSelect={setSelectedComment}
              replyingTo={replyingTo}
              setReplyingTo={setReplyingTo}
              replyContent={replyContent}
              setReplyContent={setReplyContent}
            />
          ) : (
            <Stack spacing="sm">
              {filteredComments.map(comment => (
                <CommentItem
                  key={comment.id}
                  comment={comment}
                  onStatusChange={handleStatusChange}
                  onReaction={handleReaction}
                  onReply={handleReply}
                  onSelect={() => setSelectedComment(comment)}
                  showThreaded={viewMode === 'threaded'}
                  replyingTo={replyingTo}
                  setReplyingTo={setReplyingTo}
                  replyContent={replyContent}
                  setReplyContent={setReplyContent}
                />
              ))}
            </Stack>
          )}
        </Box>
      </Stack>

      {/* Comment Details Modal */}
      {selectedComment && (
        <CommentDetailsDialog
          comment={selectedComment}
          open={!!selectedComment}
          onOpenChange={(open) => !open && setSelectedComment(null)}
          onStatusChange={handleStatusChange}
          onReaction={handleReaction}
        />
      )}
    </Widget>
  );
};

// Sub-components using primitives
const StatCard: React.FC<{
  label: string;
  value: number;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
}> = ({ label, value, color }) => (
  <Box variant="surface" padding="sm" rounded="md" border className="min-w-[80px]">
    <Text size="2xl" weight="bold" color={color} className="text-center">
      {value}
    </Text>
    <Text size="xs" color="muted" className="text-center">
      {label}
    </Text>
  </Box>
);

const FilterSelect: React.FC<{
  value: string;
  onChange: (value: any) => void;
  options: { value: string; label: string }[];
}> = ({ value, onChange, options }) => (
  <Select value={value} onValueChange={onChange}>
    <SelectTrigger className="flex-1 h-8">
      <SelectValue />
    </SelectTrigger>
    <SelectContent>
      {options.map(option => (
        <SelectItem key={option.value} value={option.value}>
          {option.label}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
);

const ViewModeToggle: React.FC<{
  viewMode: ViewMode;
  onChange: (mode: ViewMode) => void;
}> = ({ viewMode, onChange }) => (
  <Flex className="bg-[var(--mode-surface)] rounded-md border border-[var(--mode-border)]">
    <Button
      size="xs"
      variant={viewMode === 'list' ? 'primary' : 'ghost'}
      onClick={() => onChange('list')}
      className="rounded-r-none"
    >
      <MessageSquare className="h-3 w-3" />
    </Button>
    <Button
      size="xs"
      variant={viewMode === 'threaded' ? 'primary' : 'ghost'}
      onClick={() => onChange('threaded')}
      className="rounded-none border-x border-[var(--mode-border)]"
    >
      <Reply className="h-3 w-3" />
    </Button>
    <Button
      size="xs"
      variant={viewMode === 'files' ? 'primary' : 'ghost'}
      onClick={() => onChange('files')}
      className="rounded-l-none"
    >
      <FileText className="h-3 w-3" />
    </Button>
  </Flex>
);

const EmptyState: React.FC = () => (
  <Flex direction="col" align="center" justify="center" className="py-12">
    <MessageSquare className="h-12 w-12 text-[var(--mode-text-muted)] mb-4" />
    <Text size="lg" color="muted">No comments found</Text>
    <Text size="sm" color="muted">Try adjusting your search or filters</Text>
  </Flex>
);

// Comment Item Component using composition
const CommentItem: React.FC<{
  comment: CodeComment;
  onStatusChange: (id: string, status: CodeComment['status']) => void;
  onReaction: (id: string, reaction: CodeComment['reactions'][0]['type']) => void;
  onReply: (id: string) => void;
  onSelect: () => void;
  showThreaded?: boolean;
  replyingTo: string | null;
  setReplyingTo: (id: string | null) => void;
  replyContent: string;
  setReplyContent: (content: string) => void;
}> = ({ 
  comment, 
  onStatusChange, 
  onReaction, 
  onReply, 
  onSelect, 
  showThreaded = false,
  replyingTo,
  setReplyingTo,
  replyContent,
  setReplyContent
}) => {
  const getTypeColor = (type: CodeComment['type']) => {
    switch (type) {
      case 'issue': return 'border-l-[var(--mode-error)]';
      case 'suggestion': return 'border-l-[var(--mode-info)]';
      case 'question': return 'border-l-[var(--mode-warning)]';
      case 'praise': return 'border-l-[var(--mode-success)]';
      default: return 'border-l-[var(--mode-text-muted)]';
    }
  };

  const getTypeIcon = (type: CodeComment['type']) => {
    switch (type) {
      case 'issue': return <AlertCircle className="h-4 w-4" />;
      case 'suggestion': return <Edit className="h-4 w-4" />;
      case 'question': return <MessageSquare className="h-4 w-4" />;
      case 'praise': return <ThumbsUp className="h-4 w-4" />;
      default: return <MessageSquare className="h-4 w-4" />;
    }
  };

  return (
    <Box
      variant="surface"
      padding="md"
      rounded="md"
      className={`border-l-4 ${getTypeColor(comment.type)}`}
    >
      <Stack spacing="sm">
        {/* Header */}
        <Flex justify="between">
          <Flex align="center" gap="sm">
            <Box className={`text-[var(--mode-${comment.type === 'issue' ? 'error' : comment.type === 'suggestion' ? 'info' : comment.type === 'question' ? 'warning' : comment.type === 'praise' ? 'success' : 'text-muted'})]`}>
              {getTypeIcon(comment.type)}
            </Box>
            <Stack spacing="none">
              <Flex align="center" gap="xs">
                <Text size="sm" weight="medium">{comment.author.name}</Text>
                <Badge variant="outline" className="text-xs">
                  {comment.author.role}
                </Badge>
                {comment.isBot && (
                  <Badge variant="outline" className="text-xs">
                    Bot
                  </Badge>
                )}
              </Flex>
              <Flex align="center" gap="xs">
                <Clock className="h-3 w-3 text-[var(--mode-text-muted)]" />
                <Text size="xs" color="muted">
                  {new Date(comment.createdAt).toLocaleDateString()}
                </Text>
                <Text size="xs" color="muted">•</Text>
                <Text size="xs" color={comment.severity === 'critical' ? 'error' : comment.severity === 'major' ? 'warning' : 'muted'}>
                  {comment.severity}
                </Text>
              </Flex>
            </Stack>
          </Flex>

          <Flex align="center" gap="sm">
            <Badge variant="outline" className={`text-xs ${
              comment.status === 'open' ? 'border-[var(--mode-warning)] text-[var(--mode-warning)]' :
              comment.status === 'resolved' ? 'border-[var(--mode-success)] text-[var(--mode-success)]' :
              comment.status === 'acknowledged' ? 'border-[var(--mode-info)] text-[var(--mode-info)]' :
              'border-[var(--mode-error)] text-[var(--mode-error)]'
            }`}>
              {comment.status.replace('_', ' ')}
            </Badge>
            
            <Select 
              value={comment.status} 
              onValueChange={(value) => onStatusChange(comment.id, value as CodeComment['status'])}
            >
              <SelectTrigger className="w-24 h-6 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="open">Open</SelectItem>
                <SelectItem value="acknowledged">Acknowledged</SelectItem>
                <SelectItem value="resolved">Resolved</SelectItem>
                <SelectItem value="wont_fix">Won't Fix</SelectItem>
              </SelectContent>
            </Select>
          </Flex>
        </Flex>

        {/* File Location */}
        {comment.filePath && (
          <Box variant="panel" padding="sm" rounded="sm">
            <Flex align="center" justify="between">
              <Flex align="center" gap="xs">
                <MapPin className="h-3 w-3 text-[var(--mode-info)]" />
                <Text size="xs" mono>{comment.filePath}</Text>
                {comment.lineNumber > 0 && (
                  <Text size="xs" color="info">:L{comment.lineNumber}</Text>
                )}
              </Flex>
              <Button
                size="xs"
                variant="ghost"
                rightIcon={<ExternalLink className="h-3 w-3" />}
              >
                Open
              </Button>
            </Flex>
          </Box>
        )}

        {/* Content */}
        <Text size="sm">{comment.content}</Text>

        {/* Suggested Change */}
        {comment.suggestedChange && (
          <Box variant="panel" padding="sm" rounded="sm">
            <Stack spacing="xs">
              <Text size="xs" color="muted">Suggested change:</Text>
              <Box className="font-mono text-xs">
                <Box className="text-[var(--mode-error)] bg-[var(--mode-error)]/10 p-1 rounded mb-1">
                  - {comment.suggestedChange.original}
                </Box>
                <Box className="text-[var(--mode-success)] bg-[var(--mode-success)]/10 p-1 rounded">
                  + {comment.suggestedChange.suggested}
                </Box>
              </Box>
              {!comment.suggestedChange.applied && (
                <Button size="xs" variant="primary">
                  Apply Suggestion
                </Button>
              )}
            </Stack>
          </Box>
        )}

        {/* Actions */}
        <Flex justify="between">
          <Flex gap="xs">
            {comment.reactions.map(reaction => (
              <Button
                key={reaction.type}
                size="xs"
                variant={reaction.users.includes('current-user') ? 'secondary' : 'ghost'}
                onClick={() => onReaction(comment.id, reaction.type)}
              >
                {reaction.type === 'like' && '👍'}
                {reaction.type === 'dislike' && '👎'}
                {reaction.type === 'confused' && '😕'}
                {reaction.type === 'heart' && '❤️'}
                {reaction.type === 'rocket' && '🚀'}
                <Text size="xs" className="ml-1">{reaction.users.length}</Text>
              </Button>
            ))}
            
            <Button
              size="xs"
              variant="ghost"
              onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
              leftIcon={<Reply className="h-3 w-3" />}
            >
              Reply
            </Button>
          </Flex>
          
          <Button
            size="xs"
            variant="ghost"
            onClick={onSelect}
          >
            View Details
          </Button>
        </Flex>

        {/* Reply Input */}
        {replyingTo === comment.id && (
          <Form variant="compact">
            <Form.Field>
              <Form.Textarea
                placeholder="Write a reply..."
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                rows={2}
              />
            </Form.Field>
            <Flex gap="sm">
              <Button
                size="sm"
                variant="primary"
                onClick={() => onReply(comment.id)}
                disabled={!replyContent.trim()}
              >
                Post Reply
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setReplyingTo(null);
                  setReplyContent('');
                }}
              >
                Cancel
              </Button>
            </Flex>
          </Form>
        )}

        {/* Replies */}
        {showThreaded && comment.replies.length > 0 && (
          <Box className="pl-4 border-l-2 border-[var(--mode-border)]">
            <Stack spacing="sm">
              {comment.replies.map(reply => (
                <Stack key={reply.id} spacing="xs">
                  <Flex align="center" gap="sm">
                    <Text size="sm" weight="medium">{reply.author.name}</Text>
                    <Text size="xs" color="muted">
                      {new Date(reply.createdAt).toLocaleDateString()}
                    </Text>
                  </Flex>
                  <Text size="sm" color="secondary">{reply.content}</Text>
                </Stack>
              ))}
            </Stack>
          </Box>
        )}

        {/* Tags */}
        {comment.tags.length > 0 && (
          <Flex gap="xs">
            {comment.tags.map(tag => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </Flex>
        )}
      </Stack>
    </Box>
  );
};

// File Grouped Comments Component
const FileGroupedComments: React.FC<{
  commentsByFile: Map<string, CodeComment[]>;
  onStatusChange: (id: string, status: CodeComment['status']) => void;
  onReaction: (id: string, reaction: CodeComment['reactions'][0]['type']) => void;
  onReply: (id: string) => void;
  onSelect: (comment: CodeComment) => void;
  replyingTo: string | null;
  setReplyingTo: (id: string | null) => void;
  replyContent: string;
  setReplyContent: (content: string) => void;
}> = ({ 
  commentsByFile, 
  onStatusChange, 
  onReaction, 
  onReply, 
  onSelect,
  replyingTo,
  setReplyingTo,
  replyContent,
  setReplyContent
}) => {
  return (
    <Stack spacing="md">
      {Array.from(commentsByFile.entries()).map(([filePath, comments]) => (
        <Panel key={filePath} collapsible defaultCollapsed={false}>
          <Panel.Header>
            <Flex align="center" gap="sm">
              <FileText className="h-4 w-4 text-[var(--mode-info)]" />
              <Text weight="medium">{filePath}</Text>
              <Badge variant="outline" className="text-xs">
                {comments.length} {comments.length === 1 ? 'comment' : 'comments'}
              </Badge>
            </Flex>
          </Panel.Header>
          
          <Panel.Content>
            <Stack spacing="sm">
              {comments.map(comment => (
                <CommentItem
                  key={comment.id}
                  comment={comment}
                  onStatusChange={onStatusChange}
                  onReaction={onReaction}
                  onReply={onReply}
                  onSelect={() => onSelect(comment)}
                  replyingTo={replyingTo}
                  setReplyingTo={setReplyingTo}
                  replyContent={replyContent}
                  setReplyContent={setReplyContent}
                />
              ))}
            </Stack>
          </Panel.Content>
        </Panel>
      ))}
    </Stack>
  );
};

// Comment Details Dialog
const CommentDetailsDialog: React.FC<{
  comment: CodeComment;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStatusChange: (id: string, status: CodeComment['status']) => void;
  onReaction: (id: string, reaction: CodeComment['reactions'][0]['type']) => void;
}> = ({ comment, open, onOpenChange, onStatusChange, onReaction }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Comment Details
          </DialogTitle>
          <DialogDescription>
            {comment.author.name} • {new Date(comment.createdAt).toLocaleString()}
          </DialogDescription>
        </DialogHeader>
        
        <Stack spacing="md">
          <Text>{comment.content}</Text>
          
          {/* Full comment details would be implemented here */}
          <Box variant="surface" padding="lg" rounded="md" className="text-center">
            <Text color="muted">Full comment details and thread would be displayed here</Text>
          </Box>
        </Stack>
      </DialogContent>
    </Dialog>
  );
};

// Helper Functions
const generateMockComments = (context: ContextSnapshot, maxComments: number): CodeComment[] => {
  const mockComments: CodeComment[] = [];
  const now = Date.now();
  
  const authors = [
    { id: 'user-1', name: 'Sarah Chen', role: 'reviewer' as const },
    { id: 'user-2', name: 'Mike Johnson', role: 'maintainer' as const },
    { id: 'user-3', name: 'Alex Rivera', role: 'author' as const },
    { id: 'user-4', name: 'CodeQL Bot', role: 'reviewer' as const }
  ];
  
  const commentContents = [
    'This function looks good but consider adding error handling for edge cases.',
    'Great implementation! Very clean and readable.',
    'I think we can optimize this by using a Map instead of an array.',
    'This might cause a memory leak. Consider using weak references.',
    'The variable name could be more descriptive.',
    'Excellent use of TypeScript generics here!',
    'This violates our coding standards for function length.',
    'Consider extracting this into a separate utility function.',
    'The algorithm complexity seems high. Can we do better?',
    'Perfect! This follows our established patterns.'
  ];
  
  for (let i = 0; i < maxComments; i++) {
    const author = authors[Math.floor(Math.random() * authors.length)];
    const content = commentContents[Math.floor(Math.random() * commentContents.length)];
    const type = (['comment', 'suggestion', 'issue', 'question', 'praise'] as const)[Math.floor(Math.random() * 5)];
    const severity = (['info', 'minor', 'major', 'critical'] as const)[Math.floor(Math.random() * 4)];
    const status = (['open', 'resolved', 'acknowledged'] as const)[Math.floor(Math.random() * 3)];
    
    mockComments.push({
      id: `comment-${i}`,
      author: { ...author, avatar: '' },
      content,
      filePath: `src/components/Component${Math.floor(Math.random() * 5) + 1}.tsx`,
      lineNumber: Math.floor(Math.random() * 200) + 1,
      type,
      severity,
      status,
      createdAt: now - (Math.random() * 7 * 24 * 60 * 60 * 1000),
      updatedAt: now - (Math.random() * 24 * 60 * 60 * 1000),
      replies: [],
      reactions: [
        { type: 'like', users: Math.random() > 0.7 ? ['user-1', 'user-2'] : [] },
        { type: 'heart', users: Math.random() > 0.9 ? ['user-3'] : [] }
      ],
      tags: ['review', type === 'issue' ? 'bug' : 'enhancement'],
      isBot: author.name.includes('Bot'),
      threadId: `thread-${i}`,
      suggestedChange: type === 'suggestion' ? {
        original: 'const result = array.filter(item => item.active);',
        suggested: 'const result = array.filter(item => Boolean(item.active));',
        applied: false
      } : undefined
    });
  }
  
  return mockComments.sort((a, b) => b.createdAt - a.createdAt);
};

const generateMockReviewSessions = (): ReviewSession[] => {
  return [
    {
      id: 'session-1',
      reviewerName: 'Sarah Chen',
      startedAt: Date.now() - 3600000,
      status: 'in_progress',
      commentsCount: 5,
      issuesFound: 2
    },
    {
      id: 'session-2',
      reviewerName: 'Mike Johnson',
      startedAt: Date.now() - 7200000,
      completedAt: Date.now() - 1800000,
      status: 'completed',
      commentsCount: 8,
      issuesFound: 1,
      overallRating: 'approve',
      summary: 'Looks good overall, just a few minor suggestions.'
    }
  ];
};

export default CodeReviewWidget;
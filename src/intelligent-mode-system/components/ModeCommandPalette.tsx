import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Mode, ContextSnapshot, ModeCommand } from '../types';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../components/ui/dialog';
import { 
  Search, 
  Clock, 
  Star, 
  Zap, 
  ArrowRight, 
  Command,
  Keyboard,
  Filter,
  TrendingUp
} from 'lucide-react';

interface ModeCommandPaletteProps {
  mode: Mode;
  context: ContextSnapshot;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCommandSelect: (command: ModeCommand) => void;
}

interface CommandCategory {
  name: string;
  commands: ModeCommand[];
  priority: number;
}

interface CommandRelevance {
  score: number;
  reasons: string[];
}

export const ModeCommandPalette: React.FC<ModeCommandPaletteProps> = ({
  mode,
  context,
  open,
  onOpenChange,
  onCommandSelect
}) => {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [showRecentOnly, setShowRecentOnly] = useState(false);
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Reset state when opening
  useEffect(() => {
    if (open) {
      setQuery('');
      setSelectedIndex(0);
      setShowRecentOnly(false);
      setShowFavoritesOnly(false);
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [open]);

  // Generate available commands with context awareness
  const availableCommands = useMemo(() => {
    const baseCommands = mode.commands || [];
    const contextualCommands = generateContextualCommands(context, mode);
    const recentCommands = getRecentCommands(context);
    
    // Combine and deduplicate commands
    const allCommands = [
      ...baseCommands,
      ...contextualCommands,
      ...recentCommands
    ].filter((command, index, array) => 
      array.findIndex(c => c.id === command.id) === index
    );

    // Apply filters
    let filteredCommands = allCommands;
    
    if (showRecentOnly) {
      const recentIds = recentCommands.map(c => c.id);
      filteredCommands = filteredCommands.filter(c => recentIds.includes(c.id));
    }
    
    if (showFavoritesOnly) {
      filteredCommands = filteredCommands.filter(c => c.favorite);
    }

    // Apply search filter
    if (query.trim()) {
      filteredCommands = filteredCommands.filter(cmd => 
        cmd.name.toLowerCase().includes(query.toLowerCase()) ||
        cmd.description?.toLowerCase().includes(query.toLowerCase()) ||
        cmd.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase())) ||
        cmd.category?.toLowerCase().includes(query.toLowerCase())
      );
    }

    return filteredCommands;
  }, [mode, context, query, showRecentOnly, showFavoritesOnly]);

  // Group commands by category
  const commandCategories = useMemo(() => {
    const categories = new Map<string, ModeCommand[]>();
    
    availableCommands.forEach(command => {
      const category = command.category || 'General';
      if (!categories.has(category)) {
        categories.set(category, []);
      }
      categories.get(category)!.push(command);
    });

    // Sort commands within each category by relevance
    categories.forEach((commands, category) => {
      commands.sort((a, b) => {
        const scoreA = calculateCommandRelevance(a, context, mode).score;
        const scoreB = calculateCommandRelevance(b, context, mode).score;
        return scoreB - scoreA;
      });
    });

    // Convert to array and sort categories by priority
    const categoryArray: CommandCategory[] = Array.from(categories.entries()).map(([name, commands]) => ({
      name,
      commands,
      priority: getCategoryPriority(name, mode)
    }));

    return categoryArray.sort((a, b) => b.priority - a.priority);
  }, [availableCommands, context, mode]);

  // Flatten commands for keyboard navigation
  const flattenedCommands = useMemo(() => {
    return commandCategories.flatMap(category => category.commands);
  }, [commandCategories]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < flattenedCommands.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : prev);
          break;
        case 'Enter':
          e.preventDefault();
          if (flattenedCommands[selectedIndex]) {
            handleCommandSelect(flattenedCommands[selectedIndex]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          onOpenChange(false);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [open, selectedIndex, flattenedCommands, onOpenChange]);

  const handleCommandSelect = (command: ModeCommand) => {
    onCommandSelect(command);
    onOpenChange(false);
    
    // Track command usage
    trackCommandUsage(command, context);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="mode-command-palette max-w-3xl max-h-[80vh] bg-gray-900 border-gray-700">
        <DialogHeader className="pb-2">
          <DialogTitle className="flex items-center gap-2">
            <Command className="h-5 w-5" />
            Command Palette - {mode.name} Mode
          </DialogTitle>
          <DialogDescription>
            Search and execute commands contextually relevant to your current workflow
          </DialogDescription>
        </DialogHeader>

        {/* Search Input */}
        <div className="command-search relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            ref={inputRef}
            placeholder={`Search ${mode.name} commands...`}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="pl-10 bg-gray-800 border-gray-600 focus:border-blue-500"
          />
        </div>

        {/* Filters */}
        <div className="flex items-center gap-2 py-2">
          <Button
            size="sm"
            variant={showRecentOnly ? "default" : "outline"}
            onClick={() => setShowRecentOnly(!showRecentOnly)}
            className="h-7 text-xs"
          >
            <Clock className="h-3 w-3 mr-1" />
            Recent
          </Button>
          <Button
            size="sm"
            variant={showFavoritesOnly ? "default" : "outline"}
            onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
            className="h-7 text-xs"
          >
            <Star className="h-3 w-3 mr-1" />
            Favorites
          </Button>
          <div className="flex-1" />
          <Badge variant="secondary" className="text-xs">
            {flattenedCommands.length} commands
          </Badge>
        </div>

        {/* Command Categories */}
        <div className="command-categories max-h-96 overflow-y-auto space-y-4">
          {commandCategories.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No commands found matching your search</p>
              <p className="text-sm mt-1">Try a different search term or clear filters</p>
            </div>
          ) : (
            commandCategories.map((category, categoryIndex) => (
              <div key={category.name} className="command-category">
                <div className="category-header flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-300">
                    {category.name}
                  </h4>
                  <Badge variant="outline" className="text-xs">
                    {category.commands.length}
                  </Badge>
                </div>
                
                <div className="command-list space-y-1">
                  {category.commands.map((command, commandIndex) => {
                    const globalIndex = commandCategories
                      .slice(0, categoryIndex)
                      .reduce((acc, cat) => acc + cat.commands.length, 0) + commandIndex;
                    
                    return (
                      <CommandItem
                        key={command.id}
                        command={command}
                        isSelected={globalIndex === selectedIndex}
                        onClick={() => handleCommandSelect(command)}
                        context={context}
                        mode={mode}
                      />
                    );
                  })}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Footer */}
        <div className="command-footer border-t border-gray-700 pt-3 flex items-center justify-between text-xs text-gray-400">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <kbd className="kbd">↑↓</kbd>
              <span>Navigate</span>
            </div>
            <div className="flex items-center gap-1">
              <kbd className="kbd">Enter</kbd>
              <span>Execute</span>
            </div>
            <div className="flex items-center gap-1">
              <kbd className="kbd">Esc</kbd>
              <span>Close</span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Keyboard className="h-3 w-3" />
            <span>Keyboard shortcuts available</span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Command Item Component
const CommandItem: React.FC<{
  command: ModeCommand;
  isSelected: boolean;
  onClick: () => void;
  context: ContextSnapshot;
  mode: Mode;
}> = ({ command, isSelected, onClick, context, mode }) => {
  const relevance = calculateCommandRelevance(command, context, mode);
  
  return (
    <div
      className={`command-item p-3 rounded-lg cursor-pointer transition-all duration-150 ${
        isSelected 
          ? 'bg-blue-600/20 border border-blue-500/50' 
          : 'hover:bg-gray-800/50 border border-transparent'
      }`}
      onClick={onClick}
    >
      <div className="flex items-start gap-3">
        {/* Command Icon */}
        <div className="command-icon mt-0.5">
          {command.icon ? (
            <command.icon className="h-5 w-5 text-gray-400" />
          ) : (
            <Zap className="h-5 w-5 text-gray-400" />
          )}
        </div>
        
        {/* Command Content */}
        <div className="command-content flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="command-name font-medium text-white">
              {command.name}
            </span>
            
            {/* Badges */}
            <div className="flex items-center gap-1">
              {command.shortcut && (
                <kbd className="kbd text-xs px-1.5 py-0.5 bg-gray-700 rounded">
                  {command.shortcut}
                </kbd>
              )}
              
              {relevance.score > 0.8 && (
                <Badge variant="default" className="text-xs bg-green-600/20 text-green-400 border-green-500/30">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Recommended
                </Badge>
              )}
              
              {command.favorite && (
                <Star className="h-3 w-3 text-yellow-500 fill-current" />
              )}
              
              {command.recent && (
                <Clock className="h-3 w-3 text-blue-400" />
              )}
            </div>
          </div>
          
          {/* Description */}
          {command.description && (
            <p className="command-description text-sm text-gray-400 mb-2">
              {command.description}
            </p>
          )}
          
          {/* Relevance Reasons */}
          {relevance.score > 0.6 && relevance.reasons.length > 0 && (
            <div className="relevance-reasons">
              <div className="flex items-center gap-2 mb-1">
                <div className="relevance-bar flex-1 h-1 bg-gray-700 rounded-full overflow-hidden">
                  <div 
                    className={`h-full transition-all duration-300 ${
                      relevance.score > 0.8 ? 'bg-green-500' :
                      relevance.score > 0.6 ? 'bg-yellow-500' : 'bg-gray-500'
                    }`}
                    style={{ width: `${relevance.score * 100}%` }}
                  />
                </div>
                <span className="text-xs text-gray-500">
                  {Math.round(relevance.score * 100)}% relevant
                </span>
              </div>
              <div className="text-xs text-gray-500">
                {relevance.reasons[0]}
              </div>
            </div>
          )}
          
          {/* Tags */}
          {command.tags && command.tags.length > 0 && (
            <div className="command-tags flex items-center gap-1 mt-2">
              {command.tags.slice(0, 3).map(tag => (
                <Badge 
                  key={tag} 
                  variant="outline" 
                  className="text-xs border-gray-600 text-gray-400"
                >
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
        
        {/* Action Arrow */}
        <div className="command-action">
          <ArrowRight className={`h-4 w-4 transition-all duration-150 ${
            isSelected ? 'text-blue-400 translate-x-1' : 'text-gray-600'
          }`} />
        </div>
      </div>
    </div>
  );
};

// Helper Functions

const generateContextualCommands = (context: ContextSnapshot, mode: Mode): ModeCommand[] => {
  const commands: ModeCommand[] = [];
  
  // File-based commands
  if (context.fileContext.hasErrors) {
    commands.push({
      id: 'debug-current-file',
      name: 'Debug Current File',
      description: 'Start debugging the current file with errors',
      category: 'Debug',
      icon: Search,
      tags: ['debug', 'error', 'file'],
      contextRelevance: 0.9
    });
  }
  
  // Git-based commands
  if (context.environmentContext.gitStatus?.staged.length) {
    commands.push({
      id: 'review-staged-changes',
      name: 'Review Staged Changes',
      description: 'Review changes that are staged for commit',
      category: 'Review',
      icon: Search,
      tags: ['review', 'git', 'staged'],
      contextRelevance: 0.8
    });
  }
  
  // Mode-specific contextual commands
  switch (mode.id) {
    case 'architect':
      if (context.projectContext.structure.hasTests) {
        commands.push({
          id: 'analyze-test-coverage',
          name: 'Analyze Test Coverage',
          description: 'Review test coverage across the project',
          category: 'Analysis',
          icon: Search,
          tags: ['testing', 'coverage', 'analysis'],
          contextRelevance: 0.7
        });
      }
      break;
      
    case 'debug':
      commands.push({
        id: 'clear-all-breakpoints',
        name: 'Clear All Breakpoints',
        description: 'Remove all active breakpoints',
        category: 'Debug',
        icon: Search,
        tags: ['debug', 'breakpoints'],
        contextRelevance: 0.6
      });
      break;
  }
  
  return commands;
};

const getRecentCommands = (context: ContextSnapshot): ModeCommand[] => {
  // This would typically come from user context/history
  // For now, return mock recent commands
  return context.userContext.recentActions
    .filter(action => action.type === 'command')
    .slice(0, 5)
    .map((action, index) => ({
      id: `recent-${index}`,
      name: action.target || 'Recent Command',
      description: 'Recently used command',
      category: 'Recent',
      icon: Clock,
      tags: ['recent'],
      recent: true,
      contextRelevance: 0.5
    }));
};

const calculateCommandRelevance = (
  command: ModeCommand, 
  context: ContextSnapshot, 
  mode: Mode
): CommandRelevance => {
  let score = 0.1; // Base score
  const reasons: string[] = [];
  
  // Context-based relevance
  if (command.contextRelevance) {
    score += command.contextRelevance * 0.4;
  }
  
  // File context relevance
  if (context.fileContext.hasErrors && command.tags?.includes('debug')) {
    score += 0.3;
    reasons.push('File has errors - debug commands are relevant');
  }
  
  // Recent usage
  if (command.recent) {
    score += 0.2;
    reasons.push('Recently used command');
  }
  
  // Favorite commands
  if (command.favorite) {
    score += 0.15;
    reasons.push('Marked as favorite');
  }
  
  // Mode alignment
  if (command.category?.toLowerCase() === mode.id) {
    score += 0.2;
    reasons.push(`Specific to ${mode.name} mode`);
  }
  
  return {
    score: Math.min(1, score),
    reasons
  };
};

const getCategoryPriority = (categoryName: string, mode: Mode): number => {
  const priorities: Record<string, number> = {
    [mode.name]: 100,
    'Recent': 90,
    'Favorites': 85,
    'Debug': 80,
    'Analysis': 70,
    'Review': 60,
    'General': 50
  };
  
  return priorities[categoryName] || 40;
};

const trackCommandUsage = (command: ModeCommand, context: ContextSnapshot) => {
  // Track command usage for improving relevance
  console.log('Command executed:', command.id, 'in context:', context.timestamp);
};

export default ModeCommandPalette;
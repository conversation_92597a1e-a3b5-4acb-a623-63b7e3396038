import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../components/ui/select';
import { SmartPromptInput } from './SmartPromptInput';
import { ContextSnapshot } from '../types';
import { Mode } from '../types/mode.types';
import { 
  Brain, 
  Code, 
  MessageSquare, 
  Settings, 
  RotateCcw,
  GitBranch,
  FileText,
  Monitor
} from 'lucide-react';

// Mock modes for demo
const mockModes: Mode[] = [
  {
    id: 'architect',
    name: 'Architect Mode',
    description: 'System design and architecture',
    icon: '🏗️',
    color: '#8B5CF6',
    isActive: false,
    features: [],
    shortcuts: []
  },
  {
    id: 'debug',
    name: 'Debug Mode', 
    description: 'Debugging and troubleshooting',
    icon: '🐛',
    color: '#EF4444',
    isActive: false,
    features: [],
    shortcuts: []
  },
  {
    id: 'review',
    name: 'Review Mode',
    description: 'Code review and quality analysis',
    icon: '👁️',
    color: '#10B981',
    isActive: false,
    features: [],
    shortcuts: []
  },
  {
    id: 'deploy',
    name: 'Deploy Mode',
    description: 'Deployment and CI/CD',
    icon: '🚀',
    color: '#F59E0B',
    isActive: false,
    features: [],
    shortcuts: []
  },
  {
    id: 'experiment',
    name: 'Experiment Mode',
    description: 'Rapid prototyping and testing',
    icon: '🧪',
    color: '#10B981',
    isActive: false,
    features: [],
    shortcuts: []
  },
  {
    id: 'learn',
    name: 'Learn Mode',
    description: 'Documentation and learning',
    icon: '📚',
    color: '#06B6D4',
    isActive: false,
    features: [],
    shortcuts: []
  },
];

// Mock context
function createMockContext(_selectedMode: string, selectedFile: string, selectedProject: string): ContextSnapshot {
  return {
    fileContext: {
      path: selectedFile !== 'none' ? `/src/${selectedFile}` : '',
      type: selectedFile !== 'none' ? 'file' : 'none',
      language: selectedFile?.endsWith('.ts') ? 'typescript' : 
                selectedFile?.endsWith('.py') ? 'python' : 
                selectedFile?.endsWith('.go') ? 'go' : 'unknown',
      hasErrors: false,
      hasWarnings: false,
      lastModified: Date.now() - Math.floor(Math.random() * 86400000),
    },
    projectContext: {
      type: selectedProject === 'web-app' ? 'node' : 
            selectedProject === 'api-server' ? 'node' :
            selectedProject === 'cli-tool' ? 'go' : 'unknown',
      rootPath: '/Users/<USER>/projects/intelligent-mode-system',
      dependencies: {
        'react': '^18.0.0',
        'typescript': '^4.9.0',
        '@types/node': '^18.0.0',
      },
      structure: {
        directories: ['src', 'tests', 'docs'],
        fileCount: 150,
        totalSize: 2048000,
        depth: 5,
        hasTests: true,
        hasDocs: true,
      },
      configuration: {},
    },
    environmentContext: {
      gitStatus: {
        branch: 'feature/smart-prompt-input',
        ahead: 3,
        behind: 0,
        modified: ['SmartPromptInput.tsx'],
        staged: [],
        untracked: [],
        hasConflicts: false,
      },
      runningProcesses: [],
      systemResources: {
        cpuUsage: 25,
        memoryUsage: 60,
        diskUsage: 45,
      },
      openFiles: [selectedFile !== 'none' ? `/src/${selectedFile}` : ''].filter(Boolean),
      activeTerminals: 2,
    },
    userContext: {
      recentActions: [
        {
          type: 'edit',
          target: selectedFile || 'SmartPromptInput.tsx',
          timestamp: Date.now() - 30000,
        },
        {
          type: 'search',
          target: 'context suggestions',
          timestamp: Date.now() - 60000,
        },
        {
          type: 'navigate',
          target: 'components directory',
          timestamp: Date.now() - 90000,
        },
      ],
      preferences: {
        theme: 'dark',
        shortcuts: {},
        autoTransition: true,
        suggestionLevel: 'aggressive',
      },
      patterns: [],
      sessionDuration: 3600000, // 1 hour
      lastActivity: Date.now(),
    },
    timestamp: Date.now(),
  };
}

interface DemoResponse {
  prompt: string;
  metadata?: any;
  timestamp: number;
}

export const SmartPromptInputDemo: React.FC = () => {
  const [selectedModeId, setSelectedModeId] = useState('architect');
  const [selectedFile, setSelectedFile] = useState('SmartPromptInput.tsx');
  const [selectedProject, setSelectedProject] = useState('web-app');
  const [multiline, setMultiline] = useState(false);
  const [responses, setResponses] = useState<DemoResponse[]>([]);

  const currentMode = mockModes.find(m => m.id === selectedModeId) || mockModes[0];
  const context = createMockContext(selectedModeId, selectedFile, selectedProject);

  const handlePromptSubmit = (prompt: string, metadata?: any) => {
    const response: DemoResponse = {
      prompt,
      metadata,
      timestamp: Date.now(),
    };
    
    setResponses(prev => [response, ...prev.slice(0, 9)]); // Keep last 10 responses
  };

  const clearResponses = () => {
    setResponses([]);
  };

  const fileOptions = [
    { value: 'none', label: 'No file selected' },
    { value: 'SmartPromptInput.tsx', label: 'SmartPromptInput.tsx' },
    { value: 'App.tsx', label: 'App.tsx' },
    { value: 'utils.ts', label: 'utils.ts' },
    { value: 'api.py', label: 'api.py' },
    { value: 'main.go', label: 'main.go' },
    { value: 'test.spec.ts', label: 'test.spec.ts' },
  ];

  const projectOptions = [
    { value: 'none', label: 'No project selected' },
    { value: 'web-app', label: 'React Web App' },
    { value: 'api-server', label: 'Node.js API Server' },
    { value: 'cli-tool', label: 'Go CLI Tool' },
  ];

  return (
    <div className="smart-prompt-input-demo space-y-6 p-6 bg-gray-950 min-h-screen">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-white flex items-center justify-center gap-3">
          <Brain className="h-8 w-8 text-blue-400" />
          Smart Prompt Input Demo
        </h1>
        <p className="text-gray-400 max-w-2xl mx-auto">
          Experience contextual AI prompts that adapt to your current mode, file, and project context.
          Switch between different modes and contexts to see how suggestions change.
        </p>
      </div>

      {/* Configuration Panel */}
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Demo Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Mode Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300">Mode</label>
              <Select value={selectedModeId} onValueChange={setSelectedModeId}>
                <SelectTrigger className="bg-gray-800 border-gray-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  {mockModes.map(mode => (
                    <SelectItem key={mode.id} value={mode.id}>
                      <div className="flex items-center gap-2">
                        <span>{mode.icon}</span>
                        <span>{mode.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* File Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300">Current File</label>
              <Select value={selectedFile} onValueChange={setSelectedFile}>
                <SelectTrigger className="bg-gray-800 border-gray-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  {fileOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <span>{option.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Project Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300">Project Type</label>
              <Select value={selectedProject} onValueChange={setSelectedProject}>
                <SelectTrigger className="bg-gray-800 border-gray-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  {projectOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Code className="h-4 w-4" />
                        <span>{option.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Input Type */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300">Input Type</label>
              <Select value={multiline ? 'multiline' : 'single'} onValueChange={(v) => setMultiline(v === 'multiline')}>
                <SelectTrigger className="bg-gray-800 border-gray-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="single">Single Line</SelectItem>
                  <SelectItem value="multiline">Multi Line</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Context Display */}
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            Current Context
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-300">Mode Context</div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" style={{ color: currentMode.color, borderColor: currentMode.color }}>
                  {currentMode.icon} {currentMode.name}
                </Badge>
              </div>
              <div className="text-xs text-gray-400">{currentMode.description}</div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-300">File Context</div>
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-blue-400" />
                <span className="text-sm font-mono">{context.fileContext.path || 'No file'}</span>
              </div>
              {context.fileContext.language && (
                <Badge variant="secondary" className="text-xs">
                  {context.fileContext.language}
                </Badge>
              )}
            </div>
            
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-300">Git Context</div>
              <div className="flex items-center gap-2">
                <GitBranch className="h-4 w-4 text-green-400" />
                <span className="text-sm font-mono">{context.environmentContext.gitStatus?.branch}</span>
              </div>
              {(context.environmentContext.gitStatus?.modified.length || 0) > 0 && (
                <Badge variant="outline" className="text-xs text-orange-400 border-orange-400">
                  Changes
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Smart Prompt Input */}
      <div className="max-w-4xl mx-auto">
        <SmartPromptInput
          context={context}
          currentMode={currentMode}
          onSubmit={handlePromptSubmit}
          multiline={multiline}
          placeholder={`Ask me anything about ${currentMode.name.toLowerCase()}...`}
          showSuggestions={true}
          showTemplates={true}
          className="w-full"
        />
      </div>

      {/* Response History */}
      {responses.length > 0 && (
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Recent Prompts ({responses.length})
              </CardTitle>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={clearResponses}
                className="h-8"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Clear
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {responses.map((response, index) => (
                <div key={index} className="p-3 bg-gray-800 rounded-lg border border-gray-700">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1">
                      <div className="text-sm text-white mb-2">{response.prompt}</div>
                      {response.metadata && (
                        <div className="text-xs text-gray-400">
                          <span className="font-medium">Mode:</span> {response.metadata.mode} | 
                          <span className="font-medium"> Context:</span> {response.metadata.context?.file || 'none'}
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(response.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Feature Highlights */}
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle>✨ Features Demonstrated</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-green-400">
                <span>✅</span>
                <span className="font-medium">Context-aware suggestions</span>
              </div>
              <div className="flex items-center gap-2 text-green-400">
                <span>✅</span>
                <span className="font-medium">Mode-specific templates</span>
              </div>
              <div className="flex items-center gap-2 text-green-400">
                <span>✅</span>
                <span className="font-medium">Smart auto-completion</span>
              </div>
              <div className="flex items-center gap-2 text-green-400">
                <span>✅</span>
                <span className="font-medium">Recent prompt history</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-green-400">
                <span>✅</span>
                <span className="font-medium">Command palette (⌘K)</span>
              </div>
              <div className="flex items-center gap-2 text-green-400">
                <span>✅</span>
                <span className="font-medium">Category filtering</span>
              </div>
              <div className="flex items-center gap-2 text-green-400">
                <span>✅</span>
                <span className="font-medium">Variable substitution</span>
              </div>
              <div className="flex items-center gap-2 text-green-400">
                <span>✅</span>
                <span className="font-medium">Responsive design</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SmartPromptInputDemo;
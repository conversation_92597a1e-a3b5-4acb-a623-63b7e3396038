# Intelligent Mode System - Composition Patterns

## Overview

The Intelligent Mode System uses a composition-based architecture to build complex UI components from simple, reusable primitives. This approach provides:

- **Consistency**: All components use the same design tokens
- **Flexibility**: Components can be composed in various ways
- **Maintainability**: Changes to primitives propagate throughout the system
- **Theming**: Mode-specific themes are automatically applied

## Architecture Layers

### 1. Design Tokens (`/design-tokens`)
The foundation of our theming system, providing consistent values for:
- Colors (40+ semantic color properties per mode)
- Typography (size, weight, line-height)
- Spacing (consistent spacing scale)
- Shadows (elevation system)
- Animations (duration and easing)

### 2. Primitive Components (`/primitives`)
Basic building blocks that use design tokens:

#### Box
- Base component for all others
- Polymorphic rendering with `as` prop
- Variant system for common styles
- Direct CSS variable usage

```tsx
<Box variant="surface" padding="md" rounded="lg" border>
  Content
</Box>
```

#### Flex
- Flexbox layout primitive
- Built on Box
- Common flex patterns

```tsx
<Flex align="center" justify="between" gap="md">
  <Text>Left</Text>
  <Text>Right</Text>
</Flex>
```

#### Stack
- Vertical/horizontal stacking
- Consistent spacing
- Built on Flex

```tsx
<Stack spacing="md" direction="vertical">
  <Card>Item 1</Card>
  <Card>Item 2</Card>
</Stack>
```

#### Text
- Typography component
- Size and weight variants
- Semantic colors

```tsx
<Text size="lg" weight="semibold" color="primary">
  Heading
</Text>
```

#### Button
- Interactive button component
- Multiple variants and sizes
- Loading and disabled states
- Icon support

```tsx
<Button 
  variant="primary" 
  size="md" 
  leftIcon={<Icon />}
  loading={isLoading}
>
  Click me
</Button>
```

#### Input & Textarea
- Form input components
- Consistent styling
- Error states
- Icon support

```tsx
<Input 
  placeholder="Enter text"
  leftIcon={<SearchIcon />}
  error={hasError}
/>
```

### 3. Compound Components (`/compounds`)
Higher-level components built using primitives:

#### ModePanel
- Container for mode-specific features
- Collapsible functionality
- Header, content, and footer sections

```tsx
<Panel collapsible>
  <Panel.Header>
    <Text>Feature Name</Text>
  </Panel.Header>
  <Panel.Content>
    {/* Feature content */}
  </Panel.Content>
  <Panel.Footer>
    {/* Actions */}
  </Panel.Footer>
</Panel>
```

#### ModeWidget
- Standard widget container
- Loading, error, and empty states
- Consistent header with actions

```tsx
<Widget 
  title="System Metrics"
  icon={<ChartIcon />}
  loading={isLoading}
  error={error}
>
  {/* Widget content */}
</Widget>
```

#### ModeForm
- Form container with context
- Field, label, and input composition
- Error handling and validation

```tsx
<Form onSubmit={handleSubmit}>
  <Form.Field required>
    <Form.Label>Username</Form.Label>
    <Form.Input 
      name="username"
      error={errors.username}
      hint="Enter your username"
    />
  </Form.Field>
  <Form.Actions>
    <Button type="submit">Submit</Button>
  </Form.Actions>
</Form>
```

### 4. Widget Components (`/widgets`)
Feature-specific components using primitives and compounds:

```tsx
// Example: Refactored DiagramWidget
export const DiagramWidget = ({ context, config }) => {
  return (
    <Widget
      title="System Architecture"
      icon={<NetworkIcon />}
      size="lg"
      loading={isLoading}
      actions={
        <Widget.Actions>
          <Button size="sm" onClick={refresh}>
            Refresh
          </Button>
        </Widget.Actions>
      }
    >
      <Stack spacing="md">
        {/* Widget content using primitives */}
      </Stack>
    </Widget>
  );
};
```

## Composition Patterns

### 1. Basic Composition
Combine primitives to create simple UI elements:

```tsx
const MetricCard = ({ label, value, color }) => (
  <Box variant="surface" padding="md" rounded="md">
    <Text size="2xl" weight="bold" color={color}>
      {value}
    </Text>
    <Text size="sm" color="muted">
      {label}
    </Text>
  </Box>
);
```

### 2. Compound Components
Create components with multiple named parts:

```tsx
const FeaturePanel = Object.assign(BasePanel, {
  Header: PanelHeader,
  Content: PanelContent,
  Footer: PanelFooter,
});

// Usage
<FeaturePanel>
  <FeaturePanel.Header>Title</FeaturePanel.Header>
  <FeaturePanel.Content>...</FeaturePanel.Content>
</FeaturePanel>
```

### 3. Context-Based Composition
Share state between compound components:

```tsx
const WidgetContext = createContext();

const Widget = ({ children, ...props }) => {
  const [state, setState] = useState();
  return (
    <WidgetContext.Provider value={{ state, setState }}>
      <Box {...props}>{children}</Box>
    </WidgetContext.Provider>
  );
};

const WidgetContent = () => {
  const { state } = useContext(WidgetContext);
  return <div>{/* Use shared state */}</div>;
};
```

### 4. Polymorphic Components
Components that can render as different elements:

```tsx
<Box as="button" onClick={handleClick}>
  Click me
</Box>

<Box as="a" href="/link">
  Link
</Box>
```

## Theming Integration

All components automatically use CSS variables from the design token system:

```tsx
// Colors
<Box className="bg-[var(--mode-primary)]" />

// Spacing
<Box padding="md" /> // Uses var(--spacing-md)

// Typography
<Text size="lg" /> // Uses var(--font-size-lg)

// Shadows
<Box shadow="md" /> // Uses var(--shadow-md)
```

## Migration Guide

### From Hardcoded Styles to Composition

Before:
```tsx
<div className="bg-gray-800 p-4 rounded-lg border border-gray-600">
  <h3 className="text-lg font-semibold text-white mb-2">
    Title
  </h3>
  <p className="text-sm text-gray-400">
    Content
  </p>
</div>
```

After:
```tsx
<Box variant="surface" padding="md" rounded="lg" border>
  <Text size="lg" weight="semibold" className="mb-2">
    Title
  </Text>
  <Text size="sm" color="muted">
    Content
  </Text>
</Box>
```

### From Monolithic to Compound

Before:
```tsx
const Widget = ({ title, content, actions, loading }) => (
  <Card>
    <CardHeader>
      <CardTitle>{title}</CardTitle>
      <div>{actions}</div>
    </CardHeader>
    <CardContent>
      {loading ? <Spinner /> : content}
    </CardContent>
  </Card>
);
```

After:
```tsx
const Widget = ({ title, children, ...props }) => (
  <ModeWidget title={title} {...props}>
    {children}
  </ModeWidget>
);

// Usage with composition
<Widget title="Example" loading={loading}>
  <Stack spacing="md">
    <MetricCard {...metric1} />
    <MetricCard {...metric2} />
  </Stack>
</Widget>
```

## Best Practices

1. **Use Primitives First**: Start with primitive components before creating custom ones
2. **Compose, Don't Duplicate**: Build complex components by composing simpler ones
3. **Leverage Design Tokens**: Always use CSS variables for theming
4. **Keep Components Small**: Each component should have a single responsibility
5. **Use TypeScript**: Ensure type safety throughout the composition chain
6. **Document Patterns**: Create examples for common composition patterns

## Component Checklist

When creating new components:

- [ ] Uses primitive components as building blocks
- [ ] Applies design tokens via CSS variables
- [ ] Supports common props (className, style, ref)
- [ ] Has TypeScript types
- [ ] Follows naming conventions
- [ ] Includes usage examples
- [ ] Supports all mode themes
- [ ] Handles loading/error/empty states where applicable
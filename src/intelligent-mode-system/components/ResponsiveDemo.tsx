import React, { useState } from 'react';
import { ModeSelectorResponsive } from './ModeSelectorResponsive';
import { SmartPromptInputResponsive } from './SmartPromptInputResponsive';
import { ModeLayoutResponsive } from './layout/ModeLayoutResponsive';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  Accessibility,
  Zap,
  Layout,
  MessageSquare
} from 'lucide-react';
import { modes } from '../registry/defaultModes';
import { createMockContext } from '../utils/mockData';
import { useBreakpoint, responsiveClasses } from '../utils/responsive';
import { a11yClasses } from '../utils/accessibility';

export const ResponsiveDemo: React.FC = () => {
  const [currentMode, setCurrentMode] = useState(modes[0]);
  const [devicePreview, setDevicePreview] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const breakpoint = useBreakpoint();
  const mockContext = createMockContext();

  const handlePromptSubmit = (prompt: string, metadata?: any) => {
    console.log('Submitted prompt:', prompt, metadata);
  };

  return (
    <div className={`responsive-demo ${responsiveClasses.container}`}>
      <div className={responsiveClasses.spacing.section}>
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className={`${responsiveClasses.text.h1} mb-4`}>
            Responsive & Accessible UI Components
          </h1>
          <p className={`${responsiveClasses.text.body} text-gray-600 max-w-2xl mx-auto`}>
            Experience the intelligent mode system with responsive design and full accessibility support.
            Components adapt seamlessly across devices while maintaining WCAG compliance.
          </p>
          
          <div className="flex flex-wrap justify-center gap-2 mt-6">
            <Badge variant="secondary" className="text-sm">
              <Smartphone className="h-3 w-3 mr-1" />
              Mobile First
            </Badge>
            <Badge variant="secondary" className="text-sm">
              <Accessibility className="h-3 w-3 mr-1" />
              WCAG 2.1 AA
            </Badge>
            <Badge variant="secondary" className="text-sm">
              <Zap className="h-3 w-3 mr-1" />
              Touch Optimized
            </Badge>
          </div>
        </div>

        {/* Current Breakpoint Indicator */}
        <Card className="mb-6">
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Current Breakpoint:</span>
              <Badge>{breakpoint}</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Device Preview Selector */}
        <div className="flex justify-center mb-8">
          <div className="inline-flex rounded-lg border p-1">
            <Button
              size="sm"
              variant={devicePreview === 'mobile' ? 'default' : 'ghost'}
              onClick={() => setDevicePreview('mobile')}
              className={a11yClasses.focusVisible}
              aria-label="Mobile preview"
            >
              <Smartphone className="h-4 w-4" />
              <span className={`ml-2 ${responsiveClasses.visibility.hideMobile}`}>Mobile</span>
            </Button>
            <Button
              size="sm"
              variant={devicePreview === 'tablet' ? 'default' : 'ghost'}
              onClick={() => setDevicePreview('tablet')}
              className={a11yClasses.focusVisible}
              aria-label="Tablet preview"
            >
              <Tablet className="h-4 w-4" />
              <span className={`ml-2 ${responsiveClasses.visibility.hideMobile}`}>Tablet</span>
            </Button>
            <Button
              size="sm"
              variant={devicePreview === 'desktop' ? 'default' : 'ghost'}
              onClick={() => setDevicePreview('desktop')}
              className={a11yClasses.focusVisible}
              aria-label="Desktop preview"
            >
              <Monitor className="h-4 w-4" />
              <span className={`ml-2 ${responsiveClasses.visibility.hideMobile}`}>Desktop</span>
            </Button>
          </div>
        </div>

        {/* Component Demos */}
        <Tabs defaultValue="selector" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="selector" className={a11yClasses.focusVisible}>
              Mode Selector
            </TabsTrigger>
            <TabsTrigger value="prompt" className={a11yClasses.focusVisible}>
              Smart Prompt
            </TabsTrigger>
            <TabsTrigger value="layout" className={a11yClasses.focusVisible}>
              Mode Layout
            </TabsTrigger>
          </TabsList>

          <TabsContent value="selector" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layout className="h-5 w-5" />
                  Responsive Mode Selector
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Compact Variant */}
                  <div>
                    <h3 className="text-sm font-medium mb-3">Compact Variant (Mobile)</h3>
                    <ModeSelectorResponsive
                      modes={modes}
                      currentMode={currentMode}
                      onModeChange={setCurrentMode}
                      variant="compact"
                      allowSearch={true}
                      groupByCategory={true}
                    />
                  </div>

                  {/* Default Variant */}
                  <div>
                    <h3 className="text-sm font-medium mb-3">Default Variant</h3>
                    <ModeSelectorResponsive
                      modes={modes}
                      currentMode={currentMode}
                      onModeChange={setCurrentMode}
                      variant="default"
                      showDescriptions={true}
                    />
                  </div>

                  {/* Expanded Variant */}
                  <div className={responsiveClasses.visibility.hideTablet}>
                    <h3 className="text-sm font-medium mb-3">Expanded Variant (Desktop)</h3>
                    <ModeSelectorResponsive
                      modes={modes}
                      currentMode={currentMode}
                      onModeChange={setCurrentMode}
                      variant="expanded"
                      showDescriptions={true}
                      groupByCategory={false}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="prompt" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Smart Prompt Input
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* With Voice Input */}
                  <div>
                    <h3 className="text-sm font-medium mb-3">With Voice Input & Recent History</h3>
                    <SmartPromptInputResponsive
                      context={mockContext}
                      currentMode={currentMode}
                      onSubmit={handlePromptSubmit}
                      placeholder="Ask me anything..."
                      enableVoiceInput={true}
                      showRecent={true}
                      showSuggestions={true}
                    />
                  </div>

                  {/* Minimal */}
                  <div>
                    <h3 className="text-sm font-medium mb-3">Minimal Configuration</h3>
                    <SmartPromptInputResponsive
                      context={mockContext}
                      currentMode={currentMode}
                      onSubmit={handlePromptSubmit}
                      placeholder="Type your prompt..."
                      showRecent={false}
                      showSuggestions={false}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="layout" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layout className="h-5 w-5" />
                  Responsive Mode Layout
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-gray-600 mb-4">
                  <p>The Mode Layout component automatically adapts to different screen sizes:</p>
                  <ul className="list-disc ml-5 mt-2 space-y-1">
                    <li><strong>Mobile:</strong> Collapsible sidebar, tab-based panels, touch gestures</li>
                    <li><strong>Tablet:</strong> Reduced panel sizes, hidden low-priority panels</li>
                    <li><strong>Desktop:</strong> Full layout with resizable panels and sidebar</li>
                  </ul>
                </div>
                
                <div 
                  className={`
                    border rounded-lg overflow-hidden
                    ${devicePreview === 'mobile' ? 'max-w-sm mx-auto' : ''}
                    ${devicePreview === 'tablet' ? 'max-w-2xl mx-auto' : ''}
                  `}
                  style={{ height: '600px' }}
                >
                  <ModeLayoutResponsive
                    mode={currentMode}
                    context={mockContext}
                    className="h-full"
                  >
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <p className="text-lg font-medium mb-2">
                          {currentMode.name} Mode Content
                        </p>
                        <p className="text-sm text-gray-500">
                          Resize your browser to see responsive behavior
                        </p>
                      </div>
                    </div>
                  </ModeLayoutResponsive>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Accessibility Features */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Accessibility className="h-5 w-5" />
              Accessibility Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`grid gap-6 ${responsiveClasses.grid.cols[2]}`}>
              <div>
                <h3 className="font-medium mb-3">Keyboard Navigation</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Full keyboard support for all interactions</li>
                  <li>• Focus trapping in modals and dropdowns</li>
                  <li>• Roving tabindex for list navigation</li>
                  <li>• Keyboard shortcuts with help dialog (?)</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium mb-3">Screen Reader Support</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Semantic HTML and ARIA labels</li>
                  <li>• Live region announcements</li>
                  <li>• Descriptive button and link text</li>
                  <li>• Form field associations</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium mb-3">Visual Accessibility</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• WCAG AA color contrast ratios</li>
                  <li>• Focus indicators on all interactive elements</li>
                  <li>• Reduced motion support</li>
                  <li>• High contrast mode compatibility</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium mb-3">Mobile Accessibility</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Touch targets meet 44x44px minimum</li>
                  <li>• Gesture alternatives for all actions</li>
                  <li>• Viewport zoom enabled</li>
                  <li>• Safe area insets for notched devices</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Keyboard Shortcuts */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Keyboard Shortcuts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2">
              <div className="flex justify-between items-center py-2 border-b">
                <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 rounded">Ctrl + K</kbd>
                <span className="text-sm text-gray-600">Focus prompt input</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b">
                <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 rounded">Ctrl + /</kbd>
                <span className="text-sm text-gray-600">Toggle suggestions</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b">
                <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 rounded">Ctrl + B</kbd>
                <span className="text-sm text-gray-600">Toggle sidebar</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b">
                <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 rounded">Escape</kbd>
                <span className="text-sm text-gray-600">Close dialogs/menus</span>
              </div>
              <div className="flex justify-between items-center py-2">
                <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 rounded">?</kbd>
                <span className="text-sm text-gray-600">Show help dialog</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ResponsiveDemo;
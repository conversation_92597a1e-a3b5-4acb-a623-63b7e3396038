import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Card, CardContent } from '../../components/ui/card';
import { But<PERSON> } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../components/ui/select';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '../../components/ui/command';
import { 
  Send, 
  Lightbulb, 
  Sparkles, 
  Filter,
  Search,
  Brain,
  Code,
  FileText,
  Settings,
  Play,
  GitBranch,
  Rocket,
  BookOpen,
  FlaskConical,
  Bug,
  Eye,
  Layers,
  ChevronDown,
  ChevronUp,
  History,
  Wand2
} from 'lucide-react';
import { ContextSnapshot } from '../types';
import { Mode } from '../types/mode.types';
import { SmartSuggestion } from '../types/smart-prompt.types';

interface SmartPromptInputProps {
  context: ContextSnapshot;
  currentMode: Mode;
  onSubmit: (prompt: string, metadata?: any) => void;
  placeholder?: string;
  multiline?: boolean;
  showSuggestions?: boolean;
  showTemplates?: boolean;
  className?: string;
  disabled?: boolean;
  maxLength?: number;
}

export const SmartPromptInput: React.FC<SmartPromptInputProps> = ({
  context,
  currentMode,
  onSubmit,
  placeholder = "Ask me anything...",
  multiline = false,
  showSuggestions = true,
  className = "",
  disabled = false,
  maxLength = 2000
}) => {
  const [input, setInput] = useState('');
  const [showCommandDialog, setShowCommandDialog] = useState(false);
  const [recentPrompts, setRecentPrompts] = useState<string[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading] = useState(false);
  
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);

  // Load recent prompts from localStorage on mount
  useEffect(() => {
    const stored = localStorage.getItem('smartPromptInput.recentPrompts');
    if (stored) {
      try {
        setRecentPrompts(JSON.parse(stored));
      } catch (e) {
        console.warn('Failed to parse recent prompts:', e);
      }
    }
  }, []);

  // Generate contextual suggestions based on mode and context
  const contextualSuggestions = useMemo(() => {
    const suggestions: SmartSuggestion[] = [];

    // Mode-specific templates
    const modeTemplates = getModeTemplates(currentMode);
    suggestions.push(...modeTemplates);

    // Context-aware suggestions
    const contextSuggestions = getContextSuggestions(context, currentMode);
    suggestions.push(...contextSuggestions);

    // Smart completions based on current input
    if (input.length > 2) {
      const completions = getSmartCompletions(input, context, currentMode);
      suggestions.push(...completions);
    }

    // Recent prompts as suggestions
    const recentSuggestions = recentPrompts
      .filter(prompt => prompt.toLowerCase().includes(input.toLowerCase()))
      .slice(0, 3)
      .map((prompt, index) => ({
        id: `recent-${index}`,
        text: prompt,
        category: 'recent' as const,
        priority: 5 - index,
        tags: ['recent'],
        icon: <History className="h-4 w-4" />,
      }));
    suggestions.push(...recentSuggestions);

    return suggestions.sort((a, b) => b.priority - a.priority);
  }, [input, context, currentMode, recentPrompts]);

  // Filter suggestions based on selected category
  const filteredSuggestions = useMemo(() => {
    if (selectedCategory === 'all') return contextualSuggestions;
    return contextualSuggestions.filter(s => s.category === selectedCategory);
  }, [contextualSuggestions, selectedCategory]);

  const handleSubmit = (promptText: string = input) => {
    if (!promptText.trim() || disabled) return;

    // Add to recent prompts
    const updatedRecent = [promptText, ...recentPrompts.filter(p => p !== promptText)].slice(0, 10);
    setRecentPrompts(updatedRecent);
    localStorage.setItem('smartPromptInput.recentPrompts', JSON.stringify(updatedRecent));

    // Submit with metadata
    onSubmit(promptText, {
      mode: currentMode.id,
      context: {
        file: context.fileContext.path,
        project: context.projectContext.type,
        timestamp: Date.now()
      }
    });

    setInput('');
    setIsExpanded(false);
  };

  const handleSuggestionSelect = (suggestion: SmartSuggestion) => {
    let promptText = suggestion.text;
    
    // Replace variables if any
    if (suggestion.variables?.length) {
      suggestion.variables.forEach(variable => {
        const value = getVariableValue(variable, context);
        promptText = promptText.replace(`{${variable}}`, value);
      });
    }

    setInput(promptText);
    setShowCommandDialog(false);
    
    // Auto-submit for simple suggestions
    if (suggestion.category === 'template' || suggestion.category === 'smart') {
      setTimeout(() => handleSubmit(promptText), 100);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    } else if (e.key === 'Escape') {
      setIsExpanded(false);
      setShowCommandDialog(false);
    } else if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
      e.preventDefault();
      setShowCommandDialog(true);
    }
  };

  const getSuggestionIcon = (category: string) => {
    switch (category) {
      case 'template': return <FileText className="h-4 w-4" />;
      case 'context': return <Brain className="h-4 w-4" />;
      case 'recent': return <History className="h-4 w-4" />;
      case 'smart': return <Sparkles className="h-4 w-4" />;
      case 'completion': return <Wand2 className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  const categories = [
    { value: 'all', label: 'All', icon: <Filter className="h-4 w-4" /> },
    { value: 'template', label: 'Templates', icon: <FileText className="h-4 w-4" /> },
    { value: 'context', label: 'Context', icon: <Brain className="h-4 w-4" /> },
    { value: 'smart', label: 'Smart', icon: <Sparkles className="h-4 w-4" /> },
    { value: 'recent', label: 'Recent', icon: <History className="h-4 w-4" /> },
  ];

  return (
    <Card className={`smart-prompt-input bg-gray-900 border-gray-700 ${className}`}>
      <CardContent className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-blue-400" />
            <span className="font-medium text-white">Smart Prompt</span>
            <Badge variant="outline" className="text-xs">
              {currentMode.name}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            {showSuggestions && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCommandDialog(true)}
                className="h-6 text-xs text-gray-400 hover:text-white"
              >
                <Search className="h-3 w-3 mr-1" />
                Browse (⌘K)
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-6 w-6 p-0 text-gray-400 hover:text-white"
            >
              {isExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
            </Button>
          </div>
        </div>

        {/* Input Area */}
        <div className="relative">
          {multiline || isExpanded ? (
            <Textarea
              ref={inputRef as React.RefObject<HTMLTextAreaElement>}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              maxLength={maxLength}
              className="min-h-[100px] bg-gray-800 border-gray-600 resize-none"
              rows={isExpanded ? 6 : 4}
            />
          ) : (
            <Input
              ref={inputRef as React.RefObject<HTMLInputElement>}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              maxLength={maxLength}
              className="bg-gray-800 border-gray-600"
            />
          )}
          
          {/* Submit Button */}
          <Button
            onClick={() => handleSubmit()}
            disabled={!input.trim() || disabled || isLoading}
            size="sm"
            className="absolute right-2 top-2 h-8 w-8 p-0"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Character Count */}
        {(multiline || isExpanded) && (
          <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
            <span>{input.length}/{maxLength} characters</span>
            <span>Press Enter to submit, Shift+Enter for new line</span>
          </div>
        )}

        {/* Quick Suggestions */}
        {showSuggestions && filteredSuggestions.length > 0 && !showCommandDialog && (
          <div className="mt-3 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Suggestions</span>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-32 h-6 bg-gray-800 border-gray-600 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  {categories.map(cat => (
                    <SelectItem key={cat.value} value={cat.value} className="text-xs">
                      <div className="flex items-center gap-2">
                        {cat.icon}
                        {cat.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-40 overflow-y-auto">
              {filteredSuggestions.slice(0, 6).map(suggestion => (
                <Button
                  key={suggestion.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleSuggestionSelect(suggestion)}
                  className="justify-start text-left h-auto p-2 bg-gray-800 border-gray-600 hover:bg-gray-700"
                >
                  <div className="flex items-start gap-2 w-full">
                    <div className="text-blue-400 mt-0.5">
                      {suggestion.icon || getSuggestionIcon(suggestion.category)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-medium truncate">
                        {suggestion.text}
                      </div>
                      {suggestion.description && (
                        <div className="text-xs text-gray-400 mt-1 line-clamp-2">
                          {suggestion.description}
                        </div>
                      )}
                      <div className="flex items-center gap-1 mt-1">
                        <Badge variant="secondary" className="text-xs px-1 py-0">
                          {suggestion.category}
                        </Badge>
                        {suggestion.tags.map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs px-1 py-0">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Command Dialog */}
        <CommandDialog open={showCommandDialog} onOpenChange={setShowCommandDialog}>
          <CommandInput placeholder="Search suggestions..." />
          <CommandList>
            <CommandEmpty>No suggestions found.</CommandEmpty>
            
            {categories.map(category => {
              const categoryItems = contextualSuggestions.filter(s => 
                category.value === 'all' || s.category === category.value
              );
              
              if (categoryItems.length === 0) return null;
              
              return (
                <React.Fragment key={category.value}>
                  <CommandGroup heading={category.label}>
                    {categoryItems.slice(0, 8).map(suggestion => (
                      <CommandItem
                        key={suggestion.id}
                        onSelect={() => handleSuggestionSelect(suggestion)}
                      >
                        <div className="flex items-center gap-3 w-full">
                          <div className="text-blue-400">
                            {suggestion.icon || getSuggestionIcon(suggestion.category)}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium">{suggestion.text}</div>
                            {suggestion.description && (
                              <div className="text-sm text-gray-400">{suggestion.description}</div>
                            )}
                          </div>
                          <div className="flex items-center gap-1">
                            {suggestion.tags.map(tag => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                  <CommandSeparator />
                </React.Fragment>
              );
            })}
          </CommandList>
        </CommandDialog>
      </CardContent>
    </Card>
  );
};

// Helper function to get mode-specific templates
function getModeTemplates(mode: Mode): SmartSuggestion[] {
  const templates: Record<string, SmartSuggestion[]> = {
    architect: [
      {
        id: 'arch-review',
        text: 'Review the architecture of {currentFile} and suggest improvements',
        category: 'template',
        priority: 10,
        tags: ['architecture', 'review'],
        description: 'Analyze current file architecture',
        icon: <Layers className="h-4 w-4" />,
        variables: ['currentFile']
      },
      {
        id: 'arch-diagram',
        text: 'Generate a system diagram for this project',
        category: 'template',
        priority: 9,
        tags: ['diagram', 'system'],
        description: 'Create visual system overview',
        icon: <GitBranch className="h-4 w-4" />
      },
    ],
    debug: [
      {
        id: 'debug-analyze',
        text: 'Analyze the error in {currentFile} and suggest fixes',
        category: 'template',
        priority: 10,
        tags: ['debug', 'error'],
        description: 'Debug current file issues',
        icon: <Bug className="h-4 w-4" />,
        variables: ['currentFile']
      },
      {
        id: 'debug-logs',
        text: 'Help me add better logging to this function',
        category: 'template',
        priority: 8,
        tags: ['logging', 'debug'],
        description: 'Improve logging statements',
        icon: <FileText className="h-4 w-4" />
      },
    ],
    review: [
      {
        id: 'review-code',
        text: 'Review this code for best practices and potential issues',
        category: 'template',
        priority: 10,
        tags: ['review', 'quality'],
        description: 'Comprehensive code review',
        icon: <Eye className="h-4 w-4" />
      },
      {
        id: 'review-security',
        text: 'Check {currentFile} for security vulnerabilities',
        category: 'template',
        priority: 9,
        tags: ['security', 'review'],
        description: 'Security-focused review',
        icon: <Settings className="h-4 w-4" />,
        variables: ['currentFile']
      },
    ],
    deploy: [
      {
        id: 'deploy-setup',
        text: 'Help me set up CI/CD pipeline for this project',
        category: 'template',
        priority: 10,
        tags: ['cicd', 'deployment'],
        description: 'Configure deployment pipeline',
        icon: <Rocket className="h-4 w-4" />
      },
      {
        id: 'deploy-docker',
        text: 'Create a Dockerfile for this {projectType} project',
        category: 'template',
        priority: 9,
        tags: ['docker', 'containerization'],
        description: 'Generate Docker configuration',
        icon: <Code className="h-4 w-4" />,
        variables: ['projectType']
      },
    ],
    experiment: [
      {
        id: 'exp-prototype',
        text: 'Help me quickly prototype {feature} functionality',
        category: 'template',
        priority: 10,
        tags: ['prototype', 'experiment'],
        description: 'Rapid prototyping assistance',
        icon: <FlaskConical className="h-4 w-4" />,
        variables: ['feature']
      },
      {
        id: 'exp-test',
        text: 'Create a minimal test for this concept',
        category: 'template',
        priority: 8,
        tags: ['test', 'experiment'],
        description: 'Quick concept validation',
        icon: <Play className="h-4 w-4" />
      },
    ],
    learn: [
      {
        id: 'learn-explain',
        text: 'Explain how {currentCode} works in simple terms',
        category: 'template',
        priority: 10,
        tags: ['explain', 'learning'],
        description: 'Code explanation for learning',
        icon: <BookOpen className="h-4 w-4" />,
        variables: ['currentCode']
      },
      {
        id: 'learn-tutorial',
        text: 'Create a step-by-step tutorial for {technology}',
        category: 'template',
        priority: 9,
        tags: ['tutorial', 'learning'],
        description: 'Generate learning material',
        icon: <Lightbulb className="h-4 w-4" />,
        variables: ['technology']
      },
    ],
  };

  return templates[mode.id] || [];
}

// Helper function to get context-aware suggestions
function getContextSuggestions(context: ContextSnapshot, mode: Mode): SmartSuggestion[] {
  const suggestions: SmartSuggestion[] = [];

  // File-specific suggestions
  if (context.fileContext.path) {
    const fileName = context.fileContext.path.split('/').pop();
    const fileExt = fileName?.split('.').pop()?.toLowerCase();
    
    if (fileExt === 'ts' || fileExt === 'tsx') {
      suggestions.push({
        id: 'ctx-typescript',
        text: `Review TypeScript types in ${context.fileContext.path.split('/').pop() || 'this file'}`,
        category: 'context',
        priority: 8,
        tags: ['typescript', 'types'],
        description: 'TypeScript-specific analysis',
        icon: <Code className="h-4 w-4" />
      });
    }

    if (fileExt === 'test.ts' || fileExt === 'spec.ts') {
      suggestions.push({
        id: 'ctx-test',
        text: `Improve test coverage in ${context.fileContext.path.split('/').pop() || 'this file'}`,
        category: 'context',
        priority: 7,
        tags: ['testing', 'coverage'],
        description: 'Test improvement suggestions',
        icon: <Play className="h-4 w-4" />
      });
    }
  }

  // Project-specific suggestions
  if (context.projectContext.type) {
    suggestions.push({
      id: 'ctx-project',
      text: `Optimize this ${context.projectContext.type} project structure`,
      category: 'context',
      priority: 6,
      tags: ['project', 'optimization'],
      description: 'Project-specific improvements',
      icon: <Settings className="h-4 w-4" />
    });
  }

  // Git-specific suggestions
  if (context.environmentContext.gitStatus?.branch) {
    const branch = context.environmentContext.gitStatus.branch;
    if (branch !== 'main' && branch !== 'master') {
      suggestions.push({
        id: 'ctx-branch',
        text: `Review changes in ${branch} branch before merging`,
        category: 'context',
        priority: 7,
        tags: ['git', 'review'],
        description: 'Branch-specific review',
        icon: <GitBranch className="h-4 w-4" />
      });
    }
  }

  return suggestions;
}

// Helper function to get smart completions
function getSmartCompletions(input: string, context: ContextSnapshot, mode: Mode): SmartSuggestion[] {
  const suggestions: SmartSuggestion[] = [];
  const lowerInput = input.toLowerCase();

  // Common completion patterns
  const patterns = [
    {
      trigger: ['how to', 'how do i'],
      completion: ' implement this feature efficiently?',
      tags: ['implementation', 'guide']
    },
    {
      trigger: ['why is', 'why does'],
      completion: ' this happening and how can I fix it?',
      tags: ['debugging', 'analysis']
    },
    {
      trigger: ['optimize', 'improve'],
      completion: ' the performance of this code',
      tags: ['optimization', 'performance']
    },
    {
      trigger: ['refactor'],
      completion: ' this code to be more maintainable',
      tags: ['refactoring', 'maintainability']
    },
    {
      trigger: ['test'],
      completion: ' this functionality thoroughly',
      tags: ['testing', 'quality']
    },
  ];

  patterns.forEach((pattern, index) => {
    if (pattern.trigger.some(trigger => lowerInput.includes(trigger))) {
      suggestions.push({
        id: `completion-${index}`,
        text: input + pattern.completion,
        category: 'completion',
        priority: 5,
        tags: pattern.tags,
        description: 'Smart completion suggestion',
        icon: <Wand2 className="h-4 w-4" />
      });
    }
  });

  return suggestions;
}

// Helper function to get variable values from context
function getVariableValue(variable: string, context: ContextSnapshot): string {
  switch (variable) {
    case 'currentFile':
      return context.fileContext.path.split('/').pop() || 'this file';
    case 'projectType':
      return context.projectContext.type || 'application';
    case 'technology':
      return context.fileContext.language || 'this technology';
    case 'currentCode':
      return 'this code';
    case 'feature':
      return 'this feature';
    default:
      return `{${variable}}`;
  }
}

export default SmartPromptInput;
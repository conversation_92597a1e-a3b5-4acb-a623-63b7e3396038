import React, { useMemo, useState, useRef, useEffect } from 'react';
import { Mode, ContextSnapshot } from '../../types';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '../../../components/ui/tabs';
import { 
  Maximize2, 
  Minimize2, 
  Settings, 
  ChevronLeft, 
  ChevronRight,
  LayoutGrid,
  Sidebar,
  Menu,
  X,
  Grip
} from 'lucide-react';
import { useBreakpoint, useIsMobile, responsiveClasses } from '../../utils/responsive';
import { 
  useFocusTrap, 
  useAccessibleShortcuts, 
  announcer,
  a11yClasses,
  ariaLabels,
  semanticRoles
} from '../../utils/accessibility';

interface ModeLayoutConfig {
  sidebar?: {
    enabled: boolean;
    width: number;
    collapsible: boolean;
    position: 'left' | 'right';
  };
  panels: Array<{
    id: string;
    component: React.ComponentType<any>;
    position: 'top' | 'bottom' | 'left' | 'right' | 'center';
    size: {
      width?: string;
      height?: string;
      minWidth?: string;
      minHeight?: string;
    };
    resizable: boolean;
    collapsible: boolean;
    priority: number;
    mobileHidden?: boolean;
    accessibilityLabel: string;
  }>;
  header?: {
    enabled: boolean;
    height: number;
  };
  footer?: {
    enabled: boolean;
    height: number;
  };
}

interface ModeLayoutProps {
  mode: Mode;
  context: ContextSnapshot;
  children: React.ReactNode;
  className?: string;
}

// Generate responsive layout configuration based on mode and breakpoint
const generateLayoutConfig = (
  mode: Mode, 
  context: ContextSnapshot,
  breakpoint: string
): ModeLayoutConfig => {
  const isMobile = breakpoint === 'xs' || breakpoint === 'sm';
  const isTablet = breakpoint === 'md';
  
  const baseConfig: ModeLayoutConfig = {
    header: { enabled: true, height: isMobile ? 56 : 60 },
    footer: { enabled: false, height: 40 },
    panels: []
  };

  switch (mode.id) {
    case 'architect':
      return {
        ...baseConfig,
        sidebar: {
          enabled: !isMobile,
          width: isTablet ? 250 : 300,
          collapsible: true,
          position: 'left'
        },
        panels: [
          {
            id: 'diagram-viewer',
            component: DiagramViewerPanel,
            position: 'center',
            size: { 
              width: isMobile ? '100%' : isTablet ? '100%' : '60%', 
              height: isMobile ? '300px' : '400px' 
            },
            resizable: !isMobile,
            collapsible: false,
            priority: 1,
            mobileHidden: false,
            accessibilityLabel: 'Architecture diagram viewer'
          },
          {
            id: 'requirements',
            component: RequirementsPanel,
            position: isMobile ? 'bottom' : 'right',
            size: { 
              width: isMobile ? '100%' : '40%', 
              height: isMobile ? '200px' : 'auto',
              minWidth: '250px' 
            },
            resizable: !isMobile,
            collapsible: true,
            priority: 2,
            mobileHidden: false,
            accessibilityLabel: 'Requirements panel'
          },
          {
            id: 'decisions',
            component: DecisionLogPanel,
            position: 'bottom',
            size: { 
              height: isMobile ? '150px' : '200px', 
              minHeight: '150px' 
            },
            resizable: !isMobile,
            collapsible: true,
            priority: 3,
            mobileHidden: isTablet,
            accessibilityLabel: 'Decision log panel'
          }
        ]
      };

    case 'debug':
      return {
        ...baseConfig,
        sidebar: {
          enabled: !isMobile,
          width: isTablet ? 200 : 250,
          collapsible: true,
          position: 'left'
        },
        panels: [
          {
            id: 'log-viewer',
            component: LogViewerPanel,
            position: 'center',
            size: { 
              width: isMobile ? '100%' : '70%', 
              height: isMobile ? '250px' : '300px' 
            },
            resizable: !isMobile,
            collapsible: false,
            priority: 1,
            mobileHidden: false,
            accessibilityLabel: 'Debug log viewer'
          },
          {
            id: 'stack-trace',
            component: StackTracePanel,
            position: isMobile ? 'bottom' : 'right',
            size: { 
              width: isMobile ? '100%' : '30%', 
              height: isMobile ? '200px' : 'auto',
              minWidth: '200px' 
            },
            resizable: !isMobile,
            collapsible: true,
            priority: 2,
            mobileHidden: false,
            accessibilityLabel: 'Stack trace panel'
          },
          {
            id: 'variables',
            component: VariableInspectorPanel,
            position: 'bottom',
            size: { 
              height: isMobile ? '200px' : '250px', 
              minHeight: '150px' 
            },
            resizable: !isMobile,
            collapsible: true,
            priority: 3,
            mobileHidden: false,
            accessibilityLabel: 'Variable inspector'
          }
        ]
      };

    case 'review':
      return {
        ...baseConfig,
        sidebar: {
          enabled: !isMobile,
          width: isTablet ? 240 : 280,
          collapsible: true,
          position: 'right'
        },
        panels: [
          {
            id: 'diff-viewer',
            component: DiffViewerPanel,
            position: 'center',
            size: { 
              width: '100%', 
              height: isMobile ? '350px' : '400px' 
            },
            resizable: !isMobile,
            collapsible: false,
            priority: 1,
            mobileHidden: false,
            accessibilityLabel: 'Code diff viewer'
          },
          {
            id: 'checklist',
            component: ReviewChecklistPanel,
            position: 'bottom',
            size: { 
              height: isMobile ? '150px' : '200px', 
              minHeight: '150px' 
            },
            resizable: !isMobile,
            collapsible: true,
            priority: 2,
            mobileHidden: false,
            accessibilityLabel: 'Review checklist'
          }
        ]
      };

    default:
      return {
        ...baseConfig,
        panels: [
          {
            id: 'default-content',
            component: DefaultContentPanel,
            position: 'center',
            size: { 
              width: '100%', 
              height: isMobile ? '300px' : '400px' 
            },
            resizable: false,
            collapsible: false,
            priority: 1,
            mobileHidden: false,
            accessibilityLabel: 'Main content area'
          }
        ]
      };
  }
};

export const ModeLayoutResponsive: React.FC<ModeLayoutProps> = ({ 
  mode, 
  context, 
  children, 
  className = '' 
}) => {
  const breakpoint = useBreakpoint();
  const isMobile = useIsMobile();
  
  const layoutConfig = useMemo(() => 
    generateLayoutConfig(mode, context, breakpoint), 
    [mode, context, breakpoint]
  );
  
  const [sidebarCollapsed, setSidebarCollapsed] = useState(isMobile);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [collapsedPanels, setCollapsedPanels] = useState<Set<string>>(new Set());
  const [panelSizes, setPanelSizes] = useState<Record<string, { width?: string; height?: string }>>({});
  
  // Focus trap for mobile menu
  const mobileMenuRef = useFocusTrap(mobileMenuOpen);
  
  // Keyboard shortcuts
  const shortcuts = {
    'Escape': () => {
      if (mobileMenuOpen) setMobileMenuOpen(false);
    },
    'Ctrl+B': () => setSidebarCollapsed(!sidebarCollapsed),
    'Ctrl+M': () => setMobileMenuOpen(!mobileMenuOpen),
  };
  
  const { showHelp } = useAccessibleShortcuts(shortcuts, {
    enableHelp: true,
    announceActions: true
  });

  // Close mobile menu on breakpoint change
  useEffect(() => {
    if (!isMobile && mobileMenuOpen) {
      setMobileMenuOpen(false);
    }
  }, [isMobile, mobileMenuOpen]);

  const togglePanel = (panelId: string) => {
    setCollapsedPanels(prev => {
      const newSet = new Set(prev);
      if (newSet.has(panelId)) {
        newSet.delete(panelId);
        announcer.announce(`${panelId} panel expanded`);
      } else {
        newSet.add(panelId);
        announcer.announce(`${panelId} panel collapsed`);
      }
      return newSet;
    });
  };

  const updatePanelSize = (panelId: string, size: { width?: string; height?: string }) => {
    setPanelSizes(prev => ({
      ...prev,
      [panelId]: { ...prev[panelId], ...size }
    }));
  };

  // Filter panels based on mobile visibility
  const visiblePanels = layoutConfig.panels.filter(
    panel => !isMobile || !panel.mobileHidden
  );

  return (
    <div 
      className={`mode-layout h-full flex flex-col ${className} ${a11yClasses.focusWithin}`}
      data-mode={mode.id}
      style={{
        '--mode-primary': mode.colors?.primary || '#3b82f6',
        '--mode-secondary': mode.colors?.secondary || '#64748b',
        '--mode-accent': mode.colors?.accent || '#06b6d4'
      } as React.CSSProperties}
      role={semanticRoles.main}
      aria-label={`${mode.name} mode interface`}
    >
      {/* Skip to main content link */}
      <a 
        href="#main-content" 
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded"
      >
        Skip to main content
      </a>

      {/* Mode Header */}
      {layoutConfig.header?.enabled && (
        <ModeHeader 
          mode={mode} 
          context={context}
          height={layoutConfig.header.height}
          onMenuToggle={() => setMobileMenuOpen(!mobileMenuOpen)}
          isMobile={isMobile}
        />
      )}

      {/* Main Content Area */}
      <div className="mode-content flex-1 flex overflow-hidden relative">
        {/* Mobile Menu Overlay */}
        {isMobile && mobileMenuOpen && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
            onClick={() => setMobileMenuOpen(false)}
            aria-hidden="true"
          />
        )}

        {/* Sidebar */}
        {(layoutConfig.sidebar?.enabled || isMobile) && (
          <ModeSidebar
            ref={isMobile ? mobileMenuRef : undefined}
            mode={mode}
            context={context}
            config={layoutConfig.sidebar || { 
              enabled: true, 
              width: 280, 
              collapsible: true, 
              position: 'left' 
            }}
            collapsed={sidebarCollapsed}
            mobileOpen={mobileMenuOpen}
            isMobile={isMobile}
            onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
            onClose={() => setMobileMenuOpen(false)}
          />
        )}

        {/* Panel Container */}
        <div className="panel-container flex-1 flex flex-col overflow-hidden">
          {/* Mobile Tab Navigation for Panels */}
          {isMobile && visiblePanels.length > 1 && (
            <MobilePanelTabs
              panels={visiblePanels}
              mode={mode}
              context={context}
              collapsedPanels={collapsedPanels}
              onToggleCollapse={togglePanel}
              panelSizes={panelSizes}
              onResize={updatePanelSize}
            />
          )}

          {/* Desktop Panel Layout */}
          {!isMobile && (
            <>
              {/* Top Panels */}
              <div className="top-panels flex flex-wrap">
                {visiblePanels
                  .filter(panel => panel.position === 'top')
                  .sort((a, b) => a.priority - b.priority)
                  .map(panel => (
                    <ModePanel
                      key={panel.id}
                      panel={panel}
                      mode={mode}
                      context={context}
                      collapsed={collapsedPanels.has(panel.id)}
                      onToggleCollapse={() => togglePanel(panel.id)}
                      onResize={(size) => updatePanelSize(panel.id, size)}
                      customSize={panelSizes[panel.id]}
                      isMobile={isMobile}
                    />
                  ))}
              </div>

              {/* Middle Section - Center + Left/Right Panels */}
              <div className="middle-section flex-1 flex flex-col md:flex-row overflow-hidden">
                {/* Left Panels */}
                <div className="left-panels flex flex-col">
                  {visiblePanels
                    .filter(panel => panel.position === 'left')
                    .sort((a, b) => a.priority - b.priority)
                    .map(panel => (
                      <ModePanel
                        key={panel.id}
                        panel={panel}
                        mode={mode}
                        context={context}
                        collapsed={collapsedPanels.has(panel.id)}
                        onToggleCollapse={() => togglePanel(panel.id)}
                        onResize={(size) => updatePanelSize(panel.id, size)}
                        customSize={panelSizes[panel.id]}
                        isMobile={isMobile}
                      />
                    ))}
                </div>

                {/* Center Content */}
                <div className="center-content flex-1 flex flex-col overflow-hidden">
                  {/* Center Panels */}
                  {visiblePanels
                    .filter(panel => panel.position === 'center')
                    .sort((a, b) => a.priority - b.priority)
                    .map(panel => (
                      <ModePanel
                        key={panel.id}
                        panel={panel}
                        mode={mode}
                        context={context}
                        collapsed={collapsedPanels.has(panel.id)}
                        onToggleCollapse={() => togglePanel(panel.id)}
                        onResize={(size) => updatePanelSize(panel.id, size)}
                        customSize={panelSizes[panel.id]}
                        isMobile={isMobile}
                      />
                    ))}
                  
                  {/* Main Content */}
                  <main 
                    id="main-content"
                    className={`main-content flex-1 ${responsiveClasses.spacing.component} overflow-auto`}
                    role={semanticRoles.main}
                    aria-label="Main content area"
                  >
                    {children}
                  </main>
                </div>

                {/* Right Panels */}
                <div className="right-panels flex flex-col">
                  {visiblePanels
                    .filter(panel => panel.position === 'right')
                    .sort((a, b) => a.priority - b.priority)
                    .map(panel => (
                      <ModePanel
                        key={panel.id}
                        panel={panel}
                        mode={mode}
                        context={context}
                        collapsed={collapsedPanels.has(panel.id)}
                        onToggleCollapse={() => togglePanel(panel.id)}
                        onResize={(size) => updatePanelSize(panel.id, size)}
                        customSize={panelSizes[panel.id]}
                        isMobile={isMobile}
                      />
                    ))}
                </div>
              </div>

              {/* Bottom Panels */}
              <div className="bottom-panels flex flex-col">
                {visiblePanels
                  .filter(panel => panel.position === 'bottom')
                  .sort((a, b) => a.priority - b.priority)
                  .map(panel => (
                    <ModePanel
                      key={panel.id}
                      panel={panel}
                      mode={mode}
                      context={context}
                      collapsed={collapsedPanels.has(panel.id)}
                      onToggleCollapse={() => togglePanel(panel.id)}
                      onResize={(size) => updatePanelSize(panel.id, size)}
                      customSize={panelSizes[panel.id]}
                      isMobile={isMobile}
                    />
                  ))}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Mode Footer */}
      {layoutConfig.footer?.enabled && (
        <ModeFooter 
          mode={mode} 
          context={context}
          height={layoutConfig.footer.height}
        />
      )}

      {/* Keyboard Help Dialog */}
      {showHelp && (
        <KeyboardHelpDialog 
          shortcuts={shortcuts}
          onClose={() => {}} 
        />
      )}
    </div>
  );
};

// Header Component with mobile menu toggle
const ModeHeader: React.FC<{
  mode: Mode;
  context: ContextSnapshot;
  height: number;
  onMenuToggle: () => void;
  isMobile: boolean;
}> = ({ mode, context, height, onMenuToggle, isMobile }) => {
  return (
    <header 
      className={`mode-header bg-gray-900 border-b border-gray-700 flex items-center justify-between ${responsiveClasses.spacing.tight}`}
      style={{ height: `${height}px` }}
      role={semanticRoles.banner}
    >
      <div className="flex items-center gap-3">
        {isMobile && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onMenuToggle}
            className="md:hidden"
            aria-label={ariaLabels.menu}
          >
            <Menu className="h-5 w-5" />
          </Button>
        )}
        <span className={`${responsiveClasses.text.h3}`}>{mode.icon}</span>
        <div className="min-w-0">
          <h1 className={`${responsiveClasses.text.h4} text-white truncate`}>
            {mode.name} Mode
          </h1>
          <p className={`${responsiveClasses.text.small} text-gray-400 ${responsiveClasses.visibility.hideMobile}`}>
            {mode.description}
          </p>
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        <Badge variant="secondary" className="text-xs">
          <span className="sr-only">Mode status:</span>
          Active
        </Badge>
        <Button 
          size="sm" 
          variant="ghost"
          aria-label="Mode settings"
          className={a11yClasses.focusVisible}
        >
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    </header>
  );
};

// Responsive Sidebar Component
const ModeSidebar = React.forwardRef<
  HTMLElement,
  {
    mode: Mode;
    context: ContextSnapshot;
    config: NonNullable<ModeLayoutConfig['sidebar']>;
    collapsed: boolean;
    mobileOpen: boolean;
    isMobile: boolean;
    onToggleCollapse: () => void;
    onClose: () => void;
  }
>(({ mode, context, config, collapsed, mobileOpen, isMobile, onToggleCollapse, onClose }, ref) => {
  const sidebarClasses = isMobile
    ? `fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ${
        mobileOpen ? 'translate-x-0' : '-translate-x-full'
      }`
    : `relative transition-all duration-300 ${collapsed ? 'w-12' : ''}`;

  return (
    <aside 
      ref={ref}
      className={`mode-sidebar bg-gray-900 border-r border-gray-700 ${sidebarClasses}`}
      style={{ 
        width: isMobile ? '280px' : (collapsed ? '48px' : `${config.width}px`),
        order: !isMobile && config.position === 'left' ? -1 : 1
      }}
      role={semanticRoles.complementary}
      aria-label="Mode tools sidebar"
      aria-hidden={isMobile && !mobileOpen}
    >
      <div className="sidebar-header h-12 flex items-center justify-between px-3 border-b border-gray-700">
        {!collapsed && (
          <span className="text-sm font-medium text-gray-300">Tools</span>
        )}
        <div className="flex items-center gap-2">
          {isMobile && (
            <Button
              size="sm"
              variant="ghost"
              onClick={onClose}
              className="h-8 w-8 p-0"
              aria-label={ariaLabels.close}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
          {config.collapsible && !isMobile && (
            <Button
              size="sm"
              variant="ghost"
              onClick={onToggleCollapse}
              className="h-8 w-8 p-0"
              aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
              aria-pressed={collapsed}
            >
              {collapsed ? (
                config.position === 'left' ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />
              ) : (
                config.position === 'left' ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
      </div>
      
      <nav 
        className="sidebar-content p-2"
        role={semanticRoles.navigation}
        aria-label="Mode tools navigation"
      >
        {!collapsed && (
          <div className="space-y-2">
            <Button
              size="sm"
              variant="ghost"
              className={`w-full justify-start text-left ${a11yClasses.focusVisible}`}
            >
              <LayoutGrid className="h-4 w-4 mr-2" />
              Overview
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`w-full justify-start text-left ${a11yClasses.focusVisible}`}
            >
              <Sidebar className="h-4 w-4 mr-2" />
              Details
            </Button>
          </div>
        )}
      </nav>
    </aside>
  );
});

ModeSidebar.displayName = 'ModeSidebar';

// Accessible Panel Component
const ModePanel: React.FC<{
  panel: ModeLayoutConfig['panels'][0];
  mode: Mode;
  context: ContextSnapshot;
  collapsed: boolean;
  onToggleCollapse: () => void;
  onResize: (size: { width?: string; height?: string }) => void;
  customSize?: { width?: string; height?: string };
  isMobile: boolean;
}> = ({ panel, mode, context, collapsed, onToggleCollapse, onResize, customSize, isMobile }) => {
  const PanelComponent = panel.component;
  const [isDragging, setIsDragging] = useState(false);
  
  const panelStyle: React.CSSProperties = {
    width: customSize?.width || panel.size.width,
    height: customSize?.height || panel.size.height,
    minWidth: panel.size.minWidth,
    minHeight: panel.size.minHeight,
  };

  if (collapsed) {
    return (
      <div 
        className={`panel-collapsed bg-gray-800 border border-gray-700 m-1 rounded ${a11yClasses.focusWithin}`}
        style={{ 
          width: panel.position === 'left' || panel.position === 'right' ? '40px' : panelStyle.width,
          height: panel.position === 'top' || panel.position === 'bottom' ? '40px' : panelStyle.height 
        }}
        role="region"
        aria-label={`${panel.accessibilityLabel} (collapsed)`}
      >
        <Button
          size="sm"
          variant="ghost"
          onClick={onToggleCollapse}
          className="h-full w-full"
          aria-label={`Expand ${panel.id} panel`}
        >
          <Maximize2 className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <section 
      className={`mode-panel bg-gray-800 border border-gray-700 m-1 rounded overflow-hidden ${
        a11yClasses.focusWithin
      } ${isDragging ? 'select-none' : ''}`}
      style={panelStyle}
      role="region"
      aria-label={panel.accessibilityLabel}
    >
      <div className="panel-header bg-gray-900 px-3 py-2 flex items-center justify-between border-b border-gray-700">
        <h2 className="text-sm font-medium text-gray-300">{panel.id}</h2>
        <div className="flex items-center gap-1">
          {panel.resizable && !isMobile && (
            <Button 
              size="sm" 
              variant="ghost" 
              className="h-6 w-6 p-0 cursor-move"
              onMouseDown={() => setIsDragging(true)}
              onMouseUp={() => setIsDragging(false)}
              aria-label="Resize panel"
            >
              <Grip className="h-3 w-3" />
            </Button>
          )}
          {panel.collapsible && (
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={onToggleCollapse}
              className="h-6 w-6 p-0"
              aria-label={`Collapse ${panel.id} panel`}
            >
              <Minimize2 className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
      
      <div className="panel-content flex-1 overflow-auto">
        <PanelComponent mode={mode} context={context} />
      </div>
    </section>
  );
};

// Mobile Panel Tabs
const MobilePanelTabs: React.FC<{
  panels: ModeLayoutConfig['panels'];
  mode: Mode;
  context: ContextSnapshot;
  collapsedPanels: Set<string>;
  onToggleCollapse: (id: string) => void;
  panelSizes: Record<string, { width?: string; height?: string }>;
  onResize: (id: string, size: { width?: string; height?: string }) => void;
}> = ({ panels, mode, context, collapsedPanels, onToggleCollapse, panelSizes, onResize }) => {
  const [activeTab, setActiveTab] = useState(panels[0]?.id || '');

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="w-full justify-start overflow-x-auto">
        {panels.map(panel => (
          <TabsTrigger 
            key={panel.id} 
            value={panel.id}
            className="flex-shrink-0"
            aria-label={`${panel.accessibilityLabel} tab`}
          >
            {panel.id}
          </TabsTrigger>
        ))}
      </TabsList>
      {panels.map(panel => (
        <TabsContent key={panel.id} value={panel.id} className="mt-0">
          <ModePanel
            panel={panel}
            mode={mode}
            context={context}
            collapsed={collapsedPanels.has(panel.id)}
            onToggleCollapse={() => onToggleCollapse(panel.id)}
            onResize={(size) => onResize(panel.id, size)}
            customSize={panelSizes[panel.id]}
            isMobile={true}
          />
        </TabsContent>
      ))}
    </Tabs>
  );
};

// Footer Component
const ModeFooter: React.FC<{
  mode: Mode;
  context: ContextSnapshot;
  height: number;
}> = ({ mode, context, height }) => {
  return (
    <footer 
      className={`mode-footer bg-gray-900 border-t border-gray-700 flex items-center justify-between ${responsiveClasses.spacing.tight}`}
      style={{ height: `${height}px` }}
      role={semanticRoles.contentinfo}
    >
      <div className={`${responsiveClasses.text.small} text-gray-500`}>
        <span className="sr-only">Current mode:</span>
        {mode.name} mode active
      </div>
      <div className={`${responsiveClasses.text.small} text-gray-500 ${responsiveClasses.visibility.hideMobile}`}>
        <span className="sr-only">Last update:</span>
        Context updated: {new Date(context.timestamp).toLocaleTimeString()}
      </div>
    </footer>
  );
};

// Keyboard Help Dialog
const KeyboardHelpDialog: React.FC<{
  shortcuts: Record<string, () => void>;
  onClose: () => void;
}> = ({ shortcuts, onClose }) => {
  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
      role="dialog"
      aria-label="Keyboard shortcuts help"
    >
      <Card className="max-w-md w-full">
        <CardHeader>
          <CardTitle>Keyboard Shortcuts</CardTitle>
        </CardHeader>
        <CardContent>
          <dl className="space-y-2">
            {Object.keys(shortcuts).map(key => (
              <div key={key} className="flex justify-between">
                <dt className="font-mono text-sm">{key}</dt>
                <dd className="text-sm text-gray-500">Action</dd>
              </div>
            ))}
          </dl>
        </CardContent>
      </Card>
    </div>
  );
};

// Placeholder Panel Components
const DiagramViewerPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Diagram Viewer</div>
);

const RequirementsPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Requirements Panel</div>
);

const DecisionLogPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Decision Log Panel</div>
);

const LogViewerPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Log Viewer Panel</div>
);

const StackTracePanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Stack Trace Panel</div>
);

const VariableInspectorPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Variable Inspector Panel</div>
);

const DiffViewerPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Diff Viewer Panel</div>
);

const ReviewChecklistPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Review Checklist Panel</div>
);

const DefaultContentPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Default Content Panel</div>
);

export default ModeLayoutResponsive;
import React, { use<PERSON>emo, useState } from 'react';
import { Mode, ContextSnapshot } from '../../types';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../../../components/ui/tabs';
import { 
  Maximize2, 
  Minimize2, 
  Settings, 
  ChevronLeft, 
  ChevronRight,
  LayoutGrid,
  Sidebar
} from 'lucide-react';

interface ModeLayoutConfig {
  sidebar?: {
    enabled: boolean;
    width: number;
    collapsible: boolean;
    position: 'left' | 'right';
  };
  panels: Array<{
    id: string;
    component: React.ComponentType<any>;
    position: 'top' | 'bottom' | 'left' | 'right' | 'center';
    size: {
      width?: string;
      height?: string;
      minWidth?: string;
      minHeight?: string;
    };
    resizable: boolean;
    collapsible: boolean;
    priority: number;
  }>;
  header?: {
    enabled: boolean;
    height: number;
  };
  footer?: {
    enabled: boolean;
    height: number;
  };
}

interface ModeLayoutProps {
  mode: Mode;
  context: ContextSnapshot;
  children: React.ReactNode;
  className?: string;
}

// Generate layout configuration based on mode
const generateLayoutConfig = (mode: Mode, context: ContextSnapshot): ModeLayoutConfig => {
  const baseConfig: ModeLayoutConfig = {
    header: { enabled: true, height: 60 },
    footer: { enabled: false, height: 40 },
    panels: []
  };

  switch (mode.id) {
    case 'architect':
      return {
        ...baseConfig,
        sidebar: {
          enabled: true,
          width: 300,
          collapsible: true,
          position: 'left'
        },
        panels: [
          {
            id: 'diagram-viewer',
            component: DiagramViewerPanel,
            position: 'center',
            size: { width: '60%', height: '400px' },
            resizable: true,
            collapsible: false,
            priority: 1
          },
          {
            id: 'requirements',
            component: RequirementsPanel,
            position: 'right',
            size: { width: '40%', minWidth: '250px' },
            resizable: true,
            collapsible: true,
            priority: 2
          },
          {
            id: 'decisions',
            component: DecisionLogPanel,
            position: 'bottom',
            size: { height: '200px', minHeight: '150px' },
            resizable: true,
            collapsible: true,
            priority: 3
          }
        ]
      };

    case 'debug':
      return {
        ...baseConfig,
        sidebar: {
          enabled: true,
          width: 250,
          collapsible: true,
          position: 'left'
        },
        panels: [
          {
            id: 'log-viewer',
            component: LogViewerPanel,
            position: 'center',
            size: { width: '70%', height: '300px' },
            resizable: true,
            collapsible: false,
            priority: 1
          },
          {
            id: 'stack-trace',
            component: StackTracePanel,
            position: 'right',
            size: { width: '30%', minWidth: '200px' },
            resizable: true,
            collapsible: true,
            priority: 2
          },
          {
            id: 'variables',
            component: VariableInspectorPanel,
            position: 'bottom',
            size: { height: '250px', minHeight: '150px' },
            resizable: true,
            collapsible: true,
            priority: 3
          }
        ]
      };

    case 'review':
      return {
        ...baseConfig,
        sidebar: {
          enabled: true,
          width: 280,
          collapsible: true,
          position: 'right'
        },
        panels: [
          {
            id: 'diff-viewer',
            component: DiffViewerPanel,
            position: 'center',
            size: { width: '100%', height: '400px' },
            resizable: true,
            collapsible: false,
            priority: 1
          },
          {
            id: 'checklist',
            component: ReviewChecklistPanel,
            position: 'bottom',
            size: { height: '200px', minHeight: '150px' },
            resizable: true,
            collapsible: true,
            priority: 2
          }
        ]
      };

    default:
      return {
        ...baseConfig,
        panels: [
          {
            id: 'default-content',
            component: DefaultContentPanel,
            position: 'center',
            size: { width: '100%', height: '400px' },
            resizable: false,
            collapsible: false,
            priority: 1
          }
        ]
      };
  }
};

export const ModeLayout: React.FC<ModeLayoutProps> = ({ 
  mode, 
  context, 
  children, 
  className = '' 
}) => {
  const layoutConfig = useMemo(() => 
    generateLayoutConfig(mode, context), 
    [mode, context]
  );
  
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [collapsedPanels, setCollapsedPanels] = useState<Set<string>>(new Set());
  const [panelSizes, setPanelSizes] = useState<Record<string, { width?: string; height?: string }>>({});

  const togglePanel = (panelId: string) => {
    setCollapsedPanels(prev => {
      const newSet = new Set(prev);
      if (newSet.has(panelId)) {
        newSet.delete(panelId);
      } else {
        newSet.add(panelId);
      }
      return newSet;
    });
  };

  const updatePanelSize = (panelId: string, size: { width?: string; height?: string }) => {
    setPanelSizes(prev => ({
      ...prev,
      [panelId]: { ...prev[panelId], ...size }
    }));
  };

  return (
    <div 
      className={`mode-layout h-full flex flex-col ${className}`}
      data-mode={mode.id}
      style={{
        '--mode-primary': mode.colors?.primary || '#3b82f6',
        '--mode-secondary': mode.colors?.secondary || '#64748b',
        '--mode-accent': mode.colors?.accent || '#06b6d4'
      } as React.CSSProperties}
    >
      {/* Mode Header */}
      {layoutConfig.header?.enabled && (
        <ModeHeader 
          mode={mode} 
          context={context}
          height={layoutConfig.header.height}
        />
      )}

      {/* Main Content Area */}
      <div className="mode-content flex-1 flex overflow-hidden">
        {/* Sidebar */}
        {layoutConfig.sidebar?.enabled && (
          <ModeSidebar
            mode={mode}
            context={context}
            config={layoutConfig.sidebar}
            collapsed={sidebarCollapsed}
            onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
          />
        )}

        {/* Panel Container */}
        <div className="panel-container flex-1 flex flex-col">
          {/* Top Panels */}
          <div className="top-panels flex">
            {layoutConfig.panels
              .filter(panel => panel.position === 'top')
              .sort((a, b) => a.priority - b.priority)
              .map(panel => (
                <ModePanel
                  key={panel.id}
                  panel={panel}
                  mode={mode}
                  context={context}
                  collapsed={collapsedPanels.has(panel.id)}
                  onToggleCollapse={() => togglePanel(panel.id)}
                  onResize={(size) => updatePanelSize(panel.id, size)}
                  customSize={panelSizes[panel.id]}
                />
              ))}
          </div>

          {/* Middle Section - Center + Left/Right Panels */}
          <div className="middle-section flex-1 flex">
            {/* Left Panels */}
            <div className="left-panels flex flex-col">
              {layoutConfig.panels
                .filter(panel => panel.position === 'left')
                .sort((a, b) => a.priority - b.priority)
                .map(panel => (
                  <ModePanel
                    key={panel.id}
                    panel={panel}
                    mode={mode}
                    context={context}
                    collapsed={collapsedPanels.has(panel.id)}
                    onToggleCollapse={() => togglePanel(panel.id)}
                    onResize={(size) => updatePanelSize(panel.id, size)}
                    customSize={panelSizes[panel.id]}
                  />
                ))}
            </div>

            {/* Center Content */}
            <div className="center-content flex-1 flex flex-col">
              {/* Center Panels */}
              {layoutConfig.panels
                .filter(panel => panel.position === 'center')
                .sort((a, b) => a.priority - b.priority)
                .map(panel => (
                  <ModePanel
                    key={panel.id}
                    panel={panel}
                    mode={mode}
                    context={context}
                    collapsed={collapsedPanels.has(panel.id)}
                    onToggleCollapse={() => togglePanel(panel.id)}
                    onResize={(size) => updatePanelSize(panel.id, size)}
                    customSize={panelSizes[panel.id]}
                  />
                ))}
              
              {/* Main Content */}
              <div className="main-content flex-1 p-4">
                {children}
              </div>
            </div>

            {/* Right Panels */}
            <div className="right-panels flex flex-col">
              {layoutConfig.panels
                .filter(panel => panel.position === 'right')
                .sort((a, b) => a.priority - b.priority)
                .map(panel => (
                  <ModePanel
                    key={panel.id}
                    panel={panel}
                    mode={mode}
                    context={context}
                    collapsed={collapsedPanels.has(panel.id)}
                    onToggleCollapse={() => togglePanel(panel.id)}
                    onResize={(size) => updatePanelSize(panel.id, size)}
                    customSize={panelSizes[panel.id]}
                  />
                ))}
            </div>
          </div>

          {/* Bottom Panels */}
          <div className="bottom-panels flex flex-col">
            {layoutConfig.panels
              .filter(panel => panel.position === 'bottom')
              .sort((a, b) => a.priority - b.priority)
              .map(panel => (
                <ModePanel
                  key={panel.id}
                  panel={panel}
                  mode={mode}
                  context={context}
                  collapsed={collapsedPanels.has(panel.id)}
                  onToggleCollapse={() => togglePanel(panel.id)}
                  onResize={(size) => updatePanelSize(panel.id, size)}
                  customSize={panelSizes[panel.id]}
                />
              ))}
          </div>
        </div>
      </div>

      {/* Mode Footer */}
      {layoutConfig.footer?.enabled && (
        <ModeFooter 
          mode={mode} 
          context={context}
          height={layoutConfig.footer.height}
        />
      )}
    </div>
  );
};

// Header Component
const ModeHeader: React.FC<{
  mode: Mode;
  context: ContextSnapshot;
  height: number;
}> = ({ mode, context, height }) => {
  return (
    <header 
      className="mode-header bg-gray-900 border-b border-gray-700 flex items-center justify-between px-4"
      style={{ height: `${height}px` }}
    >
      <div className="flex items-center gap-3">
        <span className="text-2xl">{mode.icon}</span>
        <div>
          <h1 className="text-lg font-semibold text-white">{mode.name} Mode</h1>
          <p className="text-xs text-gray-400">{mode.description}</p>
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        <Badge variant="secondary" className="text-xs">
          Active
        </Badge>
        <Button size="sm" variant="ghost">
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    </header>
  );
};

// Sidebar Component
const ModeSidebar: React.FC<{
  mode: Mode;
  context: ContextSnapshot;
  config: NonNullable<ModeLayoutConfig['sidebar']>;
  collapsed: boolean;
  onToggleCollapse: () => void;
}> = ({ mode, context, config, collapsed, onToggleCollapse }) => {
  return (
    <aside 
      className={`mode-sidebar bg-gray-900 border-r border-gray-700 transition-all duration-300 ${
        collapsed ? 'w-12' : ''
      }`}
      style={{ 
        width: collapsed ? '48px' : `${config.width}px`,
        order: config.position === 'left' ? -1 : 1
      }}
    >
      <div className="sidebar-header h-12 flex items-center justify-between px-3 border-b border-gray-700">
        {!collapsed && (
          <span className="text-sm font-medium text-gray-300">Tools</span>
        )}
        {config.collapsible && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onToggleCollapse}
            className="h-8 w-8 p-0"
          >
            {collapsed ? (
              config.position === 'left' ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />
            ) : (
              config.position === 'left' ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>
      
      <div className="sidebar-content p-2">
        {/* Sidebar content based on mode */}
        {!collapsed && (
          <div className="space-y-2">
            <Button
              size="sm"
              variant="ghost"
              className="w-full justify-start text-left"
            >
              <LayoutGrid className="h-4 w-4 mr-2" />
              Overview
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="w-full justify-start text-left"
            >
              <Sidebar className="h-4 w-4 mr-2" />
              Details
            </Button>
          </div>
        )}
      </div>
    </aside>
  );
};

// Panel Component
const ModePanel: React.FC<{
  panel: ModeLayoutConfig['panels'][0];
  mode: Mode;
  context: ContextSnapshot;
  collapsed: boolean;
  onToggleCollapse: () => void;
  onResize: (size: { width?: string; height?: string }) => void;
  customSize?: { width?: string; height?: string };
}> = ({ panel, mode, context, collapsed, onToggleCollapse, onResize, customSize }) => {
  const PanelComponent = panel.component;
  
  const panelStyle: React.CSSProperties = {
    width: customSize?.width || panel.size.width,
    height: customSize?.height || panel.size.height,
    minWidth: panel.size.minWidth,
    minHeight: panel.size.minHeight,
  };

  if (collapsed) {
    return (
      <div 
        className="panel-collapsed bg-gray-800 border border-gray-700 m-1 rounded"
        style={{ 
          width: panel.position === 'left' || panel.position === 'right' ? '40px' : panelStyle.width,
          height: panel.position === 'top' || panel.position === 'bottom' ? '40px' : panelStyle.height 
        }}
      >
        <Button
          size="sm"
          variant="ghost"
          onClick={onToggleCollapse}
          className="h-full w-full"
        >
          <Maximize2 className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div 
      className="mode-panel bg-gray-800 border border-gray-700 m-1 rounded overflow-hidden"
      style={panelStyle}
    >
      <div className="panel-header bg-gray-900 px-3 py-2 flex items-center justify-between border-b border-gray-700">
        <span className="text-sm font-medium text-gray-300">{panel.id}</span>
        <div className="flex items-center gap-1">
          {panel.resizable && (
            <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
              <LayoutGrid className="h-3 w-3" />
            </Button>
          )}
          {panel.collapsible && (
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={onToggleCollapse}
              className="h-6 w-6 p-0"
            >
              <Minimize2 className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
      
      <div className="panel-content flex-1 overflow-auto">
        <PanelComponent mode={mode} context={context} />
      </div>
    </div>
  );
};

// Footer Component
const ModeFooter: React.FC<{
  mode: Mode;
  context: ContextSnapshot;
  height: number;
}> = ({ mode, context, height }) => {
  return (
    <footer 
      className="mode-footer bg-gray-900 border-t border-gray-700 flex items-center justify-between px-4"
      style={{ height: `${height}px` }}
    >
      <div className="text-xs text-gray-500">
        {mode.name} mode active
      </div>
      <div className="text-xs text-gray-500">
        Context updated: {new Date(context.timestamp).toLocaleTimeString()}
      </div>
    </footer>
  );
};

// Placeholder Panel Components (to be implemented separately)
const DiagramViewerPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Diagram Viewer</div>
);

const RequirementsPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Requirements Panel</div>
);

const DecisionLogPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Decision Log Panel</div>
);

const LogViewerPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Log Viewer Panel</div>
);

const StackTracePanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Stack Trace Panel</div>
);

const VariableInspectorPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Variable Inspector Panel</div>
);

const DiffViewerPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Diff Viewer Panel</div>
);

const ReviewChecklistPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Review Checklist Panel</div>
);

const DefaultContentPanel: React.FC<any> = () => (
  <div className="p-4 text-center text-gray-500">Default Content Panel</div>
);

export default ModeLayout;
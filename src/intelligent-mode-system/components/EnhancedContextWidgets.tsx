import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { FileText, GitBranch, Clock, Activity } from 'lucide-react';
import { Mode } from '../types/mode.types';
import { ContextSnapshot } from '../types/context.types';

interface ContextWidgetGridProps {
  mode: Mode;
  context: ContextSnapshot;
  className?: string;
}

export const ContextWidgetGrid: React.FC<ContextWidgetGridProps> = ({
  mode,
  context,
  className,
}) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Mode Status Widget */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <span className="text-lg">{mode.icon}</span>
            {mode.name} Mode
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <p className="text-xs text-muted-foreground mb-2">{mode.description}</p>
          <div className="flex flex-wrap gap-1">
            {mode.capabilities.slice(0, 3).map((capability) => (
              <Badge key={capability} variant="secondary" className="text-xs">
                {capability.replace('-', ' ')}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Project Context Widget */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <FileText className="w-4 h-4" />
            Project Context
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Type:</span>
              <Badge variant="outline">{context.projectContext.type}</Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Files:</span>
              <span>{context.projectContext.structure.fileCount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Tests:</span>
              <span>{context.projectContext.structure.hasTests ? '✓' : '✗'}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Activity Widget */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Actions:</span>
              <span>{context.userContext.recentActions.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Session:</span>
              <span>{Math.round(context.userContext.sessionDuration / 60)}m</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Last Active:</span>
              <span>{new Date(context.userContext.lastActivity).toLocaleTimeString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Environment Widget */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Environment
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3 text-xs">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-muted-foreground">CPU:</span>
                <span>{context.environmentContext.systemResources.cpuUsage}%</span>
              </div>
              <Progress value={context.environmentContext.systemResources.cpuUsage} className="h-1" />
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-muted-foreground">Memory:</span>
                <span>{context.environmentContext.systemResources.memoryUsage}%</span>
              </div>
              <Progress value={context.environmentContext.systemResources.memoryUsage} className="h-1" />
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Open Files:</span>
              <span>{context.environmentContext.openFiles.length}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mode-Specific Features */}
      {mode.features.length > 0 && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Mode Features</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-1">
              {mode.features.slice(0, 4).map((feature) => (
                <div key={feature.id} className="text-xs">
                  <span className="font-medium">{feature.name}</span>
                  <Badge variant="outline" className="ml-2 text-xs">
                    {feature.type}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
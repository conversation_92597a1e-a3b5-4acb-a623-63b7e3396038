import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Send, <PERSON>rkles, History, X, ChevronDown, Filter } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from 'cmdk';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from '../../components/ui/dropdown-menu';
import { useSmartPrompt } from '../hooks/useSmartPrompt';
import { Mode, ContextSnapshot } from '../types';
import { SmartSuggestion } from '../types/smart-prompt.types';
import { useBreakpoint, useIsMobile, responsiveClasses, touchTargetSize } from '../utils/responsive';
import { 
  useFocusTrap, 
  useAccessibleShortcuts, 
  announcer,
  a11yClasses,
  ariaLabels,
  semanticRoles
} from '../utils/accessibility';
import { ContextAwareSuggestions } from '../../components/ContextAwareSuggestions';

interface SmartPromptInputResponsiveProps {
  context: ContextSnapshot;
  currentMode: Mode;
  onSubmit: (prompt: string, metadata?: any) => void;
  placeholder?: string;
  className?: string;
  showSuggestions?: boolean;
  showRecent?: boolean;
  enableVoiceInput?: boolean;
  maxHeight?: string;
  projectContext?: any;
  environmentContext?: any;
  showContextAwareSuggestions?: boolean;
  disabled?: boolean;
}

export const SmartPromptInputResponsive: React.FC<SmartPromptInputResponsiveProps> = ({
  context,
  currentMode,
  onSubmit,
  placeholder = "Ask me anything...",
  className = '',
  showSuggestions = true,
  showRecent = true,
  enableVoiceInput = false,
  maxHeight = '400px',
  projectContext,
  environmentContext,
  showContextAwareSuggestions = true,
  disabled = false,
}) => {
  const breakpoint = useBreakpoint();
  const isMobile = useIsMobile();
  const [input, setInput] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [isVoiceRecording, setIsVoiceRecording] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const commandRef = useFocusTrap(isOpen && isMobile);
  
  const {
    suggestions,
    recentPrompts,
    isLoading,
    config,
    submitPrompt,
    clearRecent,
    generateSuggestions,
    filterSuggestions,
  } = useSmartPrompt({
    context,
    currentMode,
    onSubmit,
  });

  // Responsive layout adjustments
  const suggestionColumns = isMobile ? 1 : 2;
  const maxSuggestionsVisible = isMobile ? 5 : 8;

  // Keyboard shortcuts
  const shortcuts = {
    'Escape': () => {
      if (isOpen) {
        setIsOpen(false);
        inputRef.current?.focus();
      }
    },
    'Ctrl+Enter': () => {
      if (input.trim()) {
        handleSubmit();
      }
    },
    'Ctrl+/': () => {
      setIsOpen(!isOpen);
    },
    'Ctrl+K': () => {
      inputRef.current?.focus();
    },
  };

  useAccessibleShortcuts(shortcuts, {
    enableHelp: true,
    announceActions: true
  });

  // Filter suggestions based on category
  const filteredSuggestions = React.useMemo(() => {
    return filterSuggestions(selectedCategory, input);
  }, [filterSuggestions, selectedCategory, input]);

  // Get suggestion categories
  const categories = React.useMemo(() => {
    const cats = new Set<string>(['all']);
    suggestions.forEach(s => cats.add(s.category));
    return Array.from(cats);
  }, [suggestions]);

  const handleSubmit = useCallback(() => {
    if (!input.trim()) return;
    
    submitPrompt(input, {
      mode: currentMode.id,
      context: context.fileContext.path,
      timestamp: Date.now(),
    });
    
    setInput('');
    setIsOpen(false);
    announcer.announce('Prompt submitted');
  }, [input, submitPrompt, currentMode.id, context.fileContext.path]);

  const handleSuggestionSelect = useCallback((suggestion: SmartSuggestion) => {
    setInput(suggestion.text);
    setIsOpen(false);
    inputRef.current?.focus();
    announcer.announce(`Selected suggestion: ${suggestion.text}`);
  }, []);

  const handleVoiceInput = useCallback(() => {
    if (!enableVoiceInput) return;
    
    if (!isVoiceRecording) {
      // Start voice recording
      setIsVoiceRecording(true);
      announcer.announce('Voice recording started');
      
      // Mock voice input - in real implementation, use Web Speech API
      setTimeout(() => {
        setInput('This is a voice input example');
        setIsVoiceRecording(false);
        announcer.announce('Voice recording completed');
      }, 2000);
    } else {
      // Stop voice recording
      setIsVoiceRecording(false);
      announcer.announce('Voice recording stopped');
    }
  }, [enableVoiceInput, isVoiceRecording]);

  // Auto-generate suggestions when input changes
  useEffect(() => {
    if (input.length > 2) {
      generateSuggestions(input);
    }
  }, [input, generateSuggestions]);

  // Touch gesture support for mobile
  useEffect(() => {
    if (!isMobile) return;

    let startY = 0;
    
    const handleTouchStart = (e: TouchEvent) => {
      startY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const endY = e.changedTouches[0].clientY;
      const diffY = startY - endY;
      
      // Swipe up to open suggestions
      if (diffY > 50 && !isOpen) {
        setIsOpen(true);
      }
      // Swipe down to close suggestions
      else if (diffY < -50 && isOpen) {
        setIsOpen(false);
      }
    };

    const element = inputRef.current;
    if (element) {
      element.addEventListener('touchstart', handleTouchStart);
      element.addEventListener('touchend', handleTouchEnd);
      
      return () => {
        element.removeEventListener('touchstart', handleTouchStart);
        element.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [isMobile, isOpen]);

  return (
    <div 
      className={`smart-prompt-input relative ${className}`}
      role="search"
      aria-label="Smart prompt input"
    >
      {/* Context-Aware Suggestions */}
      {showContextAwareSuggestions && (
        <ContextAwareSuggestions
          onSuggestionClick={(prompt) => {
            setInput(prompt);
            inputRef.current?.focus();
          }}
          className="mb-4"
        />
      )}

      <div className="relative">
        <div className={`flex items-center gap-2 p-2 border rounded-lg bg-background shadow-sm ${a11yClasses.focusWithin} mode-border`}>
          <Sparkles
            className="h-5 w-5 text-primary flex-shrink-0"
            aria-hidden="true"
          />
          
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onFocus={() => !disabled && setIsOpen(true)}
            disabled={disabled}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSubmit();
              }
            }}
            placeholder={placeholder}
            className={`flex-1 outline-none text-sm ${responsiveClasses.text.body} bg-transparent mode-text-primary`}
            aria-label="Prompt input"
            aria-autocomplete="list"
            aria-expanded={isOpen}
            aria-controls="suggestions-list"
            autoComplete="off"
            spellCheck="true"
          />
          
          <div className="flex items-center gap-1">
            {enableVoiceInput && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handleVoiceInput}
                className={`${touchTargetSize.small} ${a11yClasses.focusVisible}`}
                aria-label={isVoiceRecording ? 'Stop voice recording' : 'Start voice recording'}
                aria-pressed={isVoiceRecording}
              >
                <div className={`h-4 w-4 rounded-full ${isVoiceRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-400'}`} />
              </Button>
            )}
            
            {showRecent && recentPrompts.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    className={`${touchTargetSize.small} ${a11yClasses.focusVisible}`}
                    aria-label="Recent prompts"
                  >
                    <History className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-64">
                  <div className="flex items-center justify-between px-2 py-1">
                    <span className="text-sm font-medium">Recent Prompts</span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={clearRecent}
                      className="h-6 text-xs"
                      aria-label="Clear recent prompts"
                    >
                      Clear
                    </Button>
                  </div>
                  <DropdownMenuSeparator />
                  {recentPrompts.map((prompt, index) => (
                    <DropdownMenuItem
                      key={index}
                      onClick={() => setInput(prompt)}
                      className={`text-sm ${a11yClasses.focusVisible}`}
                    >
                      <span className="truncate">{prompt}</span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            
            <Button
              size="sm"
              onClick={handleSubmit}
              disabled={!input.trim() || disabled}
              className={`${touchTargetSize.small} ${a11yClasses.focusVisible}`}
              aria-label="Submit prompt"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Suggestions Panel */}
        {showSuggestions && isOpen && (filteredSuggestions.length > 0 || isLoading) && (
          <div
            ref={commandRef as React.RefObject<HTMLDivElement>}
            id="suggestions-list"
            className={`
            absolute bottom-full left-0 right-0 mb-2 z-50
            bg-background border rounded-lg shadow-lg overflow-hidden mode-border
            ${isMobile ? 'bottom-0 fixed left-0 right-0 top-auto' : ''}
            `}
            style={{ maxHeight: isMobile ? '60vh' : maxHeight }}
            role="listbox"
            aria-label="Suggestions"
          >
            {/* Category Filter for Desktop */}
            {!isMobile && categories.length > 1 && (
              <div className="flex items-center gap-2 p-2 border-b">
                <span className="text-sm text-gray-500">Filter:</span>
                {categories.map((cat) => (
                  <Badge
                    key={cat}
                    variant={selectedCategory === cat ? 'default' : 'outline'}
                    className="cursor-pointer text-xs"
                    onClick={() => setSelectedCategory(cat)}
                    role="button"
                    tabIndex={0}
                    aria-pressed={selectedCategory === cat}
                    aria-label={`Filter by ${cat}`}
                  >
                    {cat}
                  </Badge>
                ))}
              </div>
            )}
            
            {/* Mobile Filter Button */}
            {isMobile && categories.length > 1 && (
              <div className="flex items-center justify-between p-2 border-b">
                <span className="text-sm font-medium">Suggestions</span>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowFilters(!showFilters)}
                  className={`${touchTargetSize.small} ${a11yClasses.focusVisible}`}
                  aria-label="Toggle filters"
                  aria-expanded={showFilters}
                >
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            )}
            
            {/* Mobile Filter Panel */}
            {isMobile && showFilters && (
              <div className="flex flex-wrap gap-2 p-2 border-b bg-gray-50">
                {categories.map((cat) => (
                  <Badge
                    key={cat}
                    variant={selectedCategory === cat ? 'default' : 'outline'}
                    className="cursor-pointer text-xs"
                    onClick={() => {
                      setSelectedCategory(cat);
                      setShowFilters(false);
                    }}
                  >
                    {cat}
                  </Badge>
                ))}
              </div>
            )}
            
            {/* Suggestions List */}
            <div className="overflow-y-auto p-2">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500" />
                  <span className="ml-2 text-sm text-gray-500">Loading suggestions...</span>
                </div>
              ) : (
                <div 
                  className={`
                    grid gap-2
                    ${suggestionColumns > 1 ? `grid-cols-${suggestionColumns}` : ''}
                  `}
                >
                  {filteredSuggestions.slice(0, maxSuggestionsVisible).map((suggestion) => (
                    <button
                      key={suggestion.id}
                      onClick={() => handleSuggestionSelect(suggestion)}
                      className={`
                        text-left p-3 rounded-lg border hover:bg-gray-50
                        transition-colors duration-200
                        ${a11yClasses.focusVisible}
                        ${a11yClasses.interactive}
                      `}
                      role="option"
                      aria-selected={false}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <p className={`font-medium ${responsiveClasses.text.body} truncate`}>
                            {suggestion.text}
                          </p>
                          {suggestion.description && (
                            <p className={`${responsiveClasses.text.small} text-gray-500 mt-1`}>
                              {suggestion.description}
                            </p>
                          )}
                        </div>
                        <Badge 
                          variant="outline" 
                          className="ml-2 text-xs flex-shrink-0"
                          aria-label={`Category: ${suggestion.category}`}
                        >
                          {suggestion.category}
                        </Badge>
                      </div>
                      {suggestion.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {suggestion.tags.map((tag) => (
                            <span 
                              key={tag} 
                              className="text-xs text-gray-400"
                              aria-label={`Tag: ${tag}`}
                            >
                              #{tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      
      {/* Screen reader announcements */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {filteredSuggestions.length} suggestions available
      </div>
    </div>
  );
};

export default SmartPromptInputResponsive;

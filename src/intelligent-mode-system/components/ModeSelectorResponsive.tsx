import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Mode, ContextSnapshot } from '../types';
import { Card, CardContent } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { 
  Briefcase, 
  Bug, 
  GitBranch, 
  Rocket, 
  FlaskConical, 
  GraduationCap,
  Check,
  ChevronDown,
  Search,
  X
} from 'lucide-react';
import { Button } from '../../components/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '../../components/ui/dropdown-menu';
import { Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem } from 'cmdk';
import { useBreakpoint, useIsMobile, responsiveClasses, touchTargetSize } from '../utils/responsive';
import { 
  useFocusTrap, 
  useAccessibleShortcuts, 
  announcer,
  a11yClasses,
  ariaLabels,
  semanticRoles
} from '../utils/accessibility';

interface ModeSelectorResponsiveProps {
  modes: Mode[];
  currentMode?: Mode;
  onModeChange: (mode: Mode) => void;
  className?: string;
  variant?: 'default' | 'compact' | 'expanded';
  showDescriptions?: boolean;
  groupByCategory?: boolean;
  allowSearch?: boolean;
  context?: ContextSnapshot;
}

const modeIcons: Record<string, React.ComponentType<any>> = {
  architect: Briefcase,
  debug: Bug,
  review: GitBranch,
  deploy: Rocket,
  experiment: FlaskConical,
  learn: GraduationCap,
};

export const ModeSelectorResponsive: React.FC<ModeSelectorResponsiveProps> = ({
  modes,
  currentMode,
  onModeChange,
  className = '',
  variant = 'default',
  showDescriptions = true,
  groupByCategory = false,
  allowSearch = false,
  context,
}) => {
  const breakpoint = useBreakpoint();
  const isMobile = useIsMobile();
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(0);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useFocusTrap(isOpen && isMobile);

  // Responsive variant selection
  const effectiveVariant = isMobile ? 'compact' : variant;

  // Keyboard shortcuts
  const shortcuts = {
    'Escape': () => {
      if (isOpen) {
        setIsOpen(false);
        announcer.announce('Mode selector closed');
      }
    },
    'Enter': () => {
      if (isOpen && filteredModes[highlightedIndex]) {
        handleModeSelect(filteredModes[highlightedIndex]);
      }
    },
    'ArrowDown': () => {
      if (isOpen) {
        setHighlightedIndex((prev) => 
          Math.min(prev + 1, filteredModes.length - 1)
        );
      }
    },
    'ArrowUp': () => {
      if (isOpen) {
        setHighlightedIndex((prev) => Math.max(prev - 1, 0));
      }
    },
  };

  useAccessibleShortcuts(shortcuts, {
    enableHelp: true,
    announceActions: true
  });

  // Filter modes based on search
  const filteredModes = React.useMemo(() => {
    if (!searchQuery) return modes;
    
    const query = searchQuery.toLowerCase();
    return modes.filter(mode => 
      mode.name.toLowerCase().includes(query) ||
      mode.description.toLowerCase().includes(query) ||
      mode.tags.some(tag => tag.toLowerCase().includes(query))
    );
  }, [modes, searchQuery]);

  // Group modes by category
  const groupedModes = React.useMemo(() => {
    if (!groupByCategory) return { all: filteredModes };
    
    return filteredModes.reduce((acc, mode) => {
      const category = mode.features[0]?.category || 'other';
      if (!acc[category]) acc[category] = [];
      acc[category].push(mode);
      return acc;
    }, {} as Record<string, Mode[]>);
  }, [filteredModes, groupByCategory]);

  const handleModeSelect = useCallback((mode: Mode) => {
    onModeChange(mode);
    setIsOpen(false);
    setSearchQuery('');
    announcer.announce(`Switched to ${mode.name} mode`);
  }, [onModeChange]);

  // Auto-close on outside click
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, dropdownRef]);

  // Mobile swipe gestures
  useEffect(() => {
    if (!isMobile) return;

    let startX = 0;
    let startY = 0;

    const handleTouchStart = (e: TouchEvent) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      
      const diffX = endX - startX;
      const diffY = Math.abs(endY - startY);
      
      // Horizontal swipe detection
      if (Math.abs(diffX) > 50 && diffY < 50) {
        const currentIndex = modes.findIndex(m => m.id === currentMode?.id);
        if (diffX > 0 && currentIndex > 0) {
          // Swipe right - previous mode
          handleModeSelect(modes[currentIndex - 1]);
        } else if (diffX < 0 && currentIndex < modes.length - 1) {
          // Swipe left - next mode
          handleModeSelect(modes[currentIndex + 1]);
        }
      }
    };

    const element = dropdownRef.current;
    if (element) {
      element.addEventListener('touchstart', handleTouchStart);
      element.addEventListener('touchend', handleTouchEnd);
      
      return () => {
        element.removeEventListener('touchstart', handleTouchStart);
        element.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [isMobile, modes, currentMode, handleModeSelect, dropdownRef]);

  if (effectiveVariant === 'compact') {
    return (
      <div className={`mode-selector-compact ${className}`} ref={dropdownRef}>
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              className={`${touchTargetSize.medium} ${a11yClasses.focusVisible}`}
              aria-label={`Current mode: ${currentMode?.name || 'None'}. Click to change mode`}
              aria-expanded={isOpen}
              aria-haspopup="menu"
            >
              {currentMode && (
                <>
                  {React.createElement(modeIcons[currentMode.id] || Briefcase, { 
                    className: `h-4 w-4 ${isMobile ? '' : 'mr-2'}` 
                  })}
                  <span className={responsiveClasses.visibility.hideMobile}>
                    {currentMode.name}
                  </span>
                </>
              )}
              <ChevronDown className="h-4 w-4 ml-2" aria-hidden="true" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent 
            align="start" 
            className={`w-56 ${isMobile ? 'max-h-[60vh] overflow-y-auto' : ''}`}
            role="menu"
            aria-label="Available modes"
          >
            {allowSearch && (
              <>
                <div className="p-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" aria-hidden="true" />
                    <input
                      ref={searchInputRef}
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search modes..."
                      className={`w-full pl-8 pr-8 py-2 text-sm border rounded-md ${a11yClasses.focusVisible}`}
                      aria-label="Search modes"
                    />
                    {searchQuery && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setSearchQuery('')}
                        className="absolute right-1 top-1 h-6 w-6 p-0"
                        aria-label="Clear search"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
                <DropdownMenuSeparator />
              </>
            )}
            {Object.entries(groupedModes).map(([category, categoryModes]) => (
              <React.Fragment key={category}>
                {groupByCategory && category !== 'all' && (
                  <DropdownMenuLabel className="text-xs uppercase">
                    {category}
                  </DropdownMenuLabel>
                )}
                {categoryModes.map((mode, index) => (
                  <DropdownMenuItem
                    key={mode.id}
                    onClick={() => handleModeSelect(mode)}
                    className={`${a11yClasses.focusVisible} ${
                      currentMode?.id === mode.id ? 'bg-blue-50' : ''
                    }`}
                    role="menuitem"
                    aria-current={currentMode?.id === mode.id ? 'true' : undefined}
                    data-highlighted={highlightedIndex === index}
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center">
                        {React.createElement(modeIcons[mode.id] || Briefcase, { 
                          className: 'h-4 w-4 mr-2' 
                        })}
                        <span className="font-medium">{mode.name}</span>
                      </div>
                      {currentMode?.id === mode.id && (
                        <Check className="h-4 w-4 text-blue-600" aria-label="Current mode" />
                      )}
                    </div>
                    {showDescriptions && !isMobile && (
                      <p className="text-xs text-gray-500 mt-1 ml-6">
                        {mode.description}
                      </p>
                    )}
                  </DropdownMenuItem>
                ))}
              </React.Fragment>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }

  // Default and expanded variants
  return (
    <div 
      className={`mode-selector ${className} ${responsiveClasses.grid.base}`}
      role="group"
      aria-label="Mode selector"
    >
      <div 
        className={
          effectiveVariant === 'expanded' 
            ? responsiveClasses.grid.cols[3]
            : responsiveClasses.flex.wrap
        }
      >
        {modes.map((mode) => {
          const Icon = modeIcons[mode.id] || Briefcase;
          const isActive = currentMode?.id === mode.id;
          
          return (
            <Card
              key={mode.id}
              className={`
                mode-card cursor-pointer transition-all duration-200
                ${isActive ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-lg'}
                ${a11yClasses.focusWithin}
                ${a11yClasses.interactive}
              `}
              onClick={() => handleModeSelect(mode)}
              role="button"
              tabIndex={0}
              aria-pressed={isActive}
              aria-label={`${mode.name} mode${isActive ? ' (active)' : ''}`}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleModeSelect(mode);
                }
              }}
            >
              <CardContent className={responsiveClasses.spacing.component}>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div 
                      className={`
                        p-2 rounded-lg
                        ${isActive ? 'bg-blue-100' : 'bg-gray-100'}
                      `}
                      aria-hidden="true"
                    >
                      <Icon className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className={`font-medium ${responsiveClasses.text.h4}`}>
                        {mode.name}
                      </h3>
                      {showDescriptions && (
                        <p className={`${responsiveClasses.text.small} text-gray-500 mt-1`}>
                          {mode.description}
                        </p>
                      )}
                    </div>
                  </div>
                  {isActive && (
                    <Badge 
                      variant="secondary" 
                      className="text-xs"
                      aria-label="Active mode"
                    >
                      Active
                    </Badge>
                  )}
                </div>
                
                {effectiveVariant === 'expanded' && (
                  <div className="mt-4 flex flex-wrap gap-1">
                    {mode.tags.map((tag) => (
                      <Badge 
                        key={tag} 
                        variant="outline" 
                        className="text-xs"
                        aria-label={`Tag: ${tag}`}
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default ModeSelectorResponsive;
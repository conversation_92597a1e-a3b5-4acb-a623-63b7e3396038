import React, { createContext, useContext, forwardRef } from 'react';
import { Box, BoxProps } from '../primitives/Box';
import { Flex } from '../primitives/Flex';
import { Text } from '../primitives/Text';
import { Stack } from '../primitives/Stack';
import { cn } from '@/lib/utils';
import { ModeFeature } from '../../types/mode.types';

// Context for sharing state between compound components
interface ModePanelContextValue {
  feature?: ModeFeature;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const ModePanelContext = createContext<ModePanelContextValue>({});

const useModePanelContext = () => {
  const context = useContext(ModePanelContext);
  if (!context) {
    throw new Error('ModePanel compound components must be used within ModePanel');
  }
  return context;
};

// Main ModePanel component
export interface ModePanelProps extends BoxProps {
  feature?: ModeFeature;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  onCollapse?: (collapsed: boolean) => void;
}

export const ModePanel = forwardRef<HTMLDivElement, ModePanelProps>(
  (
    {
      feature,
      collapsible = false,
      defaultCollapsed = false,
      onCollapse,
      children,
      className,
      ...props
    },
    ref
  ) => {
    const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);

    const handleToggleCollapse = () => {
      const newState = !isCollapsed;
      setIsCollapsed(newState);
      onCollapse?.(newState);
    };

    const contextValue: ModePanelContextValue = {
      feature,
      isCollapsed: collapsible ? isCollapsed : false,
      onToggleCollapse: collapsible ? handleToggleCollapse : undefined,
    };

    return (
      <ModePanelContext.Provider value={contextValue}>
        <Box
          ref={ref}
          variant="panel"
          rounded="lg"
          border
          className={cn('overflow-hidden', className)}
          {...props}
        >
          {children}
        </Box>
      </ModePanelContext.Provider>
    );
  }
);

ModePanel.displayName = 'ModePanel';

// Panel Header
interface ModePanelHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  actions?: React.ReactNode;
}

export const ModePanelHeader = forwardRef<HTMLDivElement, ModePanelHeaderProps>(
  ({ actions, children, className, ...props }, ref) => {
    const { feature, isCollapsed, onToggleCollapse } = useModePanelContext();

    return (
      <Flex
        ref={ref}
        align="center"
        justify="between"
        padding="md"
        className={cn(
          'border-b border-[var(--mode-border)]',
          onToggleCollapse && 'cursor-pointer select-none',
          className
        )}
        onClick={onToggleCollapse}
        {...props}
      >
        <Flex align="center" gap="sm">
          <Stack spacing="none">
            {children || (
              <>
                <Text weight="semibold">{feature?.name}</Text>
              </>
            )}
          </Stack>
        </Flex>
        <Flex align="center" gap="sm">
          {actions}
          {onToggleCollapse && (
            <Box
              className={cn(
                'transition-transform duration-[var(--duration-normal)]',
                isCollapsed && 'rotate-180'
              )}
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M4 6L8 10L12 6"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </Box>
          )}
        </Flex>
      </Flex>
    );
  }
);

ModePanelHeader.displayName = 'ModePanelHeader';

// Panel Content
interface ModePanelContentProps extends React.HTMLAttributes<HTMLDivElement> {
  noPadding?: boolean;
}

export const ModePanelContent = forwardRef<HTMLDivElement, ModePanelContentProps>(
  ({ noPadding = false, className, style, ...props }, ref) => {
    const { isCollapsed } = useModePanelContext();

    return (
      <Box
        ref={ref}
        padding={noPadding ? 'none' : 'md'}
        className={cn(
          'transition-all duration-[var(--duration-normal)]',
          className
        )}
        style={{
          ...style,
          maxHeight: isCollapsed ? 0 : 'none',
          opacity: isCollapsed ? 0 : 1,
          overflow: isCollapsed ? 'hidden' : 'visible',
        }}
        {...props}
      />
    );
  }
);

ModePanelContent.displayName = 'ModePanelContent';

// Panel Footer
interface ModePanelFooterProps extends React.HTMLAttributes<HTMLDivElement> {}

export const ModePanelFooter = forwardRef<HTMLDivElement, ModePanelFooterProps>(
  ({ className, ...props }, ref) => {
    const { isCollapsed } = useModePanelContext();

    if (isCollapsed) return null;

    return (
      <Flex
        ref={ref}
        padding="md"
        className={cn('border-t border-[var(--mode-border)]', className)}
        {...props}
      />
    );
  }
);

ModePanelFooter.displayName = 'ModePanelFooter';

// Export as compound component
export const ModePanelCompound = Object.assign(ModePanel, {
  Header: ModePanelHeader,
  Content: ModePanelContent,
  Footer: ModePanelFooter,
});
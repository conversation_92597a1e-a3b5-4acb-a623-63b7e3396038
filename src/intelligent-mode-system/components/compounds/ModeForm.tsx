import React, { createContext, useContext, forwardRef } from 'react';
import { Box, BoxProps } from '../primitives/Box';
import { Stack } from '../primitives/Stack';
import { Text } from '../primitives/Text';
import { Flex } from '../primitives/Flex';
import { Input, InputProps, Textarea, TextareaProps } from '../primitives/Input';
import { cn } from '@/lib/utils';

// Form context
interface ModeFormContextValue {
  variant?: 'default' | 'compact' | 'inline';
  labelPosition?: 'top' | 'left';
  showRequiredIndicator?: boolean;
}

const ModeFormContext = createContext<ModeFormContextValue>({
  variant: 'default',
  labelPosition: 'top',
  showRequiredIndicator: true,
});

const useModeFormContext = () => {
  const context = useContext(ModeFormContext);
  if (!context) {
    throw new Error('ModeForm compound components must be used within ModeForm');
  }
  return context;
};

// Main Form component
export interface ModeFormProps extends Omit<BoxProps, 'as' | 'variant'> {
  variant?: 'default' | 'compact' | 'inline';
  labelPosition?: 'top' | 'left';
  showRequiredIndicator?: boolean;
  onSubmit?: (e: React.FormEvent<HTMLFormElement>) => void;
}

export const ModeForm = forwardRef<HTMLFormElement, ModeFormProps>(
  (
    {
      variant = 'default',
      labelPosition = 'top',
      showRequiredIndicator = true,
      children,
      className,
      ...props
    },
    ref
  ) => {
    const contextValue: ModeFormContextValue = {
      variant,
      labelPosition,
      showRequiredIndicator,
    };

    return (
      <ModeFormContext.Provider value={contextValue}>
        <Box
          as="form"
          ref={ref}
          className={cn('w-full', className)}
          {...props}
        >
          {children}
        </Box>
      </ModeFormContext.Provider>
    );
  }
);

ModeForm.displayName = 'ModeForm';

// Form Field
interface ModeFormFieldProps extends React.HTMLAttributes<HTMLDivElement> {
  required?: boolean;
}

export const ModeFormField = forwardRef<HTMLDivElement, ModeFormFieldProps>(
  ({ required = false, className, ...props }, ref) => {
    const { variant, labelPosition } = useModeFormContext();

    const spacingClasses = {
      default: 'mb-6',
      compact: 'mb-4',
      inline: 'mb-0',
    };

    return (
      <Box
        ref={ref}
        className={cn(
          spacingClasses[variant || 'default'],
          labelPosition === 'left' && 'grid grid-cols-3 gap-4 items-start',
          className
        )}
        {...props}
      />
    );
  }
);

ModeFormField.displayName = 'ModeFormField';

// Form Label
interface ModeFormLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean;
}

export const ModeFormLabel = forwardRef<HTMLLabelElement, ModeFormLabelProps>(
  ({ required = false, children, className, ...props }, ref) => {
    const { showRequiredIndicator, labelPosition } = useModeFormContext();

    return (
      <Box
        as="label"
        ref={ref}
        className={cn(
          'block text-sm font-medium text-[var(--mode-text)]',
          labelPosition === 'top' ? 'mb-2' : 'mb-0',
          className
        )}
        {...props}
      >
        <Flex align="center" gap="xs" className="inline-flex">
          {children}
          {required && showRequiredIndicator && (
            <Text size="sm" color="error">
              *
            </Text>
          )}
        </Flex>
      </Box>
    );
  }
);

ModeFormLabel.displayName = 'ModeFormLabel';

// Form Input wrapper
interface ModeFormInputProps extends Omit<InputProps, 'error'> {
  error?: string;
  hint?: string;
}

export const ModeFormInput = forwardRef<HTMLInputElement, ModeFormInputProps>(
  ({ error, hint, className, ...props }, ref) => {
    const hasError = !!error;

    return (
      <Stack spacing="xs">
        <Input
          ref={ref}
          error={hasError}
          className={className}
          {...props}
        />
        {error && (
          <Text size="xs" color="error">
            {error}
          </Text>
        )}
        {hint && !error && (
          <Text size="xs" color="muted">
            {hint}
          </Text>
        )}
      </Stack>
    );
  }
);

ModeFormInput.displayName = 'ModeFormInput';

// Form Textarea wrapper
interface ModeFormTextareaProps extends Omit<TextareaProps, 'error'> {
  error?: string;
  hint?: string;
}

export const ModeFormTextarea = forwardRef<HTMLTextAreaElement, ModeFormTextareaProps>(
  ({ error, hint, className, ...props }, ref) => {
    const hasError = !!error;

    return (
      <Stack spacing="xs">
        <Textarea
          ref={ref}
          error={hasError}
          className={className}
          {...props}
        />
        {error && (
          <Text size="xs" color="error">
            {error}
          </Text>
        )}
        {hint && !error && (
          <Text size="xs" color="muted">
            {hint}
          </Text>
        )}
      </Stack>
    );
  }
);

ModeFormTextarea.displayName = 'ModeFormTextarea';

// Form Group (for related fields)
interface ModeFormGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  label?: string;
  description?: string;
}

export const ModeFormGroup = forwardRef<HTMLDivElement, ModeFormGroupProps>(
  ({ label, description, children, className, ...props }, ref) => {
    return (
      <Box
        ref={ref}
        variant="surface"
        rounded="md"
        padding="md"
        border
        className={cn('mb-6', className)}
        {...props}
      >
        {(label || description) && (
          <Box className="mb-4">
            {label && (
              <Text size="sm" weight="semibold" className="mb-1">
                {label}
              </Text>
            )}
            {description && (
              <Text size="xs" color="muted">
                {description}
              </Text>
            )}
          </Box>
        )}
        {children}
      </Box>
    );
  }
);

ModeFormGroup.displayName = 'ModeFormGroup';

// Form Actions (for submit/cancel buttons)
interface ModeFormActionsProps extends React.HTMLAttributes<HTMLDivElement> {
  align?: 'left' | 'center' | 'right' | 'between';
}

export const ModeFormActions = forwardRef<HTMLDivElement, ModeFormActionsProps>(
  ({ align = 'right', className, ...props }, ref) => {
    const alignClasses = {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end',
      between: 'justify-between',
    };

    return (
      <Flex
        ref={ref}
        align="center"
        gap="sm"
        className={cn(
          'mt-6 pt-6 border-t border-[var(--mode-border)]',
          alignClasses[align],
          className
        )}
        {...props}
      />
    );
  }
);

ModeFormActions.displayName = 'ModeFormActions';

// Export compound component
export const ModeFormCompound = Object.assign(ModeForm, {
  Field: ModeFormField,
  Label: ModeFormLabel,
  Input: ModeFormInput,
  Textarea: ModeFormTextarea,
  Group: ModeFormGroup,
  Actions: ModeFormActions,
});
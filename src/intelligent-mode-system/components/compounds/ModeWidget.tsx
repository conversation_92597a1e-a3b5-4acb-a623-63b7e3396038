import React, { createContext, useContext, forwardRef } from 'react';
import { Box, BoxProps } from '../primitives/Box';
import { Flex } from '../primitives/Flex';
import { Text } from '../primitives/Text';
import { cn } from '@/lib/utils';

// Widget sizes
export type WidgetSize = 'sm' | 'md' | 'lg' | 'full';

// Context for widget state
interface ModeWidgetContextValue {
  size: WidgetSize;
  isLoading?: boolean;
  isError?: boolean;
  isEmpty?: boolean;
}

const ModeWidgetContext = createContext<ModeWidgetContextValue>({
  size: 'md',
});

// Main Widget component
export interface ModeWidgetProps extends BoxProps {
  size?: WidgetSize;
  loading?: boolean;
  error?: boolean | string;
  empty?: boolean;
  title?: string;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
}

export const ModeWidget = forwardRef<HTMLDivElement, ModeWidgetProps>(
  (
    {
      size = 'md',
      loading = false,
      error = false,
      empty = false,
      title,
      icon,
      actions,
      children,
      className,
      ...props
    },
    ref
  ) => {
    const sizeClasses = {
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      full: 'w-full',
    };

    const contextValue: ModeWidgetContextValue = {
      size,
      isLoading: loading,
      isError: !!error,
      isEmpty: empty,
    };

    return (
      <ModeWidgetContext.Provider value={contextValue}>
        <Box
          ref={ref}
          variant="surface"
          rounded="md"
          border
          shadow="sm"
          className={cn(sizeClasses[size], 'relative', className)}
          {...props}
        >
          {(title || icon || actions) && (
            <ModeWidgetHeader title={title} icon={icon} actions={actions} />
          )}
          <ModeWidgetContent>
            {loading && <ModeWidgetLoading />}
            {error && <ModeWidgetError error={error} />}
            {empty && !loading && !error && <ModeWidgetEmpty />}
            {!loading && !error && !empty && children}
          </ModeWidgetContent>
        </Box>
      </ModeWidgetContext.Provider>
    );
  }
);

ModeWidget.displayName = 'ModeWidget';

// Widget Header
interface ModeWidgetHeaderProps {
  title?: string;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
}

const ModeWidgetHeader: React.FC<ModeWidgetHeaderProps> = ({ title, icon, actions }) => {
  return (
    <Flex
      align="center"
      justify="between"
      padding="sm"
      className="border-b border-[var(--mode-border)]"
    >
      <Flex align="center" gap="sm">
        {icon && <Box className="text-[var(--mode-text-secondary)]">{icon}</Box>}
        {title && (
          <Text size="sm" weight="medium">
            {title}
          </Text>
        )}
      </Flex>
      {actions && <Box>{actions}</Box>}
    </Flex>
  );
};

// Widget Content
interface ModeWidgetContentProps extends React.HTMLAttributes<HTMLDivElement> {}

const ModeWidgetContent = forwardRef<HTMLDivElement, ModeWidgetContentProps>(
  ({ className, ...props }, ref) => {
    return <Box ref={ref} padding="md" className={className} {...props} />;
  }
);

ModeWidgetContent.displayName = 'ModeWidgetContent';

// Loading State
const ModeWidgetLoading: React.FC = () => {
  return (
    <Flex align="center" justify="center" className="h-32">
      <Box className="animate-spin">
        <svg
          className="w-8 h-8 text-[var(--mode-primary)]"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
          />
        </svg>
      </Box>
    </Flex>
  );
};

// Error State
interface ModeWidgetErrorProps {
  error: boolean | string;
}

const ModeWidgetError: React.FC<ModeWidgetErrorProps> = ({ error }) => {
  const message = typeof error === 'string' ? error : 'An error occurred';

  return (
    <Flex direction="col" align="center" justify="center" gap="sm" className="h-32">
      <Box className="text-[var(--mode-error)]">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
          <path d="M12 8v4m0 4h.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
        </svg>
      </Box>
      <Text size="sm" color="error">
        {message}
      </Text>
    </Flex>
  );
};

// Empty State
const ModeWidgetEmpty: React.FC = () => {
  return (
    <Flex direction="col" align="center" justify="center" gap="sm" className="h-32">
      <Box className="text-[var(--mode-text-muted)]">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <rect
            x="3"
            y="3"
            width="18"
            height="18"
            rx="2"
            stroke="currentColor"
            strokeWidth="2"
            strokeDasharray="3 3"
          />
        </svg>
      </Box>
      <Text size="sm" color="muted">
        No data available
      </Text>
    </Flex>
  );
};

// Widget Actions (for header)
interface ModeWidgetActionsProps extends React.HTMLAttributes<HTMLDivElement> {}

export const ModeWidgetActions = forwardRef<HTMLDivElement, ModeWidgetActionsProps>(
  ({ className, ...props }, ref) => {
    return <Flex ref={ref} align="center" gap="xs" className={className} {...props} />;
  }
);

ModeWidgetActions.displayName = 'ModeWidgetActions';

// Export compound component
export const ModeWidgetCompound = Object.assign(ModeWidget, {
  Actions: ModeWidgetActions,
});
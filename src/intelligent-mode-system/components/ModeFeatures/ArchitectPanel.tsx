import React, { useState } from 'react';
import { Mode } from '../../types';
import { architectFeatureConfig } from '../../modes/architect.mode';

interface ArchitectPanelProps {
  mode: Mode;
  context?: any;
}

export const ArchitectPanel: React.FC<ArchitectPanelProps> = ({ mode, context }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'dependencies' | 'patterns'>('overview');
  const [showVisualization, setShowVisualization] = useState(true);

  return (
    <div className="architect-panel bg-gray-900 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <span>{mode.icon}</span>
          <span>Architecture Overview</span>
        </h3>
        <div className="flex gap-2">
          <button
            onClick={() => setShowVisualization(!showVisualization)}
            className="text-sm px-3 py-1 bg-gray-800 rounded hover:bg-gray-700"
          >
            {showVisualization ? 'Hide' : 'Show'} Visualization
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex gap-2 mb-4 border-b border-gray-700">
        <button
          onClick={() => setActiveTab('overview')}
          className={`px-3 py-2 text-sm font-medium transition-colors ${
            activeTab === 'overview'
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          System Overview
        </button>
        <button
          onClick={() => setActiveTab('dependencies')}
          className={`px-3 py-2 text-sm font-medium transition-colors ${
            activeTab === 'dependencies'
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Dependencies
        </button>
        <button
          onClick={() => setActiveTab('patterns')}
          className={`px-3 py-2 text-sm font-medium transition-colors ${
            activeTab === 'patterns'
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Patterns
        </button>
      </div>

      {/* Content Area */}
      <div className="content-area">
        {activeTab === 'overview' && (
          <div className="space-y-4">
            {showVisualization && (
              <div className="bg-gray-800 rounded p-4 h-64 flex items-center justify-center">
                <div className="text-gray-500 text-center">
                  <div className="text-4xl mb-2">🏗️</div>
                  <p>System Architecture Visualization</p>
                  <p className="text-sm text-gray-600 mt-2">
                    Component diagram would render here
                  </p>
                </div>
              </div>
            )}
            
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-gray-800 rounded p-3">
                <h4 className="text-sm font-semibold text-gray-400 mb-2">Components</h4>
                <div className="text-2xl font-bold text-blue-400">24</div>
                <div className="text-xs text-gray-500">Active modules</div>
              </div>
              <div className="bg-gray-800 rounded p-3">
                <h4 className="text-sm font-semibold text-gray-400 mb-2">Connections</h4>
                <div className="text-2xl font-bold text-green-400">67</div>
                <div className="text-xs text-gray-500">Inter-module links</div>
              </div>
              <div className="bg-gray-800 rounded p-3">
                <h4 className="text-sm font-semibold text-gray-400 mb-2">Complexity</h4>
                <div className="text-2xl font-bold text-yellow-400">Medium</div>
                <div className="text-xs text-gray-500">Overall score</div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'dependencies' && (
          <div className="space-y-4">
            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-sm font-semibold text-gray-400 mb-3">Dependency Analysis</h4>
              
              {architectFeatureConfig.dependencyAnalyzer.circularDependencyDetection && (
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-300">Circular Dependencies</span>
                    <span className="text-sm text-red-400">2 found</span>
                  </div>
                  <div className="text-xs text-gray-500 bg-red-900/20 rounded p-2">
                    <div>• ComponentA → ServiceB → ComponentA</div>
                    <div>• ModuleX → ModuleY → ModuleZ → ModuleX</div>
                  </div>
                </div>
              )}

              {architectFeatureConfig.dependencyAnalyzer.unusedDependencyDetection && (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-300">Unused Dependencies</span>
                    <span className="text-sm text-yellow-400">5 detected</span>
                  </div>
                  <div className="text-xs text-gray-500">
                    Consider removing unused dependencies to reduce bundle size
                  </div>
                </div>
              )}
            </div>

            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-sm font-semibold text-gray-400 mb-2">Dependency Graph</h4>
              <div className="h-48 flex items-center justify-center text-gray-600">
                Interactive dependency graph visualization
              </div>
            </div>
          </div>
        )}

        {activeTab === 'patterns' && (
          <div className="space-y-4">
            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-sm font-semibold text-gray-400 mb-3">Detected Patterns</h4>
              <div className="space-y-2">
                {architectFeatureConfig.architecturePatterns.patternLibrary.slice(0, 3).map((pattern) => (
                  <div key={pattern} className="flex items-center justify-between">
                    <span className="text-sm">{pattern}</span>
                    <span className="text-xs text-green-400">✓ Applied</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-sm font-semibold text-gray-400 mb-3">Suggested Patterns</h4>
              <div className="space-y-2">
                {architectFeatureConfig.architecturePatterns.patternLibrary.slice(3, 6).map((pattern) => (
                  <div key={pattern} className="flex items-center justify-between">
                    <span className="text-sm">{pattern}</span>
                    <button className="text-xs text-blue-400 hover:text-blue-300">
                      Apply →
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ArchitectPanel;
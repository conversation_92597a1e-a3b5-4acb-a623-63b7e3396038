// Export all mode-specific panels
export { ArchitectPanel } from './ArchitectPanel';
export { DebugPanel } from './DebugPanel';
export { ReviewPanel } from './ReviewPanel';

// Import panels
import { ArchitectPanel } from './ArchitectPanel';
import { DebugPanel } from './DebugPanel';
import { ReviewPanel } from './ReviewPanel';
import { Mode, ModeId } from '../../types';
import React from 'react';

// Mode Panel Map
export const modePanelMap: Record<ModeId, React.FC<{ mode: Mode; context?: any }>> = {
  architect: ArchitectPanel,
  debug: DebugPanel,
  review: ReviewPanel,
  deploy: () => <div>Deploy Panel - Coming Soon</div>,
  experiment: () => <div>Experiment Panel - Coming Soon</div>,
  learn: () => <div>Learn Panel - Coming Soon</div>,
};

// Dynamic Mode Panel Component
interface ModePanelProps {
  mode: Mode;
  context?: any;
}

export const ModePanel: React.FC<ModePanelProps> = ({ mode, context }) => {
  const PanelComponent = modePanelMap[mode.id];
  
  if (!PanelComponent) {
    return (
      <div className="mode-panel bg-gray-900 rounded-lg p-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <span>{mode.icon}</span>
          <span>{mode.name}</span>
        </h3>
        <p className="text-gray-500 mt-2">
          No specific panel available for this mode yet.
        </p>
      </div>
    );
  }

  return <PanelComponent mode={mode} context={context} />;
};
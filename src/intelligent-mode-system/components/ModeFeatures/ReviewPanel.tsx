import React, { useState } from 'react';
import { Mode } from '../../types';
import { reviewFeatureConfig } from '../../modes/review.mode';

interface ReviewPanelProps {
  mode: Mode;
  context?: any;
}

interface ReviewItem {
  id: string;
  type: 'error' | 'warning' | 'suggestion' | 'info';
  file: string;
  line: number;
  message: string;
  severity: 'critical' | 'major' | 'minor';
  autoFixable: boolean;
}

export const ReviewPanel: React.FC<ReviewPanelProps> = ({ mode, context }) => {
  const [activeTab, setActiveTab] = useState<'quality' | 'diff' | 'checklist'>('quality');
  const [showAutoFix, setShowAutoFix] = useState(true);
  
  const reviewItems: ReviewItem[] = [
    {
      id: '1',
      type: 'error',
      file: 'components/Button.tsx',
      line: 23,
      message: 'Missing prop types definition',
      severity: 'major',
      autoFixable: true,
    },
    {
      id: '2',
      type: 'warning',
      file: 'utils/helpers.ts',
      line: 45,
      message: 'Function complexity is too high (15)',
      severity: 'minor',
      autoFixable: false,
    },
    {
      id: '3',
      type: 'suggestion',
      file: 'api/client.ts',
      line: 12,
      message: 'Consider using async/await instead of promises',
      severity: 'minor',
      autoFixable: true,
    },
  ];

  const checklist = [
    { id: '1', item: 'Code follows project style guidelines', checked: true },
    { id: '2', item: 'Tests are included and passing', checked: true },
    { id: '3', item: 'Documentation is updated', checked: false },
    { id: '4', item: 'No security vulnerabilities introduced', checked: true },
    { id: '5', item: 'Performance impact considered', checked: true },
    { id: '6', item: 'Error handling is appropriate', checked: false },
    { id: '7', item: 'Code is maintainable and readable', checked: true },
  ];

  return (
    <div className="review-panel bg-gray-900 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <span>{mode.icon}</span>
          <span>Code Review Assistant</span>
        </h3>
        <div className="flex gap-2">
          {reviewFeatureConfig.qualityChecker.autoFix && (
            <button
              onClick={() => setShowAutoFix(!showAutoFix)}
              className="text-sm px-3 py-1 bg-gray-800 rounded hover:bg-gray-700"
            >
              {showAutoFix ? 'Hide' : 'Show'} Auto-Fix
            </button>
          )}
          <button className="text-sm px-3 py-1 bg-purple-600 rounded hover:bg-purple-700">
            Approve Changes
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex gap-2 mb-4 border-b border-gray-700">
        <button
          onClick={() => setActiveTab('quality')}
          className={`px-3 py-2 text-sm font-medium transition-colors ${
            activeTab === 'quality'
              ? 'text-purple-400 border-b-2 border-purple-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Quality Analysis
        </button>
        <button
          onClick={() => setActiveTab('diff')}
          className={`px-3 py-2 text-sm font-medium transition-colors ${
            activeTab === 'diff'
              ? 'text-purple-400 border-b-2 border-purple-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Diff View
        </button>
        <button
          onClick={() => setActiveTab('checklist')}
          className={`px-3 py-2 text-sm font-medium transition-colors ${
            activeTab === 'checklist'
              ? 'text-purple-400 border-b-2 border-purple-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Review Checklist
        </button>
      </div>

      {/* Content Area */}
      <div className="content-area">
        {activeTab === 'quality' && (
          <div className="space-y-4">
            {/* Quality Metrics */}
            <div className="grid grid-cols-4 gap-3">
              <div className="bg-gray-800 rounded p-3 text-center">
                <div className="text-2xl font-bold text-green-400">A</div>
                <div className="text-xs text-gray-500">Overall Grade</div>
              </div>
              <div className="bg-gray-800 rounded p-3 text-center">
                <div className="text-2xl font-bold text-yellow-400">78</div>
                <div className="text-xs text-gray-500">Maintainability</div>
              </div>
              <div className="bg-gray-800 rounded p-3 text-center">
                <div className="text-2xl font-bold text-green-400">92%</div>
                <div className="text-xs text-gray-500">Coverage</div>
              </div>
              <div className="bg-gray-800 rounded p-3 text-center">
                <div className="text-2xl font-bold text-purple-400">3</div>
                <div className="text-xs text-gray-500">Issues Found</div>
              </div>
            </div>

            {/* Review Items */}
            <div className="space-y-2">
              {reviewItems.map(item => (
                <div key={item.id} className="bg-gray-800 rounded p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className={`text-xs px-2 py-0.5 rounded ${
                          item.type === 'error' ? 'bg-red-900 text-red-300' :
                          item.type === 'warning' ? 'bg-yellow-900 text-yellow-300' :
                          item.type === 'suggestion' ? 'bg-blue-900 text-blue-300' :
                          'bg-gray-700 text-gray-300'
                        }`}>
                          {item.type}
                        </span>
                        <span className="text-xs text-gray-500">
                          {item.file}:{item.line}
                        </span>
                      </div>
                      <div className="text-sm text-gray-300">{item.message}</div>
                    </div>
                    {item.autoFixable && showAutoFix && (
                      <button className="text-xs text-blue-400 hover:text-blue-300 ml-3">
                        Auto-fix
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {reviewFeatureConfig.qualityChecker.autoFix && (
              <button className="w-full px-4 py-2 bg-blue-600 rounded hover:bg-blue-700 text-sm">
                Apply All Auto-fixes
              </button>
            )}
          </div>
        )}

        {activeTab === 'diff' && (
          <div className="space-y-4">
            <div className="bg-gray-800 rounded p-3">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-semibold text-gray-400">Changed Files</h4>
                <span className="text-xs text-gray-500">3 files changed</span>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-gray-900 rounded">
                  <span className="text-sm font-mono">src/components/Button.tsx</span>
                  <span className="text-xs text-green-400">+15 -3</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-gray-900 rounded">
                  <span className="text-sm font-mono">src/utils/helpers.ts</span>
                  <span className="text-xs text-yellow-400">+8 -12</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-gray-900 rounded">
                  <span className="text-sm font-mono">src/api/client.ts</span>
                  <span className="text-xs text-red-400">+2 -45</span>
                </div>
              </div>
            </div>

            {reviewFeatureConfig.diffViewer.sideBySide && (
              <div className="bg-gray-800 rounded p-3">
                <h4 className="text-sm font-semibold text-gray-400 mb-2">Diff View Options</h4>
                <div className="flex gap-2">
                  <button className="text-sm px-3 py-1 bg-gray-700 rounded">Side by Side</button>
                  <button className="text-sm px-3 py-1 bg-gray-900 rounded">Unified</button>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'checklist' && (
          <div className="space-y-4">
            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-sm font-semibold text-gray-400 mb-3">Review Checklist</h4>
              <div className="space-y-2">
                {checklist.map(item => (
                  <label key={item.id} className="flex items-center gap-3 p-2 hover:bg-gray-700 rounded cursor-pointer">
                    <input
                      type="checkbox"
                      checked={item.checked}
                      className="rounded border-gray-600"
                      onChange={() => {}}
                    />
                    <span className={`text-sm ${item.checked ? 'text-gray-300' : 'text-gray-500'}`}>
                      {item.item}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-sm font-semibold text-gray-400 mb-2">Completion Status</h4>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-300">Checklist Progress</span>
                <span className="text-sm text-green-400">
                  {checklist.filter(i => i.checked).length} / {checklist.length}
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-green-400 h-2 rounded-full transition-all"
                  style={{ width: `${(checklist.filter(i => i.checked).length / checklist.length) * 100}%` }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReviewPanel;
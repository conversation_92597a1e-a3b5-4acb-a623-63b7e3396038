import React, { useState } from 'react';
import { Mode } from '../../types';
import { debugFeatureConfig } from '../../modes/debug.mode';

interface DebugPanelProps {
  mode: Mode;
  context?: any;
}

interface Breakpoint {
  id: string;
  file: string;
  line: number;
  condition?: string;
  enabled: boolean;
  hitCount: number;
}

export const DebugPanel: React.FC<DebugPanelProps> = ({ mode, context }) => {
  const [activeView, setActiveView] = useState<'breakpoints' | 'errors' | 'performance'>('breakpoints');
  const [breakpoints, setBreakpoints] = useState<Breakpoint[]>([
    { id: '1', file: 'app.tsx', line: 42, enabled: true, hitCount: 3 },
    { id: '2', file: 'utils.ts', line: 15, condition: 'value > 100', enabled: true, hitCount: 0 },
    { id: '3', file: 'api/handler.ts', line: 78, enabled: false, hitCount: 12 },
  ]);

  const toggleBreakpoint = (id: string) => {
    setBreakpoints(prev => 
      prev.map(bp => bp.id === id ? { ...bp, enabled: !bp.enabled } : bp)
    );
  };

  return (
    <div className="debug-panel bg-gray-900 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <span>{mode.icon}</span>
          <span>Debug Console</span>
        </h3>
        <div className="flex gap-2">
          <button className="text-sm px-3 py-1 bg-red-600 rounded hover:bg-red-700">
            Clear All
          </button>
          <button className="text-sm px-3 py-1 bg-gray-800 rounded hover:bg-gray-700">
            Export Log
          </button>
        </div>
      </div>

      {/* View Selector */}
      <div className="flex gap-2 mb-4 border-b border-gray-700">
        <button
          onClick={() => setActiveView('breakpoints')}
          className={`px-3 py-2 text-sm font-medium transition-colors ${
            activeView === 'breakpoints'
              ? 'text-red-400 border-b-2 border-red-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Breakpoints
        </button>
        <button
          onClick={() => setActiveView('errors')}
          className={`px-3 py-2 text-sm font-medium transition-colors ${
            activeView === 'errors'
              ? 'text-red-400 border-b-2 border-red-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Error Analysis
        </button>
        <button
          onClick={() => setActiveView('performance')}
          className={`px-3 py-2 text-sm font-medium transition-colors ${
            activeView === 'performance'
              ? 'text-red-400 border-b-2 border-red-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Performance
        </button>
      </div>

      {/* Content Area */}
      <div className="content-area">
        {activeView === 'breakpoints' && (
          <div className="space-y-2">
            <div className="bg-gray-800 rounded p-3">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-semibold text-gray-400">Active Breakpoints</h4>
                <span className="text-xs text-gray-500">
                  {breakpoints.filter(bp => bp.enabled).length} / {breakpoints.length} enabled
                </span>
              </div>
              
              <div className="space-y-2">
                {breakpoints.map(bp => (
                  <div key={bp.id} className="flex items-center justify-between p-2 bg-gray-900 rounded">
                    <div className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        checked={bp.enabled}
                        onChange={() => toggleBreakpoint(bp.id)}
                        className="rounded border-gray-600"
                      />
                      <div>
                        <div className="text-sm font-mono">
                          {bp.file}:{bp.line}
                        </div>
                        {bp.condition && (
                          <div className="text-xs text-gray-500">
                            Condition: {bp.condition}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      Hit {bp.hitCount} times
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {debugFeatureConfig.breakpointManager.logPoints && (
              <div className="bg-gray-800 rounded p-3">
                <h4 className="text-sm font-semibold text-gray-400 mb-2">Log Points</h4>
                <button className="text-sm text-blue-400 hover:text-blue-300">
                  + Add Log Point
                </button>
              </div>
            )}
          </div>
        )}

        {activeView === 'errors' && (
          <div className="space-y-4">
            <div className="bg-gray-800 rounded p-3">
              <h4 className="text-sm font-semibold text-gray-400 mb-3">Current Errors</h4>
              
              <div className="space-y-2">
                <div className="bg-red-900/20 border border-red-800 rounded p-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="text-sm font-semibold text-red-400">TypeError</div>
                      <div className="text-sm text-gray-300 mt-1">
                        Cannot read property 'map' of undefined
                      </div>
                      <div className="text-xs text-gray-500 mt-2 font-mono">
                        at App.tsx:156:24
                      </div>
                    </div>
                    <button className="text-xs text-blue-400 hover:text-blue-300">
                      Fix →
                    </button>
                  </div>
                </div>

                <div className="bg-yellow-900/20 border border-yellow-800 rounded p-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="text-sm font-semibold text-yellow-400">Warning</div>
                      <div className="text-sm text-gray-300 mt-1">
                        React Hook useEffect has missing dependencies
                      </div>
                      <div className="text-xs text-gray-500 mt-2 font-mono">
                        at hooks/useData.ts:23:6
                      </div>
                    </div>
                    <button className="text-xs text-blue-400 hover:text-blue-300">
                      Fix →
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {debugFeatureConfig.errorDetector.errorPatternRecognition && (
              <div className="bg-gray-800 rounded p-3">
                <h4 className="text-sm font-semibold text-gray-400 mb-2">Error Patterns</h4>
                <div className="text-sm text-gray-300">
                  <div>• Async/Await misuse detected (3 occurrences)</div>
                  <div>• Potential memory leak in useEffect</div>
                  <div>• Missing error boundaries</div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeView === 'performance' && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-800 rounded p-3">
                <h4 className="text-sm font-semibold text-gray-400 mb-2">CPU Usage</h4>
                <div className="text-2xl font-bold text-green-400">45%</div>
                <div className="text-xs text-gray-500">Average last 5 min</div>
              </div>
              <div className="bg-gray-800 rounded p-3">
                <h4 className="text-sm font-semibold text-gray-400 mb-2">Memory</h4>
                <div className="text-2xl font-bold text-yellow-400">1.2GB</div>
                <div className="text-xs text-gray-500">67% of heap used</div>
              </div>
            </div>

            {debugFeatureConfig.performanceProfiler.cpuProfiling && (
              <div className="bg-gray-800 rounded p-3">
                <h4 className="text-sm font-semibold text-gray-400 mb-3">Performance Hotspots</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-mono">calculateLayout()</span>
                    <span className="text-sm text-red-400">234ms</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-mono">renderComponent()</span>
                    <span className="text-sm text-yellow-400">89ms</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-mono">fetchData()</span>
                    <span className="text-sm text-green-400">45ms</span>
                  </div>
                </div>
              </div>
            )}

            <button className="w-full px-4 py-2 bg-blue-600 rounded hover:bg-blue-700 text-sm">
              Start Profiling Session
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DebugPanel;
/* Enhanced Grid System for ClaudeCodeSession */
/* Semantic layout with named grid areas and container queries */

/* Main session layout container */
.session-layout {
  display: grid;
  grid-template-areas: 
    "header header header"
    "sidebar content preview"
    "input input input";
  grid-template-columns: 300px 1fr 400px;
  grid-template-rows: auto 1fr auto;
  gap: 1rem;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

/* Grid area assignments */
.session-header {
  grid-area: header;
  z-index: 10;
}

.session-sidebar {
  grid-area: sidebar;
  overflow-y: auto;
  border-right: 1px solid hsl(var(--border));
}

.session-content {
  grid-area: content;
  overflow-y: auto;
  position: relative;
}

.session-preview {
  grid-area: preview;
  overflow-y: auto;
  border-left: 1px solid hsl(var(--border));
}

.session-input {
  grid-area: input;
  border-top: 1px solid hsl(var(--border));
  z-index: 5;
}

/* Container query responsive breakpoints */
@container (max-width: 1024px) {
  .session-layout {
    grid-template-areas:
      "header"
      "content"
      "input";
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
  }
  
  .session-sidebar,
  .session-preview {
    display: none;
  }
}

@container (max-width: 768px) {
  .session-layout {
    gap: 0.5rem;
  }
  
  .session-content {
    padding: 0.5rem;
  }
}

/* Mode-specific layout variations */
.session-layout.architect-mode {
  grid-template-columns: 350px 1fr 450px;
}

.session-layout.architect-mode .session-sidebar {
  background: hsl(var(--blue-50));
  border-right-color: hsl(var(--blue-200));
}

.session-layout.debug-mode {
  grid-template-areas: 
    "header header"
    "content preview"
    "input input";
  grid-template-columns: 1fr 1fr;
}

.session-layout.debug-mode .session-sidebar {
  display: none;
}

.session-layout.review-mode {
  grid-template-columns: 250px 1fr 300px;
}

.session-layout.review-mode .session-content {
  background: hsl(var(--green-50));
}

/* Adaptive grid for context widgets */
.context-widget-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  padding: 1rem;
}

.context-widget-container {
  display: flex;
  flex-direction: column;
  min-height: 120px;
  border-radius: 0.5rem;
  border: 1px solid hsl(var(--border));
  background: hsl(var(--card));
  transition: all 0.2s ease;
}

.context-widget-container:hover {
  border-color: hsl(var(--primary));
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.1);
}

/* Responsive context widget layouts */
@container (max-width: 640px) {
  .context-widget-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 0.5rem;
  }
}

/* Smart panel positioning */
.smart-panel {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.smart-panel.floating {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 320px;
  max-height: 400px;
  z-index: 20;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px hsl(var(--foreground) / 0.1);
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
}

.smart-panel.sidebar {
  border-radius: 0;
  box-shadow: none;
}

/* Performance optimizations */
.session-content,
.session-sidebar,
.session-preview {
  contain: layout style paint;
  will-change: scroll-position;
}

/* Virtualized list container */
.virtualized-container {
  height: 100%;
  overflow: auto;
  contain: strict;
}

/* Focus management for accessibility */
.session-layout:focus-within .session-content {
  outline: 2px solid hsl(var(--primary));
  outline-offset: -2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .session-layout {
    gap: 2px;
  }
  
  .session-sidebar,
  .session-preview {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .context-widget-container,
  .smart-panel {
    transition: none;
  }
}

/* Print styles */
@media print {
  .session-layout {
    grid-template-areas: "content";
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .session-sidebar,
  .session-preview,
  .session-input {
    display: none;
  }
}
import { useMemo } from 'react';
import { useModeContext } from '@/contexts/ModeContext';
import { useIsMobile, useIsTablet, useIsDesktop } from '@/intelligent-mode-system/utils/responsive';
import { Mode } from '@/intelligent-mode-system/types/mode.types';

export interface LayoutConfiguration {
  layout: 'stack' | 'hybrid' | 'three-column' | 'split' | 'document-flow';
  sidebarPosition: 'left' | 'right' | 'bottom' | 'collapsible' | 'floating' | 'hidden';
  previewMode: 'modal' | 'split' | 'side-by-side' | 'overlay' | 'inline';
  gridColumns: 1 | 2 | 3;
  contextWidgetPosition: 'sidebar' | 'floating' | 'inline' | 'bottom-sheet';
  density: 'compact' | 'comfortable' | 'spacious';
  primaryContentWidth: string;
  sidebarWidth: string;
  previewWidth: string;
  gridGap: string;
  containerClass: string;
}

export interface ResponsiveLayoutConfig {
  mobile: LayoutConfiguration;
  tablet: LayoutConfiguration;
  desktop: LayoutConfiguration;
}

const getModeSpecificLayout = (mode: Mode, deviceType: 'mobile' | 'tablet' | 'desktop'): Partial<LayoutConfiguration> => {
  const baseConfig: Record<string, Partial<LayoutConfiguration>> = {
    architect: {
      sidebarPosition: 'left',
      previewMode: 'side-by-side',
      contextWidgetPosition: 'sidebar',
      density: 'comfortable',
      sidebarWidth: '350px',
      previewWidth: '450px',
      containerClass: 'architect-mode'
    },
    debug: {
      layout: 'split',
      sidebarPosition: 'hidden',
      previewMode: 'split',
      contextWidgetPosition: 'floating',
      density: 'compact',
      previewWidth: '50%',
      containerClass: 'debug-mode'
    },
    review: {
      layout: 'document-flow',
      sidebarPosition: 'left',
      previewMode: 'inline',
      contextWidgetPosition: 'sidebar',
      density: 'spacious',
      sidebarWidth: '250px',
      previewWidth: '300px',
      containerClass: 'review-mode'
    },
    deploy: {
      layout: 'three-column',
      sidebarPosition: 'left',
      previewMode: 'side-by-side',
      contextWidgetPosition: 'sidebar',
      density: 'comfortable',
      sidebarWidth: '300px',
      previewWidth: '400px',
      containerClass: 'deploy-mode'
    }
  };

  const modeConfig = baseConfig[mode.id] || baseConfig.architect;

  // Device-specific overrides
  if (deviceType === 'mobile') {
    return {
      ...modeConfig,
      layout: 'stack',
      sidebarPosition: 'bottom',
      previewMode: 'modal',
      contextWidgetPosition: 'bottom-sheet',
      density: 'compact'
    };
  }

  if (deviceType === 'tablet') {
    return {
      ...modeConfig,
      layout: 'hybrid',
      sidebarPosition: 'collapsible',
      previewMode: modeConfig.previewMode === 'side-by-side' ? 'split' : modeConfig.previewMode,
      contextWidgetPosition: 'floating'
    };
  }

  return modeConfig;
};

const getDefaultLayoutConfig = (deviceType: 'mobile' | 'tablet' | 'desktop'): LayoutConfiguration => {
  const baseConfig: LayoutConfiguration = {
    layout: 'three-column',
    sidebarPosition: 'left',
    previewMode: 'side-by-side',
    gridColumns: 3,
    contextWidgetPosition: 'sidebar',
    density: 'comfortable',
    primaryContentWidth: '1fr',
    sidebarWidth: '300px',
    previewWidth: '400px',
    gridGap: '1rem',
    containerClass: 'session-layout'
  };

  if (deviceType === 'mobile') {
    return {
      ...baseConfig,
      layout: 'stack',
      sidebarPosition: 'bottom',
      previewMode: 'modal',
      gridColumns: 1,
      contextWidgetPosition: 'bottom-sheet',
      density: 'compact',
      gridGap: '0.5rem'
    };
  }

  if (deviceType === 'tablet') {
    return {
      ...baseConfig,
      layout: 'hybrid',
      sidebarPosition: 'collapsible',
      previewMode: 'split',
      gridColumns: 2,
      contextWidgetPosition: 'floating',
      sidebarWidth: '280px',
      previewWidth: '350px'
    };
  }

  return baseConfig;
};

export const useAdaptiveLayout = (): LayoutConfiguration => {
  const { currentMode } = useModeContext();
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isDesktop = useIsDesktop();
  const deviceType = isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop';

  return useMemo(() => {
    const defaultConfig = getDefaultLayoutConfig(deviceType);
    const modeSpecificConfig = getModeSpecificLayout(currentMode, deviceType);
    
    return {
      ...defaultConfig,
      ...modeSpecificConfig
    };
  }, [currentMode, deviceType]);
};

export const useLayoutClasses = (): string => {
  const layout = useAdaptiveLayout();
  const { currentMode } = useModeContext();
  
  return useMemo(() => {
    const classes = [
      layout.containerClass,
      `layout-${layout.layout}`,
      `sidebar-${layout.sidebarPosition}`,
      `preview-${layout.previewMode}`,
      `density-${layout.density}`,
      `mode-${currentMode.id}`
    ];
    
    return classes.filter(Boolean).join(' ');
  }, [layout, currentMode.id]);
};

export const useGridStyles = (): React.CSSProperties => {
  const layout = useAdaptiveLayout();
  
  return useMemo(() => {
    const styles: React.CSSProperties = {
      gap: layout.gridGap
    };

    if (layout.layout === 'three-column') {
      styles.gridTemplateColumns = `${layout.sidebarWidth} ${layout.primaryContentWidth} ${layout.previewWidth}`;
    } else if (layout.layout === 'split') {
      styles.gridTemplateColumns = `${layout.primaryContentWidth} ${layout.previewWidth}`;
    } else if (layout.layout === 'stack') {
      styles.gridTemplateColumns = '1fr';
    }

    return styles;
  }, [layout]);
};

// Hook for context widget positioning
export const useContextWidgetLayout = () => {
  const layout = useAdaptiveLayout();
  const isMobile = useIsMobile();
  
  return useMemo(() => {
    return {
      position: layout.contextWidgetPosition,
      shouldFloat: layout.contextWidgetPosition === 'floating',
      shouldCollapse: isMobile && layout.contextWidgetPosition !== 'bottom-sheet',
      containerClass: `context-widgets-${layout.contextWidgetPosition}`,
      gridColumns: isMobile ? 1 : layout.gridColumns === 3 ? 2 : 1
    };
  }, [layout, isMobile]);
};

// Performance optimization hook
export const useLayoutPerformance = () => {
  const layout = useAdaptiveLayout();
  
  return useMemo(() => {
    return {
      shouldVirtualize: layout.density === 'compact',
      shouldLazyLoad: layout.layout === 'three-column',
      shouldPreload: layout.previewMode === 'side-by-side',
      optimizeFor: layout.density === 'compact' ? 'speed' : 'quality'
    };
  }, [layout]);
};
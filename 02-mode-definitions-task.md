# Task 2: Mode Definitions & Context Providers
**Priority: High | Estimated Time: 3-4 days**

## Objective
Implement the six core modes (Architect, Debug, Review, Deploy, Experiment, Learn) with their specific context providers and inference rules.

## Deliverables
1. Complete mode definitions with contextual prompts
2. Mode-specific context providers
3. Inference rules for each mode
4. Mode transition logic
5. Context-aware prompt templates

## Implementation Tasks

### 2.1 Mode Definitions
**File: `src/lib/modes/definitions/`**

Create individual files for each mode:
- `ArchitectMode.ts`
- `DebugMode.ts` 
- `ReviewMode.ts`
- `DeployMode.ts`
- `ExperimentMode.ts`
- `LearnMode.ts`

### 2.2 Architect Mode Implementation
**File: `src/lib/modes/definitions/ArchitectMode.ts`**

```typescript
export const ArchitectModeDefinition: ModeDefinition = {
  id: 'architect',
  name: 'Architect',
  description: 'System design, architecture planning, and technical decisions',
  icon: Building2,
  color: '#3b82f6',
  requirements: [
    { type: 'project_structure', priority: 'high' },
    { type: 'existing_architecture', priority: 'medium' },
    { type: 'requirements', priority: 'high' }
  ],
  contextProviders: [
    'ProjectStructureProvider',
    'ArchitectureAnalyzer',
    'RequirementsExtractor'
  ],
  inferenceRules: [
    detectMonolithToMicroservices,
    identifyScalabilityBottlenecks,
    suggestDesignPatterns
  ],
  prompts: ArchitectModePrompts,
  layout: {
    sidebar: 'left',
    panels: [
      { type: 'diagram', position: 'main' },
      { type: 'requirements', position: 'sidebar' },
      { type: 'decisions', position: 'bottom' }
    ]
  }
};

const ArchitectModePrompts = {
  systemAnalysis: `
    Analyze the current system architecture for {{projectName}}.
    
    Context:
    - Project Type: {{projectType}}
    - Current Architecture: {{currentArchitecture}}
    - Scale Requirements: {{scaleRequirements}}
    - Performance Constraints: {{performanceConstraints}}
    
    Focus on:
    1. Architectural patterns and their effectiveness
    2. Scalability bottlenecks and solutions
    3. Technology stack alignment with requirements
    4. Integration points and data flow
    
    Provide specific, actionable recommendations.
  `,
  // ... more prompts
};
```

### 2.3 Context Providers Implementation
**File: `src/lib/context/providers/`**

#### Project Structure Provider
```typescript
class ProjectStructureProvider implements ContextProvider {
  priority = 'high' as const;
  cacheTTL = 5 * 60 * 1000; // 5 minutes
  
  async gatherContext(inputs: RawContextInputs): Promise<Partial<SessionContext>> {
    const fileStructure = await this.analyzeFileStructure(inputs.projectPath);
    const projectType = await this.detectProjectType(fileStructure);
    const buildSystem = await this.detectBuildSystem(fileStructure);
    
    return {
      environment: {
        projectPath: inputs.projectPath,
        projectType,
        fileStructure,
        buildSystem
      }
    };
  }
  
  private async analyzeFileStructure(projectPath: string): Promise<FileTree> {
    // Implementation for scanning and analyzing project structure
  }
}
```

#### Code Analysis Provider
```typescript
class CodeAnalysisProvider implements ContextProvider {
  priority = 'medium' as const;
  cacheTTL = 2 * 60 * 1000; // 2 minutes
  
  async gatherContext(inputs: RawContextInputs): Promise<Partial<SessionContext>> {
    const codebaseAnalysis = await this.analyzeCodebase(inputs.projectPath);
    const patterns = await this.detectDesignPatterns(codebaseAnalysis);
    const complexity = await this.calculateComplexity(codebaseAnalysis);
    
    return {
      semantic: {
        codebase: codebaseAnalysis,
        patterns,
        complexity
      }
    };
  }
}
```

### 2.4 Inference Rules Implementation
**File: `src/lib/modes/inference/`**

```typescript
interface InferenceRule {
  name: string;
  condition: (context: Partial<SessionContext>) => boolean;
  enrich: (context: Partial<SessionContext>) => Promise<Partial<SessionContext>>;
  confidence: number;
}

// Architect Mode Rules
export const detectMonolithToMicroservices: InferenceRule = {
  name: 'detect_monolith_to_microservices',
  condition: (context) => {
    return context.environment?.projectType === 'monolith' &&
           context.semantic?.complexity?.score > 0.8;
  },
  enrich: async (context) => {
    return {
      semantic: {
        ...context.semantic,
        currentTask: 'microservices_migration',
        recommendations: ['Consider microservices architecture']
      }
    };
  },
  confidence: 0.85
};

// Debug Mode Rules
export const categorizeErrorType: InferenceRule = {
  name: 'categorize_error_type',
  condition: (context) => {
    return context.semantic?.codebase?.recentErrors?.length > 0;
  },
  enrich: async (context) => {
    const errors = context.semantic?.codebase?.recentErrors || [];
    const errorCategories = this.categorizeErrors(errors);
    
    return {
      semantic: {
        ...context.semantic,
        currentTask: 'debugging',
        errorAnalysis: errorCategories
      }
    };
  },
  confidence: 0.9
};
```

### 2.5 Mode Transition Engine
**File: `src/lib/modes/ModeTransitionEngine.ts`**

```typescript
class ModeTransitionEngine {
  private transitionRules: TransitionRule[];
  
  async suggestModeTransition(
    context: SessionContext,
    trigger: TransitionTrigger
  ): Promise<ModeTransitionSuggestion | null> {
    const applicableRules = this.findApplicableRules(context, trigger);
    if (applicableRules.length === 0) return null;
    
    const bestRule = this.selectBestRule(applicableRules, context);
    return this.createSuggestion(bestRule, context, trigger);
  }
  
  async validateTransition(
    fromMode: ModeId,
    toMode: ModeId,
    context: SessionContext
  ): Promise<boolean> {
    // Validation logic
  }
}

// Transition Rules
const TRANSITION_RULES: TransitionRule[] = [
  {
    fromMode: 'architect',
    toMode: 'debug',
    condition: (context, trigger) => {
      return trigger.type === 'error_detected' || 
             context.semantic.codebase.recentErrors?.length > 0;
    },
    confidence: 0.85,
    reasoning: 'Error detected - switching to debug mode for investigation'
  }
  // ... more rules
];
```

## Testing Requirements
- Unit tests for each mode definition
- Integration tests for context providers
- Tests for inference rule logic
- Mode transition validation tests

## Acceptance Criteria
- [ ] All six modes are properly defined with complete metadata
- [ ] Context providers gather relevant information for each mode
- [ ] Inference rules correctly identify mode-appropriate contexts
- [ ] Mode transitions work based on context changes
- [ ] Prompt templates are contextually relevant

## Implementation Prompts

### Prompt 1: Architect Mode Complete Implementation
```
Implement a complete Architect Mode for an AI development assistant. Include:

1. Mode definition with metadata, requirements, and layout
2. Context-aware prompt templates for system design tasks
3. Inference rules for detecting architectural needs
4. Integration with project structure analysis
5. Support for diagram generation and requirements tracking

Focus on enterprise-level system design workflows and provide actionable architectural guidance.
```

### Prompt 2: Debug Mode with Error Analysis
```
Create a Debug Mode implementation that excels at problem-solving. Requirements:

1. Error categorization and root cause analysis
2. Context providers for system state and logs
3. Inference rules for debugging strategy selection
4. Integration with stack trace analysis
5. Performance bottleneck detection

Include comprehensive error handling patterns and debugging methodologies.
```

### Prompt 3: Context Provider Architecture
```
Design and implement a context provider system for gathering development environment information. Include:

1. Project structure analysis (file types, dependencies, build systems)
2. Code analysis (complexity, patterns, recent changes)
3. Git integration (branch status, recent commits, conflicts)
4. User behavior tracking (preferences, success patterns)
5. Performance monitoring and caching

Ensure providers are modular, testable, and handle failures gracefully.
```

### Prompt 4: Mode Transition Intelligence
```
Implement an intelligent mode transition system that suggests optimal mode switches based on context changes. Features:

1. Rule-based transition suggestions
2. Context change detection
3. Transition validation and prerequisites
4. User pattern learning for personalized suggestions
5. Confidence scoring for recommendations

Include comprehensive transition rules for all mode combinations.
```
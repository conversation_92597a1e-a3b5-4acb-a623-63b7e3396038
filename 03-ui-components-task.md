# Task 3: Mode-Aware UI Components
**Priority: Medium | Estimated Time: 4-5 days**

## Objective
Create adaptive UI components that change based on the active mode, providing specialized interfaces and tools for each development workflow.

## Deliverables
1. Mode-aware layout system with dynamic panels
2. Context-sensitive command palette
3. Mode-specific widgets and tools
4. Smart input system with contextual suggestions
5. Mode transition UI with smooth animations

## Implementation Tasks

### 3.1 Mode Layout System
**File: `src/components/modes/ModeLayout.tsx`**

```typescript
interface ModeLayoutProps {
  mode: ModeDefinition;
  context: SessionContext;
  children: React.ReactNode;
}

const ModeLayout: React.FC<ModeLayoutProps> = ({ mode, context, children }) => {
  const layoutConfig = useMemo(() => 
    generateLayoutConfig(mode, context), [mode, context]
  );
  
  return (
    <div 
      className={`mode-layout mode-${mode.id}`}
      data-mode={mode.id}
      style={{
        '--mode-primary': mode.colors.primary,
        '--mode-secondary': mode.colors.secondary,
        '--mode-accent': mode.colors.accent
      }}
    >
      <ModeHeader mode={mode} context={context} />
      
      <div className="mode-content">
        {layoutConfig.sidebar && (
          <ModeSidebar 
            config={layoutConfig.sidebar}
            context={context}
            className="mode-sidebar"
          />
        )}
        
        <div className="mode-main">
          {children}
        </div>
        
        {layoutConfig.panels.map(panel => (
          <ModePanel
            key={panel.id}
            config={panel}
            context={context}
            className={`mode-panel-${panel.position}`}
          />
        ))}
      </div>
      
      <ModeFooter mode={mode} context={context} />
    </div>
  );
};
```

### 3.2 Mode Selector Component
**File: `src/components/modes/ModeSelector.tsx`**

```typescript
const ModeSelector: React.FC = () => {
  const { currentMode, availableModes, switchMode, isTransitioning } = useSessionMode();
  const [showTransitionSuggestion, setShowTransitionSuggestion] = useState(false);
  
  return (
    <div className="mode-selector">
      <Select 
        value={currentMode.id} 
        onValueChange={switchMode}
        disabled={isTransitioning}
      >
        <SelectTrigger className="mode-selector-trigger">
          <SelectValue>
            <div className="flex items-center gap-2">
              <currentMode.icon className="h-4 w-4" />
              <span>{currentMode.name}</span>
              {isTransitioning && <Loader2 className="h-3 w-3 animate-spin" />}
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {availableModes.map(mode => (
            <SelectItem key={mode.id} value={mode.id}>
              <div className="flex items-center gap-3 py-1">
                <mode.icon className="h-4 w-4" />
                <div>
                  <div className="font-medium">{mode.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {mode.description}
                  </div>
                </div>
                {mode.id !== currentMode.id && (
                  <Badge variant="outline" className="ml-auto">
                    Switch
                  </Badge>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      <ModeTransitionSuggestion 
        show={showTransitionSuggestion}
        onAccept={() => {/* handle transition */}}
        onDismiss={() => setShowTransitionSuggestion(false)}
      />
    </div>
  );
};
```

### 3.3 Context-Aware Command Palette
**File: `src/components/modes/ModeCommandPalette.tsx`**

```typescript
interface ModeCommandPaletteProps {
  mode: ModeDefinition;
  context: SessionContext;
  onCommandSelect: (command: ModeCommand) => void;
  onClose: () => void;
}

const ModeCommandPalette: React.FC<ModeCommandPaletteProps> = ({
  mode,
  context,
  onCommandSelect,
  onClose
}) => {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  
  const availableCommands = useMemo(() => {
    const baseCommands = mode.commands;
    const contextualCommands = generateContextualCommands(context);
    const recentCommands = getRecentCommands(context.user.preferences);
    
    return [
      ...prioritizeCommands(baseCommands, context),
      ...contextualCommands,
      ...recentCommands
    ].filter(cmd => 
      cmd.name.toLowerCase().includes(query.toLowerCase()) ||
      cmd.description.toLowerCase().includes(query.toLowerCase()) ||
      cmd.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );
  }, [mode, context, query]);
  
  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="mode-command-palette max-w-2xl">
        <div className="command-search">
          <Search className="search-icon h-4 w-4" />
          <Input
            placeholder={`Search ${mode.name} commands...`}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="search-input"
            autoFocus
          />
        </div>
        
        <div className="command-categories">
          {groupCommandsByCategory(availableCommands).map(([category, commands]) => (
            <div key={category} className="command-category">
              <div className="category-header">
                <h4 className="text-sm font-medium text-muted-foreground">
                  {category}
                </h4>
                <Badge variant="secondary" className="text-xs">
                  {commands.length}
                </Badge>
              </div>
              
              <div className="command-list">
                {commands.map((command, index) => (
                  <CommandItem
                    key={command.id}
                    command={command}
                    isSelected={index === selectedIndex}
                    onClick={() => onCommandSelect(command)}
                    context={context}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
        
        <div className="command-footer">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <kbd className="kbd">↑↓</kbd> Navigate
            <kbd className="kbd">Enter</kbd> Execute
            <kbd className="kbd">Esc</kbd> Close
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const CommandItem: React.FC<{
  command: ModeCommand;
  isSelected: boolean;
  onClick: () => void;
  context: SessionContext;
}> = ({ command, isSelected, onClick, context }) => {
  const relevanceScore = calculateCommandRelevance(command, context);
  
  return (
    <div
      className={cn(
        "command-item p-3 rounded-lg cursor-pointer transition-colors",
        isSelected ? "bg-accent" : "hover:bg-muted/50"
      )}
      onClick={onClick}
    >
      <div className="flex items-start gap-3">
        <div className="command-icon">
          <command.icon className="h-5 w-5" />
        </div>
        
        <div className="command-content flex-1">
          <div className="flex items-center gap-2">
            <span className="command-name font-medium">{command.name}</span>
            {command.shortcut && (
              <kbd className="kbd text-xs">{command.shortcut}</kbd>
            )}
            {relevanceScore > 0.8 && (
              <Badge variant="default" className="text-xs">
                Recommended
              </Badge>
            )}
          </div>
          
          <p className="command-description text-sm text-muted-foreground mt-1">
            {command.description}
          </p>
          
          {command.contextRelevance && (
            <div className="command-relevance mt-2">
              <div className="flex items-center gap-2">
                <div className="relevance-bar">
                  <div 
                    className="relevance-fill"
                    style={{ width: `${relevanceScore * 100}%` }}
                  />
                </div>
                <span className="text-xs text-muted-foreground">
                  {Math.round(relevanceScore * 100)}% relevant
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
```

### 3.4 Mode-Specific Widgets
**File: `src/components/modes/widgets/`**

#### Architect Mode Widgets
```typescript
// DiagramWidget.tsx
const DiagramWidget: React.FC<WidgetProps> = ({ context, config }) => {
  const [diagramType, setDiagramType] = useState<'system' | 'component' | 'sequence'>('system');
  const [diagramData, setDiagramData] = useState<DiagramData | null>(null);
  
  return (
    <Card className="diagram-widget h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Network className="h-4 w-4" />
          System Diagram
        </CardTitle>
        <div className="flex gap-2">
          <Select value={diagramType} onValueChange={setDiagramType}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="system">System</SelectItem>
              <SelectItem value="component">Component</SelectItem>
              <SelectItem value="sequence">Sequence</SelectItem>
            </SelectContent>
          </Select>
          <Button size="sm" variant="outline">
            <Download className="h-3 w-3 mr-1" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="diagram-canvas h-96">
          <MermaidDiagram 
            definition={diagramData?.definition || ''}
            type={diagramType}
          />
        </div>
      </CardContent>
    </Card>
  );
};

// RequirementsWidget.tsx
const RequirementsWidget: React.FC<WidgetProps> = ({ context }) => {
  const [requirements, setRequirements] = useState<Requirement[]>([]);
  const [filter, setFilter] = useState<'all' | 'functional' | 'non-functional'>('all');
  
  return (
    <Card className="requirements-widget h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckSquare className="h-4 w-4" />
          Requirements
        </CardTitle>
        <div className="flex gap-2">
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Requirements</SelectItem>
              <SelectItem value="functional">Functional</SelectItem>
              <SelectItem value="non-functional">Non-Functional</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="requirements-list space-y-2 max-h-80 overflow-y-auto">
          {requirements.map(req => (
            <RequirementItem key={req.id} requirement={req} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
```

#### Debug Mode Widgets
```typescript
// LogViewerWidget.tsx
const LogViewerWidget: React.FC<WidgetProps> = ({ context }) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filter, setFilter] = useState('');
  const [level, setLevel] = useState<LogLevel>('all');
  
  return (
    <Card className="log-viewer-widget h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          Application Logs
          <Badge variant="secondary">{logs.length}</Badge>
        </CardTitle>
        <div className="flex gap-2">
          <Input
            placeholder="Filter logs..."
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="flex-1"
          />
          <Select value={level} onValueChange={setLevel}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              <SelectItem value="error">Error</SelectItem>
              <SelectItem value="warn">Warning</SelectItem>
              <SelectItem value="info">Info</SelectItem>
              <SelectItem value="debug">Debug</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="log-container max-h-96 overflow-y-auto">
          <VirtualizedLogList 
            logs={logs}
            filter={filter}
            level={level}
          />
        </div>
      </CardContent>
    </Card>
  );
};

// StackTraceWidget.tsx
const StackTraceWidget: React.FC<WidgetProps> = ({ context }) => {
  const [stackTrace, setStackTrace] = useState<StackFrame[]>([]);
  const [selectedFrame, setSelectedFrame] = useState<number>(0);
  
  return (
    <Card className="stack-trace-widget h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Layers className="h-4 w-4" />
          Stack Trace
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="stack-frames space-y-1">
          {stackTrace.map((frame, index) => (
            <StackFrameItem
              key={index}
              frame={frame}
              isSelected={index === selectedFrame}
              onClick={() => setSelectedFrame(index)}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
```

### 3.5 Smart Input System
**File: `src/components/modes/SmartPromptInput.tsx`**

```typescript
const SmartPromptInput: React.FC<{
  mode: ModeDefinition;
  context: SessionContext;
  onSubmit: (prompt: string, options: PromptOptions) => void;
}> = ({ mode, context, onSubmit }) => {
  const [input, setInput] = useState('');
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  
  const debouncedInput = useDebounce(input, 300);
  
  useEffect(() => {
    if (debouncedInput.length > 2) {
      generateSmartSuggestions(debouncedInput, mode, context)
        .then(setSuggestions);
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  }, [debouncedInput, mode, context]);
  
  return (
    <div className="smart-prompt-input">
      <div className="input-container">
        <Textarea
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder={`Ask ${mode.name} mode anything...`}
          className="smart-input"
          rows={3}
        />
        
        <div className="input-actions">
          <div className="input-tools">
            <Button size="sm" variant="ghost">
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="ghost">
              <Mic className="h-4 w-4" />
            </Button>
          </div>
          
          <Button 
            onClick={() => onSubmit(input, {})}
            disabled={!input.trim()}
          >
            Send
          </Button>
        </div>
      </div>
      
      {showSuggestions && suggestions.length > 0 && (
        <SuggestionsList
          suggestions={suggestions}
          selectedIndex={selectedSuggestion}
          onSelect={(suggestion) => {
            setInput(suggestion.content);
            setShowSuggestions(false);
          }}
        />
      )}
      
      <ContextIndicators context={context} mode={mode} />
    </div>
  );
};
```

## Testing Requirements
- Component rendering tests for all modes
- Interaction tests for command palette
- Widget functionality tests
- Layout responsiveness tests
- Accessibility compliance tests

## Acceptance Criteria
- [ ] Layout adapts correctly for each mode
- [ ] Command palette shows contextually relevant commands
- [ ] Widgets display appropriate data for their mode
- [ ] Mode transitions are smooth and intuitive
- [ ] All components are accessible and responsive

## Implementation Prompts

### Prompt 1: Mode Layout System
```
Create a flexible mode layout system for an AI development assistant. Requirements:

1. Dynamic panel arrangement based on mode configuration
2. Resizable panels with persistent sizing
3. Mode-specific styling and theming
4. Smooth transitions between layouts
5. Responsive design for different screen sizes

Include support for sidebar, main content, and floating panels. Ensure layouts are optimized for each mode's workflow.
```

### Prompt 2: Context-Aware Command Palette
```
Implement an intelligent command palette that adapts to the current mode and context. Features:

1. Fuzzy search with relevance scoring
2. Command categorization and grouping
3. Contextual command suggestions
4. Keyboard navigation and shortcuts
5. Recent commands and favorites

Include visual indicators for command relevance and execution status.
```

### Prompt 3: Mode-Specific Widget System
```
Design a widget system for displaying mode-specific information and tools. Requirements:

1. Architect mode: Diagram viewer, requirements tracker, decision log
2. Debug mode: Log viewer, stack trace analyzer, variable inspector
3. Review mode: Diff viewer, checklist, metrics dashboard
4. Configurable widget layouts and sizing
5. Real-time data updates and interactions

Focus on performance and usability for development workflows.
```

### Prompt 4: Smart Input with Contextual Suggestions
```
Create an intelligent input system that provides contextual suggestions and assistance. Features:

1. Real-time suggestion generation based on context
2. Template expansion and snippet insertion
3. File and symbol autocompletion
4. Voice input support
5. Multi-modal input (text, files, images)

Include debounced suggestion loading and keyboard navigation.
```
# Claude Code Session: UI/UX Enhancement Plan

This document outlines a detailed plan to refactor and enhance the User Interface (UI) and User Experience (UX) of the Claude Code Session page. The primary focus is on improving layout, visual hierarchy, and user flow, while also laying the groundwork for better component architecture and long-term maintainability.

## 1. Guiding Principles

*   **Clarity and Focus:** The user should always know where they are, what they can do, and what the system is doing. The UI should guide their attention to the most important elements at any given time.
*   **Composability and Reusability:** Break down the monolithic `ClaudeCodeSession` component into smaller, single-purpose components that are easier to understand, test, and reuse.
*   **State Management:** Centralize and streamline state management to reduce prop-drilling and create a more predictable data flow. We will leverage React Context and custom hooks for this.
*   **Progressive Disclosure:** Hide complexity by default. Show advanced options and information only when the user needs them.

## 2. Proposed Component Architecture

The current `ClaudeCodeSession` component is a "god component" that handles everything. We will refactor this into a more structured and logical architecture.

```mermaid
graph TD
    subgraph PageContainer["@/src/pages/ClaudeSessionPage.tsx (New)"]
        A[SessionProvider] --> B[ClaudeSessionPage]
    end

    subgraph SessionProvider["@/src/contexts/SessionContext.tsx (New)"]
        A -- Manages all session state & logic --> B
    end

    subgraph ClaudeSessionPage
        C[SessionLayout]
    end

    subgraph SessionLayout["@/src/components/session/SessionLayout.tsx (New)"]
        C --> D[SessionHeader]
        C --> E[MainContent]
        C --> F[SessionFooter]
    end

    subgraph MainContent
        E --> G[MessageFlow]
        E -- Optional --> H[SidePanel]
    end
    
    subgraph SidePanel
        H --> I[TimelineNavigator]
        H --> J[WebviewPreview]
        H --> K[ModeSpecificPanels]
    end

    subgraph SessionFooter["@/src/components/session/SessionFooter.tsx (New)"]
        F --> L[SmartPromptInput]
        F --> M[PromptQueue]
    end

    style PageContainer fill:#f9f,stroke:#333,stroke-width:2px
    style SessionProvider fill:#ccf,stroke:#333,stroke-width:2px
```

**Key Changes:**

*   **`SessionProvider` (New):** A new context provider will encapsulate all the state and logic currently in `ClaudeCodeSession`. This includes `messages`, `isLoading`, `error`, `claudeSessionId`, and all the handler functions (`handleSendPrompt`, `handleCancelExecution`, etc.). Components will access this via a `useSession()` hook.
*   **`ClaudeSessionPage` (New):** A new top-level component that simply composes the `SessionProvider` and the `SessionLayout`.
*   **`SessionLayout` (New):** This component will be responsible for the overall page structure (header, main content, footer). It will use CSS Grid or Flexbox to manage the layout.
*   **`SessionHeader` (Refactor):** The existing `SessionHeader` will be simplified. It will receive props directly from the `useSession()` hook.
*   **`MainContent` (Conceptual):** A wrapper for the main content area, which will contain the `MessageFlow` and the `SidePanel`.
*   **`SidePanel` (New):** A new component to manage the various side panels (Timeline, Preview, Mode-Specific). This will simplify the logic in the main layout.
*   **`SessionFooter` (New):** A new component to group the `SmartPromptInput` and `PromptQueue`, creating a clear "action bar" at the bottom of the page.

## 3. Layout, Visual Hierarchy & User Flow Recommendations

### 3.1. Main Layout

*   **Consistent Padding & Spacing:** Introduce a consistent spacing scale (e.g., using multiples of 4px or 8px) and apply it throughout the interface. This will create a more harmonious and professional look.
*   **Clearer Gutters:** Increase the gutter width between the main content area and the side panel to create a stronger visual separation.
*   **Vertical Rhythm:** Ensure consistent vertical spacing between elements in the message flow to improve readability.

### 3.2. Session Header

*   **Group Related Actions:** Group the "Back" button and session title on the left. Group session metadata (ID, tokens) and actions (Copy, Timeline, Settings) on the right.
*   **Prioritize Information:** Make the project path more prominent. Consider using a breadcrumb-style display for long paths.
*   **Reduce Clutter:** The header is currently quite busy. We can simplify it by using icons more effectively and perhaps moving less-used actions into the "Settings" dropdown.

### 3.3. Message Flow

*   **Improved Readability:** Increase the line height and add more padding within message bubbles.
*   **Distinguish User vs. Assistant:** Use different background colors or a subtle border to more clearly distinguish between user prompts and assistant responses.
*   **Better Tool Rendering:** The rendering of tool usage and results can be improved. We should design a standard, clean format for displaying tool calls and their outputs.

### 3.4. Prompt Input & Queue

*   **Floating Input:** The current floating prompt input is good, but it can sometimes feel disconnected from the conversation. We will anchor it to the bottom of the screen within the `SessionFooter`.
*   **Queued Prompts:** The queued prompts display is a bit small and can be easily missed. We will make this more prominent and provide clearer controls for managing the queue.

## 4. Actionable Implementation Plan

Here is a step-by-step plan to implement these changes.

1.  **Create `SessionProvider` and `useSession` hook:**
    *   Create `src/contexts/SessionContext.tsx`.
    *   Move all state and handler functions from `ClaudeCodeSession.tsx` into the `SessionProvider`.
    *   Export a `useSession` hook to access the context.

2.  **Refactor `ClaudeCodeSession.tsx`:**
    *   Rename `ClaudeCodeSession.tsx` to `ClaudeSessionPage.tsx` and move it to a new `src/pages` directory.
    *   Wrap the entire component in the new `SessionProvider`.
    *   Replace direct state access and prop drilling with the `useSession` hook.

3.  **Create `SessionLayout.tsx`, `SessionHeader.tsx`, `SessionFooter.tsx`:**
    *   Create the new layout components.
    *   Move the relevant JSX from `ClaudeSessionPage.tsx` into these new components.
    *   Refactor the `SessionHeader` to be a "dumb" component that receives props from the `useSession` hook.

4.  **Implement the `SidePanel.tsx` component:**
    *   Create the `SidePanel` component to manage the logic for showing/hiding the Timeline, Preview, and Mode-Specific panels.

5.  **Apply CSS changes for layout and spacing:**
    *   Update the CSS to reflect the new layout structure and apply the consistent spacing and gutter improvements.
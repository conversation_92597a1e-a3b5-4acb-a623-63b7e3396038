:root {
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --secondary-color: #14b8a6;
    --accent-color: #f59e0b;
    --success-color: #22c55e;
    --error-color: #ef4444;
    --background-light: #f8fafc;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

#code-search-container {
    margin-bottom: 24px;
    margin-left: 0;
    max-width: none;
    width: 100%;
    min-height: 200px;
    box-shadow: var(--shadow-md);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background: white;
    padding: 12px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

#code-search-container:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

#search-results {
    order: 4;
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background-color: var(--background-light);
    margin-top: 8px;
    width: 100%;
    max-width: none;
    box-shadow: var(--shadow-sm);
    position: relative;
    transition: all 0.3s ease;
}

#search-results:hover {
    box-shadow: var(--shadow-md);
}

#search-results ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

#search-results li {
    padding: 8px 8px;
    border-bottom: 1px solid var(--border-color);
    background: white;
    transition: all 0.2s ease-in-out;
}

#search-results li:last-child {
    border-bottom: none;
    border-radius: 0 0 12px 12px;
}

#search-results li:last-child {
    border-bottom: none;
}

#search-results li:hover {
    background-color: var(--background-light);
    transform: scale(1.01);
}

#search-results code {
    font-family: 'JetBrains Mono', ui-monospace, 'SF Mono', Menlo, Consolas, monospace;
    font-size: 12px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    padding: 12px;
    margin: 16px 0;
    display: block;
    overflow-x: auto;
    width: 100%;
    max-width: none;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 12px;
    order: 1;
    margin-bottom: 8px;
}

#search-input {
    flex: 1;
    min-width: 0;
    padding: 5px 12px;
    font-size: 14px;
    line-height: 20px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: white;
    transition: all 0.2s ease;
}

#search-input:focus {
    border-color: #0969da;
    outline: none;
    box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.3);
}

.buttons-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    order: 2;
    width: 100%;
    margin-bottom: 16px;
}

#search-btn {
    white-space: nowrap;
    padding: 5px 16px;
    font-size: 14px;
    line-height: 20px;
    background-color: #f6f8fa;
    color: #24292f;
    border: 1px solid rgba(27, 31, 36, 0.15);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    box-shadow: 0 1px 0 rgba(27, 31, 36, 0.04);
}

#search-btn:hover {
    background-color: #f3f4f6;
    border-color: rgba(27, 31, 36, 0.15);
}

#search-btn:active {
    background-color: #ebecf0;
    box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.2);
}

#search-btn:disabled {
    background-color: #f6f8fa;
    border-color: rgba(27, 31, 36, 0.15);
    color: rgba(27, 31, 36, 0.3);
    cursor: not-allowed;
}

#index-repo-btn {
    order: unset;
    align-self: flex-start;
    white-space: nowrap;
    padding: 5px 16px;
    font-size: 14px;
    line-height: 20px;
    background-color: #2da44e;
    color: white;
    border: 1px solid rgba(27, 31, 36, 0.15);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    box-shadow: 0 1px 0 rgba(27, 31, 36, 0.1);
    width: fit-content;
}

#index-repo-btn:hover {
    background-color: #2c974b;
    transform: none;
    box-shadow: 0 1px 0 rgba(27, 31, 36, 0.1);
}

#index-repo-btn:active {
    background-color: #298e46;
    box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.2);
}

#index-repo-btn:disabled {
    background-color: #94d3a2;
    border-color: rgba(27, 31, 36, 0.15);
    color: rgba(255, 255, 255, 0.8);
    cursor: not-allowed;
}

#clear-index-btn {
    order: unset;
    align-self: flex-start;
    white-space: nowrap;
    padding: 5px 16px;
    font-size: 14px;
    line-height: 20px;
    background-color: #f6f8fa;
    color: #cf222e;
    border: 1px solid rgba(27, 31, 36, 0.15);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    box-shadow: 0 1px 0 rgba(27, 31, 36, 0.04);
    display: block;
    width: fit-content;
}

#clear-index-btn:hover {
    background-color: #f3f4f6;
    color: #a40e26;
    border-color: rgba(27, 31, 36, 0.15);
}

#clear-index-btn:active {
    background-color: #ebecf0;
    box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.2);
}

#clear-index-btn:disabled {
    background-color: #f6f8fa;
    border-color: rgba(27, 31, 36, 0.15);
    color: rgba(207, 34, 46, 0.5);
    cursor: not-allowed;
}

.flash-warn {
    color: var(--accent-color);
    background: linear-gradient(to right, #fef3c7, #fffbeb);
    border: 1px solid var(--accent-color);
    border-radius: 8px;
    padding: 12px 20px;
    margin-bottom: 20px;
    animation: slideIn 0.3s ease-out;
}

#indexing-status {
    order: 5;
    font-size: 14px;
    color: var(--text-secondary);
    margin-top: 12px;
    padding: 8px;
    border-radius: 6px;
    background-color: var(--background-light);
    transition: all 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Progress bar animation */
.progress-bar {
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
    transition: width 0.3s ease;
    animation: shimmer 2s infinite linear;
    background-size: 200% 100%;
}

@keyframes shimmer {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Search results link and file info improvements */
#search-results .file-path-link {
    font-size: 14px;
    font-weight: 600;
    color: #0969da;
    text-decoration: none;
    transition: color 0.2s ease;
}

#search-results .file-path-link:hover {
    color: #0550ae;
    text-decoration: underline;
}

#search-results .file-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

#search-results .code-preview {
    background: linear-gradient(135deg, #f6f8fa 0%, #f1f3f4 100%);
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    padding: 16px;
    font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    color: #24292f;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin-top: 12px;
    margin-left: 0;
    margin-right: 0;
    width: 100%;
    max-width: none;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Recent repos improvements */
#recent-repos .repo-item {
    padding: 16px 20px;
    border-bottom: 1px solid #e1e4e8;
    transition: all 0.2s ease;
}

#recent-repos .repo-item:hover {
    background-color: #f8f9fa;
    border-left: 4px solid #0969da;
    padding-left: 20px;
}

#recent-repos .repo-name {
    font-size: 15px;
    font-weight: 600;
    color: #0969da;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 6px;
}

#recent-repos .repo-stats {
    color: #656d76;
    font-size: 12px;
    margin: 2px 0;
}

/* Match score styling */
.match-score {
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    margin-left: 8px;
}

.match-score.high {
    background-color: #dcfce7;
    color: #15803d;
    border: 1px solid #bbf7d0;
}

.match-score.medium {
    background-color: #fef3c7;
    color: #d97706;
    border: 1px solid #fed7aa;
}

.match-score.low {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

/* Search result item hover effect */
.search-result-item {
    transition: all 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8fafc;
    border-left: 3px solid var(--primary-color);
    padding-left: 13px;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    #code-search-container {
        max-width: none;
        width: 100%;
        margin: 16px 0;
    }
    
    #search-results {
        max-width: none;
        width: 100%;
    }
    
    #recent-repos {
        max-width: none;
        width: 100%;
    }
    
    #search-results code {
        margin: 16px 0;
        width: 100%;
        max-width: none;
        padding: 16px;
    }
    
    #search-results li {
        padding: 16px 20px;
    }
}

/* Make sure our UI aligns with GitHub's UI */
.Layout-sidebar #code-search-container {
    margin-top: 25px;
    width: 100%;
}

.Layout-main > #code-search-container {
    margin-left: 0;
}

#repo-content-pjax-container + #code-search-container,
nav.UnderlineNav + #code-search-container {
    margin-top: 16px;
    margin-left: 0;
}

#show-recent-btn {
    font-size: 12px;
    padding: 4px 8px;
    background: transparent;
    border: none;
    color: #656d76;
    cursor: pointer;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s ease;
}

#show-recent-btn:hover {
    background-color: #f3f4f6;
    color: #24292f;
}

#recent-repos {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: white;
    margin-top: 12px;
    max-width: none;
    width: 100%;
    box-shadow: var(--shadow-sm);
}

#recent-repos .Box-header {
    background-color: #f6f8fa;
    border-bottom: 1px solid var(--border-color);
    border-radius: 8px 8px 0 0;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#recent-repos .Box-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

#close-recent-btn {
    background: transparent;
    border: none;
    color: #656d76;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

#close-recent-btn:hover {
    background-color: #f3f4f6;
    color: #24292f;
}

#recent-repos-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
}

.go-to-repo-btn {
    font-size: 12px;
    padding: 4px 8px;
    background-color: #f6f8fa;
    border: 1px solid rgba(27, 31, 36, 0.15);
    border-radius: 4px;
    color: #24292f;
    cursor: pointer;
    transition: all 0.2s ease;
}

.go-to-repo-btn:hover {
    background-color: #f3f4f6;
}

/* Recent repos list item styles - integrated above with repo-item class */
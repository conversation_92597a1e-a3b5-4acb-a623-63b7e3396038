{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "composite": true, "allowSyntheticDefaultImports": true, "downlevelIteration": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"]}
{"compilerOptions": {"target": "ES2020", "module": "ES2020", "lib": ["ES2020"], "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "composite": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"], "references": [{"path": "../core"}]}
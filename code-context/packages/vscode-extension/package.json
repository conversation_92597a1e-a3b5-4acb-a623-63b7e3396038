{"name": "semanticcodesearch", "displayName": "Semantic Code Search", "publisher": "zilliz", "description": "Code indexing and semantic search (built by Code Context)", "version": "0.0.7", "private": true, "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "keywords": ["code", "vibe coding", "AI coding", "agent", "MCP", "code search", "semantic search", "index", "search", "navigation", "symbols"], "icon": "resources/icon.png", "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "semanticCodeSearch.semanticSearch", "title": "Semantic Search", "category": "Semantic Code Search"}, {"command": "semanticCodeSearch.indexCodebase", "title": "Index Codebase", "category": "Semantic Code Search"}, {"command": "semanticCodeSearch.clearIndex", "title": "Clear Index", "category": "Semantic Code Search"}], "menus": {"editor/context": [{"command": "semanticCodeSearch.semanticSearch", "when": "editorHasSelection", "group": "navigation@100"}]}, "viewsContainers": {"activitybar": [{"id": "semanticCodeSearchSidebar", "title": "Semantic Code Search", "icon": "./resources/activity_bar.svg"}]}, "views": {"semanticCodeSearchSidebar": [{"id": "semanticSearchView", "name": "Semantic Search", "type": "webview"}]}, "configuration": {"title": "Semantic Code Search", "properties": {"semanticCodeSearch.embeddingProvider.provider": {"type": "string", "enum": ["OpenAI", "VoyageAI", "Ollama", "Gemini"], "description": "Embedding service provider"}, "semanticCodeSearch.embeddingProvider.model": {"type": "string", "description": "Embedding model to use"}, "semanticCodeSearch.embeddingProvider.apiKey": {"type": "string", "description": "API key for the embedding provider"}, "semanticCodeSearch.embeddingProvider.baseURL": {"type": "string", "description": "Custom API endpoint URL (optional)"}, "semanticCodeSearch.embeddingProvider.host": {"type": "string", "description": "Ollama server host URL (optional)"}, "semanticCodeSearch.embeddingProvider.keepAlive": {"type": "string", "description": "Keep model alive duration for <PERSON><PERSON><PERSON> (optional)"}, "semanticCodeSearch.embeddingProvider.outputDimensionality": {"type": "number", "description": "Output dimension for Gemini embedding (supports Matryoshka representation: 3072, 1536, 768, 256)"}, "semanticCodeSearch.milvus.address": {"type": "string", "description": "Milvus server address (e.g., http://localhost:19530)"}, "semanticCodeSearch.milvus.token": {"type": "string", "description": "Milvus authentication token (optional)"}, "semanticCodeSearch.autoSync.enabled": {"type": "boolean", "default": true, "description": "Enable automatic synchronization of workspace index every 5 minutes"}, "semanticCodeSearch.autoSync.intervalMinutes": {"type": "number", "default": 5, "minimum": 1, "maximum": 60, "description": "Auto-sync interval in minutes (1-60). This runs periodic sync to keep the index updated."}, "semanticCodeSearch.splitter.type": {"type": "string", "enum": ["langchain", "ast"], "enumDescriptions": ["Language-aware text splitter with support for multiple programming languages", "AST-based code splitter that splits code by logical units (functions, classes, etc.)"], "default": "langchain", "description": "Code splitter type to use for breaking down code into chunks"}, "semanticCodeSearch.splitter.chunkSize": {"type": "number", "default": 1000, "minimum": 100, "maximum": 5000, "description": "Maximum size of each code chunk in characters"}, "semanticCodeSearch.splitter.chunkOverlap": {"type": "number", "default": 200, "minimum": 0, "maximum": 1000, "description": "Number of overlapping characters between consecutive chunks"}}}}, "scripts": {"vscode:prepublish": "pnpm run webpack", "compile": "pnpm clean && tsc --build --force && node copy-assets.js", "webpack": "pnpm clean && webpack --mode production && node copy-assets.js", "webpack:dev": "webpack --mode development && node copy-assets.js", "watch": "tsc -watch -p ./", "clean": "rm -rf dist", "lint": "eslint src", "lint:fix": "eslint src --fix", "typecheck": "tsc --noEmit", "package": "vsce package --no-dependencies", "release": "vsce publish --no-dependencies"}, "dependencies": {"@zilliz/code-context-core": "workspace:*", "web-tree-sitter": "^0.25.6"}, "devDependencies": {"@types/node": "20.x", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/vsce": "^3.4.0", "copy-webpack-plugin": "^12.0.2", "eslint": "^9.25.1", "source-map-loader": "^5.0.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.99.9", "webpack-cli": "^5.1.4"}, "repository": {"type": "git", "url": "https://github.com/zilliztech/code-context.git", "directory": "packages/vscode-extension"}, "license": "MIT"}
# CodeContext Environment Variables Example
# 
# Copy this file to ~/.codecontext/.env and modify the values as needed
#
# Usage: cp env.example ~/.codecontext/.env

# =============================================================================
# Embedding Provider Configuration
# =============================================================================

# Embedding provider: OpenAI, VoyageAI, Gemini, Ollama
EMBEDDING_PROVIDER=OpenAI

# Embedding model (provider-specific)
EMBEDDING_MODEL=text-embedding-3-small

# Embedding batch size for processing (default: 100)
EMBEDDING_BATCH_SIZE=100

# =============================================================================
# OpenAI Configuration
# =============================================================================

# OpenAI API key
OPENAI_API_KEY=your-openai-api-key-here

# OpenAI base URL (optional, for custom endpoints)
# OPENAI_BASE_URL=https://api.openai.com/v1

# =============================================================================
# VoyageAI Configuration
# =============================================================================

# VoyageAI API key
# VOYAGEAI_API_KEY=your-voyageai-api-key-here

# =============================================================================
# Gemini Configuration
# =============================================================================

# Google Gemini API key
# GEMINI_API_KEY=your-gemini-api-key-here

# =============================================================================
# Ollama Configuration
# =============================================================================

# Ollama model name
# OLLAMA_MODEL=

# Ollama host (default: http://localhost:11434)
# OLLAMA_HOST=http://localhost:11434

# =============================================================================
# Vector Database Configuration (Milvus/Zilliz)
# =============================================================================

# Milvus server address
MILVUS_ADDRESS=your-zilliz-cloud-public-endpoint

# Milvus authentication token
# MILVUS_TOKEN=your-zilliz-cloud-api-key

# Zilliz Cloud base URL (optional, default: https://api.cloud.zilliz.com)
# ZILLIZ_BASE_URL=https://api.cloud.zilliz.com

# =============================================================================
# Code Splitter Configuration
# =============================================================================

# Code splitter type: ast, langchain
SPLITTER_TYPE=ast

name: CI

on:
  push:
    branches: [ master, main ]
  pull_request:
    branches: [ master, main ]

jobs:
  lint_and_build:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [20.x, 22.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 10
        
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'pnpm'
        
    - name: Install dependencies
      run: pnpm install --frozen-lockfile
      
    # - name: Lint code
    #   run: pnpm lint
      
    - name: Build packages
      run: pnpm build

